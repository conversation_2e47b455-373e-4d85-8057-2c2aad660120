<!--
  ~ Copyright (c) 2015-2022 BiliBili Inc.
  -->

<!DOCTYPE html>
<html lang="" xmlns="http://www.w3.org/1999/html">
    <script type="text/javascript">
    </script>

    <head>
        <meta charset="UTF-8">
        <title>backdoor</title>
    </head>
    <body>
        <form action='' method="post" th:object="${ctx}">
            <label for='exp'>在线调试</label>
            <br/>
            <textarea id='exp' type="text" name="exp" rows='10' cols='120'
                      placeholder="IDEA右键获取方法引用, 例如: com.bilibili.sycpb.cpm.scv.app.controller.view.BackdoorV2Controller#eval&#10;一行一个参数" th:text="${ctx.exp}"></textarea>
            <br/>
            <input type='submit' value='调用方法'/>
        </form>
        <label for="res"></label>
        <br/>
        <textarea id="res" rows='20' cols='120' placeholder='调用返回的结果' th:text="${ctx.res}"></textarea>
    </body>
</html>