package sycpb.platform.cpm.pandora.app.aspect;

import com.bilibili.databus.exception.DataBusException;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import io.grpc.Context;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.jooq.exception.DataAccessException;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.CannotCreateTransactionException;
import redis.shaded.jedis.exceptions.JedisDataException;
import sycpb.platform.cpm.pandora.app.grpc.interceptor.MetricReportServerInterceptor;
import sycpb.platform.cpm.pandora.app.grpc.interceptor.MetricDataHolder;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraServiceErr;
import sycpb.platform.cpm.pandora.infra.exception.BusinessException;
import sycpb.platform.cpm.pandora.service.exceptions.BusinessRuleViolationError;

import javax.annotation.Nullable;
import java.lang.reflect.UndeclaredThrowableException;
import java.net.SocketTimeoutException;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @Description RPC 服务切面，用于简化 gRPC 调用、捕获异常并准备指标数据。
 *              指标上报的具体逻辑已移至 MetricReportServerInterceptor。
 * @date 2025/4/23
 */
@Slf4j
@Aspect
@Component
@Order(1)
@RequiredArgsConstructor
public class AspectService {

    /** Operator 类的完全限定名，用于 MAPI 请求判断 */
    private static final String OPERATOR_CLASS_NAME = "com.bapis.ad.pandora.core.Operator";

    /**
     * 环绕通知，应用于所有标记了 @RpcServiceAspect 注解的类的方法。
     * @param joinPoint 切入点，代表被拦截的方法
     * @return 被拦截方法的返回值 (如果正常执行)
     */
    @Around("@within(sycpb.platform.cpm.pandora.app.aspect.RpcServiceAspect)")
    public Object rpcServiceAspect(ProceedingJoinPoint joinPoint) {
        final var args = joinPoint.getArgs();
        final var streamObserver = (StreamObserver<?>) args[1];

        // 从 gRPC Context 中获取传递的信息
        AtomicReference<MetricDataHolder> metricDataHolderRef = MetricReportServerInterceptor.METRIC_DATA_HOLDER_CTX_KEY.get(Context.current());
        String accountId = MetricReportServerInterceptor.ACCOUNT_ID_CTX_KEY.get(Context.current());
        String requestSource = MetricReportServerInterceptor.REQUEST_SOURCE_CTX_KEY.get(Context.current());

        // 防御性检查：如果 Holder 未在 Context 中找到 (理论上不应发生)
        if (metricDataHolderRef == null) {
             log.info("AspectService: 在 Context 中未找到 MetricDataHolder，拦截器可能未运行或失败，将在无详细指标的情况下继续执行。method:{}", getMethodName(joinPoint));
             try {
                 return joinPoint.proceed();
             } catch (Throwable t) {
                 // 在没有指标上下文的情况下执行失败
                 log.error("AspectService: 在没有指标上下文的情况下执行失败。method:{}", getMethodName(joinPoint), t);
                 streamObserver.onError(Status.UNKNOWN
                         .withDescription(t.getMessage())
                         .withCause(t)
                         .asRuntimeException());
                 return null;
             }
        }

        // 获取可变的 MetricDataHolder 实例
        MetricDataHolder metricData = metricDataHolderRef.get();
        String methodName = getMethodName(joinPoint);
        metricData.methodName = methodName;
        ErrorCodeEnum.DomainType domainType = getDomainTypeFromAnnotation(joinPoint);
        metricData.domainType = domainType.name();
        boolean isMapi = isMapiRequest(joinPoint);
        metricData.isMapi = isMapi;

        try {
            final var obj = joinPoint.proceed(); // gRPC 成功执行时，MetricDataHolder 中使用默认的成功状态 (SUCCESS)
            streamObserver.onCompleted();
            return obj;
        } catch (BusinessException businessException) {
            metricData.domainType = businessException.getDomainType().name();
            metricData.errType = businessException.getErrorType().name();
            metricData.errorCode = businessException.getErrCode();
            metricData.errorMsg = businessException.getSubCode().getDesc();
            log.error("businessException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getErrorCode(),metricData.getErrorMsg(), isMapi, businessException);
            streamObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(businessException.getErrMsg()) // 使用原始异常信息
                    .asRuntimeException());
            return null;
        } catch (IllegalArgumentException illegalArgumentException) {
            BusinessException businessException = convertToBizException(joinPoint, illegalArgumentException);
            metricData.domainType = businessException.getDomainType().name();
            metricData.errType = businessException.getErrorType().name();
            metricData.errorCode = businessException.getErrCode();
            metricData.errorMsg = businessException.getSubCode().getDesc();
            log.error("illegalArgumentException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getErrorCode(), metricData.getErrorMsg(), isMapi, businessException);
            streamObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(illegalArgumentException.getMessage()) // 使用原始异常信息
                    .asRuntimeException());
            return null;
        } catch (StatusRuntimeException err) {
            // 捕获下游 gRPC 调用返回的异常，默认为系统错误
            metricData.errType = ErrorCodeEnum.ErrorType.SYS_ERR.name();
            switch(err.getStatus().getCode()) {
                case DEADLINE_EXCEEDED:
                    metricData.errorCode = ErrorCodeEnum.SubCode.TIMEOUT.getCode();
                    metricData.errorMsg = ErrorCodeEnum.SubCode.TIMEOUT.getDesc();
                    break;
                case CANCELLED:
                    metricData.errorCode = ErrorCodeEnum.SubCode.CLIENT_CANCELLED.getCode();
                    metricData.errorMsg = ErrorCodeEnum.SubCode.CLIENT_CANCELLED.getDesc();
                    metricData.errType = ErrorCodeEnum.ErrorType.BIZ_ERR.name(); // 业务错误
                    break;
                case INVALID_ARGUMENT: // 参数无效
                    metricData.errorCode = ErrorCodeEnum.SubCode.PARAM_INVALID.getCode();
                    metricData.errorMsg = ErrorCodeEnum.SubCode.PARAM_INVALID.getDesc();
                    metricData.errType = ErrorCodeEnum.ErrorType.BIZ_ERR.name(); // 业务错误
                    break;
                case UNAUTHENTICATED: // 未认证
                case PERMISSION_DENIED: // 无权限
                     metricData.errorCode = ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                     metricData.errorMsg = ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                     break;
                case NOT_FOUND: // 未找到资源
                     metricData.errorCode = ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                     metricData.errorMsg = ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                     break;
                case UNAVAILABLE: // 下游服务不可用
                     metricData.errorCode = ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                     metricData.errorMsg = ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                     break;
                default: // 其他下游 gRPC 错误
                    metricData.errorCode = ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                    metricData.errorMsg = ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                    break;
            }
            log.error("statusRuntimeException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}, status:{}", methodName, accountId, requestSource, metricData.getErrorCode(), metricData.getErrorMsg(), isMapi, err.getStatus(), err);
            streamObserver.onError(err);  // 将原始的 StatusRuntimeException 传递给客户端
            return null;
        } catch (NullPointerException err) {
            metricData.errType = ErrorCodeEnum.ErrorType.SYS_ERR.name();
            metricData.errorCode = ErrorCodeEnum.SubCode.NULL_POINTER_ERR.getCode();
            metricData.errorMsg = ErrorCodeEnum.SubCode.NULL_POINTER_ERR.getDesc();
            log.error("NullPointerException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getErrorCode(), metricData.getErrorMsg(), isMapi, err);
            streamObserver.onError(Status.INTERNAL
                    .withDescription(err.getMessage())
                    .withCause(err)
                    .asRuntimeException());
            return null;
        } catch (DataAccessException | CannotCreateTransactionException err) {
            metricData.errType = ErrorCodeEnum.ErrorType.SYS_ERR.name();
            metricData.errorCode = ErrorCodeEnum.SubCode.DB_ERROR.getCode();
            metricData.errorMsg = ErrorCodeEnum.SubCode.DB_ERROR.getDesc();
            log.error("DataAccessException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getErrorCode(), metricData.getErrorMsg(), isMapi, err);
            streamObserver.onError(Status.INTERNAL
                    .withDescription(err.getMessage())
                    .withCause(err)
                    .asRuntimeException());
            return null;
        } catch (DataBusException err) {
            metricData.errType = ErrorCodeEnum.ErrorType.SYS_ERR.name();
            metricData.errorCode = ErrorCodeEnum.SubCode.DATABUS_ERROR.getCode();
            metricData.errorMsg = ErrorCodeEnum.SubCode.DATABUS_ERROR.getDesc();
            log.error("DataBusException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getErrorCode(), metricData.getErrorMsg(), isMapi, err);
            streamObserver.onError(Status.INTERNAL
                    .withDescription(err.getMessage())
                    .withCause(err)
                    .asRuntimeException());
            return null;
        } catch (PandoraServiceErr err) {
            metricData.errType = ErrorCodeEnum.ErrorType.BIZ_ERR.name();
            metricData.errorCode = ErrorCodeEnum.SubCode.PARAM_INVALID.getCode();
            metricData.errorMsg = ErrorCodeEnum.SubCode.PARAM_INVALID.getDesc();
            log.error("PandoraServiceErr, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getErrorCode(), metricData.getErrorMsg(), isMapi, err);
            streamObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(err.getMsg())
                    .asRuntimeException());
            return null;
        } catch (BusinessRuleViolationError err) {
            metricData.errType = ErrorCodeEnum.ErrorType.BIZ_ERR.name();
            metricData.errorCode = ErrorCodeEnum.SubCode.PARAM_INVALID.getCode();
            metricData.errorMsg = ErrorCodeEnum.SubCode.PARAM_INVALID.getDesc();
            log.error("BusinessRuleViolationError, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getErrorCode(), metricData.getErrorMsg(), isMapi, err);
            streamObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(err.getMessage())
                    .asRuntimeException());
            return null;
        } catch (JedisDataException err) {
            metricData.errType = ErrorCodeEnum.ErrorType.SYS_ERR.name();
            metricData.errorCode = ErrorCodeEnum.SubCode.REDIS_ERROR.getCode();
            metricData.errorMsg = ErrorCodeEnum.SubCode.REDIS_ERROR.getDesc();
            log.error("JedisDataException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getErrorCode(), metricData.getErrorMsg(), isMapi, err);
            streamObserver.onError(Status.INTERNAL
                    .withDescription(err.getMessage())
                    .withCause(err)
                    .asRuntimeException());
            return null;
        } catch (UndeclaredThrowableException ute) {
            // 处理 UndeclaredThrowableException，提取原始异常
            Throwable cause = ute.getUndeclaredThrowable();
            if (cause == null) {
                log.error("UndeclaredThrowableException with null cause, method:{}, accountId:{}, requestSource:{}, isMapi:{}",
                        methodName, accountId, requestSource, isMapi);
                cause = ute; // 使用原始异常作为fallback
            }
            if (cause instanceof SocketTimeoutException) {
                metricData.errType = ErrorCodeEnum.ErrorType.SYS_ERR.name();
                metricData.errorCode = ErrorCodeEnum.SubCode.TIMEOUT.getCode();
                metricData.errorMsg = ErrorCodeEnum.SubCode.TIMEOUT.getDesc();
                log.error("SocketTimeoutException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}",
                        methodName, accountId, requestSource, metricData.getErrorCode(), metricData.getErrorMsg(), isMapi, cause);
                streamObserver.onError(Status.DEADLINE_EXCEEDED
                        .withDescription(cause.getMessage())
                        .withCause(cause)
                        .asRuntimeException());
            } else {
                // 其他未声明的异常
                metricData.errType = ErrorCodeEnum.ErrorType.SYS_ERR.name();
                metricData.errorCode = ErrorCodeEnum.SubCode.SYSTEM_ERROR.getCode();
                metricData.errorMsg = ErrorCodeEnum.SubCode.SYSTEM_ERROR.getDesc();
                log.error("UndeclaredThrowableException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}",
                        methodName, accountId, requestSource, metricData.getErrorCode(), metricData.getErrorMsg(), isMapi, cause);
                streamObserver.onError(Status.UNKNOWN
                        .withDescription(cause.getMessage())
                        .withCause(cause)
                        .asRuntimeException());
            }
            return null;
        } catch (Throwable t) {
            // 捕获所有其他未发现到的异常
            metricData.errType = ErrorCodeEnum.ErrorType.SYS_ERR.name();
            metricData.errorCode = ErrorCodeEnum.SubCode.SYSTEM_ERROR.getCode();
            metricData.errorMsg = ErrorCodeEnum.SubCode.SYSTEM_ERROR.getDesc();
            log.error("throwable, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getErrorCode(), metricData.getErrorMsg(), isMapi, t);
            streamObserver.onError(Status.UNKNOWN
                    .withDescription(t.getMessage())
                    .withCause(t)
                    .asRuntimeException());
            return null;
        }
    }

    /**
     * 将 IllegalArgumentException 转换为 BusinessException。
     * 从注解中获取业务域类型。
     *
     * @param joinPoint 切入点
     * @param exception IllegalArgumentException 异常
     * @return 转换后的 BusinessException
     */
    private BusinessException convertToBizException(ProceedingJoinPoint joinPoint, IllegalArgumentException exception) {
        ErrorCodeEnum.DomainType domainType = getDomainTypeFromAnnotation(joinPoint);
        // 使用 PARAM_INVALID 作为错误子码，使用 IllegalArgumentException 的错误信息作为错误描述
        String errorMsg = exception.getMessage();
        return new BusinessException(domainType, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID, errorMsg);
    }

    /**
     * 从目标类上的 @RpcServiceAspect 注解中获取业务域类型。
     *
     * @param joinPoint 切入点
     * @return 业务域类型，如果注解不存在或未指定，则返回 OTHER
     */
    private ErrorCodeEnum.DomainType getDomainTypeFromAnnotation(ProceedingJoinPoint joinPoint) {
        Class<?> targetClass = joinPoint.getTarget().getClass();
        RpcServiceAspect annotation = targetClass.getAnnotation(RpcServiceAspect.class);
        if (annotation != null) {
            return annotation.domainType();
        }
        // 如果注解不存在或未指定 domainType，返回默认值
        return ErrorCodeEnum.DomainType.OTHER;
    }

    /**
     * 获取被拦截的方法名。
     *
     * @param joinPoint 切入点
     * @return 方法名
     */
    private String getMethodName(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        // 仅使用方法名
        return signature.getName();
        // 如果需要更详细的名称（类名.方法名），可以使用下面的代码:
        // return joinPoint.getTarget().getClass().getSimpleName() + "." + signature.getName();
    }

    /**
     * 判断 gRPC 请求是否为 MAPI 请求。
     * MAPI 请求的判断依据是请求参数中包含 Operator 对象，且其 flag 字段值为 1。
     *
     * @param joinPoint 切入点
     * @return 如果是 MAPI 请求则返回 true，否则返回 false
     */
    private boolean isMapiRequest(ProceedingJoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            // gRPC 方法至少需要请求对象和 StreamObserver 两个参数
            if (args == null || args.length < 2) {
                return false;
            }
            Object requestParam = args[0];
            if (requestParam == null) {
                return false;
            }
            // 获取Operator对象
            Object operator = getOperatorFromRequest(requestParam);
            if (operator == null) {
                return false;
            }
            // 检查flag字段值
            Integer flag = getOperatorFlag(operator);
            return flag != null && flag == 1;
        } catch (Exception e) {
            log.info("isMapiRequest, 判断MAPI请求时发生异常");
            return false;
        }
    }

    /**
     * 通过反射从请求参数对象中获取 Operator 对象。
     * 假设 Operator 对象通过名为 getOperator() 的方法获取。
     *
     * @param requestParam 请求参数对象
     * @return Operator 对象，如果不存在或获取失败则返回 null
     */
    @Nullable
    private Object getOperatorFromRequest(Object requestParam) {
        try {
            // 尝试获取 getOperator 方法
            java.lang.reflect.Method getOperatorMethod = requestParam.getClass().getMethod("getOperator");
            Object operator = getOperatorMethod.invoke(requestParam);
            if (operator != null && operator.getClass().getName().equals(OPERATOR_CLASS_NAME)) {
                 return operator;
            }
        } catch (Exception e) {
            log.info("isMapiRequest, 获取Operator对象失败");
        }
        return null;
    }

    /**
     * 通过反射获取 Operator 对象中的 flag 字段值。
     * 假设 flag 值通过名为 getFlag() 的方法获取，且返回类型为 Integer。
     *
     * @param operator Operator 对象
     * @return flag 字段值 (Integer)，如果获取失败或类型不匹配则返回 null
     */
    @Nullable
    private Integer getOperatorFlag(Object operator) {
        try {
            // 尝试获取 getFlag 方法
            java.lang.reflect.Method getFlagMethod = operator.getClass().getMethod("getFlag");
            Object flagObj = getFlagMethod.invoke(operator);
            if (flagObj instanceof Integer) {
                return (Integer) flagObj;
            } else if (flagObj != null) {
                log.info("isMapiRequest, 获取Operator对象的flag字段失败, 返回了预期之外的类型");
            }
        } catch (Exception e) {
            log.info("isMapiRequest, 获取Operator对象的flag字段失败");
        }
        return null;
    }
}