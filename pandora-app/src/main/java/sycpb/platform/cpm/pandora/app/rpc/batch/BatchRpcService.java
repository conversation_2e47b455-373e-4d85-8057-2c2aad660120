package sycpb.platform.cpm.pandora.app.rpc.batch;

import com.bapis.ad.pandora.core.batch.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.server.RPCService;
import sycpb.platform.cpm.pandora.app.aspect.RpcServiceAspect;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.service.api.core.batch.BatchApiMapper;
import sycpb.platform.cpm.pandora.service.api.core.batch.IBatchOperationService;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.BatchOperationReqBo;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.ListBatchOperationReqBo;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.RegisterResultBo;

@RpcServiceAspect(domainType = ErrorCodeEnum.DomainType.BATCH_OPERATION)
@RPCService
@RequiredArgsConstructor
public class BatchRpcService extends BatchServiceGrpc.BatchServiceImplBase {
    private final IBatchOperationService batchOperationService;
    private final ObjectMapper objectMapper;

    @SneakyThrows
    @Override
    public void getBatchOperation(GetBatchOperationReq request, StreamObserver<GetBatchOperationResp> responseObserver) {
        final var queryBo = new ListBatchOperationReqBo();
        queryBo.setOperationId(request.getOperationId());
        queryBo.setPageNo(1);
        queryBo.setPageSize(1);
        final var pagination = batchOperationService.listBatchOperations(queryBo);
        if (CollectionUtils.isEmpty(pagination.getDataList())) {
            responseObserver.onNext(GetBatchOperationResp.getDefaultInstance());
            return;
        }
        final var lauBatchOperationBo = pagination.getDataList().get(0);
        final var batchOperationReqBo = objectMapper.readValue(lauBatchOperationBo.getOperationExt(), BatchOperationReqBo.class);
        responseObserver.onNext(GetBatchOperationResp.newBuilder()
                .setOperationFull(BatchApiMapper.MAPPER.toRo(lauBatchOperationBo, batchOperationReqBo.getOperations()))
                .build());
    }

    @Override
    public void registerCampaignOperation(RegisterCampaignOperationReq request, StreamObserver<RegisterOperationResp> responseObserver) {
        final var registerResultBo = batchOperationService.register(BatchApiMapper.MAPPER.fromRpcBo(request));
        responseObserver.onNext(newRespInstance(registerResultBo));
    }

    @Override
    public void registerUnitOperation(RegisterUnitOperationReq request, StreamObserver<RegisterOperationResp> responseObserver) {
        final var registerResultBo = batchOperationService.register(BatchApiMapper.MAPPER.fromRpcBo(request));
        responseObserver.onNext(newRespInstance(registerResultBo));
    }

    @Override
    public void registerCreativeOperation(RegisterCreativeOperationReq request, StreamObserver<RegisterOperationResp> responseObserver) {
        final var registerResultBo = batchOperationService.register(BatchApiMapper.MAPPER.fromRpcBo(request));
        responseObserver.onNext(newRespInstance(registerResultBo));
    }

    @Override
    public void registerComponentOperation(RegisterComponentOperationReq request, StreamObserver<RegisterOperationResp> responseObserver) {
        final var operationId = batchOperationService.register(BatchApiMapper.MAPPER.fromRpcBo(request));
        responseObserver.onNext(newRespInstance(operationId));
    }

    @Override
    public void registerAnchorOperation(RegisterAnchorOperationReq request, StreamObserver<RegisterOperationResp> responseObserver) {
        final var operationId = batchOperationService.register(BatchApiMapper.MAPPER.fromRpcBo(request));
        responseObserver.onNext(newRespInstance(operationId));
    }

    @Override
    public void updateBatchOperation(UpdateBatchOperationReq request, StreamObserver<UpdateBatchOperationResp> responseObserver) {
        batchOperationService.updateStatus(request.getOperator().getOperatorId(), request.getOperationId(), request.getOperationStatus());
        responseObserver.onNext(UpdateBatchOperationResp.getDefaultInstance());
    }

    @Override
    public void listBatchOperation(ListBatchOperationReq request, StreamObserver<ListBatchOperationResp> responseObserver) {
        final var pagination = batchOperationService.listBatchOperations(BatchApiMapper.MAPPER.fromRpcBo(request));
        responseObserver.onNext(ListBatchOperationResp.newBuilder()
                .addAllEntity(BatchApiMapper.MAPPER.toBatchOperationEntities(pagination.getDataList()))
                .setTotal(pagination.getTotal())
                .build());
    }

    @Override
    public void listBatchOperationDetail(ListBatchOperationDetailReq request, StreamObserver<ListBatchOperationDetailResp> responseObserver) {
        final var bos = batchOperationService.listBatchOperationDetails(request.getOperationId());
        responseObserver.onNext(ListBatchOperationDetailResp.newBuilder()
                .addAllEntity(BatchApiMapper.MAPPER.toBatchOperationDetailEntities(bos)).build());
    }

    private RegisterOperationResp newRespInstance(RegisterResultBo bo) {
        return BatchApiMapper.MAPPER.toRpcResp(bo);
    }
}
