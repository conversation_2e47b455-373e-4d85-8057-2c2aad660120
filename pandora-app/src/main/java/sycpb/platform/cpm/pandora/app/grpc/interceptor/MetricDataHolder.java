package sycpb.platform.cpm.pandora.app.grpc.interceptor;

import lombok.Data;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;

/**
 * 用于在 gRPC Context 中传递指标数据的Holder.
 * 由 Interceptor 创建并放入 Context, 由 AspectService 修改其内部状态.
 */
@Data
public class MetricDataHolder {
    /** 错误码, 默认为成功 */
    public String errorCode = ErrorCodeEnum.SubCode.SUCCESS.getCode();
    /** 错误信息, 默认为成功 */
    public String errorMsg = ErrorCodeEnum.SubCode.SUCCESS.getDesc();
    /** 业务域类型, 默认为 OTHER */
    public String domainType = ErrorCodeEnum.DomainType.OTHER.name();
    /** 错误类型 (BIZ_ERR, SYS_ERR, NONE), 默认为 NONE */
    public String errType = "NONE";
    /** 是否为 MAPI 请求, 默认为 false */
    public Boolean isMapi = false;
    /** gRPC 方法名, 默认为 UNKNOWN */
    public String methodName = "UNKNOWN";

    @Override
    public String toString() {
        return "MetricDataHolder{" +
                "errorCode='" + errorCode + '\'' +
                ", errorMsg='" + errorMsg + '\'' +
                ", domainType='" + domainType + '\'' +
                ", errType='" + errType + '\'' +
                ", isMapi=" + isMapi +
                ", methodName='" + methodName + '\'' +
                '}';
    }
}
