package sycpb.platform.cpm.pandora.app.rpc.list;

import com.bapis.ad.pandora.core.list.*;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import pleiades.venus.starter.rpc.server.RPCService;
import sycpb.platform.cpm.pandora.app.aspect.RpcServiceAspect;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.service.api.core.v6.campaign.CampaignApiMapper;
import sycpb.platform.cpm.pandora.service.api.core.v6.campaign.ICampaignService;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.IUnitCreativeService;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.UnitCreativeApiMapper;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.IUnitService;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.UnitApiMapper;

@RpcServiceAspect(domainType = ErrorCodeEnum.DomainType.CAMPAIGN)
@RPCService
@RequiredArgsConstructor
public class ListRpcService extends ListServiceGrpc.ListServiceImplBase {
    private final ICampaignService campaignService;
    private final IUnitService unitService;
    private final IUnitCreativeService unitCreativeService;

    @Override
    public void listCampaign(ListCampaignReq request, StreamObserver<ListCampaignResp> responseObserver) {
        final var builder = ListCampaignResp.newBuilder();
        for (var bo : campaignService.list(CampaignApiMapper.MAPPER.fromRpcBo(request))) {
            builder.putCampaignInfo(bo.getCampaignId(), CampaignApiMapper.MAPPER.toRpcBo(bo));
        }
        responseObserver.onNext(builder.build());
    }

    @Override
    public void listUnit(ListUnitReq request, StreamObserver<ListUnitResp> responseObserver) {
        final var builder = ListUnitResp.newBuilder();
        for (var bo : unitService.list(UnitApiMapper.MAPPER.fromRo(request))) {
            builder.putUnitInfo(bo.getUnitId(), UnitApiMapper.MAPPER.toRo(bo));
        }
        responseObserver.onNext(builder.build());
    }

    @Override
    public void listCreative(ListCreativeReq request, StreamObserver<ListCreativeResp> responseObserver) {
        final var builder = ListCreativeResp.newBuilder();
        for (var bo : unitCreativeService.list(UnitCreativeApiMapper.MAPPER.fromRo(request))) {
            builder.putCreativeInfo(bo.getCreativeId(), UnitCreativeApiMapper.MAPPER.toRo(bo));
        }
        responseObserver.onNext(builder.build());
    }
}
