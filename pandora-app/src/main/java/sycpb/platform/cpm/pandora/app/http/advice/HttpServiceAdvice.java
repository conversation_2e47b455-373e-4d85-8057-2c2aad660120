package sycpb.platform.cpm.pandora.app.http.advice;

import com.bilibili.warp.grpc.error.ServerCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import sycpb.platform.cpm.pandora.service.exceptions.BusinessRuleViolationError;

@Slf4j
@RestControllerAdvice
public class HttpServiceAdvice {
    @ExceptionHandler({
            BusinessRuleViolationError.class,
    })
    public PandoraErrorResponse handleBusinessRuleViolationErr(BusinessRuleViolationError err) {
        return handle(err, err.getCode());
    }

    @ExceptionHandler({
            IllegalArgumentException.class,
    })
    public PandoraErrorResponse handleBadArgumentErr(Throwable t) {
        return handle(t, ServerCode.BAD_REQUEST.code());
    }

    @ExceptionHandler(Throwable.class)
    public PandoraErrorResponse handleDefaultErr(Throwable t) {
        return handle(t, ServerCode.SERVER_ERROR.code());
    }

    private static PandoraErrorResponse handle(Throwable t, int code) {
        log.error("HttpServiceAdvice", t);
        return new PandoraErrorResponse(code, t.getMessage());
    }
}
