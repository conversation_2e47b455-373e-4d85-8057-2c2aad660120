package sycpb.platform.cpm.pandora.app.scheduled;

import com.bapis.ad.pandora.core.batch.BatchOperationType;
import com.dianping.cat.CatConstants;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import sycpb.platform.cpm.boggart.cat.CatDecorators;
import sycpb.platform.cpm.pandora.infra.config.ThreadPoolConfig;
import sycpb.platform.cpm.pandora.service.api.core.batch.IBatchOperationService;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.GetBatchOperationReqBo;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.LauBatchOperationBo;
import sycpb.platform.cpm.pandora.service.constants.BatchOperationStatus;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class BatchOperationJob {
    private final IBatchOperationService batchOperationService;

    @Resource(name = ThreadPoolConfig.TP_MAP)
    private Map<String, ThreadPoolExecutor> tpMap;

    @PostConstruct
    public void init() {
        final var tp = tpMap.get("batch");
        if (Objects.isNull(tp)) {
            log.info("batch executor disabled");
            return;
        }
        log.info("batch executor running");
        Executors.newSingleThreadExecutor().execute(() -> run(tp));
    }

    @Scheduled(cron = "0 0/5 * * * ?")
    public void revokeDisabledAccountId() {
        try {
            // 解除黑名单
            for (Integer disabledAccountId : batchOperationService.getDisabledAccountIds()) {
                if (!batchOperationService.isDisabledAccountId(disabledAccountId)) {
                    batchOperationService.removeDisabledAccountId(disabledAccountId);
                }
            }
        } catch (Throwable t) {
            log.error("revoke disabled account_id error", t);
        }
    }

    private void run(ThreadPoolExecutor tp) {
        while (true) {
            try {
                final var reqBo = new GetBatchOperationReqBo();
                reqBo.setExcludedAccountIds(batchOperationService.getInvalidAccountIds());
                final var bo = batchOperationService.fetchNextOperation(reqBo);
                // 没事了, 休息一会儿
                if (Objects.isNull(bo)) {
                    //log.info("no work to do, sleeping");
                    TimeUnit.SECONDS.sleep(2);
                    continue;
                }
                // 拉黑
                if (batchOperationService.isDisabledAccountId(bo.getAccountId())) {
                    batchOperationService.addDisabledAccountId(bo.getAccountId());
                    log.info("account_id={} disabled", bo.getAccountId());
                    continue;
                }
                try {
                    tp.execute(() -> tryExec(bo));
                } catch (RejectedExecutionException ree) {
                    // 线程池队列满了, 休息一下
                    log.info("too much work to do, take a break");
                    TimeUnit.SECONDS.sleep(2);
                }
            } catch (Throwable t) {
                log.error("batch operation executor error", t);
            }
        }
    }

    public void test(Long operationId) {
        final var reqBo = new GetBatchOperationReqBo();
        reqBo.setOperationId(operationId);
        final var batchOperationBo = batchOperationService.fetchNextOperation(reqBo);
        tryExec(batchOperationBo);
    }

    @SneakyThrows
    private void tryExec(LauBatchOperationBo bo) {
        final var reqBo = new GetBatchOperationReqBo();
        reqBo.setOperationId(bo.getOperationId());
        final var batchOperationBo = batchOperationService.fetchNextOperation(reqBo);
        // 排队执行过程中任务可能已经被其他执行器执行了
        if (Objects.isNull(batchOperationBo)) return;

        // 账号被拉黑/同账号有任务在执行
        if (batchOperationService.getInvalidAccountIds().contains(batchOperationBo.getAccountId())) return;

        final var lock = batchOperationService.getOperationLock(batchOperationBo.getOperationId());
        final var ok = lock.tryLock(0, 30, TimeUnit.SECONDS);
        // 有其他执行器锁了这个任务
        if (!ok) return;

        try {
            log.info("operation started: {}", batchOperationBo.getOperationId());
            batchOperationService.addWorkingAccountId(batchOperationBo.getAccountId());
            batchOperationService.updateStatus(null, batchOperationBo.getOperationId(), BatchOperationStatus.PROCESSING);
            final var batchOperationType = BatchOperationType.forNumber(batchOperationBo.getOperationType());
            CatDecorators.run(() -> batchOperationService.handleJob(batchOperationBo), CatConstants.TYPE_ACTION, batchOperationType.name());
            batchOperationService.updateStatus(null, batchOperationBo.getOperationId(), BatchOperationStatus.FINISHED);
            log.info("operation succeeded: {}", batchOperationBo.getOperationId());
        } catch (Throwable t) {
            log.error("operation failed: {}", batchOperationBo.getOperationId(), t);
            batchOperationService.updateStatus(null, batchOperationBo.getOperationId(), BatchOperationStatus.FAILED);
        } finally {
            batchOperationService.removeWorkingAccountId(batchOperationBo.getAccountId());
            if (lock.isLocked()) {
                lock.unlock();
            }
        }
    }
}
