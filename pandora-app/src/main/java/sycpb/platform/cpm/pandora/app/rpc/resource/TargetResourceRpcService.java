package sycpb.platform.cpm.pandora.app.rpc.resource;

import com.bapis.ad.pandora.resource.GetTargetPackageReply;
import com.bapis.ad.pandora.resource.GetTargetPackageReq;
import com.bapis.ad.pandora.resource.TargetPackageInfo;
import com.bapis.ad.pandora.resource.TargetServiceGrpc;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import pleiades.venus.starter.rpc.server.RPCService;
import sycpb.platform.cpm.pandora.app.aspect.RpcServiceAspect;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraDataErr;
import sycpb.platform.cpm.pandora.service.api.resource.target.IResTargetService;
import sycpb.platform.cpm.pandora.service.api.resource.unit.ResUnitApiMapper;

import java.util.Objects;

@RpcServiceAspect(domainType = ErrorCodeEnum.DomainType.RESOURCE)
@RPCService
@RequiredArgsConstructor
public class TargetResourceRpcService extends TargetServiceGrpc.TargetServiceImplBase {
    private final IResTargetService resTargetService;

    @Override
    public void getTargetPackage(GetTargetPackageReq request, StreamObserver<GetTargetPackageReply> responseObserver) {
        final var mo = resTargetService.getTargetPackage(request.getTargetPackageId());
        if (Objects.isNull(mo)) throw PandoraDataErr.notExists("定向包");

        responseObserver.onNext(GetTargetPackageReply.newBuilder()
                .setTargetPackageInfo(TargetPackageInfo.newBuilder()
                        .setTargetPackageId(mo.getId())
                        .setTargetPackageName(mo.getName())
                        .setTargetInfo(ResUnitApiMapper.MAPPER.toRo(mo.getTarget()))
                        .build())
                .build());
    }
}
