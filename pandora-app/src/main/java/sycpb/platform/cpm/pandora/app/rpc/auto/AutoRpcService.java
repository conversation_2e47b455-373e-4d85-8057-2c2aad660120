package sycpb.platform.cpm.pandora.app.rpc.auto;

import com.bapis.ad.pandora.core.auto.*;
import com.bapis.ad.pandora.core.v6.CreativeExploreExtraCondition;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.server.RPCService;
import sycpb.platform.cpm.pandora.app.aspect.RpcServiceAspect;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.service.api.core.auto.AutoApiMapper;
import sycpb.platform.cpm.pandora.service.api.core.auto.IAutoService;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.ICreativeExploreService;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore.CreativeExploreBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore.CreativeExploreExtraInfoBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.explore.CreativeExploreCandidateBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.explore.CreativeExploreCandidateDetailBo;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@RpcServiceAspect(domainType = ErrorCodeEnum.DomainType.CREATIVE)
@RPCService
@RequiredArgsConstructor
public class AutoRpcService extends AutoServiceGrpc.AutoServiceImplBase {
    private final IAutoService autoService;
    private final ICreativeExploreService creativeExploreService;

    @Override
    public void fetchAutoCreative(final FetchAutoCreativeReq request, final StreamObserver<FetchAutoCreativeResp> responseObserver) {
        final var bos = autoService.fetchAutoCreatives(AutoApiMapper.MAPPER.fromRo(request));
        if (CollectionUtils.isEmpty(bos)) {
            responseObserver.onNext(FetchAutoCreativeResp.getDefaultInstance());
        } else {
            final var builder = FetchAutoCreativeResp.newBuilder();
            for (final var bo : bos) {
                builder.putAutoCreativeInfo(bo.getCreativeId(), AutoApiMapper.MAPPER.toRo(bo));
            }
            responseObserver.onNext(builder.build());
        }
    }

    @Override
    public void creativeExplore(CreativeExploreReq request, StreamObserver<CreativeExploreReply> responseObserver) {
        CreativeExploreBo creativeExploreBo = AutoApiMapper.MAPPER.fromRo(request);
        String queryKey = creativeExploreService.exploreCreative(creativeExploreBo);
        CreativeExploreReply reply = CreativeExploreReply.newBuilder()
                .setQueryKey(queryKey)
                .build();
        responseObserver.onNext(reply);
    }

    @Override
    public void getCreativeExploreCandidateDetail(GetCreativeExploreCandidateDetailReq request, StreamObserver<GetCreativeExploreCandidateDetailReply> responseObserver) {
        CreativeExploreCandidateDetailBo candidateDetailByPage = creativeExploreService.getCandidateDetailByPage(AutoApiMapper.MAPPER.fromRo(request));
        GetCreativeExploreCandidateDetailReply reply = AutoApiMapper.MAPPER.toRo(candidateDetailByPage);
        responseObserver.onNext(reply);
    }

    @Override
    public void getCreativeExploreRemainTimes(GetCreativeExploreRemainTimesReq request, StreamObserver<GetCreativeExploreRemainTimesReply> responseObserver) {
        int unitId = request.getUnitId();
        Integer remainTimes = creativeExploreService.getCreativeExploreRemainTimes(unitId);
        GetCreativeExploreRemainTimesReply reply = GetCreativeExploreRemainTimesReply.newBuilder()
                .setCount(remainTimes)
                .build();
        responseObserver.onNext(reply);
    }

    @Override
    public void getCreativeExploreCandidate(GetCreativeExploreCandidateReq request, StreamObserver<GetCreativeExploreCandidateReply> responseObserver) {
        List<CreativeExploreCandidateBo> candidateList = creativeExploreService.getCandidateList(request.getParentUnitId());
        List<CreativeExploreCandidateEntity> entityList = AutoApiMapper.MAPPER.toRoList(candidateList);
        GetCreativeExploreCandidateReply.Builder builder = GetCreativeExploreCandidateReply.newBuilder();
        if (!CollectionUtils.isEmpty(entityList)) {
            builder.addAllEntity(entityList);
        }
        responseObserver.onNext(builder.build());
    }

    @Override
    public void getCreativeExploreExtraCondition(GetCreativeExploreExtraConditionReq request, StreamObserver<GetCreativeExploreExtraConditionReply> responseObserver) {
        int unitId = request.getUnitId();
        CreativeExploreExtraInfoBo creativeExploreExtraCondition = creativeExploreService.getCreativeExploreExtraCondition(unitId);
        CreativeExploreExtraCondition entity = AutoApiMapper.MAPPER.toRo(creativeExploreExtraCondition);
        if (Objects.isNull(entity)) {
            entity = CreativeExploreExtraCondition.getDefaultInstance();
        }
        GetCreativeExploreExtraConditionReply reply = GetCreativeExploreExtraConditionReply.newBuilder()
                .setEntity(entity)
                .build();
        responseObserver.onNext(reply);
    }

    @Override
    public void countCreativeExploreCandidate(CountCreativeExploreCandidateReq request, StreamObserver<CountCreativeExploreCandidateReply> responseObserver) {
        List<Integer> creativeIdList = request.getCreativeIdList();
        Map<Integer, Integer> creativeIdCountMap = creativeExploreService.countCreativeCandidate(creativeIdList);
        CountCreativeExploreCandidateReply.Builder builder = CountCreativeExploreCandidateReply.newBuilder();
        if (!CollectionUtils.isEmpty(creativeIdCountMap)) {
            builder.putAllCreativeCandidateCount(creativeIdCountMap);
        }
        responseObserver.onNext(builder.build());
    }
}
