package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @ClassName LauCreativeAigcMaterialReplaceHistoryBo
 * <AUTHOR>
 * @Date 2024/10/25 8:41 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LauCreativeAigcMaterialReplaceHistoryBo {
    private Integer accountId;
    private Integer campaignId;
    private Integer unitId;
    private Integer creativeId;
    private List<LauCreativeAigcMaterialHistoryElementBo> elementBoList;
    private Set<Long> imageMaterialIdSet;
    private Set<String> rawTitleSet;
}
