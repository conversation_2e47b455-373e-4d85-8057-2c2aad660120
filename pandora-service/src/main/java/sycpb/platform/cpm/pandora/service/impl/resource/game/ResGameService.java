package sycpb.platform.cpm.pandora.service.impl.resource.game;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.adp.game.GameResourceServiceGrpc;
import com.bapis.ad.adp.game.ListBiliMiniGameReq;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;
import pleiades.venus.starter.rpc.client.RPCClient;
import sycpb.platform.cpm.pandora.infra.common.http.HttpConstants;
import sycpb.platform.cpm.pandora.infra.common.http.HttpResponse;
import sycpb.platform.cpm.pandora.infra.config.RedisConfig;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraAssert;
import sycpb.platform.cpm.pandora.infra.err.PandoraRemoteAccessErr;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.infra.utils.SignUtils;
import sycpb.platform.cpm.pandora.service.api.resource.game.QueryResGameAllListBo;
import sycpb.platform.cpm.pandora.service.api.resource.game.QueryResGameBo;
import sycpb.platform.cpm.pandora.service.api.resource.game.ResGameBo;
import sycpb.platform.cpm.pandora.service.config.GameCenterProperties;
import sycpb.platform.cpm.pandora.service.proxy.CpmAdpProxy;
import sycpb.platform.cpm.pandora.service.proxy.GameCenterProxy;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName ResGameService
 * <AUTHOR>
 * @Date 2024/6/12 7:29 下午
 * @Version 1.0
 **/
@Service
@RequiredArgsConstructor
public class ResGameService {


    private static final String PANDORA_ALL_GAME_CENTER_GAME_CACHE_KEY = "pandora:all:all:game_center:game:cache:key";

    @Autowired
    private GameCenterProperties gameCenterProperties;

    @Resource(name = RedisConfig.PANDORA_CLUSTER)
    private RedissonClient redissonClient;

    private final CpmAdpProxy cpmAdpProxy;

    private final GameCenterProxy gameCenterProxy;

    public BiliMiniGameBo getMiniGame(Integer gameBaseId, Long cpMid) {
        PandoraAssert.isTrue(NumberUtils.isPositive(gameBaseId), "游戏平台id不能为空",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        PandoraAssert.isTrue(NumberUtils.isPositive(cpMid), "游戏平台cp_mid不能为空",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        final var resp = cpmAdpProxy.listBiliMiniGame(gameBaseId, cpMid);
        PandoraAssert.isTrue(!CollectionUtils.isEmpty(resp.getEntityList()), "未找到对应的小游戏",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
        return GameMapper.MAPPER.fromRo(resp.getEntityList().get(0));
    }

    @SneakyThrows
    public ResGameBo getByGameBaseIdAndPlatform(Integer gameBaseId, Integer platform) {
        return  gameCenterProxy.getByGameBaseIdAndPlatform(gameBaseId, platform);
    }

    public Map<String, Integer> getGameCenterGameNameIdMapWithCache() {
        RBucket<String> bucket = redissonClient.getBucket(PANDORA_ALL_GAME_CENTER_GAME_CACHE_KEY);
        String cacheStr = bucket.get();
        List<ResGameBo> resGameBos = new ArrayList<>();
        if (!StringUtils.isEmpty(cacheStr)) {
            resGameBos = JSON.parseArray(cacheStr, ResGameBo.class);
        } else {
            resGameBos = gameCenterProxy.getAllAndroidGames();
            bucket.set(JSON.toJSONString(resGameBos), 1, TimeUnit.HOURS);
        }

        return resGameBos.stream()
                .collect(Collectors.toMap(ResGameBo::getGameName, ResGameBo::getGameBaseId,
                        (exist, replace) -> replace));
    }

}
