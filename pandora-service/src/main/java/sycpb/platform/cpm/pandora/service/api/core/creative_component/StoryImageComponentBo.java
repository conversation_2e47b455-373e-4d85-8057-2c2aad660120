package sycpb.platform.cpm.pandora.service.api.core.creative_component;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.javers.core.metamodel.annotation.DiffInclude;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StoryImageComponentBo {
    private Long      id;
    private Timestamp ctime;
    private Integer   accountId;
    private Long      componentId;
    private String    componentName;
    @DiffInclude
    private String    imageUrl;
    private String    imageMd5;
}
