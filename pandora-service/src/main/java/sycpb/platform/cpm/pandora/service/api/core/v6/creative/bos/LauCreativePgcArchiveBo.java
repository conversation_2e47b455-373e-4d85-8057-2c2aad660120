package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LauCreativePgcArchiveBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Long id;
    private Integer unitId;
    @CompareMeta(logGroupId = true, comparable = false)
    private Integer creativeId;
    @CompareMeta(shadow = true)
    private Long mid;
    @CompareMeta(shadow = true)
    private Long aid;
    @CompareMeta
    private Integer episodeId;
    @CompareMeta
    private Integer seasonId;
    @CompareMeta
    private Long  materialNo;

    @CompareMeta(uk = true)
    public String uk() {
        return creativeId + "-" + aid + "-" + seasonId + "-" + episodeId + "-" + materialNo;
    }
}
