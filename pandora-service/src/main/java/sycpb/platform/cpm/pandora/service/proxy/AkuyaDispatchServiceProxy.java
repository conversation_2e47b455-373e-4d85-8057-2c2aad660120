package sycpb.platform.cpm.pandora.service.proxy;


import com.bapis.archive.service.*;
import com.bapis.datacenter.service.oneservice.MapValue;
import com.bapis.datacenter.service.oneservice.OneServiceOpenApiManagerGrpc;
import com.bapis.datacenter.service.oneservice.QueryReq;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraAssert;
import sycpb.platform.cpm.pandora.infra.utils.DataCenterQueryUtils;
import sycpb.platform.cpm.pandora.service.config.OneServiceProperties;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AkuyaDispatchServiceProxy {
    private static final String ACCOUNT_ID = "account_id";
    private static final String LOG_DATE = "log_date";
    private static final String ACCOUNT_ID_ASC = "account_id asc";
    private static final String OPERATION_EQUAL = "=";
    private static final String OPERATION_GT = ">";

    @RPCClient("datacenter.oneservice.akuya-dispatch-service")
    private OneServiceOpenApiManagerGrpc.OneServiceOpenApiManagerBlockingStub serviceBlockingStub;

    @Autowired
    private OneServiceProperties oneServiceProperties;

    public List<MapValue> getSanlianAdMonthCost(Integer startAccountId, String date) {
        QueryReq.Builder builder = DataCenterQueryUtils.baseReq(
                oneServiceProperties.getAdCostAppKey(), oneServiceProperties.getAdCostSecret(), oneServiceProperties.getAdCostApiId());
        DataCenterQueryUtils.appendParams(builder, ACCOUNT_ID, OPERATION_GT, Lists.newArrayList(startAccountId.toString()));
        DataCenterQueryUtils.appendParams(builder, LOG_DATE, OPERATION_EQUAL, Lists.newArrayList(date));
        DataCenterQueryUtils.appendOrderByClause(builder, ACCOUNT_ID_ASC);
        DataCenterQueryUtils.setPage(builder, 1, 1000);
        return serviceBlockingStub.withWaitForReady()
                .withDeadlineAfter(3000, TimeUnit.MILLISECONDS)
                .query(builder.build()).getRowsList();
    }


}
