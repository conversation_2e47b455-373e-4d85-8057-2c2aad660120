package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.Data;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.UnitMonitoringService;

@Data
public class LauUnitMonitoringBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Long      id;
    private Integer   unitId;
    @CompareMeta
    private String    url;
    private Integer   type;

    @CompareMeta(uk = true)
    public String uk() {
        return unitId + "-" + type;
    }

    @CompareMeta(logDescription = "url")
    public String getLogDescription() {
        return UnitMonitoringService.getMonitorTypeDesc(type);
    }
}
