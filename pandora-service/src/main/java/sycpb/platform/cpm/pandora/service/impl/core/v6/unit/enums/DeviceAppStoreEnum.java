package sycpb.platform.cpm.pandora.service.impl.core.v6.unit.enums;

import com.google.common.base.Joiner;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeviceAppStoreEnum {


    XIAO_MI(1, "小米"),
    HUA_WEI(2, "华为"),
    OPPO(3, "OPPO"),
    VIVO(4, "VIVO"),
    HONOR(5, "荣耀");

    private final Integer deviceAppStoreCode;
    private final String deviceAppStoreName;


    public static final List<DeviceAppStoreEnum> defaultEnums = Arrays.asList(XIAO_MI, VIVO);

    public static final List<DeviceAppStoreEnum> chooseEnums = Arrays.asList(HUA_WEI, OPPO, HONOR);

    public static String getAllAppStores() {

        return Joiner.on(",").join(getAllAppStoreCode());
    }

    public static Set<Integer> getAllAppStoreCode() {
        return Arrays.stream(DeviceAppStoreEnum.values()).map(x -> x.getDeviceAppStoreCode()).collect(Collectors.toSet());
    }

    public static String getAppStoreValue(String code) {
        switch (code) {
            case "1":
            case "-1":
                return "小米";
            case "2":
            case "-2":
                return "华为";
            case "3":
            case "-3":
                return "OPPO";
            case "4":
            case "-4":
                return "VIVO";
            case "5":
            case "-5":
                return "荣耀";
        }
        return "";
    }
}