package sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos;

import com.bapis.ad.pandora.resource.Relation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

import java.util.Objects;

import static com.bapis.ad.pandora.resource.OsTarget.forNumber;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauUnitTargetBiliClientUpgradeBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Long      id;
    private Integer   unitId;
    private Integer   os;
    @CompareMeta
    private Integer   relation;
    @CompareMeta
    private Integer   smallerVersion;
    @CompareMeta
    private Integer   largerVersion;

    @CompareMeta(uk = true)
    public String uk() {
        return unitId + "-" + os;
    }

    @CompareMeta(logValue = "udfBiliClient")
    public String udfBiliClient() {
        final var rel = Relation.forNumber(relation);
        if (Objects.isNull(rel)) return "未知";

        switch (rel) {
            case RELATION_BETWEEN:
                return "[" + smallerVersion + ", " + largerVersion + "]";
            case RELATION_GTE:
                return ">= " + smallerVersion;
            case RELATION_GT:
                return "> " + smallerVersion;
            case RELATION_LTE:
                return "<= " + largerVersion;
            case RELATION_LT:
                return "< " + largerVersion;
            default:
                return "不限";
        }
    }

    @CompareMeta(logDescription = "udfBiliClient")
    public String udfBiliClientDesc() {
        final var osTarget = forNumber(os);
        if (Objects.isNull(osTarget)) return "未知";

        switch (osTarget) {
            case OS_ANDROID:
                return "安卓客户端版本";
            case OS_IOS:
                return "iPhone客户端版本";
            case OS_IPAD:
                return "iPad客户端版本";
            default:
                return "未知";
        }
    }
}
