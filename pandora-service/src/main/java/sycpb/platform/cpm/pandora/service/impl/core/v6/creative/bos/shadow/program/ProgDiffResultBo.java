package sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.program;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProgDiffResultBo {

    private List<ProgDiffItemBo> diffItems;

    private boolean needAudit;

    // 仅仅是替链信息变更
    private boolean onlyCpsLinkReplaceUpdate;

}
