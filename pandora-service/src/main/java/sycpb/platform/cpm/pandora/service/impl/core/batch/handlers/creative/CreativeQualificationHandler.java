package sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.creative;

import org.jooq.DSLContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.compare.OperationType;
import sycpb.platform.cpm.pandora.infra.compare.bos.ChangeLogBo;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauCreativeQualificationDao;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeExtraPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeQualificationPo;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.IOperationLogService;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperationLogContextBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauCreativeQualificationBo;
import sycpb.platform.cpm.pandora.service.impl.core.batch.BatchOperationExtService;
import sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.unit.UnitTargetTemplateHandler;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.CreativeQualificationService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.UnitCreativeImplMapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeExtra.LAU_CREATIVE_EXTRA;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeQualification.LAU_CREATIVE_QUALIFICATION;

@Service
public class CreativeQualificationHandler extends CreativeTemplateHandler {
    @Resource
    private LauCreativeQualificationDao lauCreativeQualificationDao;

    public CreativeQualificationHandler(IOperationLogService operationLogService,
                                        BatchOperationExtService batchOperationExtService,
                                        @Qualifier(MySqlConfig.AD_CORE_DSL_CONTEXT) DSLContext adCore) {
        super(operationLogService, batchOperationExtService, adCore);
    }

    @Override
    public void handleRequest(CreativeContext ctx) {
        super.handleRequest(ctx);
        final var creativeQualificationMap = adCore.select(LAU_CREATIVE_QUALIFICATION.ID, LAU_CREATIVE_QUALIFICATION.CREATIVE_ID, LAU_CREATIVE_QUALIFICATION.QUALIFICATION_ID)
                .from(LAU_CREATIVE_QUALIFICATION)
                .where(LAU_CREATIVE_QUALIFICATION.CREATIVE_ID.in(ctx.getAllTargetIds()))
                .fetch()
                .into(LauCreativeQualificationPo.class)
                .stream()
                .map(UnitCreativeImplMapper.MAPPER::fromPo)
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(LauCreativeQualificationBo::getCreativeId));
        ctx.setCreativeQualificationMap(creativeQualificationMap);

        final var creativeExtraMap = adCore.select(LAU_CREATIVE_EXTRA.CREATIVE_ID, LAU_CREATIVE_EXTRA.QUALIFICATION_PACKAGE_ID)
                .from(LAU_CREATIVE_EXTRA)
                .where(LAU_CREATIVE_EXTRA.CREATIVE_ID.in(ctx.getAllTargetIds()))
                .fetch()
                .into(LauCreativeExtraPo.class)
                .stream()
                .collect(Collectors.toMap(LauCreativeExtraPo::getCreativeId, Function.identity()));
        ctx.setCreativeExtraMap(creativeExtraMap);
    }

    @Override
    public void handleTarget(CreativeContext ctx) {
        super.handleTarget(ctx);
        ctx.setLauCreativeExtraPo(ctx.getCreativeExtraMap().get(ctx.getTargetId()));
        ctx.setLauCreativeQualificationPos(ctx.getCreativeQualificationMap().get(ctx.getTargetId()));
    }

    @Override
    public CompareBo compare(CreativeContext ctx) {
        final var compareBo = new CompareBo();
        final var newQualificationPackageId = ctx.getOperationBo().getQualification().getQualificationPackageId().intValue();
        final var newQualificationIds = UnitTargetTemplateHandler.normalize(ctx.getOperationBo().getQualification().getQualificationIds());
        if (NumberUtils.isPositive(newQualificationPackageId)) {
            final var curQualificationPackageId = ctx.getLauCreativeExtraPo().getQualificationPackageId();
            compareBo.setCurVersion(curQualificationPackageId.toString());
            compareBo.setNewVersion(String.valueOf(newQualificationPackageId));
            if (!Objects.equals(curQualificationPackageId, newQualificationPackageId)) {
                compareBo.setChanged(true);
                final var logContextBo = OperationLogContextBo.newContext(ctx.getOperatorBo(), "lau_unit_creative");
                compareBo.setOperationLogContextBo(logContextBo);
                logContextBo.setChanges(List.of(ChangeLogBo.builder()
                        .key("qualificationPackageId")
                        .desc("资质包id")
                        .oldValue(curQualificationPackageId.toString())
                        .newValue(String.valueOf(newQualificationPackageId))
                        .build()));
                logContextBo.updateContext(ctx.getTargetId(), OperationType.UPDATE);
            }
        } else {
            final var curQualificationIds = UnitTargetTemplateHandler.normalize(Optional.ofNullable(ctx.getLauCreativeQualificationPos()).orElse(List.of())
                    .stream()
                    .map(LauCreativeQualificationBo::getQualificationId)
                    .collect(Collectors.toList()));
            compareBo.setCurVersion(curQualificationIds.toString());
            compareBo.setNewVersion(newQualificationIds.toString());
            if (!UnitTargetTemplateHandler.equals(curQualificationIds, newQualificationIds)) {
                compareBo.setChanged(true);
                final var logContextBo = OperationLogContextBo.newContext(ctx.getOperatorBo(), "lau_unit_creative");
                compareBo.setOperationLogContextBo(logContextBo);
                logContextBo.setChanges(List.of(ChangeLogBo.builder()
                        .key("qualificationIds")
                        .desc("资质id")
                        .oldValue(curQualificationIds.toString())
                        .newValue(newQualificationIds.toString())
                        .build()));
                logContextBo.updateContext(ctx.getTargetId(), OperationType.UPDATE);
            }
        }
        return compareBo;
    }

    @Override
    public void batchUpdateTarget(CreativeContext ctx) {
        if (CollectionUtils.isEmpty(ctx.getUpdatingTargetIds())) return;

        if (NumberUtils.isPositive(ctx.getOperationBo().getQualification().getQualificationPackageId())) {
            adCore.update(LAU_CREATIVE_EXTRA)
                    .set(LAU_CREATIVE_EXTRA.QUALIFICATION_PACKAGE_ID, ctx.getOperationBo().getQualification().getQualificationPackageId().intValue())
                    .where(LAU_CREATIVE_EXTRA.CREATIVE_ID.in(ctx.getUpdatingTargetIds()))
                    .execute();
            // 变更资质包需要删除资质
            JooqFunctions.safeDelete(adCore, LAU_CREATIVE_QUALIFICATION, LAU_CREATIVE_QUALIFICATION.CREATIVE_ID.in(ctx.getUpdatingTargetIds()));
        } else {
            final List<LauCreativeQualificationBo> curBos = new ArrayList<>();
            final List<LauCreativeQualificationBo> newBos = new ArrayList<>();
            for (Integer targetId : ctx.getUpdatingTargetIds()) {
                newBos.addAll(CreativeQualificationService.genCreativeQualificationBos(targetId, ctx.getOperationBo().getQualification().getQualificationIds()));
                Optional.ofNullable(ctx.getCreativeQualificationMap().get(targetId)).ifPresent(curBos::addAll);
            }
            JooqFunctions.save(curBos, newBos, JooqFunctions.JooqSaveContext.minimum(LauCreativeQualificationBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeQualificationDao));
            // 变更资质需要删除资质包
            adCore.update(LAU_CREATIVE_EXTRA)
                    .set(LAU_CREATIVE_EXTRA.QUALIFICATION_PACKAGE_ID, 0)
                    .where(LAU_CREATIVE_EXTRA.CREATIVE_ID.in(ctx.getUpdatingTargetIds()))
                    .execute();
        }
    }

    @Override
    public void batchUpdate(CreativeContext ctx) {
        ((CreativeQualificationHandler) AopContext.currentProxy()).batchUpdateWithTx(ctx);
    }

    @Transactional(MySqlConfig.AD_CORE_TX_MGR)
    public void batchUpdateWithTx(CreativeContext ctx) {
        super.batchUpdate(ctx);
    }
}
