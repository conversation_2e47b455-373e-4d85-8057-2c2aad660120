package sycpb.platform.cpm.pandora.service.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName OneServiceConfig
 * <AUTHOR>
 * @Date 2025/3/23 5:03 下午
 * @Version 1.0
 **/
@Configuration
public class OneServiceConfig {

    @Bean
    @ConfigurationProperties("one.service")
    public OneServiceProperties oneServiceProperties() {
        return new OneServiceProperties();
    }

}
