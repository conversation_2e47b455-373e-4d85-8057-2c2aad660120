package sycpb.platform.cpm.pandora.service.impl.resource.game;

import com.bapis.ad.adp.game.BiliMiniGameEntity;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface GameMapper {
    GameMapper MAPPER = Mappers.getMapper(GameMapper.class);

    BiliMiniGameBo fromRo(BiliMiniGameEntity x);
}
