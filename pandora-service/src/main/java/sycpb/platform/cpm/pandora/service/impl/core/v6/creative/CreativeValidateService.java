package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.component.*;
import com.bapis.ad.mgk.ValidateAndGetPageListReply;
import com.bapis.ad.pandora.core.CreativeComponentType;
import com.bapis.ad.pandora.resource.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.api.archive.BiliArchiveBo;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraAssert;
import sycpb.platform.cpm.pandora.infra.err.PandoraDataErr;
import sycpb.platform.cpm.pandora.infra.utils.EmojiUtils;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.creative_component.*;
import sycpb.platform.cpm.pandora.service.api.core.legacy.underframe_component.LegacyQueryUnderframeComponentBo;
import sycpb.platform.cpm.pandora.service.api.core.legacy.underframe_component.LegacyUnderframeComponentService;
import sycpb.platform.cpm.pandora.service.api.core.v6.campaign.bos.LauCampaignBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.*;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore.CreativeExploreExtraInfoBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitExtraBo;
import sycpb.platform.cpm.pandora.service.api.resource.account.IAccountService;
import sycpb.platform.cpm.pandora.service.api.resource.account.bos.AccountBo;
import sycpb.platform.cpm.pandora.service.api.resource.creative.IResCreativeService;
import sycpb.platform.cpm.pandora.service.api.resource.creative.ResCreativeApiMapper;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.CreativeConfigCampaignUnitInfoBo;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.CreativeContentGoodsBo;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.QueryCreativeContentBo;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.TemplateGroupDetailBo;
import sycpb.platform.cpm.pandora.service.api.resource.goods.IResCreativeGoodsContentService;
import sycpb.platform.cpm.pandora.service.api.resource.goods.bos.QueryCreativeGoodsContentBo;
import sycpb.platform.cpm.pandora.service.api.resource.location.bos.TemplateGroupBo;
import sycpb.platform.cpm.pandora.service.api.resource.mgk.MgkLandingPageMapper;
import sycpb.platform.cpm.pandora.service.api.resource.mgk.PageGroupSource;
import sycpb.platform.cpm.pandora.service.api.resource.mgk.bos.LandingPageBo;
import sycpb.platform.cpm.pandora.service.api.resource.minigame.bos.MiniGameBo;
import sycpb.platform.cpm.pandora.service.api.resource.qualification.bos.LauQualificationBo;
import sycpb.platform.cpm.pandora.service.api.resource.qualification.bos.LauQualificationPackageBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.BusMarkBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.CreativeContentBo;
import sycpb.platform.cpm.pandora.service.api.resource.space.bos.AdSpaceBo;
import sycpb.platform.cpm.pandora.service.constants.*;
import sycpb.platform.cpm.pandora.service.enums.ProductWildcardEnum;
import sycpb.platform.cpm.pandora.service.impl.core.v6.count.SanlianAdCountService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.ValidateCreativeContentContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.enums.SmartTitleTypeEnum;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.splash_screen.enums.InteractType;
import sycpb.platform.cpm.pandora.service.impl.resource.game.ResGameService;
import sycpb.platform.cpm.pandora.service.impl.resource.minigame.ResMiniGameService;
import sycpb.platform.cpm.pandora.service.impl.resource.qualification.ResQualificationService;
import sycpb.platform.cpm.pandora.service.impl.resource.space.ResAdSpaceService;
import sycpb.platform.cpm.pandora.service.proxy.AccountServiceProxy;
import sycpb.platform.cpm.pandora.service.proxy.CpmMgkPortalProxy;
import sycpb.platform.cpm.pandora.service.proxy.CpmScvProxy;
import sycpb.platform.cpm.pandora.service.utils.CompareUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bapis.ad.component.BizCodeEnum.BUSINESS_TOOL_VALUE;

/**
 * @ClassName CreativeValidateService
 * <AUTHOR>
 * @Date 2024/6/22 2:53 下午
 * @Version 1.0
 **/
@Slf4j
@Service
public class CreativeValidateService {

    public static final Integer SUPPORT_PREFER_DIRECT_CALL_UP_LABEL_ID = 479;

    public static final Integer SUPPORT_SPLASH_SCREEN_TWIST_LABEL_ID = 839;
    public static final Integer SUPPORT_AI_ADS_AGENT_PROJECT_ACCOUNT_LABEL_ID = 1058;
    public static final int MIN_TITLE_DESC_LENGTH = 2;

    public static final Integer IS_PPC_WX_MINI_PROGRAM = 1043;

    @Resource
    private IResCreativeService resCreativeService;

    @Resource
    private ResMiniGameService resMiniGameService;

    @Resource
    private ResQualificationService resQualificationService;

    @Resource
    private AccountServiceProxy accountServiceProxy;

    @Resource
    private ResAdSpaceService resAdSpaceService;

    @Resource
    private ResCreativeComponentService resCreativeComponentService;

    @Resource
    private CpmScvProxy cpmScvProxy;

    @Resource
    private CpmMgkPortalProxy cpmMgkPortalProxy;
    @Resource
    private IResCreativeGoodsContentService resCreativeGoodsContentService;

    @Resource
    private IAccountService accountService;

    @Resource
    private LegacyUnderframeComponentService underframeComponentService;


    @Autowired
    private UnitCreativeExtService unitCreativeExtService;
    @Resource
    private ResGameService resGameService;

    @Resource
    private SanlianAdCountService sanlianAdCountService;

    private static final Integer NORMAL_UNIT_CREATIVE_NUM_LIMIT = 10;
    private static final Integer AI_AGENT_UNIT_CREATIVE_NUM_LIMIT = 30;

    public void validateCreativeBatchReplaceMaterial(CreativeBatchReplaceMaterialBo materialBo) {
        materialBo.getPicList().forEach(picBo -> {
            PandoraAssert.isTrue(!StringUtils.isEmpty(picBo.getCurrentContent()), "替换图片历史url不可为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            PandoraAssert.isTrue(!StringUtils.isEmpty(picBo.getCurrentContentMd5()), "替换图片历史md5不可为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            PandoraAssert.isTrue(!StringUtils.isEmpty(picBo.getCurrentContentRejectedReason()), "历史图片拒审理由不可为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            PandoraAssert.isTrue(!StringUtils.isEmpty(picBo.getNewContent()), "替换图片url不可为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            PandoraAssert.isTrue(NumberUtils.isPositive(picBo.getNewContentId()), "替换图片id不可为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        });

        materialBo.getTitleList().forEach(titleBo -> {
            PandoraAssert.isTrue(!StringUtils.isEmpty(titleBo.getCurrentContent()), "替换标题历史url不可为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            PandoraAssert.isTrue(!StringUtils.isEmpty(titleBo.getCurrentContentMd5()), "替换标题历史md5不可为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            PandoraAssert.isTrue(!StringUtils.isEmpty(titleBo.getCurrentContentRejectedReason()), "历史标题拒审理由不可为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            PandoraAssert.isTrue(!StringUtils.isEmpty(titleBo.getNewContent()), "替换标题内容不可为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        });
    }

    public void validateCreative(SaveUnitCreativeContextBo ctx) {
        // 校验数量
        validateCreateNum(ctx);
        // 校验状态
        validateStatus(ctx);
        // 基本校验
        validateCommon(ctx);
        // 校验内容
        validateSupportBiliNative(ctx);
        // 填写内容
        validateContentByRule(ctx);
        // 场景
        validateScene(ctx);
        // 稿件
        validateArchive(ctx);
        // 动态
        validateDynamic(ctx);
        // 自动投放
        validateAuto(ctx);
        // b站小游戏
        validateBiliMiniGame(ctx);
        // 闪屏
        validateSplashScreen(ctx);
    }


    private void validateSplashScreen(SaveUnitCreativeContextBo ctx) {
        if (!ctx.getCampaignBo().getAdType().equals(AdType.ADTYPE_SPLASH_VALUE)) {
            return;
        }
        for (CreativeBo creativeBo : ctx.getNewVersion().getCreatives()) {
            if (Objects.nonNull(creativeBo.getLauSplashScreenCreativeBo()) && ctx.getLauUnitBo().getPromotionPurposeType().equals(PromotionContentType.PPC_LIVE_ROOM_VALUE)
                    && creativeBo.getLauSplashScreenCreativeBo().getInteractType().equals(InteractType.TWIST.getCode())) {
                PandoraAssert.isTrue(ctx.getAccountLabelIds().contains(SUPPORT_SPLASH_SCREEN_TWIST_LABEL_ID), "直播闪屏暂不支持扭一扭",
                        ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            }
        }
    }

    private void validateBiliMiniGame(SaveUnitCreativeContextBo ctx) {
        // 原生跳过b小校验
        if (Objects.isNull(ctx.getLauUnitBiliMiniGameBo()) || NumberUtils.isPositive(ctx.getLauUnitExtraBo().getIsBiliNative()))
            return;

        final var miniGameBo = resGameService.getMiniGame(ctx.getLauUnitBiliMiniGameBo().getGameBaseId(), ctx.getLauUnitBiliMiniGameBo().getCpMid());
        for (final CreativeBo bo : ctx.getNewVersion().getCreatives()) {
            PandoraAssert.isTrue(Objects.equals(miniGameBo.getGameLink(), bo.getLauCreativeExtraBo().getRawJumpUrl()), "小游戏跳转链接和游戏平台的数据不匹配",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        }
    }

    // 自动投放: https://www.tapd.cn/********/prong/stories/view/11********004359383
    private void validateAuto(SaveUnitCreativeContextBo ctx) {
        final var campaignBo = ctx.getCampaignBo();
        Integer supportCreativeExplore = ctx.getNewVersion().getUnit().getSupportCreativeExplore();
        if (!NumberUtils.isPositive(campaignBo.getSupportAuto())) {
            Assert.isTrue(!NumberUtils.isPositive(supportCreativeExplore),
                    "非自动投放不允许开启创意探索");
            return;
        }

        Integer currentCreativeExploreStatus = ctx.getCurrentVersion().getUnit().getLauUnitBo().getCreativeExploreStatus();
        // 当前有没有未删除探索创意
        boolean hasExploreCreative = CreativeExploreStatus.VALID_OR_PAUSED_STATUS_LIST.contains(currentCreativeExploreStatus);
        CreativeExploreExtraInfoBo currentCreativeExploreExtraCondition =
                ctx.getCurrentVersion().getUnit().getCreativeExploreExtraCondition();
        CreativeExploreExtraInfoBo newCreativeExploreExtraCondition =
                ctx.getNewVersion().getUnit().getCreativeExploreExtraCondition();
        if (Objects.nonNull(currentCreativeExploreExtraCondition) && hasExploreCreative) {
            Assert.isTrue(CompareUtils.compareEqual(currentCreativeExploreExtraCondition, newCreativeExploreExtraCondition),
                    "单元绑定的创意探索额外条件不允许变更");
        }

        if (NumberUtils.isPositive(ctx.getNewVersion().getUnit().getSupportCreativeExplore())) {
            Assert.notNull(newCreativeExploreExtraCondition, "开启创意探索时,自定义条件不可为空");
        }

        final var parentUnitId = ctx.getLauUnitExtraBo().getParentUnitId();
        for (final CreativeBo creativeBo : ctx.getNewVersion().getCreatives()) {
            final var parentCreativeId = creativeBo.getLauCreativeExtraBo().getParentCreativeId();
            if (NumberUtils.isPositive(parentUnitId)) {
                PandoraAssert.isTrue(NumberUtils.isPositive(parentCreativeId), "自动投放托管创意必须指定父 ID",
                        ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
                PandoraAssert.isTrue(OperatorTypeConstants.SYSTEM_OPERATOR_TYPE_LIST.contains(ctx.getOperator().getOperatorType()),
                        "您当前无权限编辑自动托管子创意",
                        ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
                LauUnitCreativeBo parentUnitCreativeBo = ctx.getParentUnitCreativeBoMap().get(parentCreativeId);
                PandoraAssert.isTrue(Objects.nonNull(parentUnitCreativeBo) && Objects.equals(parentUnitCreativeBo.getUnitId(), parentUnitId), "自动投放托管创意父 ID 匹配失败",
                        ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);
            } else {
                log.info("parentCreativeId: {}, parentUnitId: {}", parentCreativeId, parentUnitId);
                PandoraAssert.isTrue(Objects.equals(parentCreativeId, 0), "自动投放单元下不能创建托管创意",
                        ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
                if (!ctx.isPersonalUp()) {
                    PandoraAssert.isTrue(CollectionUtils.isEmpty(ctx.getNewVersion().getUnit().getLauUnitSceneBo().getSelectedScenes().getList()), "自动投放仅支持优选广告位",
                            ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
                }
            }
        }
    }

    private void validateStatus(SaveUnitCreativeContextBo ctx) {
        LauCampaignBo campaignBo = ctx.getCampaignBo();
        PandoraAssert.isTrue(!LaunchStatus.FORBID_MODIFY_LAUNCH_STATUS_LIST.contains(campaignBo.getStatus()),
                "禁止在已结束/已删除的计划下操作创意",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
        PandoraAssert.isTrue(AdpVersion.MODEL_MERGE == campaignBo.getAdpVersion(),
                "只能在新三连计划下保存新三连创意",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
        LauUnitBo lauUnitBo = ctx.getLauUnitBo();
        PandoraAssert.isTrue(AdpVersion.MODEL_MERGE == lauUnitBo.getAdpVersion(),
                "只能在新三连单元下保存新三连创意",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
        PandoraAssert.isTrue(!LaunchStatus.FORBID_MODIFY_LAUNCH_STATUS_LIST.contains(lauUnitBo.getStatus()),
                "禁止在已结束/已删除的单元下操作创意",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
    }

    private void validateCreateNum(SaveUnitCreativeContextBo ctx) {
        List<CreativeBo> creatives = ctx.getNewVersion().getCreatives();
        // 没新建
        if (CollectionUtils.isEmpty(creatives) || ctx.isPersonalUp()) {
            return;
        }

        if (creatives.stream().allMatch(creative ->
                NumberUtils.isPositive(creative.getLauUnitCreativeBo().getCreativeId()))) {
            return;
        }

        // 自动投放 不计
        if (creatives.stream().anyMatch(creative ->
                NumberUtils.isPositive(creative.getLauCreativeExtraBo().getParentCreativeId()))) {
            return;
        }

        Integer accountId = ctx.getAccountId();
        sanlianAdCountService.validateCreativeCreateLimit(accountId);
    }

    private void validateCommon(SaveUnitCreativeContextBo ctx) {
        List<CreativeBo> currentCreativeBos = ctx.getCurrentVersion().getCreatives();
        List<CreativeBo> newCreativeBos = ctx.getNewVersion().getCreatives();

        PandoraAssert.isTrue(!CollectionUtils.isEmpty(newCreativeBos), "创意数量不能为空",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        // 给算法和引擎aaa自动投放项目服务 算法老师的要求
        Integer operatorType = ctx.getOperator().getOperatorType();
        Integer unitCreativeLimit = ctx.getAccountLabelIds().contains(SUPPORT_AI_ADS_AGENT_PROJECT_ACCOUNT_LABEL_ID) ?
                AI_AGENT_UNIT_CREATIVE_NUM_LIMIT : NORMAL_UNIT_CREATIVE_NUM_LIMIT;
        PandoraAssert.isTrue(newCreativeBos.size() <= unitCreativeLimit,
                "单个单元下最多保存" + unitCreativeLimit + "个创意",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        // 校验更新创意版本
        List<LauUnitCreativeBo> currentLauUnitCreativeBoList = currentCreativeBos.stream()
                .map(CreativeBo::getLauUnitCreativeBo)
                .collect(Collectors.toList());
        PandoraAssert.isTrue(currentLauUnitCreativeBoList.stream()
                        .allMatch(lauUnitCreativeBo -> AdpVersion.MODEL_MERGE == lauUnitCreativeBo.getAdpVersion()),
                "只能编辑新三连创意",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);

        Map<Integer, Integer> creativeIdTemplateGroupIdMap = currentLauUnitCreativeBoList.stream()
                .collect(Collectors.toMap(LauUnitCreativeBo::getCreativeId,
                        LauUnitCreativeBo::getTemplateGroupId));
        //校验更新创意id合法性
        Set<Integer> currentCreativeIds = creativeIdTemplateGroupIdMap.keySet();
        List<Integer> updateCreativeIds = newCreativeBos.stream()
                .map(CreativeBo::getLauUnitCreativeBo)
                .map(LauUnitCreativeBo::getCreativeId)
                .filter(NumberUtils::isPositive)
                .collect(Collectors.toList());
        PandoraAssert.isTrue(currentCreativeIds.containsAll(updateCreativeIds),
                "部分更新创意不存在或不属于当前单元,保存失败",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);

        newCreativeBos.forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (!NumberUtils.isPositive(creativeId)) {
                return;
            }
            Integer currentTemplateGroupId = creativeIdTemplateGroupIdMap.get(creativeId);
            PandoraAssert.notNull(creativeId, "创意" + creativeId + "不存在或不属于当前单元，更新失败",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
            Integer templateGroupId = creativeBo.getLauUnitCreativeBo().getTemplateGroupId();
            PandoraAssert.isTrue(Objects.equals(currentTemplateGroupId, templateGroupId),
                    "创意" + creativeId + "模板组id不可变更,更新失败,历史模板组id:"
                            + currentTemplateGroupId + "目前模板组id:" + templateGroupId,
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);

            if (NumberUtils.isPositive(creativeBo.getLauCreativeExtraBo().getIsSmartDerivative())) {
                PandoraAssert.isTrue(GeneralVersion.GENERAL_VERSION_V2.equals(ctx.getLauUnitExtraBo().getGeneralVersion()),
                        "当前创意版本不支持智能衍生功能", ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
            }

        });

        final Set<Integer> updateCreativeIdSet = new HashSet<>(updateCreativeIds);
        PandoraAssert.isTrue(updateCreativeIdSet.size() == updateCreativeIds.size(), "保存时存在重复的创意id",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        if (ctx.isProgrammatic() && !GeneralVersion.GENERAL_VERSION_V2.equals(ctx.getLauUnitExtraBo().getGeneralVersion())) {
            PandoraAssert.isTrue(newCreativeBos.size() == 1, "一个单元下只能有一个程序化创意",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        }
    }

    // 前面生成模板情况的时候已经校验过单元和计划本身是否支持原生了
    private void validateSupportBiliNative(SaveUnitCreativeContextBo ctx) {
        LauUnitExtraBo currentLauUnitExtraBo = ctx.getLauUnitExtraBo();
        if (Objects.isNull(currentLauUnitExtraBo)) {
            return;
        }

        Integer currentIsBiliNative = currentLauUnitExtraBo.getIsBiliNative();
        if (IsBiliNative.UNDEFINED.equals(currentIsBiliNative)) {
            return;
        }

        Integer isBiliNative = ctx.getNewVersion().getUnit().getLauUnitExtraBo().getIsBiliNative();

        PandoraAssert.isTrue(Objects.equals(isBiliNative, currentIsBiliNative),
                "当前单元下已创建过原生/非原生创意,只能创建相同投放类型的创意",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
    }

    private void validateContentByRule(SaveUnitCreativeContextBo ctx) {
        if (ctx.getLauUnitBo().getPromotionPurposeType() == PromotionContentType.PPC_GAME_CARD_VALUE) {
            return;
        }
        ValidateCreativeContentContextBo validateCtxBo = generateValidateCtx(ctx);

        List<CreativeContentBo> contentRuleList = new ArrayList<>(ctx.getNewVersion().getCreatives().size());
        // 逐个验证
        ctx.getNewVersion().getCreatives()
                .forEach(creativeBo -> {
                    CreativeContentBo contentRuleBo = getCreativeContent(creativeBo, validateCtxBo);
                    contentRuleList.add(contentRuleBo);
                    validateSingleCreativeContentByRule(creativeBo, contentRuleBo, validateCtxBo);
                });
        // 验证单元公共信息
        // 监测链接
        Integer playMonitoringAccessibility = contentRuleList.stream().map(CreativeContentBo::getPlayMonitoringAccessibility)
                .max(Integer::compareTo).orElse(0);
        validateMonitoring(ctx.getNewVersion().getUnit().getLauUnitMonitoringBos(), playMonitoringAccessibility);
    }

    private void validateMonitoring(List<LauUnitMonitoringBo> currentMonitoringBos, Integer playMonitoringAccessibility) {
        if (CollectionUtils.isEmpty(currentMonitoringBos)) {
            return;
        }

        // 播放监测
        currentMonitoringBos.forEach(monitoringBo ->
                PandoraAssert.support(Objects.equals(MonitorType.MONITOR_PLAY_MONITORING.getNumber(), monitoringBo.getType()),
                        playMonitoringAccessibility, "播放监测"));
    }

    private ValidateCreativeContentContextBo generateValidateCtx(SaveUnitCreativeContextBo ctx) {
        ValidateCreativeContentContextBo validateCtx = new ValidateCreativeContentContextBo();

        // 账户
        validateCtx.setAccountId(ctx.getAccountId());
        validateCtx.setAccountLabelIds(new ArrayList<>(ctx.getAccountLabelIds()));
        validateCtx.setAccountBo(ctx.getAccountBo());

        validateCtx.setLauCampaignBo(ctx.getCampaignBo());
        validateCtx.setLauUnitBo(ctx.getLauUnitBo());
        validateCtx.setIsBiliNative(ctx.getNewVersion().getUnit().getLauUnitExtraBo().getIsBiliNative());

        validateCtx.setNeedSkipAccountAuthorize(ctx.isNeedSkipAccountAuthorize());

        List<LauCreativeExtraBo> lauCreativeExtraBos = ctx.getNewVersion().getCreatives().stream()
                .map(CreativeBo::getLauCreativeExtraBo)
                .collect(Collectors.toList());

        // 微信小游戏
        List<Integer> miniGameIds = ctx.getNewVersion().getCreatives().stream()
                .map(CreativeBo::getLauCreativeMiniGameMappingBo)
                .filter(Objects::nonNull)
                .map(LauCreativeMiniGameMappingBo::getMiniGameId)
                .filter(NumberUtils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        List<MiniGameBo> miniGameBos = resMiniGameService.fetchByIds(miniGameIds);
        Map<Integer, MiniGameBo> miniGameBoMap = miniGameBos.stream()
                .collect(Collectors.toMap(MiniGameBo::getId, Function.identity()));
        validateCtx.setMiniGameBoMap(miniGameBoMap);

        Map<Integer, Integer> currentMiniGameIdMap = ctx.getCurrentVersion().getCreatives().stream()
                .map(CreativeBo::getLauCreativeMiniGameMappingBo)
                .filter(Objects::nonNull)
                .filter(t -> NumberUtils.isPositive(t.getMiniGameId()))
                .collect(Collectors.toMap(LauCreativeMiniGameMappingBo::getCreativeId, LauCreativeMiniGameMappingBo::getMiniGameId));
        validateCtx.setCurrentMiniGameIdMap(currentMiniGameIdMap);


        // 资质
        List<Integer> qualificationIds = ctx.getNewVersion().getCreatives().stream()
                .map(CreativeBo::getQualificationIds)
                .filter(qualificationIdList -> !CollectionUtils.isEmpty(qualificationIdList))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        List<LauQualificationBo> lauQualificationBos = resQualificationService.fetchByIds(qualificationIds);
        Map<Integer, LauQualificationBo> qualificationBoMap = lauQualificationBos.stream()
                .collect(Collectors.toMap(LauQualificationBo::getId, Function.identity()));
        validateCtx.setQualificationBoMap(qualificationBoMap);

        // 资质包
        List<Integer> qualificationPackageIds = lauCreativeExtraBos.stream()
                .map(LauCreativeExtraBo::getQualificationPackageId)
                .filter(id -> !Objects.equals(0, id))
                .distinct()
                .collect(Collectors.toList());
        List<LauQualificationPackageBo> lauQualificationPackageBos =
                resQualificationService.fetchPackageByPackageIds(qualificationPackageIds);
        Map<Integer, LauQualificationPackageBo> qualificationPackageBoMap = lauQualificationPackageBos.stream()
                .collect(Collectors.toMap(LauQualificationPackageBo::getId, Function.identity()));
        validateCtx.setQualificationPackageBoMap(qualificationPackageBoMap);

        // up主
        List<Long> biliSpaceMids = lauCreativeExtraBos.stream()
                .map(LauCreativeExtraBo::getBiliSpaceMid)
                .filter(NumberUtils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> upDescMap = accountServiceProxy.getUpDescMap(biliSpaceMids);
        validateCtx.setUpDescMap(upDescMap);

        // 商业空间
        List<Integer> adSpaceIds = lauCreativeExtraBos.stream()
                .map(LauCreativeExtraBo::getAdSpaceId)
                .filter(NumberUtils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        List<AdSpaceBo> adSpaceBos = resAdSpaceService.fetchByIds(adSpaceIds);
        Map<Integer, AdSpaceBo> adSpaceMap = adSpaceBos.stream()
                .collect(Collectors.toMap(AdSpaceBo::getId, Function.identity()));
        validateCtx.setAdSpaceMap(adSpaceMap);

        // 同产品同代理 资质/资质包就是这样的 没办法
        Set<Integer> qualificationAccountIds = lauQualificationBos.stream()
                .map(LauQualificationBo::getAccountId)
                .collect(Collectors.toSet());
        Set<Integer> qualificationPackageAccountIds = lauQualificationPackageBos.stream()
                .map(LauQualificationPackageBo::getAccountId)
                .collect(Collectors.toSet());
        List<Integer> queryAccountIds = Stream.of(qualificationAccountIds, qualificationPackageAccountIds)
                .filter(accountIdSet -> !CollectionUtils.isEmpty(accountIdSet))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        List<AccountBo> qualificationAccountBoList = accountService.list(queryAccountIds);
        Map<Integer, AccountBo> qualificationAccountMap = qualificationAccountBoList.stream()
                .collect(Collectors.toMap(AccountBo::getAccountId, Function.identity()));
        validateCtx.setQualificationAccountMap(qualificationAccountMap);

        // 落地页
        if (Objects.nonNull(ctx.getLandingPageContext())) {
            validateCtx.setLandingPageBoMap(ctx.getLandingPageContext().getLandingPageBoMap());
        }

        return validateCtx;
    }

    private void validateSingleCreativeContentByRule(CreativeBo creativeBo,
                                                     CreativeContentBo contentRuleBo,
                                                     ValidateCreativeContentContextBo validateCtx) {
        PandoraAssert.notNull(contentRuleBo, "当前配置下不存在匹配的创意填写规则",
                ErrorCodeEnum.DomainType.CONFIG, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);

        // 创意名称
        LauUnitCreativeBo lauUnitCreativeBo = creativeBo.getLauUnitCreativeBo();
        String creativeName = lauUnitCreativeBo.getCreativeName();
        validateCreativeName(creativeName);

        // 标题
        TemplateGroupBo templateGroupBo = creativeBo.getTemplateGroupDetailBo().getTemplateGroupBo();
        validateTitle(creativeBo.getLauCreativeTitleBos(), contentRuleBo, templateGroupBo, validateCtx);

        // 描述
        validateDescription(creativeBo.getLauUnitCreativeBo().getDescription(), contentRuleBo, templateGroupBo, validateCtx);

        // 落地页/落地页组
        LauCreativeExtraBo lauCreativeExtraBo = creativeBo.getLauCreativeExtraBo();
        validateLandingPage(creativeBo, contentRuleBo, validateCtx);

        // 唤起链接
        String schemaUrl = creativeBo.getLauUnitCreativeBo().getSchemeUrl();
        validateSchemaUrl(schemaUrl, validateCtx, contentRuleBo);

        // 微信小程序
        LauCreativeMiniGameMappingBo miniGameMappingBo = creativeBo.getLauCreativeMiniGameMappingBo();
        validateMiniGame(validateCtx, miniGameMappingBo, contentRuleBo, lauUnitCreativeBo.getCreativeId());

        // 老起飞组件 -> 黄车组件
        LauCreativeFlyExtInfoBo flyExtInfoBo = creativeBo.getLauCreativeFlyExtInfoBo();
        validateYellowCar(flyExtInfoBo, contentRuleBo, validateCtx);

        // 创意组件
        List<LauCreativeComponentBo> componentBos = creativeBo.getLauCreativeComponentBos();
        validateCreativeComponents(validateCtx, componentBos, contentRuleBo);

        // 空间设置
        validateSpace(validateCtx, lauUnitCreativeBo, lauCreativeExtraBo, contentRuleBo);

        // 按钮
        LauCreativeButtonCopyBo buttonCopyBo = creativeBo.getLauCreativeButtonCopyBo();
        validateButton(buttonCopyBo, contentRuleBo);

        // 商业标
        Integer busMarkId = creativeBo.getLauUnitCreativeBo().getBusMarkId();
        validateBusMarkId(busMarkId, creativeBo.getTemplateGroupDetailBo(), contentRuleBo);

        // 资质
        List<Integer> qualificationIds = creativeBo.getQualificationIds();
        Integer qualificationPackageId = lauCreativeExtraBo.getQualificationPackageId();
        validateQualification(validateCtx, qualificationIds, qualificationPackageId, contentRuleBo);

        // 校验ogv materialNo
        if (Objects.nonNull(creativeBo.getLauCreativePgcArchiveBo())) {
            Long materialNo = creativeBo.getLauCreativePgcArchiveBo().getMaterialNo();
            validateOgvMaterialNo(materialNo, contentRuleBo);
        }


    }

    private CreativeContentBo getCreativeContent(CreativeBo creativeBo,
                                                 ValidateCreativeContentContextBo validateCtx) {
        LauCampaignBo lauCampaignBo = validateCtx.getLauCampaignBo();
        LauUnitBo lauUnitBo = validateCtx.getLauUnitBo();
        CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo =
                ResCreativeApiMapper.MAPPER.fromCampaignAndUnitBo(lauCampaignBo, lauUnitBo);

        QueryCreativeContentBo queryContentBo = new QueryCreativeContentBo();
        queryContentBo.setAccountId(validateCtx.getAccountId());
        queryContentBo.setAccountLabelIds(validateCtx.getAccountLabelIds());
        queryContentBo.setLaunchMode(validateCtx.getIsBiliNative());
        queryContentBo.setSceneIds(Collections.emptyList());
        queryContentBo.setTemplateGroupId(creativeBo.getTemplateGroupDetailBo().getTemplateGroupBo().getTemplateGroupId());
        queryContentBo.setCreativeConfigCampaignUnitInfoBo(creativeConfigCampaignUnitInfoBo);
        return resCreativeService.getCreativeContent(queryContentBo);
    }

    private void validateCreativeName(String creativeName) {
        PandoraAssert.isTrue(!StringUtils.isEmpty(creativeName), "创意名称不可为空",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        PandoraAssert.isTrue(creativeName.length() <= 64, "创意名称长度不可大于64",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        // emoji
        PandoraAssert.isTrue(!EmojiUtils.hasEmoji(creativeName), "创意名称不支持保存Emoji",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
    }

    private void validateTitle(List<LauCreativeTitleBo> titleBoList,
                               CreativeContentBo contentRuleBo,
                               TemplateGroupBo templateGroupBo, ValidateCreativeContentContextBo validateCtx) {
        PandoraAssert.support(!CollectionUtils.isEmpty(titleBoList),
                contentRuleBo.getTitleAccessibility(), "创意标题");
        titleBoList.forEach(title -> {
            int curr = title.getRawTitle().length();
            // emoji
            PandoraAssert.isTrue(!EmojiUtils.hasEmoji(title.getRawTitle()), "标题不支持保存Emoji",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            if (!CollectionUtils.isEmpty(title.getLauCreativeSmartTitleBos())) {
                for (LauCreativeSmartTitleBo smartBo : title.getLauCreativeSmartTitleBos()) {
                    SmartTitleTypeEnum type = SmartTitleTypeEnum.getByCode(smartBo.getType());
                    PandoraAssert.isTrue(title.getRawTitle().contains(type.getPlaceHolder()), "智能标题占位符错误或不存在",
                            ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
                    curr = curr - type.getPlaceHolder().length() + type.getValidLength();
                }
            }
            if (validateCtx.isDPACreative()) {
                curr = getLengthWithoutWildcard(title.getRawTitle());
            }
            Integer floor = templateGroupBo.getTitleMinLength();
            Integer ceiling = templateGroupBo.getTitleMaxLength();
            PandoraAssert.isBetween(curr, floor, ceiling, "创意标题长度");
        });
    }

    private int getLengthWithoutWildcard(String rawTitle) {
        if (!StringUtils.hasText(rawTitle)) {
            return 0;
        }
        for (String key : ProductWildcardEnum.NO_URL_KEYS) {
            rawTitle = rawTitle.replace(key, "");
        }
        return !rawTitle.isEmpty() ? rawTitle.length() : MIN_TITLE_DESC_LENGTH;
    }

    private void validateDescription(String description,
                                     CreativeContentBo contentRuleBo,
                                     TemplateGroupBo templateGroupBo,
                                     ValidateCreativeContentContextBo validateCtx) {
        PandoraAssert.support(!StringUtils.isEmpty(description), contentRuleBo.getDescriptionAccessibility(), "创意描述");
        // DPA创意标题、描述长度需排除通配符
        int descCurr = validateCtx.isDPACreative() ? getLengthWithoutWildcard(description) : description.length();
        Integer descFloor = templateGroupBo.getDescriptionMinLength();
        Integer descCeiling = templateGroupBo.getDescriptionMaxLength();
        PandoraAssert.isBetween(descCurr, descFloor, descCeiling, "创意描述长度");
    }

    public static final List<Integer> CONSULT_LANDING_PAGE_CPA_TARGETS = Lists.newArrayList(CpaTarget.CPA_FORM_SUBMIT_VALUE, CpaTarget.CPA_VIDEO_PLAY_VALUE, CpaTarget.CPA_COMMENT_CLICK_VALUE);


    private void validateLandingPage(CreativeBo creativeBo,
                                     CreativeContentBo contentRuleBo,
                                     ValidateCreativeContentContextBo validateCtx) {

        LauCreativeExtraBo lauCreativeExtraBo = creativeBo.getLauCreativeExtraBo();
        LauCreativeLandingPageGroupBo lauCreativeLandingPageGroupBo = creativeBo.getLauCreativeLandingPageGroupBo();

        boolean hasLandingPage = StringUtils.hasText(lauCreativeExtraBo.getRawJumpUrl())
                || NumberUtils.isPositive(lauCreativeExtraBo.getMgkPageId());
        boolean hasLandingPageGroup = Objects.nonNull(lauCreativeLandingPageGroupBo)
                && NumberUtils.isPositive(lauCreativeLandingPageGroupBo.getGroupId());
        PandoraAssert.support(hasLandingPage || hasLandingPageGroup,
                contentRuleBo.getJumpUrlAccessibility(), "落地页/落地页组");
        // 落地页/落地页组有效性前面已经校验过了
        // 强制填充在前面完成
        boolean hasMgkPage = NumberUtils.isPositive(lauCreativeExtraBo.getMgkPageId())
                || hasLandingPageGroup && PageGroupSource.isFromMgk(lauCreativeLandingPageGroupBo.getGroupSource());
        if (Objects.equals(PromotionContentType.PPC_SHOP_GOODS_VALUE, validateCtx.getLauUnitBo().getPromotionPurposeType())) {
            PandoraAssert.isTrue(!hasMgkPage, "会员购不支持建站落地页",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
        }

        validateConsultPage(creativeBo, validateCtx);

        String jumpUrl = lauCreativeExtraBo.getRawJumpUrl();
        if (validateCtx.isDPACreative() && !jumpUrl.contains("http")) {
            PandoraAssert.isTrue(ProductWildcardEnum.URL_KEYS.stream().anyMatch(jumpUrl::contains), "落地页链接中使用了不支持的通配符",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        }
    }

    private static final Integer consult_type = 6;

    private void validateConsultPage(CreativeBo creativeBo,
                                     ValidateCreativeContentContextBo validateCtx) {
        if (CONSULT_LANDING_PAGE_CPA_TARGETS.contains(validateCtx.getLauUnitBo().getOcpcTarget().getValue())) {
            return;
        }

        LauCreativeExtraBo lauCreativeExtraBo = creativeBo.getLauCreativeExtraBo();
        CreativeCpsReplaceLinkBo cpsReplaceLinkInfo = creativeBo.getCpsReplaceLinkInfo();
        // 是否有替链接
        boolean hasReplaceLink = creativeBo.getCpsReplaceLinkInfo() != null && NumberUtils.isPositive(cpsReplaceLinkInfo.getIsReplaceUrl());
        // 是否原生投放
        boolean isNative = NumberUtils.isPositive(validateCtx.getIsBiliNative());

        // 先校验原链 和 替链 ;有替链就校验替链，没有的话，看原链
        if (Objects.nonNull(validateCtx.getLandingPageBoMap())) {

            Long mgkPageId = hasReplaceLink ? cpsReplaceLinkInfo.getConversionUrlPageId() : lauCreativeExtraBo.getMgkPageId();
            LandingPageBo landingPageBo = validateCtx.getLandingPageBoMap().get(mgkPageId);
            PandoraAssert.isTrue(landingPageBo == null || !Objects.equals(landingPageBo.getType(), consult_type),
                    "当前投放目标暂不支持添加咨询页，请返回修改",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
        }


        // 开始校验 锚点,评论组件,框下组件
        List<Long> pageIdList = new ArrayList<>();


        // 原生锚点: 常规投放不校验原生锚点 ; 有替链不校验原生锚点
        if (isNative && !hasReplaceLink &&
                Objects.nonNull(creativeBo.getArchiveBizAnchorWrapBo()) && Objects.nonNull(creativeBo.getArchiveBizAnchorWrapBo().getArchiveBizAnchorBo())) {

            ArchiveBizAnchorBo archiveBizAnchorBo = creativeBo.getArchiveBizAnchorWrapBo().getArchiveBizAnchorBo();
            if (NumberUtils.isPositive(archiveBizAnchorBo.getConversionUrlPageId())) {
                pageIdList.add(archiveBizAnchorBo.getConversionUrlPageId());
            }

            if (NumberUtils.isPositive(archiveBizAnchorBo.getIosUrlPageId())) {
                pageIdList.add(archiveBizAnchorBo.getIosUrlPageId());
            }

            if (NumberUtils.isPositive(archiveBizAnchorBo.getAndroidUrlPageId())) {
                pageIdList.add(archiveBizAnchorBo.getAndroidUrlPageId());
            }

        }


        //框下组件 : 有替链就不校验
        if (!hasReplaceLink && !CollectionUtils.isEmpty(creativeBo.getLauCreativeComponentBos())) {
            List<LauCreativeComponentBo> underframeComponentBos = creativeBo.getLauCreativeComponentBos().stream().filter(componentBo -> LauCreativeComponentType.isUnderframe(componentBo.getComponentType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(underframeComponentBos)) {
                LegacyQueryUnderframeComponentBo queryUnderframeComponentBo = LegacyQueryUnderframeComponentBo.builder().ids(underframeComponentBos.stream().map(LauCreativeComponentBo::getComponentId).distinct().collect(Collectors.toList())).build();
                List<UnderframeComponentBo> underframeComponents = underframeComponentService.list(queryUnderframeComponentBo);
                if (!CollectionUtils.isEmpty(underframeComponents)) {
                    List<Long> mgkPageIds = underframeComponents.stream().map(UnderframeComponentBo::getMgkPageId).distinct().collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(mgkPageIds)) {
                        pageIdList.addAll(mgkPageIds);
                    }
                }
            }
        }


        //评论组件 : 有替链就不校验
        if (!hasReplaceLink && !CollectionUtils.isEmpty(creativeBo.getLauCreativeArchiveBos()) && Objects.nonNull(creativeBo.getLauCreativeArchiveBos().get(0)) && NumberUtils.isPositive(creativeBo.getLauCreativeArchiveBos().get(0).getAvid())) {
            Long avid = creativeBo.getLauCreativeArchiveBos().get(0).getAvid();
            List<CommentComponent> components = cpmScvProxy.components(Collections.singletonList(avid), 1, 1);
            if (!CollectionUtils.isEmpty(components)) {
                CommentComponent component = components.get(0);

                if (NumberUtils.isPositive(component.getConversionUrlPageId())) {
                    pageIdList.add(component.getConversionUrlPageId());
                }

                if (NumberUtils.isPositive(component.getIosUrlPageId())) {
                    pageIdList.add(component.getIosUrlPageId());
                }

                if (NumberUtils.isPositive(component.getAndroidUrlPageId())) {
                    pageIdList.add(component.getAndroidUrlPageId());
                }
            }

        }

        if (pageIdList.isEmpty()) {
            return;
        }

        List<LandingPageBo> landingPageBoList = cpmMgkPortalProxy.validateGetPageList(pageIdList);
        for (LandingPageBo landingPageBo : landingPageBoList) {
            PandoraAssert.isTrue(!Objects.equals(landingPageBo.getType(), consult_type),
                    "当前投放目标暂不支持添加咨询页，请返回修改",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
        }


    }


    private void validateSchemaUrl(String schemaUrl,
                                   ValidateCreativeContentContextBo validateCtx,
                                   CreativeContentBo contentRuleBo) {
        Integer promotionContentType = validateCtx.getLauUnitBo().getPromotionPurposeType();
        if (PromotionContentType.PPC_TAOBAO_TMALL_GOODS_ID_VALUE == promotionContentType) {
            return;
        }
        boolean hasSchemeUrl = StringUtils.hasText(schemaUrl);
        PandoraAssert.support(hasSchemeUrl, contentRuleBo.getSchemeUrlAccessibility(), "唤起链接");
        // DPA创意，使用了通配符作为唤起链接则校验通配符
        if (hasSchemeUrl && validateCtx.isDPACreative() && schemaUrl.matches(".*\\$\\{[^}]+}.*")) {
            PandoraAssert.isTrue(ProductWildcardEnum.URL_KEYS.stream().anyMatch(schemaUrl::contains), "唤起链接中使用了不支持的通配符",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        }
    }

    private void validateMiniGame(ValidateCreativeContentContextBo validateCtx,
                                  LauCreativeMiniGameMappingBo miniGameMappingBo,
                                  CreativeContentBo contentRuleBo, Integer creativeId) {
        Integer accountId = validateCtx.getAccountId();

        boolean hasMiniGameMapping = Objects.nonNull(miniGameMappingBo) &&
                (NumberUtils.isPositive(miniGameMappingBo.getMiniGameId())
                        || StringUtils.hasText(miniGameMappingBo.getGameUrl()));
        Integer currentMiniGameId = validateCtx.getCurrentMiniGameIdMap().get(creativeId);

        // 如果命中了1043 且 是存量数据 且 没有修改。 就跳过“当前不支持填写微信一跳”的校验
        if (validateCtx.getAccountLabelIds().contains(IS_PPC_WX_MINI_PROGRAM) && hasMiniGameMapping &&
                Objects.equals(currentMiniGameId, miniGameMappingBo.getMiniGameId())) {
        } else {
            PandoraAssert.support(hasMiniGameMapping, contentRuleBo.getWechatMiniGameAccessibility(), "微信一跳");
        }

        if (!hasMiniGameMapping) {
            return;
        }

        // 如果命中白名单 那么创意层级的微信小程序就不允许修改了
        // todo sjc 后续会开启校验
//        if (validateCtx.getAccountLabelIds().contains(IS_PPC_WX_MINI_PROGRAM)) {
//            if (!NumberUtils.isPositive(currentMiniGameId)) {
//                PandoraAssert.isTrue(Objects.equals(currentMiniGameId, miniGameMappingBo.getMiniGameId()),
//                        "当前不支持修改微信小程序",
//                        ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
//            } else {
//                PandoraAssert.isTrue(NumberUtils.isPositive(miniGameMappingBo.getMiniGameId()), "微信小游戏不允许在单元上新建",
//                        ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);
//            }
//        }

        PandoraAssert.isTrue(NumberUtils.isPositive(miniGameMappingBo.getMiniGameId()), "微信小游戏id不可为空",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        PandoraAssert.hasText(miniGameMappingBo.getGameUrl(), "微信小游戏url不可为空",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        PandoraAssert.isTrue(miniGameMappingBo.getGameUrl().contains("__TRACKID__"), "绑定微信小游戏路径必须包含通配符__TRACKID__",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        MiniGameBo miniGameBo = validateCtx.getMiniGameBoMap().get(miniGameMappingBo.getMiniGameId());
        PandoraAssert.notNull(miniGameBo, "未知的微信小游戏:" + miniGameMappingBo.getMiniGameId(),
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);
        PandoraAssert.isTrue(validateCtx.isNeedSkipAccountAuthorize()
                        || miniGameBo.getAccountId().equals(accountId), "您不能绑定不属于当前账户的微信小游戏",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
        PandoraAssert.isTrue(!Objects.equals(miniGameBo.getState(), 0), "您不能绑定已下架的微信小游戏",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
    }

    private void validateYellowCar(LauCreativeFlyExtInfoBo flyExtInfoBo,
                                   CreativeContentBo contentRuleBo,
                                   ValidateCreativeContentContextBo validateCtx) {
        boolean hasYellowCar = Objects.nonNull(flyExtInfoBo)
                && NumberUtils.isPositive(flyExtInfoBo.getIsYellowCar());
        PandoraAssert.support(hasYellowCar, contentRuleBo.getYellowCarComponentAccessibility(), "Story转化组件");
        LauCampaignBo lauCampaignBo = validateCtx.getLauCampaignBo();
        LauUnitBo lauUnitBo = validateCtx.getLauUnitBo();
        Integer promotionPurposeType = lauCampaignBo.getPromotionPurposeType();
        Integer promotionContentType = lauUnitBo.getPromotionPurposeType();
        if (hasYellowCar) {
            PandoraAssert.isTrue(contentRuleBo.getSupportYellowCarComponentType().contains(flyExtInfoBo.getIsYellowCar()),
                    "当前配置不支持该类型Story转化组件",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            if (PromotionPurposeType.PPT_TRADE_VALUE == promotionPurposeType
                    && PromotionContentType.PPC_GOODS_CONTENT_VALUE == promotionContentType) {
                PandoraAssert.hasText(flyExtInfoBo.getYellowCarTitle(), "Story转化组件卖点不可为空",
                        ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
                PandoraAssert.isBetween(flyExtInfoBo.getYellowCarTitle().length(), 1, 10, "Story转化组件卖点长度");
            } else {
                PandoraAssert.isTrue(!StringUtils.hasText(flyExtInfoBo.getYellowCarTitle()),
                        "目前仅交易经营计划&带货内容单元下选择Story转化组件时,支持填写组件卖点",
                        ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            }
        } else {
            PandoraAssert.isTrue(Objects.isNull(flyExtInfoBo) || !StringUtils.hasText(flyExtInfoBo.getYellowCarTitle()),
                    "未选择Story转化组件时,不允许填写卖点",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        }
    }

    private void validateCreativeComponents(ValidateCreativeContentContextBo validateCtx,
                                            List<LauCreativeComponentBo> componentBos,
                                            CreativeContentBo contentRuleBo) {
        Integer accountId = validateCtx.getAccountId();
        LauUnitBo lauUnitBo = validateCtx.getLauUnitBo();
        boolean needSkipAccountAuthorize = validateCtx.isNeedSkipAccountAuthorize();

        // 老必选Story组件
        List<LauCreativeComponentBo> storyComponentBos = CollectionUtils.isEmpty(componentBos) ?
                Collections.emptyList() : componentBos.stream()
                .filter(componentBo -> LauCreativeComponentType.isStory(componentBo.getComponentType()))
                .collect(Collectors.toList());
        PandoraAssert.support(!CollectionUtils.isEmpty(storyComponentBos),
                contentRuleBo.getStoryComponentAccessibility(),
                "转化组件");

        // 框下组件
        List<LauCreativeComponentBo> underframeComponentBos = CollectionUtils.isEmpty(componentBos) ?
                Collections.emptyList() : componentBos.stream()
                .filter(componentBo -> LauCreativeComponentType.isUnderframe(componentBo.getComponentType()))
                .collect(Collectors.toList());
        PandoraAssert.support(!CollectionUtils.isEmpty(underframeComponentBos),
                contentRuleBo.getUnderframeComponentAccessibility(),
                "框下转化组件");

        if (!CollectionUtils.isEmpty(componentBos)) {
            PandoraAssert.isTrue(storyComponentBos.size() <= 1, "单个创意最多只能保存一个转化组件",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            PandoraAssert.isTrue(underframeComponentBos.size() <= 1, "单个创意最多只能保存一个框下转化组件",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            PandoraAssert.isTrue(componentBos.size() == storyComponentBos.size() + underframeComponentBos.size(),
                    "创意组件仅支持转化组件/框下转化组件",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            componentBos.forEach(componentBo -> {
                validateSingleCreativeComponent(accountId, needSkipAccountAuthorize, lauUnitBo, componentBo);
            });
        }
    }

    private void validateSingleCreativeComponent(Integer accountId,
                                                 boolean needSkipAccountAuthorize,
                                                 LauUnitBo lauUnitBo,
                                                 LauCreativeComponentBo componentBo) {
        Long componentId = componentBo.getComponentId();
        Integer componentType = componentBo.getComponentType();

        CreativeComponentBo creativeComponentBo =
                resCreativeComponentService.get(componentId, componentType);

        if (Objects.equals(componentType, CreativeComponentType.STORY_COMMON_VALUE)) {
            StoryCommonComponentBo storyCommonBo = creativeComponentBo.getStoryCommon();
            PandoraAssert.notNull(storyCommonBo,
                    "Story基础创意组件不存在,组件id:" + componentBo.getId(),
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
            PandoraAssert.isTrue(needSkipAccountAuthorize
                            || Objects.equals(storyCommonBo.getAccountId(), accountId),
                    "Story基础创意组件" + componentId + "不属于当前账户",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
        } else if (Objects.equals(componentType, CreativeComponentType.STORY_COUPON_VALUE)) {
            StoryCouponComponentBo storyCouponBo = creativeComponentBo.getStoryCoupon();
            PandoraAssert.notNull(storyCouponBo,
                    "Story优惠券创意组件不存在,组件id:" + componentId,
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
            PandoraAssert.isTrue(needSkipAccountAuthorize
                            || Objects.equals(storyCouponBo.getAccountId(), accountId),
                    "Story优惠券创意组件" + componentId + "不属于当前账户",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);

            // 优惠券获取end时间 <= 优惠券有效end时间
            PandoraAssert.isTrue(storyCouponBo.getObtainPeriodEnd().compareTo(
                            storyCouponBo.getUsePeriodEnd()) <= 0, "优惠券的获取时间不能晚于优惠券的使用时间",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);
            // 优惠券获取end时间 >= 单元投放结束时间
            PandoraAssert.isTrue(storyCouponBo.getObtainPeriodEnd().compareTo(Timestamp.valueOf(
                            LocalDate.parse(lauUnitBo.getLaunchEndDate()).atStartOfDay())) >= 0,
                    "必须满足单元投放结束时间 <= 优惠券投放结束时间",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);
            // 优惠券获取begin时间 <= 单元投放开始时间
            PandoraAssert.isTrue(storyCouponBo.getObtainPeriodStart().compareTo(Timestamp.valueOf(
                            LocalDate.parse(lauUnitBo.getLaunchEndDate()).atStartOfDay())) <= 0,
                    "必须满足单元投放开始时间 >= 优惠券投放开始时间",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);
        } else if (Objects.equals(componentType, CreativeComponentType.STORY_IMAGE_VALUE)) {
            StoryImageComponentBo storyImageBo = creativeComponentBo.getStoryImage();
            PandoraAssert.notNull(storyImageBo,
                    "Story图片创意组件不存在,组件id:" + componentId,
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
            PandoraAssert.isTrue(needSkipAccountAuthorize
                            || Objects.equals(storyImageBo.getAccountId(), accountId),
                    "Story图片创意组件" + componentId + "不属于当前账户",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
        } else if (Objects.equals(componentType, CreativeComponentType.UNDERFRAME_VALUE)) {
            UnderframeComponentBo underframeBo = creativeComponentBo.getUnderframe();
            PandoraAssert.notNull(underframeBo,
                    "框下创意组件不存在,组件id:" + componentId,
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
            PandoraAssert.isTrue(needSkipAccountAuthorize
                            || Objects.equals(underframeBo.getAccountId(), accountId),
                    "框下创意组件" + componentId + "不属于当前账户",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
        } else if (needSkipAccountAuthorize
                || Objects.equals(componentType, CreativeComponentType.STORY_PRICE_DIFFERENCE.getNumber())) {
            StoryPriceDifferenceComponentBo priceDifferenceComponentBo = creativeComponentBo.getStoryPriceDifference();
            PandoraAssert.notNull(priceDifferenceComponentBo,
                    "Story价差卡创意组件不存在,组件id:" + componentId,
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
            PandoraAssert.isTrue(Objects.equals(priceDifferenceComponentBo.getAccountId(), accountId),
                    "Story价差卡创意组件" + componentId + "不属于当前账户",
                    ErrorCodeEnum.DomainType.CREATIVE_COMPONENT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
        } else {
            throw new PandoraDataErr("未知的创意组件类型:" + creativeComponentBo.getType());
        }
    }

    private void validateSpace(ValidateCreativeContentContextBo validateCtx,
                               LauUnitCreativeBo lauUnitCreativeBo,
                               LauCreativeExtraBo lauCreativeExtraBo,
                               CreativeContentBo contentRuleBo) {
        // 图片模板组不校验品牌空间了 按潇秦老师指示已经前置强行清除了
        if (TemplateGroup.isImage(lauUnitCreativeBo.getTemplateGroupId())) {
            return;
        }

        Integer adSpaceId = lauCreativeExtraBo.getAdSpaceId();
        Long biliSpaceMid = lauCreativeExtraBo.getBiliSpaceMid();
        PandoraAssert.support(NumberUtils.isPositive(adSpaceId) || NumberUtils.isPositive(biliSpaceMid),
                contentRuleBo.getBrandSpaceAccessibility(), "空间设置");
        PandoraAssert.isTrue(!NumberUtils.isPositive(adSpaceId) || !NumberUtils.isPositive(biliSpaceMid),
                "品牌空间设置不可同时填写真实号mid和商业自定义id",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        if (NumberUtils.isPositive(lauCreativeExtraBo.getBiliSpaceMid())) {
            PandoraAssert.isTrue(validateCtx.getUpDescMap().containsKey(lauCreativeExtraBo.getBiliSpaceMid()),
                    "品牌空间设置真实号mid不存在:" + lauCreativeExtraBo.getBiliSpaceMid(),
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
        } else if (NumberUtils.isPositive(lauCreativeExtraBo.getAdSpaceId())) {
            AdSpaceBo adSpaceBo = validateCtx.getAdSpaceMap().get(lauCreativeExtraBo.getAdSpaceId());
            PandoraAssert.notNull(adSpaceBo,
                    "品牌空间设置商业自定义id不存在:" + lauCreativeExtraBo.getAdSpaceId(),
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
            PandoraAssert.isTrue(validateCtx.isNeedSkipAccountAuthorize()
                            || Objects.equals(adSpaceBo.getAccountId(), validateCtx.getAccountId()),
                    "品牌空间设置商业自定义id不属于当前账户:" + validateCtx.getAccountId(),
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
        }
    }

    private void validateButton(LauCreativeButtonCopyBo buttonCopyBo,
                                CreativeContentBo contentRuleBo) {
        boolean hasButton = Objects.nonNull(buttonCopyBo) && NumberUtils.isPositive(buttonCopyBo.getButtonCopyId());
        PandoraAssert.support(hasButton, contentRuleBo.getButtonAccessibility(), "按钮");
        // 按钮是否存在 前面已经校验过了
    }

    private void validateOgvMaterialNo(Long materialNo, CreativeContentBo contentRuleBo) {
        PandoraAssert.support(NumberUtils.isPositive(materialNo), contentRuleBo.getOgvHighlightAccessibility(), "ogv高能片段");
    }

    private void validateBusMarkId(Integer busMarkId,
                                   TemplateGroupDetailBo templateGroupDetailBo,
                                   CreativeContentBo contentRuleBo) {
        boolean hasBusMarkId = NumberUtils.isPositive(busMarkId);

        Set<Integer> busMarkIdSet = templateGroupDetailBo.getTemplateGroupBo().getBusMarks().stream()
                .map(BusMarkBo::getBusMarkId)
                .collect(Collectors.toSet());

        if (!hasBusMarkId && CollectionUtils.isEmpty(busMarkIdSet)) {
            return;
        }

        PandoraAssert.support(hasBusMarkId, contentRuleBo.getBusMarkIdAccessibility(), "商业标");

        if (!hasBusMarkId) {
            return;
        }


        PandoraAssert.isTrue(busMarkIdSet.contains(busMarkId), "不支持当前商业标id:" + busMarkId,
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
    }

    private void validateQualification(ValidateCreativeContentContextBo validateCtx,
                                       List<Integer> qualificationIds,
                                       Integer qualificationPackageId,
                                       CreativeContentBo contentRuleBo) {
        PandoraAssert.support(!CollectionUtils.isEmpty(qualificationIds)
                        || NumberUtils.isPositive(qualificationPackageId),
                contentRuleBo.getQualificationAccessibility(), "资质");
        PandoraAssert.isTrue(CollectionUtils.isEmpty(qualificationIds) || !NumberUtils.isPositive(qualificationPackageId),
                "资质和资质包不可同时选择",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        if (!CollectionUtils.isEmpty(qualificationIds)) {
            Map<Integer, LauQualificationBo> qualificationBoMap = validateCtx.getQualificationBoMap();
            qualificationIds.forEach(qualificationId -> {
                LauQualificationBo qualificationBo = qualificationBoMap.get(qualificationId);
                PandoraAssert.notNull(qualificationBo, "资质" + qualificationId + "不存在",
                        ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
                long validTo = qualificationBo.getQuaValidTo().getTime();
                long validFrom = qualificationBo.getQuaValidFrom().getTime();
                long curr = System.currentTimeMillis();
                PandoraAssert.isTrue(validFrom <= curr, "资质" + qualificationId + "未生效，保存失败",
                        ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_STATUS_INVALID);
                PandoraAssert.isTrue(validTo >= curr, "资质" + qualificationId + "已过期，保存失败",
                        ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_STATUS_INVALID);
                Integer accountId = qualificationBo.getAccountId();
                AccountBo accountBo = validateCtx.getQualificationAccountMap().get(accountId);
                PandoraAssert.notNull(accountBo, "资质" + qualificationId + "对应的账户" + accountId + "不存在",
                        ErrorCodeEnum.DomainType.ACCOUNT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
                AccountBo creativeAccountBo = validateCtx.getAccountBo();
                PandoraAssert.isTrue(validateCtx.isNeedSkipAccountAuthorize()
                                || Objects.equals(creativeAccountBo.getProductId(), accountBo.getProductId())
                                && Objects.equals(creativeAccountBo.getGroupId(), accountBo.getGroupId())
                                && Objects.equals(creativeAccountBo.getDependencyAgentId(), accountBo.getDependencyAgentId()), "资质仅支持同产品同代理",
                        ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
                PandoraAssert.isTrue(Objects.equals(accountBo.getAdStatus(), 0)
                                || Objects.equals(accountBo.getIsSupportFly(), 1)
                                || Objects.equals(accountBo.getIsSupportContent(), 1),
                        "资质仅支持三连推广账户",
                        ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
            });
        }

        if (NumberUtils.isPositive(qualificationPackageId)) {
            Map<Integer, LauQualificationPackageBo> qualificationPackageBoMap = validateCtx.getQualificationPackageBoMap();
            LauQualificationPackageBo qualificationPackageBo = qualificationPackageBoMap.get(qualificationPackageId);
            PandoraAssert.notNull(qualificationPackageBo, "资质包" + qualificationPackageId + "不存在",
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
            Integer accountId = qualificationPackageBo.getAccountId();
            AccountBo accountBo = validateCtx.getQualificationAccountMap().get(accountId);
            PandoraAssert.notNull(accountBo, "资质包" + qualificationPackageId + "对应的账户" + accountId + "不存在",
                    ErrorCodeEnum.DomainType.ACCOUNT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);
            AccountBo creativeAccountBo = validateCtx.getAccountBo();
            PandoraAssert.isTrue(validateCtx.isNeedSkipAccountAuthorize()
                            || Objects.equals(creativeAccountBo.getProductId(), accountBo.getProductId())
                            && Objects.equals(creativeAccountBo.getGroupId(), accountBo.getGroupId())
                            && Objects.equals(creativeAccountBo.getDependencyAgentId(), accountBo.getDependencyAgentId()), "资质包仅支持同产品同代理",
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
            PandoraAssert.isTrue(Objects.equals(accountBo.getAdStatus(), 0)
                            || Objects.equals(accountBo.getIsSupportFly(), 1)
                            || Objects.equals(accountBo.getIsSupportContent(), 1), "资质包仅支持三连推广账户",
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
        }

    }

    private void validateScene(SaveUnitCreativeContextBo ctx) {
        if (ctx.getAccountLabelIds().contains(AccountLabelIds.SCENE_NOT_MERGED_LABEL_ID) || ctx.isPersonalUp()) return;

        final var selectedScenes = ctx.getNewVersion().getUnit().getLauUnitSceneBo().getSelectedScenes();
        PandoraAssert.isTrue(CollectionUtils.isEmpty(selectedScenes.getList()) || (Objects.equals(selectedScenes.getList().size(), 1) && selectedScenes.getList().contains(Scene.PC_BANNER)), "创意不支持指定场景",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
    }

    private void validateArchive(SaveUnitCreativeContextBo ctx) {
        List<CreativeBo> creatives = ctx.getNewVersion().getCreatives();

        List<LauCreativeArchiveBo> archiveBos = creatives.stream().map(CreativeBo::getLauCreativeArchiveBos)
                .filter(archiveBo -> !CollectionUtils.isEmpty(archiveBo))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(archiveBos)) {
            return;
        }

        Set<Integer> accountLabelIds = ctx.getAccountLabelIds();
        validateArchiveBase(ctx, archiveBos);

        boolean hasDirectCallUpLabel = ctx.getAccountLabelIds().contains(SUPPORT_PREFER_DIRECT_CALL_UP_LABEL_ID);
        Integer ppt = ctx.getCampaignBo().getPromotionPurposeType();
        Integer ppc = ctx.getLauUnitBo().getPromotionPurposeType();
        Integer cpaTarget = ctx.getLauUnitBo().getOcpcTarget().getValue();
        Map<Long, CreativeContentGoodsBo> creativeContentGoodsBoMap = ctx.getCreativeContentGoodsBoMap();
        Integer isBiliNative = ctx.getLauUnitExtraBo().getIsBiliNative();
        LauUnitSceneBo lauUnitSceneBo = ctx.getNewVersion().getUnit().getLauUnitSceneBo();
        Map<Long, BiliArchiveBo> biliArchiveMap = ctx.getBiliArchiveMap();
        validateArchiveGoods(hasDirectCallUpLabel, ppt, ppc, cpaTarget, creatives, accountLabelIds, creativeContentGoodsBoMap);
        validatePugvArchive(biliArchiveMap, ppc, isBiliNative, lauUnitSceneBo);
    }

    private void validateArchiveBase(SaveUnitCreativeContextBo ctx, List<LauCreativeArchiveBo> archiveBos) {

        Integer accountId = ctx.getAccountId();

        List<Long> avids = archiveBos.stream().map(LauCreativeArchiveBo::getAvid)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(avids)) {
            return;
        }

        List<BiliArchiveBo> totalBiliArchiveBos = new ArrayList<>(ctx.getBiliArchiveMap().values());
        Set<Long> validAvidSet = totalBiliArchiveBos.stream()
                .map(BiliArchiveBo::getAvid)
                .collect(Collectors.toSet());

        // 查不到avid 报错
        List<Long> invalidAvids = avids.stream()
                .filter(avid -> !validAvidSet.contains(avid))
                .collect(Collectors.toList());
        PandoraAssert.isTrue(CollectionUtils.isEmpty(invalidAvids),
                "创意保存失败,部分稿件不存在,avid=" + JSON.toJSONString(invalidAvids),
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_NOT_FOUND);

        // state < 0 报错 状态异常 报错
        Set<Long> invalidStateAvids = totalBiliArchiveBos.stream()
                .filter(archiveBo -> archiveBo.getState() < 0)
                .map(BiliArchiveBo::getAvid)
                .collect(Collectors.toSet());
        PandoraAssert.isTrue(CollectionUtils.isEmpty(invalidStateAvids),
                "创意保存失败,部分稿件状态异常,avid=" + JSON.toJSONString(invalidStateAvids),
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_STATUS_INVALID);

        boolean isOgv = Objects.equals(PromotionContentType.PPC_OGV_PROMOTION_VALUE, ctx.getLauUnitBo().getPromotionPurposeType());
        if (isOgv) {
            // ogv下都要是pgc稿件
            List<Long> notPgcAvids = totalBiliArchiveBos.stream()
                    .filter(archiveBo -> !archiveBo.getIsPgc())
                    .map(BiliArchiveBo::getAvid)
                    .collect(Collectors.toList());
            PandoraAssert.isTrue(CollectionUtils.isEmpty(notPgcAvids),
                    "当前单元推广内容为ogv,不允许投放非ogv稿件,非ogv稿件id:" + JSON.toJSONString(notPgcAvids),
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        } else {
            // 非ogv下不允许pgc稿件
            List<Long> pgcAvids = totalBiliArchiveBos.stream()
                    .filter(archiveBo -> archiveBo.getIsPgc())
                    .map(BiliArchiveBo::getAvid)
                    .collect(Collectors.toList());
            PandoraAssert.isTrue(CollectionUtils.isEmpty(pgcAvids),
                    "当前单元推广内容非ogv,不允许投放ogv稿件,ogv稿件id:" + JSON.toJSONString(pgcAvids),
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        }

        if (ctx.isNeedSkipAccountAuthorize()) {
            return;
        }
        // 小号稿件信息检验
        // 由于授权重构 其他类型待跟进
        List<Long> cmVideoAvids = cpmScvProxy.getCmVideoAvids(accountId, avids);
        Set<Long> cmVideoAvidSet = new HashSet<>(cmVideoAvids);

        archiveBos.forEach(archiveBo -> {
            Long avid = archiveBo.getAvid();
            if (Objects.equals(archiveBo.getSource(), CreativeArchiveSource.ARCHIVE_SOURCE_CM_VIDEO_VALUE)) {
                PandoraAssert.isTrue(cmVideoAvidSet.contains(avid),
                        "稿件类型错误,avid=" + avid + "不是本地视频或不属于当前账户",
                        ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PERMISSION_DENIED);
            } else {
                PandoraAssert.isTrue(!cmVideoAvidSet.contains(avid), "稿件类型错误, avid=" + avid + "是当前账户本地视频",
                        ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            }
        });
    }

    private void validateArchiveGoods(boolean hasDirectCallUpLabel,
                                      Integer ppt,
                                      Integer ppc,
                                      Integer cpaTarget,
                                      List<CreativeBo> creativeBos,
                                      Set<Integer> accountLabelIds,
                                      Map<Long, CreativeContentGoodsBo> goodsContentMap
    ) {

        // 交易经营-带货内容取消带货稿件校验
        // tapd https://www.tapd.cn/********/prong/stories/view/11********004526089
        if (Objects.equals(ppt, PromotionPurposeType.PPT_TRADE_VALUE)
                && Objects.equals(ppc, PromotionContentType.PPC_GOODS_CONTENT_VALUE)
                && Objects.equals(cpaTarget, CpaTarget.CPA_LP_CALLUP_SUCC_VALUE)
        ) {
            return;
        }

        // 此白名单不校验带货cid稿件
        boolean goodsAccountArchiveNotCid = accountLabelIds.contains(AccountLabelIds.GOODS_ACCOUNT_ARCHIVE_NOT_CID);
        List<CreativeBo> needValidateCreativeBos = creativeBos
                .stream()
                .filter(creative -> needValidateGoodsContent(ppt, ppc, cpaTarget, creative, goodsAccountArchiveNotCid) || needValidateYellowCarType(creative))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needValidateCreativeBos)) {
            return;
        }

        List<Long> avids = needValidateCreativeBos
                .stream()
                .map(CreativeBo::getLauCreativeArchiveBos)
                .filter(archiveBo -> !CollectionUtils.isEmpty(archiveBo))
                .flatMap(Collection::stream)
                .map(LauCreativeArchiveBo::getAvid)
                .distinct()
                .collect(Collectors.toList());
        //经营号支持story评论前置组件 https://www.tapd.cn/********/prong/stories/view/11********004352864

        List<CommentComponent> components = cpmScvProxy.componentsTop(avids, 1, avids.size());
        Map<Long, Integer> aidBizCodeMap = components
                .stream()
                .collect(Collectors.toMap(CommentComponent::getAid, CommentComponent::getBizCode));
        needValidateCreativeBos.forEach(creative -> {
            List<LauCreativeArchiveBo> creativeArchiveBos = creative.getLauCreativeArchiveBos();
            Long avid = CollectionUtils.isEmpty(creativeArchiveBos) ? null : creativeArchiveBos.get(0).getAvid();
            CreativeContentGoodsBo goodsContentBo = goodsContentMap.get(avid);

            validateGoodsContent(ppt, ppc, cpaTarget, creative, avid, goodsContentBo, goodsAccountArchiveNotCid);
            boolean isBusinessToolCommentComponent = Objects.equals(BUSINESS_TOOL_VALUE, aidBizCodeMap.get(avid));
            validateYellowCarType(creative, hasDirectCallUpLabel, avid, goodsContentBo, isBusinessToolCommentComponent);
        });
    }

    private boolean needValidateGoodsContent(
            Integer ppt, Integer ppc, Integer cpaTarget, CreativeBo creativeBo, boolean goodsAccountArchiveNotCid) {
        return Objects.equals(ppt, PromotionPurposeType.PPT_TRADE_VALUE)
                && Objects.equals(ppc, PromotionContentType.PPC_GOODS_CONTENT_VALUE)
                && (Objects.equals(cpaTarget, CpaTarget.CPA_LP_CALLUP_SUCC_VALUE)
                || (Objects.equals(cpaTarget, CpaTarget.CPA_ORDER_PLACE_VALUE) && !goodsAccountArchiveNotCid))
                && Objects.equals(creativeBo.getLauUnitCreativeBo().getTemplateGroupId(), TemplateGroup.BILI_ARCHIVE)
                && !CollectionUtils.isEmpty(creativeBo.getLauCreativeArchiveBos());
    }

    private void validateGoodsContent(Integer ppt,
                                      Integer ppc,
                                      Integer cpaTarget,
                                      CreativeBo creativeBo,
                                      Long avid,
                                      CreativeContentGoodsBo goodsContentBo,
                                      boolean goodsAccountArchiveNotCid
    ) {
        if (!needValidateGoodsContent(ppt, ppc, cpaTarget, creativeBo, goodsAccountArchiveNotCid)) {
            return;
        }

        if (Objects.equals(CpaTarget.CPA_LP_CALLUP_SUCC_VALUE, cpaTarget)) {
            PandoraAssert.isTrue(Objects.nonNull(goodsContentBo) && goodsContentBo.isGoodsContent(),
                    "当前单元配置要求带货稿件, avid=" + avid,
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        } else if (Objects.equals(CpaTarget.CPA_ORDER_PLACE_VALUE, cpaTarget)) {
            //此白名单不校验带货cid稿件
            if (!goodsAccountArchiveNotCid) {
                PandoraAssert.isTrue(Objects.nonNull(goodsContentBo) && goodsContentBo.getIsCidGoods(),
                        "当前单元配置要求带货cid稿件, avid=" + avid,
                        ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            }
        }
    }

    private boolean needValidateYellowCarType(CreativeBo creativeBo) {
        LauCreativeFlyExtInfoBo lauCreativeFlyExtInfoBo = creativeBo.getLauCreativeFlyExtInfoBo();
        if (Objects.isNull(lauCreativeFlyExtInfoBo)) {
            return false;
        }

        Integer yellowCarType = lauCreativeFlyExtInfoBo.getIsYellowCar();

        return !Objects.equals(yellowCarType, GoodsStyle.GOODS_STYLE_NOT_SUPPORT_VALUE)
                && !Objects.equals(yellowCarType, GoodsStyle.GOODS_STYLE_LIVE_SHOPPING_CART_VALUE);
    }

    private void validateYellowCarType(CreativeBo creativeBo,
                                       boolean hasDirectCallUpLabel,
                                       Long avid,
                                       CreativeContentGoodsBo goodsContentBo,
                                       boolean isBusinessToolCommentComponent) {
        if (!needValidateYellowCarType(creativeBo)) {
            return;
        }

        PandoraAssert.notNull(avid, "当前创意未绑定稿件,不允许绑定带货组件",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        LauCreativeFlyExtInfoBo lauCreativeFlyExtInfoBo = creativeBo.getLauCreativeFlyExtInfoBo();
        if (Objects.isNull(lauCreativeFlyExtInfoBo)) {
            return;
        }
        Integer yellowCarType = lauCreativeFlyExtInfoBo.getIsYellowCar();

        if (Objects.equals(yellowCarType, GoodsStyle.GOODS_STYLE_LIVE_SHOPPING_CART_VALUE)) {
            return;
        }

        if (Objects.equals(yellowCarType, GoodsStyle.GOODS_STYLE_SHOPPING_CART_VALUE)) {
            PandoraAssert.isTrue(Objects.nonNull(goodsContentBo)
                            && goodsContentBo.isSupportYellowCarShoppingCart(),
                    "当前稿件不支持购物车创意组件, avid=" + avid,
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            return;
        }

        if (Objects.equals(yellowCarType, GoodsStyle.GOODS_STYLE_COMMENT_AHEAD_VALUE)) {
            var supportGoodsStyleCommentAhead = (Objects.nonNull(goodsContentBo) && goodsContentBo.isGoodsContent()) || isBusinessToolCommentComponent;
            PandoraAssert.isTrue(supportGoodsStyleCommentAhead, "当前稿件非带货稿件,不支持评论前置创意组件, avid=" + avid,
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            return;
        }

        if (Objects.equals(yellowCarType, GoodsStyle.GOODS_STYLE_DIRECT_CALL_UP_VALUE)) {
            PandoraAssert.isTrue(hasDirectCallUpLabel && Objects.nonNull(goodsContentBo)
                            && goodsContentBo.isGoodsContent(),
                    "当前稿件非带货稿件或没有一跳唤起账号标签,不支持一跳唤起创意组件, avid=" + avid,
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            return;
        }

        throw new PandoraDataErr("未知的带货创意组件类型:" + yellowCarType);
    }

    private void validateDynamic(SaveUnitCreativeContextBo ctx) {
        List<LauCreativeFlyDynamicInfoBo> dynamicInfoBoList = ctx.getNewVersion().getCreatives().stream()
                .filter(creative -> TemplateGroup.isDynamic(
                        creative.getLauUnitCreativeBo().getTemplateGroupId()))
                .map(CreativeBo::getLauCreativeFlyDynamicInfoBo)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dynamicInfoBoList)) {
            return;
        }
        //此白名单不校验带货cid动态
        boolean goodsAccountArchiveNotCid = ctx.getAccountLabelIds().contains(AccountLabelIds.GOODS_ACCOUNT_ARCHIVE_NOT_CID);
        // 只校验订单提交
        if (goodsAccountArchiveNotCid || !Objects.equals(ctx.getLauUnitBo().getOcpcTarget().getValue(), CpaTarget.CPA_ORDER_PLACE_VALUE)) {
            return;
        }

        List<Long> dynamicIds = dynamicInfoBoList.stream()
                .map(LauCreativeFlyDynamicInfoBo::getDynamicId)
                .distinct()
                .collect(Collectors.toList());

        PandoraAssert.isTrue(dynamicIds.size() <= 100, "单次最大处理100个动态",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        QueryCreativeGoodsContentBo queryBo = new QueryCreativeGoodsContentBo();
        queryBo.setDynamicIdList(dynamicIds);
        List<CreativeContentGoodsBo> goodsContentBos =
                resCreativeGoodsContentService.getCidGoodsContent(queryBo);
        Map<Long, CreativeContentGoodsBo> goodsContentMap = goodsContentBos.stream()
                .collect(Collectors.toMap(CreativeContentGoodsBo::getContentId, Function.identity()));
        dynamicIds.forEach(dynamicId -> {
            CreativeContentGoodsBo goodsContentBo = goodsContentMap.get(dynamicId);
            PandoraAssert.isTrue(Objects.nonNull(goodsContentBo) && goodsContentBo.getIsCidGoods(),
                    "订单提交优化目标要求带货cid动态, 动态id=" + dynamicId + "不满足要求",
                    ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        });
    }

    private void validatePugvArchive(
            Map<Long, BiliArchiveBo> biliArchiveMap,
            Integer unitPpt,
            Integer isBiliNative,
            LauUnitSceneBo lauUnitSceneBo
    ) {
        log.info("validatePugvArchive, biliArchive ={}, unitPpt={}, isBiliNative={}, lauUnitSceneBo={}", biliArchiveMap.keySet(), unitPpt, isBiliNative, lauUnitSceneBo.getSelectedScenes());
        //biliArchiveMap中筛出isPugv是true的，返回true
        boolean isPugvAd = biliArchiveMap.values().stream()
                .anyMatch(BiliArchiveBo::getIsPugv);

        Integer channelId = lauUnitSceneBo.getChannelId();
        List<Integer> sceneIds = lauUnitSceneBo.getSelectedScenes().getList();
        if (!isPugvAd) {
            return;
        }
        if (!Objects.equals(PromotionContentType.PPC_CLUE_VALUE, unitPpt) ||
                Objects.equals(isBiliNative, 0) ||
                Objects.equals(Channel.PC, channelId)
        ) {
            throw new PandoraDataErr("当前投放位置不支持课堂稿件");
        }

        if (CollectionUtils.isEmpty(sceneIds)) {
            return;
        }

        //sceneIds其中有一个不符合PUGV_SCENE_ID_LIST的就返回false
        if (sceneIds.stream().anyMatch(sceneId -> !CommonConstants.PUGV_SCENE_ID_LIST.contains(sceneId))) {
            throw new PandoraDataErr("当前投放位置不支持课堂稿件");
        }
    }
}
