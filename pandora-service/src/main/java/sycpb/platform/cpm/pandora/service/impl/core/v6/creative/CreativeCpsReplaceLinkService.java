package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import com.bapis.ad.pandora.core.CreativeComponentType;
import com.bapis.ad.pandora.resource.PromotionContentType;
import com.bapis.ad.pandora.resource.PromotionPurposeType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauCreativeLinkReplaceDao;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeLinkReplacePo;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraAssert;
import sycpb.platform.cpm.pandora.infra.exception.BusinessException;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.*;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitGameBo;
import sycpb.platform.cpm.pandora.service.api.resource.game.ResGameBo;
import sycpb.platform.cpm.pandora.service.constants.IsReplaceLinkConstant;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.AnchorInfoBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.ProgCreativeAuditContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.UrlParamService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.landing_page.LandingPageService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.unit.UnitGameService;
import sycpb.platform.cpm.pandora.service.impl.resource.game.ResGameService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeLinkReplace.LAU_CREATIVE_LINK_REPLACE;

/**
 * 创意替链 service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CreativeCpsReplaceLinkService {

    private final LauCreativeLinkReplaceDao lauCreativeLinkReplaceDao;
    private final UrlParamService urlParamService;
    private final ArchiveAnchorService archiveAnchorService;
    private final ArchiveTopCommentService archiveTopCommentService;
    private final UnitGameService unitGameService;
    private final ResGameService resGameService;

    @Resource(name = MySqlConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;

    public List<CreativeCpsReplaceLinkBo> fetchByUnitId(Integer unitId) {
        List<LauCreativeLinkReplacePo> creativeLinkReplacePos = lauCreativeLinkReplaceDao.fetchByUnitId(unitId);
        creativeLinkReplacePos = creativeLinkReplacePos.stream().filter(t -> !Objects.equals(IsReplaceLinkConstant.YES_DARK, t.getIsReplaceUrl())).collect(Collectors.toList());
        return UnitCreativeImplMapper.MAPPER.fromPos(creativeLinkReplacePos);
    }

    public CreativeCpsReplaceLinkBo fetchByCreativeId(Integer creativeId) {

        if (!NumberUtils.isPositive(creativeId)) {
            return null;
        }

        List<CreativeCpsReplaceLinkBo> creativeCpsReplaceLinkBos = UnitCreativeImplMapper.MAPPER.fromPos(lauCreativeLinkReplaceDao.fetchByCreativeId(creativeId));
        if (CollectionUtils.isEmpty(creativeCpsReplaceLinkBos)) {
            return null;
        }
        return creativeCpsReplaceLinkBos.get(0);
    }

    public List<CreativeCpsReplaceLinkBo> fetch(Collection<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.EMPTY_LIST;
        }

        return UnitCreativeImplMapper.MAPPER.fromPos(lauCreativeLinkReplaceDao.fetchByCreativeId(creativeIds.toArray(creativeIds.toArray(new Integer[0]))));
    }

    public void save(SaveUnitCreativeContextBo ctx) {
        var currentVersions = extract(ctx.getCurrentVersion());
        var newVersions = extract(ctx.getNewVersion());

        save(currentVersions, newVersions);
    }

    public void save(List<CreativeCpsReplaceLinkBo> currentVersions, List<CreativeCpsReplaceLinkBo> newVersions) {

        // 开启的才需要保存
        newVersions = newVersions.stream().filter(t -> NumberUtils.isPositive(t.getIsReplaceUrl())).collect(Collectors.toMap(t -> t.getCreativeId(), t -> t, (a, b) -> b)).values().stream().collect(Collectors.toList());

        JooqFunctions.save(currentVersions, newVersions, JooqFunctions.JooqSaveContext.minimum(CreativeCpsReplaceLinkBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeLinkReplaceDao)
                .setDSLContext(adCore)
                .setTableImpl(LAU_CREATIVE_LINK_REPLACE)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_LINK_REPLACE.ID)
                .setDatabasePrimaryKeyMapper(LauCreativeLinkReplacePo::getId));
    }

    public void save(ProgCreativeAuditContextBo ctx) {
        final var currentVersion = ctx.getCurVersion().getCreativeCpsReplaceLinkBo();
        final var newVersion = ctx.getNewVersion().getCreativeCpsReplaceLinkBo();

        final List<CreativeCpsReplaceLinkBo> currentVersions = currentVersion != null ? List.of(currentVersion) : Collections.emptyList();
        final List<CreativeCpsReplaceLinkBo> newVersions = newVersion != null ? List.of(newVersion) : Collections.emptyList();

        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(CreativeCpsReplaceLinkBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeLinkReplaceDao)
                .setDSLContext(adCore)
                .setTableImpl(LAU_CREATIVE_LINK_REPLACE)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_LINK_REPLACE.ID)
                .setDatabasePrimaryKeyMapper(LauCreativeLinkReplacePo::getId)
                .setOpLog(ctx.genCreativeCompareConsumer());
        JooqFunctions.save(currentVersions, newVersions, ctx0);
    }

    public void saveCpsReplaceUrlByCreativeId(Integer creativeId, List<CreativeCpsReplaceLinkBo> replaceLinkBos, SaveUnitCreativeContextBo ctx) {
        List<LauCreativeLinkReplacePo> creativeLinkReplacePos = lauCreativeLinkReplaceDao.fetchByCreativeId(creativeId);
        List<CreativeCpsReplaceLinkBo> creativeCpsReplaceLinkBos = UnitCreativeImplMapper.MAPPER.fromPos(creativeLinkReplacePos);
        JooqFunctions.save(creativeCpsReplaceLinkBos, replaceLinkBos, JooqFunctions.JooqSaveContext.minimum(CreativeCpsReplaceLinkBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeLinkReplaceDao)
                .setOpLog(ctx.genCreativeCompareConsumer())
        );
    }

    public List<CreativeCpsReplaceLinkBo> extract(UnitCreativeBo x) {
        return UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(x, CreativeBo::getCpsReplaceLinkInfo);
    }

    public void handle(SaveUnitCreativeContextBo ctx) {
        boolean isNative = NumberUtils.isPositive(ctx.getLauUnitExtraBo().getIsBiliNative());
        List<CreativeBo> creativeBos = ctx.getNewVersion().getCreatives();
        for (CreativeBo creativeBo : creativeBos) {


            CreativeCpsReplaceLinkBo cpsReplaceLinkInfo = creativeBo.getCpsReplaceLinkInfo();
            if (cpsReplaceLinkInfo == null) {
                continue;
            }
            if (!NumberUtils.isPositive(cpsReplaceLinkInfo.getIsReplaceUrl())) {
                continue;
            }

            Integer supportCpsReplaceLinkTag = 930;
            PandoraAssert.isTrue(ctx.getAccountLabelIds().contains(supportCpsReplaceLinkTag), "当前账户不支持替链",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);

            // https://www.tapd.cn/********/prong/stories/view/11********004548657
            // 前端校验逻辑 https://doc.weixin.qq.com/sheet/e3_AREAeQZjAMcZN57SSzwS2i1JRaPQ6?scode=ANYAEAdoABEwWAW1erAREAeQZjAMc&tab=s7qatt
            PandoraAssert.isTrue(isNative, "非原生投放不支持替链",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);

            Integer ppt = ctx.getCampaignBo().getPromotionPurposeType();
            Integer ppc = ctx.getLauUnitBo().getPromotionPurposeType();

            if (Objects.equals(ppc, PromotionContentType.PPC_LIVE_ROOM_VALUE) || Objects.equals(ppc, PromotionContentType.PPC_LIVE_RESERVE_VALUE)) {
                throw new BusinessException(ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL, "当前推广内容不支持替链");
            }

            Long avid = creativeBo.getLauUnitCreativeBo().getVideoId();
            PandoraAssert.isTrue(checkAvid(avid), "当前稿件不支持替链",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.OPERATE_ILLEGAL);

            checkReplaceLinkInfo(cpsReplaceLinkInfo, ppt, ppc);

            // 应用推广-安卓游戏中心，不需要用户传，根据游戏id去填充
            if (Objects.equals(ppt, PromotionPurposeType.PPT_APP_VALUE) && Objects.equals(ppc, PromotionContentType.PPC_GAME_CENTER_VALUE)) {
                // bilibili://game_center/detail?id=106674&sourceType=adPut&source=cpc___SOURCEID_____CREATIVEID_____ADTYPE_____REQUESTID__&sourceFrom=9788
                LauUnitGameBo unitGameBo = unitGameService.get(ctx.getLauUnitBo().getUnitId());
                ResGameBo gameBo = resGameService.getByGameBaseIdAndPlatform(unitGameBo.getGameBaseId(), unitGameBo.getPlatformType());
                cpsReplaceLinkInfo.setConversionUrl(gameBo.getGameLink());
            }

            if (StringUtils.isNotEmpty(cpsReplaceLinkInfo.getAppJumpUrl())) {
                //单元上设置双端app, 双端要求落地页链接非app store 的就行，保持不变。
                boolean doubleAppSet = NumberUtils.isPositive(ctx.getLauUnitExtraBo().getAndroidAppPackageId()) || NumberUtils.isPositive(ctx.getLauUnitExtraBo().getIosAppPackageId());
                if (doubleAppSet) {
                    PandoraAssert.isTrue(!LandingPageService.isAppStore(creativeBo.getCpsReplaceLinkInfo().getAppJumpUrl()), "落地页不支持双端下载",
                            ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
                }
            }

            // 宏替换
            final var urlParamContext = new UrlParamService.Context();
            urlParamContext.setCpsReplaceUrl(true);
            urlParamContext.setInner(ctx.getLandingPageContext().isInner());
            urlParamContext.setPromotionContentType(ctx.getLauUnitBo().getPromotionPurposeType());
            urlParamContext.setBusinessDomain(ctx.getLauUnitBo().getBusinessDomain());
            urlParamContext.setSubPkgChannelId(Optional.ofNullable(ctx.getLauUnitGameBo()).map(LauUnitGameBo::getSubPkg).orElse(null));
            urlParamContext.setSalesType(ctx.getLauUnitBo().getSalesType().getValue());
            urlParamContext.setProgrammatic(ctx.isProgrammatic());
            urlParamContext.setNativeLandingPage(creativeBo.isNativeLandingPage());
            urlParamContext.setHasUnderFrameComponent(!CollectionUtils.isEmpty(creativeBo.getLauCreativeComponentBos()) && creativeBo.getLauCreativeComponentBos()
                    .stream()
                    .anyMatch(componentBo -> componentBo.getComponentType().equals(CreativeComponentType.UNDERFRAME_VALUE)));
            urlParamContext.setSmartTitle(creativeBo.getLauCreativeTitleBos().stream().anyMatch(x -> !CollectionUtils.isEmpty(x.getLauCreativeSmartTitleBos())));
            urlParamContext.setPageGroup(Optional.ofNullable(creativeBo.getLauCreativeLandingPageGroupBo()).map(LauCreativeLandingPageGroupBo::getGroupId).map(NumberUtils::isPositive).orElse(false));

            String rawUrl = cpsReplaceLinkInfo.getConversionUrl();
            if (NumberUtils.isPositive(cpsReplaceLinkInfo.getConversionUrlPageId())) {
                urlParamContext.setMgkPageId(true);
                final var mgkLandingPageBo = ctx.getLandingPageContext().getLandingPageBoMap().get(cpsReplaceLinkInfo.getConversionUrlPageId());
                rawUrl = mgkLandingPageBo.getPromotionPurposeContentSecondary();
            }
            final var landingPageUrl = urlParamService.applyUrlParamsForReplaceUrl(rawUrl, urlParamContext);
            cpsReplaceLinkInfo.setConversionUrl(landingPageUrl);
        }
    }

    private boolean checkAvid(Long avid) {
        List<ArchiveTopCommentService.ArchiveTopComment> archiveTopComments = archiveTopCommentService.getTopCommentByPriority(avid);
        AnchorInfoBo anchorInfoBo  = archiveAnchorService.queryBizAnchorByPriority(avid);
        if (CollectionUtils.isEmpty(archiveTopComments) && Objects.isNull(anchorInfoBo)) {
            return false;
        }
        return true;
    }

    private void checkReplaceLinkInfo(CreativeCpsReplaceLinkBo replaceLinkInfo, Integer ppt, Integer ppc) {
        if (Objects.equals(ppt, PromotionPurposeType.PPT_APP_VALUE) && Objects.equals(ppc, PromotionContentType.PPC_GAME_CENTER_VALUE)) {
            return;
        }
        // 应用推广-应用包下载
        if (Objects.equals(ppt, PromotionPurposeType.PPT_APP_VALUE) && Objects.equals(ppc, PromotionContentType.PPC_APP_DOWNLOAD_VALUE)) {
            checkConversionUrl(replaceLinkInfo);
        }
        // 应用推广-应用唤起
        else if (Objects.equals(ppt, PromotionPurposeType.PPT_APP_VALUE) && Objects.equals(ppc, PromotionContentType.PPC_APP_CALL_UP_VALUE)) {
            PandoraAssert.isTrue(StringUtils.isNotEmpty(replaceLinkInfo.getAppJumpUrl()), "唤端链接不可为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            checkConversionUrl(replaceLinkInfo);
        }
        // 交易经营-带货内容/电商链接
        else if (Objects.equals(ppt, PromotionPurposeType.PPT_TRADE_VALUE)
                && (Objects.equals(ppc, PromotionContentType.PPC_GOODS_CONTENT_VALUE) || Objects.equals(ppc, PromotionContentType.PPC_GOODS_URL_VALUE))) {
            checkConversionUrl(replaceLinkInfo);
        } else {
            checkConversionUrl(replaceLinkInfo);
            PandoraAssert.isTrue(StringUtils.isEmpty(replaceLinkInfo.getAppJumpUrl()), "不可填唤端链接",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        }
        return;
    }

    // conversionUrlType： 1-三方落地页 5-高能建站落地页
    private void checkConversionUrl(CreativeCpsReplaceLinkBo replaceLinkInfo) {
        if (replaceLinkInfo.getConversionUrlType() == 1) {
            PandoraAssert.isTrue(StringUtils.isNotEmpty(replaceLinkInfo.getConversionUrl()), "三方落地页不可为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            PandoraAssert.isTrue(StringUtils.startsWith(replaceLinkInfo.getConversionUrl(), "https://"), "三方落地页链接必须以https开头",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        } else if (replaceLinkInfo.getConversionUrlType() == 5) { // 高能建站落地页
            PandoraAssert.isTrue(NumberUtils.isPositive(replaceLinkInfo.getConversionUrlPageId()), "高能建站落地页pageId不能为空",
                    ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        }
    }
}
