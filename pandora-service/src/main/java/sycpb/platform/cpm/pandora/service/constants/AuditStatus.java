package sycpb.platform.cpm.pandora.service.constants;

import java.util.Objects;

public class AuditStatus {
    public static final int AUDITING = 1;
    public static final int PASSED = 2;
    public static final int REJECTED = 3;
    public static final int LANDING_PAGE_AUDITING = 4;

    public static final int UNKNOWN = 99;

    public static boolean isRejected(Integer v) {
        return Objects.equals(v, REJECTED);
    }

    public static boolean isPassed(Integer v) {
        return Objects.equals(v, PASSED);
    }

    public static boolean isWaiting(Integer v) {
        return Objects.equals(v, AUDITING);
    }

    public static final String AUDITING_DESC = "审核中";

    public static final String PASSED_DESC = "审核通过";

    public static final String REJECTED_DESC = "审核拒绝";

    public static final String UNKNOWN_DESC = "未知";

    public static final String LANDING_PAGE_AUDITING_DESC = "落地页待审核";

    public static String auditingStatusMapper(Integer auditStatus) {
        switch (auditStatus) {
            case AUDITING:
                return AUDITING_DESC;
            case PASSED:
                return PASSED_DESC;
            case REJECTED:
                return REJECTED_DESC;
            default:
                return UNKNOWN_DESC;
        }

    }

    public static Integer fromBizStatus(int bizStatus) {

        if (Objects.equals(ProgrammaticElementAuditStatus.AUDITING, bizStatus)) {
            return AUDITING;
        }

        if (Objects.equals(ProgrammaticElementAuditStatus.AUDIT_REJECTED, bizStatus)) {
            return REJECTED;
        }

        if (Objects.equals(ProgrammaticElementAuditStatus.PASSED, bizStatus)) {
            return PASSED;
        }

        if (Objects.equals(ProgrammaticElementAuditStatus.LANDING_PAGE_AUDITING, bizStatus)) {
            return LANDING_PAGE_AUDITING;
        }

        return AUDITING;
    }
}
