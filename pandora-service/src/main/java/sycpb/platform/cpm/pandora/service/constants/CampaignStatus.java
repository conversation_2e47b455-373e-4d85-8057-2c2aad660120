package sycpb.platform.cpm.pandora.service.constants;

import org.springframework.util.Assert;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;

public class CampaignStatus {
    public static final int VALID = 1;
    public static final int PAUSED = 2;
    public static final int DELETED = 4;

    public static int toCampaignStatus(Integer status) {
        Assert.isTrue(NumberUtils.isPositive(status), "状态不能为空");
        switch (status) {
            case LaunchStatus.VALID:
                return VALID;
            case LaunchStatus.PAUSED:
                return PAUSED;
            case LaunchStatus.DELETED:
                return DELETED;
            default:
                throw new IllegalArgumentException("状态无法识别");
        }
    }
}
