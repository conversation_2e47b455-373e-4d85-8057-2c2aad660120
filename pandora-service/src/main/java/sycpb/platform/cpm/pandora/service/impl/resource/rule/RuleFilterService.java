package sycpb.platform.cpm.pandora.service.impl.resource.rule;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.SlotGroupTemplateFlatMappingBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.IRuleService;
import sycpb.platform.cpm.pandora.service.api.resource.rule.RuleBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.StdRuleBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.*;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.*;
import sycpb.platform.cpm.pandora.service.api.resource.unit.bos.UnitLimitInfoBo;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Service
@RequiredArgsConstructor
public class RuleFilterService {
    private final IRuleService ruleService;

    public List<PromotionPurposeTypeBo> getPromotionPurposeType(StdParamBo paramBo) {
        final var pptRules = ruleService.getPromotionPurposeTypeRules();
        return filterWithoutSortStd(pptRules, paramBo);
    }

    public CampaignContentBo getCampaignContent(StdParamBo paramBo) {
        final var rules = ruleService.getCampaignContentRules();
        final var bos = filterAndSortStd(rules, paramBo);
        if (CollectionUtils.isEmpty(bos)) return null;

        return bos.get(0);
    }

    public List<PromotionContentTypeBo> getPpc(StdParamBo paramBo) {
        final var ppcRules = ruleService.getPromotionContentTypeRules();
        return filterWithoutSortStd(ppcRules, paramBo);
    }

    public List<CpaTargetBo> getCpaTarget(StdParamBo paramBo) {
        final var cpaTargetRules = ruleService.getCpaTargetRules();
        return filterWithoutSortStd(cpaTargetRules, paramBo);
    }

    public Map<Integer, List<CpaTargetBo>> getDeepCpaTargetMap(StdParamBo paramBo) {
        final Map<Integer, List<CpaTargetBo>> map = new HashMap<>();
        for (var ruleBo : partialFilterStd(ruleService.getDeepCpaTargetRules(), paramBo)) {
            for (var cpaTargetId : ruleBo.getInput().getCpaTargetIds()) {
                map.putIfAbsent(cpaTargetId, new ArrayList<>());
                map.get(cpaTargetId).add(ruleBo.getOutput());
            }
        }
        return map;
    }

    public Map<Integer, List<CpaTargetBo>> getAssistCpaTargetMap(StdParamBo paramBo) {
        final Map<Integer, List<CpaTargetBo>> map = new HashMap<>();
        for (var ruleBo : partialFilterStd(ruleService.getAssistCpaTargetRules(), paramBo)) {
            for (var cpaTargetId : ruleBo.getInput().getCpaTargetIds()) {
                map.putIfAbsent(cpaTargetId, new ArrayList<>());
                map.get(cpaTargetId).add(ruleBo.getOutput());
            }
        }
        return map;
    }

    public Map<Integer, Map<Integer, Integer>> getNoBidMap(StdParamBo paramBo) {
        final Map<Integer, Map<Integer, Integer>> map = new HashMap<>();
        final var noBidRules = ruleService.getCpaTargetExtRules()
                .stream()
                .filter(x -> NumberUtils.isPositive(x.getOutput().getSupportNoBid()))
                .collect(Collectors.toList());
        for (var ruleBo : partialFilterStd(noBidRules, paramBo)) {
            for (var cpaTargetId : ruleBo.getInput().getCpaTargetIds()) {
               map.putIfAbsent(cpaTargetId, new HashMap<>());
               for (var deepCpaTargetId : ruleBo.getInput().getDeepCpaTargetIds()) {
                   if (NumberUtils.isPositive(deepCpaTargetId)) {
                       map.get(cpaTargetId).put(deepCpaTargetId, translateSupport(ruleBo.getOutput().getSupportTwoStageOptimization()));
                   }               }
            }
        }
        return map;
    }

    public Map<Integer, Map<Integer, Integer>> getTwoStageOptimizationMap(StdParamBo paramBo) {
        final Map<Integer, Map<Integer, Integer>> map = new HashMap<>();
        final var twoStageOptimizationRules = ruleService.getCpaTargetExtRules()
                .stream()
                .filter(x -> NumberUtils.isPositive(x.getOutput().getSupportTwoStageOptimization()))
                .collect(Collectors.toList());
        for (var ruleBo : partialFilterStd(twoStageOptimizationRules, paramBo)) {
            for (var cpaTargetId : ruleBo.getInput().getCpaTargetIds()) {
                map.putIfAbsent(cpaTargetId, new HashMap<>());
                for (var deepCpaTargetId : ruleBo.getInput().getDeepCpaTargetIds()) {
                    if (NumberUtils.isPositive(deepCpaTargetId)) {
                        map.get(cpaTargetId).put(deepCpaTargetId, translateSupport(ruleBo.getOutput().getSupportTwoStageOptimization()));
                    }
                }
            }
        }
        return map;
    }

    public UnitContentBo getUnitContent(StdParamBo paramBo) {
        final var rules = ruleService.getUnitContentRules();
        final var bos = filterAndSortStd(rules, paramBo);
        if (CollectionUtils.isEmpty(bos)) return null;

        return bos.get(0);
    }

    public List<SlotGroupTemplateFlatMappingBo> getSlotGroupTemplateMapping(StdParamBo paramBo) {
        final var slotGroupTemplateMappingRules = ruleService.getSlotGroupTemplateMappingRules();
        return filterAndSortStd(slotGroupTemplateMappingRules, paramBo)
                .stream()
                .flatMap(y -> y.getTemplateIds()
                        .stream()
                        .map(z -> {
                            final var bo = new SlotGroupTemplateFlatMappingBo();
                            bo.setSlotGroupId(y.getSlotGroupId());
                            bo.setTemplateId(z);
                            return bo;
                        })).collect(Collectors.toList());
    }

    public NativeBo getSupportBiliNative(NativeParamBo paramBo) {
        final var rules = ruleService.getSupportBiliNativeRules();
        if (CollectionUtils.isEmpty(rules)) return null;

        final var list = new ArrayList<RuleBo<NativeParamBo, NativeBo>>();
        for (RuleBo<NativeParamBo, NativeBo> rule : rules) {
            if (compatible(rule.getInput(), paramBo)) {
                list.add(rule);
            }
        }
        return list.stream()
                .sorted((y, z) -> Integer.compare(z.getScore(), y.getScore()))
                .map(RuleBo::getOutput)
                .findFirst()
                .orElse(null);
    }

    public UnitBidBo getUnitBid(StdParamBo paramBo) {
        final var rules = ruleService.getUnitBidRules();
        final var bos = filterAndSortStd(rules, paramBo);
        if (CollectionUtils.isEmpty(bos)) return null;

        return bos.get(0);
    }

    public List<BusMarkBo> getBusMarks(BusMarkParamBo paramBo) {
        final var rules = ruleService.getBusMarkRules();
        if (CollectionUtils.isEmpty(rules)) return List.of();

        final var list = new ArrayList<RuleBo<BusMarkParamBo, BusMarkBo>>();
        for (RuleBo<BusMarkParamBo, BusMarkBo> rule : rules) {
            if (compatible(rule.getInput(), paramBo)) {
                list.add(rule);
            }
        }
        return list.stream()
                .sorted((y, z) -> Integer.compare(z.getScore(), y.getScore()))
                .map(RuleBo::getOutput)
                .collect(Collectors.toList());
    }

    public CreativeContentBo getCreativeContent(CreativeContentParamBo paramBo) {
        final var rules = ruleService.getCreativeContentRules();
        if (CollectionUtils.isEmpty(rules)) return null;

        final var list = new ArrayList<RuleBo<CreativeContentParamBo, CreativeContentBo>>();
        for (RuleBo<CreativeContentParamBo, CreativeContentBo> rule : rules) {
            if (compatible(rule.getInput(), paramBo)) {
                list.add(rule);
            }
        }
        final var sortedList = list.stream()
                .sorted((y, z) -> Integer.compare(z.getScore(), y.getScore()))
                .collect(Collectors.toList());
        log.info("param = {}, sorted list = {}", paramBo, sortedList);
        if (sortedList.isEmpty()) return null;
        return sortedList.get(0).getOutput();
    }

    public UnitLimitInfoBo getUnitLimitInfoByLabel(StdParamBo paramBo) {
        final var rules = ruleService.getUnitLimitByLabelRules();
        final var list = new ArrayList<StdRuleBo<UnitLimitInfoBo>>();
        for (StdRuleBo<UnitLimitInfoBo> rule : rules) {
            if (compatible(rule.getInput(), paramBo)) {
                list.add(rule);
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0).getOutput();
    }

    public UnitLimitInfoBo getUnitLimitInfoByCost(UnitLimitByCostParamBo paramBo) {
        final var rules = ruleService.getUnitLimitByCostRules();
        final var list = new ArrayList<RuleBo<UnitLimitByCostParamBo, UnitLimitInfoBo>>();
        for (RuleBo<UnitLimitByCostParamBo, UnitLimitInfoBo> rule : rules) {
            if (compatible(rule.getInput(), paramBo)) {
                list.add(rule);
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0).getOutput();
    }

    private boolean compatible(BusMarkParamBo requirement, BusMarkParamBo actual) {
        if (collectionInCompatible(requirement.getCardTypeIds(), actual.getCardTypeIds())) return false;

        if (collectionInCompatible(requirement.getAccountLabelIds(), actual.getAccountLabelIds())) return false;

        return true;
    }

    private boolean compatible(UnitLimitByCostParamBo requirement, UnitLimitByCostParamBo actual) {
        Double actualCost = actual.getAccountActualCost();
        Double accountMaxCost = requirement.getAccountMaxCost();
        Double accountMinCost = requirement.getAccountMinCost();
        return actualCost >= accountMinCost && actualCost < accountMaxCost;
    }

    private <T> List<T> filterWithoutSortStd(List<StdRuleBo<T>> rules, StdParamBo paramBo) {
        List<StdRuleBo<T>> list = filterStd(rules, paramBo);
        return list.stream()
                .map(StdRuleBo::getOutput)
                .collect(Collectors.toList());
    }

    private <T> List<T> filterAndSortStd(List<StdRuleBo<T>> rules, StdParamBo paramBo) {
        List<StdRuleBo<T>> list = filterStd(rules, paramBo);

        return list.stream()
                .sorted((x, y) -> Integer.compare(y.getScore(), x.getScore()))
                .map(StdRuleBo::getOutput)
                .collect(Collectors.toList());
    }

    private <T> List<StdRuleBo<T>> partialFilterStd(List<StdRuleBo<T>> rules, StdParamBo paramBo) {
        if (CollectionUtils.isEmpty(rules)) return List.of();

        final var list = new ArrayList<StdRuleBo<T>>();
        for (StdRuleBo<T> rule : rules) {
            if (partialCompatible(rule.getInput(), paramBo)) {
                list.add(rule);
            }
        }

        return list;
    }

    private <T> List<StdRuleBo<T>> filterStd(List<StdRuleBo<T>> rules, StdParamBo paramBo) {
        if (CollectionUtils.isEmpty(rules)) return List.of();

        final var list = new ArrayList<StdRuleBo<T>>();
        for (StdRuleBo<T> rule : rules) {
            if (compatible(rule.getInput(), paramBo)) {
                list.add(rule);
            }
        }

        return list;
    }

    private boolean partialCompatible(StdParamBo requirement, StdParamBo actual) {
        if (collectionInCompatible(requirement.getPromotionPurposeTypes(), actual.getPromotionPurposeTypes())) return false;

        if (collectionInCompatible(requirement.getPromotionContentTypes(), actual.getPromotionContentTypes())) return false;

        if (collectionInCompatible(requirement.getBaseTargetIds(), actual.getBaseTargetIds())) return false;

        if (collectionInCompatible(requirement.getAccountLabelIds(), actual.getAccountLabelIds())) return false;

        return true;
    }

    private boolean compatible(StdParamBo requirement, StdParamBo actual) {
        if (collectionInCompatible(requirement.getPromotionPurposeTypes(), actual.getPromotionPurposeTypes())) return false;

        if (collectionInCompatible(requirement.getPromotionContentTypes(), actual.getPromotionContentTypes())) return false;

        if (collectionInCompatible(requirement.getBaseTargetIds(), actual.getBaseTargetIds())) return false;

        if (collectionInCompatible(requirement.getCpaTargetIds(), actual.getCpaTargetIds())) return false;

        if (collectionInCompatible(requirement.getDeepCpaTargetIds(), actual.getDeepCpaTargetIds())) return false;

        if (collectionInCompatible(requirement.getAccountLabelIds(), actual.getAccountLabelIds())) return false;

        return true;
    }

    private static <T> boolean collectionInCompatible(List<T> requirement, List<T> actual) {
        if (!CollectionUtils.isEmpty(requirement)) {
            if (CollectionUtils.isEmpty(actual)) return true;

            return !CollectionUtils.containsAny(new HashSet<>(requirement), actual);
        }
        return false;
    }

    private boolean compatible(CreativeContentParamBo requirement, CreativeContentParamBo actual) {
        if (!Objects.equals(requirement.getTemplateGroupId(), actual.getTemplateGroupId())) return false;

        if (collectionInCompatible(requirement.getLaunchModes(), actual.getLaunchModes())) return false;

        if (collectionInCompatible(requirement.getPromotionPurposeTypes(), actual.getPromotionPurposeTypes())) return false;

        if (collectionInCompatible(requirement.getPromotionContentTypes(), actual.getPromotionContentTypes())) return false;

        if (collectionInCompatible(requirement.getCpaTargets(), actual.getCpaTargets())) return false;

        if (collectionInCompatible(requirement.getSceneIds(), actual.getSceneIds())) return false;

        if (collectionInCompatible(requirement.getAccountLabelIds(), actual.getAccountLabelIds())) return false;

        return true;
    }

    private boolean compatible(NativeParamBo requirement, NativeParamBo actual) {
        if (collectionInCompatible(requirement.getPromotionPurposeTypes(), actual.getPromotionPurposeTypes())) return false;

        if (collectionInCompatible(requirement.getPromotionContentTypes(), actual.getPromotionContentTypes())) return false;

        if (collectionInCompatible(requirement.getCpaTargetIds(), actual.getCpaTargetIds())) return false;

        if (collectionInCompatible(requirement.getDeepCpaTargetIds(), actual.getDeepCpaTargetIds())) return false;

        if (collectionInCompatible(requirement.getAccountLabelIds(), actual.getAccountLabelIds())) return false;

        if (collectionInCompatible(requirement.getBaseTargetIds(), actual.getBaseTargetIds())) return false;

        return true;
    }

    private Integer translateSupport(Integer support) {
        return support - 1;
    }
}
