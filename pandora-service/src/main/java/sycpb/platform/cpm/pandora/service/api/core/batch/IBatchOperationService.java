package sycpb.platform.cpm.pandora.service.api.core.batch;

import org.redisson.api.RLock;
import sycpb.platform.cpm.pandora.infra.pagination.Pagination;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.*;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperatorBo;

import java.util.List;
import java.util.Set;

public interface IBatchOperationService {
    RegisterResultBo register(BatchOperationReqBo reqBo);
    void updateStatus(Integer accountId, Long operationId, Integer status);
    void retry(Long operationId);
    LauBatchOperationBo fetchNextOperation(GetBatchOperationReqBo reqBo);
    Pagination<LauBatchOperationBo> listBatchOperations(ListBatchOperationReqBo reqBo);
    List<LauBatchOperationDetailBo> listBatchOperationDetails(Long operationId);
    void handleJob(LauBatchOperationBo bo);
    List<Integer> getInvalidAccountIds();
    void addWorkingAccountId(Integer accountId);

    Set<Integer> getWorkingAccountId();

    void removeWorkingAccountId(Integer accountId);
    Set<Integer> getDisabledAccountIds();
    boolean isDisabledAccountId(Integer accountId);
    void addDisabledAccountId(Integer accountId);
    void removeDisabledAccountId(Integer accountId);
    RLock getOperationLock(Long operationId);

    // 内部使用
    void updateCreative(Integer creativeId, OperatorBo operator, Integer operationType, boolean fromUpperLevel);
}
