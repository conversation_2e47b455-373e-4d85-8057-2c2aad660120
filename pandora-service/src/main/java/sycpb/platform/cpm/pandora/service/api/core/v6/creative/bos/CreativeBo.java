package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.TemplateGroupDetailBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.LauCreativeLandingPageBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.LauCreativeTemplateBo;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreativeBo {
    private LauUnitCreativeBo lauUnitCreativeBo;
    private LauCreativeExtraBo lauCreativeExtraBo;
    private LauCreativeTabBo lauCreativeTabBo;

    // 创意动态
    private LauCreativeFlyDynamicInfoBo lauCreativeFlyDynamicInfoBo;
    private LauCreativeMiniGameMappingBo lauCreativeMiniGameMappingBo;
    private LauCreativeFlyExtInfoBo lauCreativeFlyExtInfoBo;
    private LauCreativeButtonCopyBo lauCreativeButtonCopyBo;
    private LauCreativeLandingPageBo lauCreativeLandingPageBo;
    private LauCreativeLandingPageGroupBo lauCreativeLandingPageGroupBo;
    private LauCreativePgcArchiveBo lauCreativePgcArchiveBo;
    private List<LauCreativeQualificationBo> lauCreativeQualificationBos;
    private List<LauCreativeTitleBo> lauCreativeTitleBos;
    private List<LauCreativeImageBo> lauCreativeImageBos;
    private List<LauCreativeArchiveBo> lauCreativeArchiveBos;
    private List<LauCreativeComponentBo> lauCreativeComponentBos;

    // 稿件锚点
    private ArchiveBizAnchorWrapBo archiveBizAnchorWrapBo;

    private List<Integer> qualificationIds;
    private Integer isGif;
    private List<LauProgrammaticCreativeDetailBo> lauProgrammaticCreativeDetailBos;
    private boolean isTemplateLandingPage;
    private boolean isNativeLandingPage;
    // 程序化额外元素是否改动
    private boolean isMiscElemChanged;
    // 程序化素材是否改动，影子的才有该字段
    protected boolean isProgMaterialChanged;
    private boolean triggerAudit;
    private boolean isMergedShadow;
    private boolean isPugvCreative;
    private TemplateGroupDetailBo templateGroupDetailBo;
    private List<LauCreativeTemplateBo> lauCreativeTemplateBos;

    //加个原生创意和原生稿件
    private LauNativeArchiveBo lauNativeArchiveBo;
    private LauCreativeNativeArchiveRelativityBo lauCreativeNativeArchiveRelativityBo;

    // cps替换链接
    private CreativeCpsReplaceLinkBo cpsReplaceLinkInfo;

    // 闪屏创意相关
    private LauSplashScreenCreativeBo lauSplashScreenCreativeBo;
    private List<LauSplashScreenCreativeButtonBo> lauSplashScreenCreativeButtonBos;
    private LauSplashScreenCreativeButtonSlideInfoBo lauSplashScreenCreativeButtonSlideInfoBo;
    private LauSplashScreenCreativeButtonTwistInfoBo lauSplashScreenCreativeButtonTwistInfoBo;
    private List<LauSplashScreenCreativeImageBo> lauSplashScreenCreativeImageBos;
}
