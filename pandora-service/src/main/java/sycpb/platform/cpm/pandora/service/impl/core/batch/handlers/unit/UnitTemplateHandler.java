package sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.unit;

import com.bapis.ad.pandora.core.batch.BatchOperationType;
import lombok.Getter;
import lombok.Setter;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.compare.bos.ChangeLogBo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCampaignPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitExtraPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitTargetRulePo;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.BatchOperationReqBo;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.IOperationLogService;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperationLogContextBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitSearchNegativeKeywordBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitTargetCrowdPackBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.SearchAdUnitKeyWordBo;
import sycpb.platform.cpm.pandora.service.api.resource.account.IAccountLabelService;
import sycpb.platform.cpm.pandora.service.api.resource.live_reserve.ResLiveReserveBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.UnitBidBo;
import sycpb.platform.cpm.pandora.service.common.InfoReportService;
import sycpb.platform.cpm.pandora.service.common.bo.InfoReportCompensationLogBo;
import sycpb.platform.cpm.pandora.service.constants.AdpVersion;
import sycpb.platform.cpm.pandora.service.constants.LaunchStatus;
import sycpb.platform.cpm.pandora.service.constants.OperatorTypeConstants;
import sycpb.platform.cpm.pandora.service.impl.core.batch.BatchOperationExtService;
import sycpb.platform.cpm.pandora.service.impl.core.batch.bos.LauUnitNextdayBudgetBo;
import sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.TemplateBatchOperationHandler;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.bos.ResAppPackageBo;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnit.LAU_UNIT;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitExtra.LAU_UNIT_EXTRA;
import static sycpb.platform.cpm.pandora.service.constants.AccountLabelIds.OLD_SANLIAN_ALLOW_EDIT_LABEL_ID;

public abstract class UnitTemplateHandler extends TemplateBatchOperationHandler<UnitTemplateHandler.UnitContext> {
    @Getter
    @Setter
    protected static class UnitBidContext {
        private Integer scale;
        private Integer bidValue;
        private Integer curBidValue;
        private ChangeLogBo changeLogBo;
    }

    @Getter
    @Setter
    public static class UnitContext extends Context {
        // 请求层级
        private Map<Integer, LauUnitPo> unitMap;
        private Map<Integer, LauUnitExtraPo> unitExtraMap;
        private Map<Integer, LauUnitNextdayBudgetBo> nextDatBudgetMap;
        private List<Integer> deprecatingUnitNextDayBudgetIdList;
        private Map<Integer, LauUnitTargetRulePo> unitTargetRuleMap;
        private Map<Integer, List<LauUnitTargetCrowdPackBo>> crowdPackMap;
        private Map<Integer, ResLiveReserveBo> unitLiveReserveMap;
        private Map<Integer, LauCampaignPo> campaignMap;
        private Map<Integer, ResAppPackageBo> resAppPackageBoMap;
        private List<Integer> accountLabels;

        private String requestDate;
        private Integer status;
        private boolean isDelete;
        private Timestamp nextDayBudgetEffectTime;
        private Map<String, UnitBidContext> unitBidContextMap;
        // 校验用 目前仅用于改价 不能使用原来的unitMap 会影响比对逻辑
        private Map<Integer, LauUnitPo> validationUnitMap;


        // 操作层级
        private String launchHourLog;
        private List<LauUnitTargetCrowdPackBo> crowdPackBos;
        private Integer realTimeDataQueryMode;
        private HashMap<Integer, UnitBidBo> unitBidMap;

        // 对象层级
        private LauUnitPo lauUnitPo;
        private LauUnitExtraPo lauUnitExtraPo;
        private LauUnitNextdayBudgetBo lauUnitNextdayBudgetBo;
        private LauUnitTargetRulePo lauUnitTargetRulePo;

        private List<SearchAdUnitKeyWordBo> searchAdUnitKeyWords;
        private List<LauUnitSearchNegativeKeywordBo> negSearchAdUnitKeyWords;
        private Integer isStoreDirectLaunch;
        private String deviceAppStore;
        private UnitBidContext unitBidContext;

        //是否需要type=2的赔付埋点
        private boolean needReportCompensationLogType2;

        //是否需要type=6的赔付埋点
        private boolean needReportCompensationLogType6;
        // 请求是否来自于更高级别的级联操作 比如删除单元级联删除创意 目前只有级联删除场景
        private boolean isFromUpperLevel;
    }

    final DSLContext adCore;
    @Autowired
    private IAccountLabelService accountLabelService;
    public UnitTemplateHandler(IOperationLogService operationLogService,
                               BatchOperationExtService batchOperationExtService,
                               DSLContext adCore) {
        super(operationLogService, batchOperationExtService);
        this.adCore = adCore;
    }

    @Override
    public UnitContext makeContext(BatchOperationReqBo reqBo) {
        final var ctx = new UnitContext();
        ctx.status = reqBo.getStatus();
        ctx.isDelete = Objects.equals(reqBo.getStatus(), LaunchStatus.DELETED);
        ctx.isFromUpperLevel = reqBo.isFromUpperLevel();
        return ctx;
    }

    @Override
    public void handleRequest(UnitContext ctx) {
        ctx.setUnitMap(fetchUnitMap(ctx.getAllTargetIds()));
        ctx.setRequestDate(LocalDate.now().format(DateTimeFormatter.ISO_DATE));
        ctx.accountLabels = accountLabelService.list(ctx.getOperatorBo().getOperatorId());
    }

    @Override
    public void handleTarget(UnitContext ctx) {
        ctx.setLauUnitPo(ctx.getUnitMap().get(ctx.getTargetId()));
    }

    @Override
    public List<Function<UnitContext, String>> fetchValidators() {
        return List.of(
                c -> Objects.isNull(c.getLauUnitPo()) ? "单元id对应的数据不存在" : null,
                c -> Objects.equals(LaunchStatus.DELETED, c.getLauUnitPo().getStatus()) ? "单元已删除" : null,
                c -> !Objects.equals(c.getOperatorBo().getOperatorId(), c.getLauUnitPo().getAccountId()) ? "无权限操作该单元" : null,
                c -> !c.isFromUpperLevel()
                        && NumberUtils.isPositive(c.getLauUnitPo().getParentUnitId())
                        && !OperatorTypeConstants.SYSTEM_OPERATOR_TYPE_LIST.contains(c.getOperatorBo().getOperatorType()) ?
                        "您当前无权操作自动托管子单元" + c.getLauUnitPo().getUnitId() : null,
                c -> {
                    final var operationType = c.getBatchOperationBo().getOperationType();
                    final var finished = c.getLauUnitPo().getLaunchEndDate().compareTo(c.getRequestDate()) < 0;
                    if (Objects.equals(operationType, BatchOperationType.BATCH_DELETE_VALUE)
                            || Objects.equals(operationType, BatchOperationType.BATCH_CLEANUP_VALUE)
                            || Objects.equals(operationType, BatchOperationType.BATCH_PAUSE_VALUE)) {
                        return null;
                    }
                    if (Objects.equals(operationType, BatchOperationType.BATCH_EXTEND_DATE_RANGE_VALUE)) {
                        return finished ? null : "续投仅支持已结束的单元";
                    }
                    return finished ? "单元已结束" : null;
                },
                c -> (!c.getBatchOperationBo().getOperationType().equals(BatchOperationType.BATCH_PAUSE_VALUE)
                    && !c.getBatchOperationBo().getOperationType().equals( BatchOperationType.BATCH_DELETE_VALUE)
                        && !c.getBatchOperationBo().getOperationType().equals( BatchOperationType.BATCH_CLEANUP_VALUE)
                        && !c.getAccountLabels().contains(OLD_SANLIAN_ALLOW_EDIT_LABEL_ID) && c.getLauUnitPo().getAdpVersion().equals(AdpVersion.ARCHIVE_MERGE))?"旧版单元不支持修改":null
        );
    }

    private Map<Integer, LauUnitPo> fetchUnitMap(Collection<Integer> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) return Map.of();

        return adCore.select(LAU_UNIT.ACCOUNT_ID,
                        LAU_UNIT.CAMPAIGN_ID,
                        LAU_UNIT.UNIT_ID,
                        LAU_UNIT.STATUS,
                        LAU_UNIT.BUDGET,
                        LAU_UNIT.ADP_VERSION,
                        LAU_UNIT.LAUNCH_BEGIN_DATE,
                        LAU_UNIT.LAUNCH_END_DATE,
                        LAU_UNIT.LAUNCH_TIME,
                        LAU_UNIT.SALES_TYPE,
                        LAU_UNIT.COST_PRICE,
                        LAU_UNIT.OCPC_TARGET,
                        LAU_UNIT.TWO_STAGE_BID,
                        LAU_UNIT.OCPX_TARGET_TWO,
                        LAU_UNIT.OCPX_TARGET_TWO_BID,
                        LAU_UNIT.TARGET_PACKAGE_ID,
                        LAU_UNIT.APP_PACKAGE_ID,
                        LAU_UNIT.IS_STORE_DIRECT_LAUNCH,
                        LAU_UNIT.IS_ALL_AD_SEARCH_UNIT,
                        LAU_UNIT.SEARCH_PRICE_COEFFICIENT,
                        LAU_UNIT.PROMOTION_PURPOSE_TYPE,
                        LAU_UNIT.SPEED_MODE,
                        LAU_UNIT.IS_NO_BID,
                        LAU_UNIT.OCPX_MODE,
                        LAU_UNIT.PARENT_UNIT_ID
                ).from(LAU_UNIT)
                .where(LAU_UNIT.UNIT_ID.in(targetIds))
                .fetch()
                .into(LauUnitPo.class)
                .stream()
                .collect(Collectors.toMap(LauUnitPo::getUnitId, Function.identity()));
    }

    // 在自己的handler里面判断是否需要查询，灵活自取。
    public Map<Integer, LauUnitExtraPo> fetchUnitExtraMap(Collection<Integer> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) return Map.of();

        return adCore.select(LAU_UNIT_EXTRA.ACCOUNT_ID,
                        LAU_UNIT_EXTRA.UNIT_ID,
                        LAU_UNIT_EXTRA.DEVICE_APP_STORE,
                        LAU_UNIT_EXTRA.ANDROID_APP_PACKAGE_ID,
                        LAU_UNIT_EXTRA.IOS_APP_PACKAGE_ID
                ).from(LAU_UNIT_EXTRA)
                .where(LAU_UNIT_EXTRA.UNIT_ID.in(targetIds))
                .fetch()
                .into(LauUnitExtraPo.class)
                .stream()
                .collect(Collectors.toMap(LauUnitExtraPo::getUnitId, Function.identity()));
    }

    @Resource
    private InfoReportService infoReportService;

    @Override
    public void batchUpdate(UnitContext ctx) {
        super.batchUpdate(ctx);
        try {
            infoReportCompensationLog(ctx);
        } catch (Exception ignored) {
        }
    }

    private void infoReportCompensationLog(UnitContext ctx) {
        if (!ctx.needReportCompensationLogType2  && !ctx.needReportCompensationLogType6 ) {
            return;
        }
        if (CollectionUtils.isEmpty(ctx.getOperationLogContextBos())) {
            return;
        }

        InfoReportCompensationLogBo infoReportCompensationLogBo = new InfoReportCompensationLogBo();
        infoReportCompensationLogBo.setNeedTyp2Report(ctx.needReportCompensationLogType2);
        infoReportCompensationLogBo.setNeedTyp6Report(ctx.needReportCompensationLogType6);
        infoReportCompensationLogBo.setNeedReport(true);
        infoReportCompensationLogBo.setOperator_id(String.valueOf(ctx.getOperatorBo().getOperatorId()));
        for (OperationLogContextBo operationLogContextBo : ctx.getOperationLogContextBos()) {
            infoReportCompensationLogBo.setUnitId(String.valueOf(operationLogContextBo.getObjId()));
            infoReportService.reportCompensationLog(infoReportCompensationLogBo);
        }
    }

    public static String validateNoBid(UnitContext ctx) {
        final var lauUnitPo = ctx.getLauUnitPo();
        if (NumberUtils.isPositive(lauUnitPo.getIsNoBid())) {
            return "nobid单元不支持此操作";
        }

        return null;
    }

}