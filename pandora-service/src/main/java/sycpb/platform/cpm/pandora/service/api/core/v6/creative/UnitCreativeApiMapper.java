package sycpb.platform.cpm.pandora.service.api.core.v6.creative;

import com.bapis.ad.pandora.core.list.ListCreativeReq;
import com.bapis.ad.pandora.core.v6.*;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.common.EnumMapper;
import sycpb.platform.cpm.pandora.infra.common.NumMapper;
import sycpb.platform.cpm.pandora.infra.common.comma_split.CommaSplitMapper;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.*;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore.*;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.AdProductMappingBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitExtraBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitShopGoodsBo;
import sycpb.platform.cpm.pandora.service.api.resource.mgk.PageGroupSource;
import sycpb.platform.cpm.pandora.service.constants.AuditStatus;

import java.util.List;
import java.util.Objects;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = {EnumMapper.class, CommaSplitMapper.class, NumMapper.class})
public interface UnitCreativeApiMapper {
    UnitCreativeApiMapper MAPPER = Mappers.getMapper(UnitCreativeApiMapper.class);


    /**
     * 素材点击区域 0-常规区域 1-全素材区域
     */
    String CLICK_AREA = "0";
    /**
     * 是否可跳过: 0-否 1-是
     */
    String IS_SKIP = "1";
    /**
     * 展示时长：3s 5s
     * 图片3s
     * 视频5s
     */
    String DURATION = "3";
    /**
     * 广告标和跳过按钮位置样式:0-默认样式,广告标右上,跳过按钮右下 1-实验样式,广告标右上,跳过按钮右下(虽然和0样式相同,但是0表示维持以前的默认配置,位置和按钮大小都不做变更) 2-广告标左上，跳过按钮右上
     */
    String MARK_WITH_SKIP_STYLE = "0";
    /**
     * 跳过按钮占屏幕高度
     */
    String SKIP_BUTTON_HEIGHT = "0.0557F";

    Long SHOW_REJECT_REASON_EARLIEST_CREATIVE_CTIME = 1745424000000L;

    @Mapping(target = "creativeIds", source = "creativeId")
    QueryCreativeBo fromRo(ListCreativeReq x);

    @Mapping(target = "isSystemGenerated", source = "isAutoFill")
    ListCreativeInfo toRo(ListCreativeBo x);

    CreativeMaterialDetail toRo(CreativeMaterialDetailBo x);

    @Mapping(target = "creatives", source = "creativeSet")
    UnitCreativeBo fromRo(UnitCreativeInfo x);

    @Mapping(target = "creativeSet", source = "creatives")
    UnitCreativeInfo toRo(UnitCreativeBo x);

    @Mapping(target = "lauUnitSceneBo", source = "x")
    @Mapping(target = "lauUnitMonitoringBos", source = "x.monitorSet")
    @Mapping(target = "lauUnitExtraBo", source = "x")
    @Mapping(target = "lauUnitBusinessCategoryBo", source = "x")
    @Mapping(target = "lauUnitBo", source = "x")
    CreativeLevelUnitBo fromRo(CreativeLevelUnitInfo x);

    @Mapping(target = ".", source = "x.lauUnitBo")
    @Mapping(target = "isBiliNative", source = "x.lauUnitExtraBo.isBiliNative")
    @Mapping(target = "allowAigcReplaceMaterial", source = "x.lauUnitExtraBo.allowAigcReplaceMaterial")
    @Mapping(target = "tagSet", source = "x.lauUnitExtraBo.tags.list")
    @Mapping(target = "sceneIdSet", source = "x.lauUnitSceneBo.selectedScenes.list")
    @Mapping(target = "channelId", source = "x.lauUnitSceneBo.channelId")
    @Mapping(target = "businessCategory", source = "x.lauUnitBusinessCategoryBo")
    @Mapping(target = "monitorSet", source = "x.lauUnitMonitoringBos")
    @Mapping(target = "generalVersion", source = "x.lauUnitExtraBo.generalVersion")
    @Mapping(target = "supportCreativeExplore", source = "x.supportCreativeExplore")
    @Mapping(target = "hasExploreCreative", source = "x.hasExploreCreative")
    @Mapping(target = "creativeExploreExtraCondition", source = "x.creativeExploreExtraCondition")
    CreativeLevelUnitInfo toRo(CreativeLevelUnitBo x);

    @Mapping(target = "lauUnitCreativeBo", source = "x")
    @Mapping(target = "lauCreativeExtraBo", source = "x")
    @Mapping(target = "lauCreativeFlyDynamicInfoBo", source = "dynamic", conditionExpression = "java(x.getDynamic().getDynamicId() > 0)")
    @Mapping(target = "lauCreativeMiniGameMappingBo", source = "miniGame", conditionExpression = "java(x.getMiniGame().getGameId() > 0)")
    @Mapping(target = "lauCreativeFlyExtInfoBo", source = "goodsComponent")
    @Mapping(target = "lauCreativeImageBos", source = "imageSet")
    @Mapping(target = "lauCreativeTitleBos", source = "titleSet")
    @Mapping(target = "lauCreativeArchiveBos", source = "archiveSet")
    @Mapping(target = "qualificationIds", source = "qualificationIdSet")
    @Mapping(target = "lauCreativeComponentBos", source = "creativeComponentSet")
    @Mapping(target = "lauCreativeButtonCopyBo.buttonCopyId", source = "buttonId")
    @Mapping(target = "lauCreativeLandingPageGroupBo", expression = "java(UnitCreativeApiMapper.toLauCreativeLandingPageGroupBo(x))")
    @Mapping(target = "lauCreativePgcArchiveBo", expression = "java(UnitCreativeApiMapper.toLauCreativePgcArchiveBo(x))")
    @Mapping(target = "lauSplashScreenCreativeBo", source = "splashScreenInfo")
    CreativeBo fromRo(CreativeInfo x);


    CreativeCpsReplaceLinkBo fromRo(CpsReplaceLinkInfo x);

    List<CpsReplaceLinkInfo> fromBos(List<CreativeCpsReplaceLinkBo> creativeCpsReplaceLinkBos);

    CpsReplaceLinkInfo fromBo(CreativeCpsReplaceLinkBo creativeCpsReplaceLinkBo);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "accountId", ignore = true)
    @Mapping(target = "creativeId", ignore = true)
    @Mapping(target = "campaignId", ignore = true)
    @Mapping(target = "unitId", ignore = true)
    @Mapping(target = "duration", constant = DURATION)
    @Mapping(target = "isSkip", constant = IS_SKIP)
    @Mapping(target = "skipButtonHeight", constant = SKIP_BUTTON_HEIGHT)
    @Mapping(target = "markWithSkipStyle", constant = MARK_WITH_SKIP_STYLE)
    @Mapping(target = "clickArea", constant = CLICK_AREA)
    LauSplashScreenCreativeBo fromRo(CreativeSplashScreenInfo x);

    CreativeSplashScreenInfo toRo(LauSplashScreenCreativeBo x);

    @Mapping(target = ".", source = "lauUnitCreativeBo")
    @Mapping(target = "callUpScheme.schemeUrl", source = "lauUnitCreativeBo.schemeUrl")
    @Mapping(target = "callUpScheme.isSystemGenerated", source = "lauUnitCreativeBo.isAutoFill")
    @Mapping(target = "landingPage", expression = "java(UnitCreativeApiMapper.toRo(x.getLauCreativeLandingPageGroupBo(), x.getLauCreativeExtraBo()))")
    @Mapping(target = "qualificationPackageId", source = "lauCreativeExtraBo.qualificationPackageId")
    @Mapping(target = "adSpaceId", source = "lauCreativeExtraBo.adSpaceId")
    @Mapping(target = "biliSpaceMid", source = "lauCreativeExtraBo.biliSpaceMid")
    @Mapping(target = "imageSet", source = "lauCreativeImageBos")
    @Mapping(target = "titleSet", source = "lauCreativeTitleBos")
    @Mapping(target = "archiveSet", source = "lauCreativeArchiveBos")
    @Mapping(target = "dynamic", source = "lauCreativeFlyDynamicInfoBo")
    @Mapping(target = "miniGame", source = "lauCreativeMiniGameMappingBo")
    @Mapping(target = "goodsComponent", source = "lauCreativeFlyExtInfoBo")
    @Mapping(target = "qualificationIdSet", source = "qualificationIds")
    @Mapping(target = "creativeComponentSet", source = "lauCreativeComponentBos")
    @Mapping(target = "buttonId", source = "lauCreativeButtonCopyBo.buttonCopyId")
    @Mapping(target = "parentCreativeId", source = "lauCreativeExtraBo.parentCreativeId")
    @Mapping(target = "ogvInfo", expression = "java(UnitCreativeApiMapper.toCreativeOgvInfo(x))")
    @Mapping(target = "splashScreenInfo", source = "lauSplashScreenCreativeBo")
    @Mapping(target = "isSmartDerivative", source = "lauCreativeExtraBo.isSmartDerivative")
    @Mapping(target = "rejectReason", source = "lauUnitCreativeBo.reason", conditionExpression = "java(UnitCreativeApiMapper.shouldShowCreativeRejectReason(x))")
    CreativeInfo toRo(CreativeBo x);

    @Mapping(target = "adpVersion", ignore = true)
    @Mapping(target = "accountId", ignore = true)
    @Mapping(target = "campaignId", ignore = true)
    CreativeLevelLauUnitBo toLauUnitBo(CreativeLevelUnitInfo x);

    @Mapping(target = "selectedScenes", source = "sceneIdSet")
    LauUnitSceneBo toLauUnitSceneBo(CreativeLevelUnitInfo x);

    @Mapping(target = "tags", source = "tagSet")
    @Mapping(target = "accountId", ignore = true)
    LauUnitExtraBo toLauUnitExtraBo(CreativeLevelUnitInfo x);

    @Mapping(target = ".", source = "x.businessCategory")
    LauUnitBusinessCategoryBo toLauUnitBusinessCategory(CreativeLevelUnitInfo x);

    LauUnitMonitoringBo fromRo(MonitorInfo x);

    @Mapping(target = "status", ignore = true)
    @Mapping(target = "auditStatus", ignore = true)
    @Mapping(target = "schemeUrl", source = "x.callUpScheme.schemeUrl")
    @Mapping(target = "isAutoFill", source = "x.callUpScheme.isSystemGenerated")
    @Mapping(target = "cmMark", expression = "java(new Integer(1))")
    @Mapping(target = "reason", ignore = true)
    LauUnitCreativeBo toLauUnitCreativeBo(CreativeInfo x);

    LauUnitCreativeBo copy(LauUnitCreativeBo x);

    @Mapping(target = "rawJumpUrl", source = "x.landingPage.url")
    @Mapping(target = "mgkPageId", source = "x.landingPage.mgkPageId")
    LauCreativeExtraBo toLauCreativeExtraBo(CreativeInfo x);

    LauCreativeFlyDynamicInfoBo fromRo(CreativeDynamicInfo x);

    @Mapping(target = "mid", source = "dynamicUpMid")
    CreativeDynamicInfo toRo(LauCreativeFlyDynamicInfoBo x);

    @Mapping(target = "gameUrl", source = "url")
    @Mapping(target = "miniGameId", source = "gameId")
    LauCreativeMiniGameMappingBo fromRo(CreativeMiniGameInfo x);

    @Mapping(target = "url", source = "gameUrl")
    @Mapping(target = "gameId", source = "miniGameId")
    CreativeMiniGameInfo toRo(LauCreativeMiniGameMappingBo x);

    @Mapping(target = "isYellowCar", source = "style")
    @Mapping(target = "yellowCarTitle", source = "feature")
    LauCreativeFlyExtInfoBo fromRo(CreativeGoodsComponentInfo x);

    @Mapping(target = "style", source = "isYellowCar")
    @Mapping(target = "feature", source = "yellowCarTitle")
    CreativeGoodsComponentInfo toRo(LauCreativeFlyExtInfoBo x);

    @Mapping(target = "imageUrl", source = "url")
    LauCreativeImageBo fromRo(CreativeImageInfo x);

    @Mapping(target = "url", source = "imageUrl")
    CreativeImageInfo toRo(LauCreativeImageBo x);

    @Mapping(target = "id", ignore = true)
    LauCreativeImageBo copyImage(LauCreativeImageBo x);

    @Mapping(target = "rawTitle", source = "title")
    @Mapping(target = "lauCreativeSmartTitleBos", source = "smartTitleSet")
    @Mapping(target = "materialId", ignore = true)
    LauCreativeTitleBo fromRo(CreativeTitleInfo x);

    LauCreativeSmartTitleBo fromRo(CreativeSmartTitleInfo x);

    @Mapping(target = "title", source = "rawTitle")
    @Mapping(target = "smartTitleSet", source = "lauCreativeSmartTitleBos")
    CreativeTitleInfo toRo(LauCreativeTitleBo x);

    @Mapping(target = "id", ignore = true)
    LauCreativeTitleBo copyTitle(LauCreativeTitleBo x);

    @Mapping(target = "id", ignore = true)
    LauCreativeSmartTitleBo copySmartTitle(LauCreativeSmartTitleBo x);

    @Mapping(target = "coverMaterialId", source = "cover.materialId")
    @Mapping(target = "isUseSmartCut", source = "cover.isUseSmartCut")
    LauCreativeArchiveBo fromRo(CreativeArchiveInfo x);

    @Mapping(target = "id", ignore = true)
    LauCreativeArchiveBo copyArchive(LauCreativeArchiveBo x);

    @Mapping(target = "cover.materialId", source = "coverMaterialId")
    @Mapping(target = "cover.url", source = "coverUrl")
    @Mapping(target = "cover.isUseSmartCut", source = "isUseSmartCut")
    CreativeArchiveInfo toRo(LauCreativeArchiveBo x);

    @Mapping(target = "componentType", source = "type")
    LauCreativeComponentBo fromRo(CreativeComponentInfo x);

    @Mapping(target = "type", source = "componentType")
    CreativeComponentInfo toRo(LauCreativeComponentBo x);

    @Mapping(target = "picList", source = "pic")
    @Mapping(target = "titleList", source = "title")
    CreativeBatchReplaceMaterialBo fromRo(BatchReplaceCreativeElementReq x);

    CreativeAigcReplaceHistoryInfo toRo(LauCreativeAigcMaterialHistoryShowElementBo x);

    CreativeExploreFakeCreativeSaveBo convertBo(UnitCreativeIdsBo x);

    static LauCreativeLandingPageGroupBo toLauCreativeLandingPageGroupBo(CreativeInfo x) {
        final var landingPage = x.getLandingPage();
        final var bo = new LauCreativeLandingPageGroupBo();
        if (NumberUtils.isPositive(landingPage.getMgkPageGroupId())) {
            bo.setGroupId(landingPage.getMgkPageGroupId());
            bo.setGroupSource(PageGroupSource.FROM_MGK);
        } else if (NumberUtils.isPositive(landingPage.getThirdPartyPageGroupId())) {
            bo.setGroupId(landingPage.getThirdPartyPageGroupId());
            bo.setGroupSource(PageGroupSource.FROM_THIRD_PARTY);
        } else {
            return null;
        }
        return bo;
    }

    static CreativeLandingPageInfo toRo(LauCreativeLandingPageGroupBo groupBo, LauCreativeExtraBo extraBo) {
        final var builder = CreativeLandingPageInfo.newBuilder();
        if (Objects.nonNull(extraBo)) {
            if (StringUtils.hasText(extraBo.getRawJumpUrl())) {
                builder.setUrl(extraBo.getRawJumpUrl());
            } else if (NumberUtils.isPositive(extraBo.getMgkPageId())) {
                builder.setMgkPageId(extraBo.getMgkPageId());
            }
        }
        if (Objects.nonNull(groupBo) && NumberUtils.isPositive(groupBo.getGroupId())) {
            if (PageGroupSource.isFromMgk(groupBo.getGroupSource())) {
                builder.setMgkPageGroupId(groupBo.getGroupId());
            } else if (PageGroupSource.isFromThirdParty(groupBo.getGroupSource())) {
                builder.setThirdPartyPageGroupId(groupBo.getGroupId());
            }
        }
        return builder.build();
    }

    static LauCreativePgcArchiveBo toLauCreativePgcArchiveBo(CreativeInfo x) {
        final var builder = LauCreativePgcArchiveBo.builder();
        if (StringUtils.hasText(x.getOgvInfo().getMaterialNo())) {
            builder.materialNo(Long.parseLong(x.getOgvInfo().getMaterialNo()));
            return builder.build();
        }
        return builder.materialNo(0L).build();
    }

    static CreativeOgvInfo toCreativeOgvInfo(CreativeBo x) {
        final var builder = CreativeOgvInfo.newBuilder();
        if (Objects.nonNull(x.getLauCreativePgcArchiveBo()) && NumberUtils.isPositive(x.getLauCreativePgcArchiveBo().getMaterialNo())) {
            builder.setMaterialNo(String.valueOf(x.getLauCreativePgcArchiveBo().getMaterialNo()));
        }
        return builder.build();
    }

    static boolean shouldShowCreativeRejectReason(CreativeBo x) {
        return x.getLauUnitCreativeBo().getCtime() > SHOW_REJECT_REASON_EARLIEST_CREATIVE_CTIME && x.getLauUnitCreativeBo().getAuditStatus().equals(AuditStatus.REJECTED);
    }
}
