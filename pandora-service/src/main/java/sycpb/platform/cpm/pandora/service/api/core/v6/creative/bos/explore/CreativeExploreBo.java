package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperatorBo;

import java.util.List;

/**
 * @ClassName CreativeExploreBo
 * <AUTHOR>
 * @Date 2025/3/20 8:23 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreativeExploreBo {
    // unitId存在时，需要把单元和创意信息都查出来填充
    private Integer unitId;
    private CreativeExploreUnitInfoBo unitBo;
    private List<CreativeExploreCreativeInfoBo> creativeBoList;
    private CreativeExploreExtraInfoBo extraBo;
    private boolean isJobRefresh;
    private OperatorBo operator;
    // redisKey存在时，要把最后的结果创意id list存redis
    private String redisKey;
}
