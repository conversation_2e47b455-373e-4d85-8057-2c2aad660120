package sycpb.platform.cpm.pandora.service.impl.core.v6.unit;

import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauUnitBiliMiniGameDao;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitBiliMiniGamePo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitBiliMiniGameBo;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UnitBiliMiniGameService {
    private final LauUnitBiliMiniGameDao lauUnitBiliMiniGameDao;

    public void save(SaveUnitContextBo ctx) {
        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(LauUnitBiliMiniGameBo.class, UnitImplMapper.MAPPER::toPo, lauUnitBiliMiniGameDao)
                .setOpLog(ctx.genCompareConsumer());
        JooqFunctions.save(ctx.getCurrentVersion().getLauUnitBiliMiniGameBo(), ctx.getNewVersion().getLauUnitBiliMiniGameBo(), ctx0);
    }

    public LauUnitBiliMiniGameBo get(Integer unitId) {
        return UnitImplMapper.MAPPER.fromPo(lauUnitBiliMiniGameDao.fetchOneByUnitId(unitId));
    }

    public List<LauUnitBiliMiniGameBo> list(List<Integer> unitIdList) {
        if (CollectionUtils.isEmpty(unitIdList)) {
            return Collections.emptyList();
        }
        List<LauUnitBiliMiniGamePo> lauUnitBiliMiniGamePos = lauUnitBiliMiniGameDao.fetchByUnitId(unitIdList.toArray(new Integer[0]));
        return lauUnitBiliMiniGamePos.stream()
                .map(UnitImplMapper.MAPPER::fromPo)
                .collect(Collectors.toList());
    }
}
