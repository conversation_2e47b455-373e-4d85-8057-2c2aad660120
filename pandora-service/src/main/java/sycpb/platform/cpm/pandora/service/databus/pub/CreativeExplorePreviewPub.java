package sycpb.platform.cpm.pandora.service.databus.pub;

import com.alibaba.fastjson.JSON;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore.CreativeExploreBo;

import javax.annotation.Resource;

/**
 * @ClassName CreativeExplorePreviewPub
 * <AUTHOR>
 * @Date 2025/4/9 5:53 下午
 * @Version 1.0
 **/
@Slf4j
@Service
public class CreativeExplorePreviewPub {

    private final String topic;
    private final String group;

    public static final String CREATIVE_EXPLORE_PREVIEW_DATABUS_KEY = "creative-explore-preview";

    @Resource
    private DatabusTemplate databusTemplate;

    public CreativeExplorePreviewPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(CREATIVE_EXPLORE_PREVIEW_DATABUS_KEY);
        topic = databusProperty.getTopic();
        group = databusProperty.getPub().getGroup();
    }

    public void pub(String key, CreativeExploreBo exploreBo) {
        log.info("CreativeExplorePreviewPub pub msg, key={}, exploreBo = {}", key, JSON.toJSONString(exploreBo));
        Assert.notNull(exploreBo, "预览探索创意内容不可为空");
        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(key, exploreBo)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("CreativeExplorePreviewPub pub msg success, msg={}", JSON.toJSONString(exploreBo));
        } else {
            Throwable throwable = result.getThrowable();
            log.error("CreativeExplorePreviewPub pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }
    }

}
