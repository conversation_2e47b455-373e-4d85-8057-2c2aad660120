package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.broadcast;

import lombok.Data;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.BroadcastSmartCropImageBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.broadcast.BroadcastCreativeInfoBo;
import sycpb.platform.cpm.pandora.service.databus.pub.bo.CreativeAuditOtherInfoBo;

import java.util.List;
import java.util.Map;

@Data
public class UnitCreativeIdBroadCastBo {
    // 智能剪裁数据
    private List<BroadcastSmartCropImageBo> smart_crop_image;

    private List<BroadcastCreativeInfoBo> creative_infos;

    private BroadCastAuditCreativeBo audit_creative;

    private Long event_time;


}
