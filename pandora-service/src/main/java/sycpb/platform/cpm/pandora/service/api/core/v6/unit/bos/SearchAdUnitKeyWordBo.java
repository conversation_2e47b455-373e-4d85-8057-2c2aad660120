package sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchAdUnitKeyWordBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Long      id;
    private Integer unitId;
    @CompareMeta
    private String keyWord;
    private String keywordMd5;
    @CompareMeta
    private Integer source;
    @CompareMeta
    private String searchWord;

    @CompareMeta(uk = true)
    public String uk() {
        return unitId + "-" + keywordMd5;
    }
}
