package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.infra.common.comma_split.CommaSplitMapper;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeNativeArchiveRelativityPo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauCreativeNativeArchiveRelativityBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauNativeArchiveBo;

import java.util.List;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = CommaSplitMapper.class)
public interface ICreativeNativeArchiveRelativityMapper {

    ICreativeNativeArchiveRelativityMapper MAPPER = Mappers.getMapper(ICreativeNativeArchiveRelativityMapper.class);

    List<LauCreativeNativeArchiveRelativityBo> fromPos(List<LauCreativeNativeArchiveRelativityPo> pos);
    LauCreativeNativeArchiveRelativityBo fromPo(LauCreativeNativeArchiveRelativityPo po);

    List<LauCreativeNativeArchiveRelativityPo> toBos(List<LauCreativeNativeArchiveRelativityBo> bos);

    LauCreativeNativeArchiveRelativityPo toPo(LauCreativeNativeArchiveRelativityBo bo);

    LauCreativeNativeArchiveRelativityBo copy(LauCreativeNativeArchiveRelativityBo relativityBo);

}
