package sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.*;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.ShadowCreativeBo;

import java.util.List;

/**
 * 程序化审核需要变更的信息
 */
@Getter
@Setter
@NoArgsConstructor
public class ProgCreativeAuditSaveBo {

    // 原始创意信息
    private LauUnitCreativeBo lauUnitCreativeBo;
    // 程序化元素列表
    private List<LauProgrammaticCreativeDetailBo> lauProgrammaticCreativeDetailBos;

    // 创意稿件
    private List<LauCreativeArchiveBo> lauCreativeArchiveBos;
    // 创意动态
    private List<LauCreativeFlyDynamicInfoBo> lauCreativeFlyDynamicInfoBos;
    // 创意标题列表
    private List<LauCreativeTitleBo> lauCreativeTitleBos;
    // 创意图片列表
    private List<LauCreativeImageBo> lauCreativeImageBos;
    // 创意按钮
    private List<LauCreativeButtonCopyBo> lauCreativeButtonCopyBos;

    // 创意组件
    private List<LauCreativeComponentBo> lauCreativeComponentBos;

    // 创意原生落地页 tab
    private List<LauCreativeTabBo> lauCreativeTabBos;
    // 创意落地页
    private List<LauCreativeLandingPageBo> lauCreativeLandingPageBos;
    // 落地页组
    private List<LauCreativeLandingPageGroupBo> lauCreativeLandingPageGroupBos;
    // 替链
    private CreativeCpsReplaceLinkBo creativeCpsReplaceLinkBo;
}
