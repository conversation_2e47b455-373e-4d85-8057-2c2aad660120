package sycpb.platform.cpm.pandora.service.api.resource.unit.bos;

import lombok.Data;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.CpaTargetBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.DeepCpaTargetBo;

import java.util.List;

@Data
public class NestedCpaTargetBo {
    private CpaTargetBo baseTarget;
    private CpaTargetBo cpaTarget;
    private List<DeepCpaTargetBo> deepCpaTargets;
    private List<CpaTargetBo> assistCpaTargets;
    private Integer isNoBid;
}
