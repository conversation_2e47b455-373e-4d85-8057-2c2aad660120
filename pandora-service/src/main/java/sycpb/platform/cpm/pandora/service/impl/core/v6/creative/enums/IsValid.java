package sycpb.platform.cpm.pandora.service.impl.core.v6.creative.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @date 4/15/24
 **/
@AllArgsConstructor
public enum IsValid {

    TRUE(1, "是"),
    FALSE(0, "否");
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static IsValid getByCode(int code) {
        for (IsValid bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code IsValid "+code);
    }

}
