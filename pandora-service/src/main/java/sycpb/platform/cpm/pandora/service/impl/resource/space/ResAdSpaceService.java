package sycpb.platform.cpm.pandora.service.impl.resource.space;

import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauAccountInfoPo;
import sycpb.platform.cpm.pandora.service.api.resource.space.AdSpaceMapper;
import sycpb.platform.cpm.pandora.service.api.resource.space.bos.AdSpaceBo;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauAccountInfo.LAU_ACCOUNT_INFO;

/**
 * @ClassName ResAdSpaceService
 * <AUTHOR>
 * @Date 2024/6/25 12:17 上午
 * @Version 1.0
 **/
@Service
public class ResAdSpaceService {

    @Resource(name = MySqlConfig.AD_DSL_CONTEXT)
    private DSLContext ad;

    public List<AdSpaceBo> fetchByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<LauAccountInfoPo> pos = ad.selectFrom(LAU_ACCOUNT_INFO)
                .where(LAU_ACCOUNT_INFO.ID.in(ids))
                .and(LAU_ACCOUNT_INFO.IS_DELETED.eq(0))
                .fetch().into(LauAccountInfoPo.class);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return AdSpaceMapper.MAPPER.fromPos(pos);
    }

}
