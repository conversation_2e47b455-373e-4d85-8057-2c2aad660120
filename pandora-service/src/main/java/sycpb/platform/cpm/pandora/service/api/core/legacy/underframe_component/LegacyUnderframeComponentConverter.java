package sycpb.platform.cpm.pandora.service.api.core.legacy.underframe_component;

import com.bapis.ad.pandora.core.GetUnderframeComponentsReq;
import com.bapis.ad.pandora.core.ListUnderframeComponentReq;
import com.bapis.ad.pandora.core.UnderframeComponent;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauUnderframeComponentPo;
import sycpb.platform.cpm.pandora.service.api.core.creative_component.UnderframeComponentBo;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface LegacyUnderframeComponentConverter {
    LegacyUnderframeComponentConverter MAPPER = Mappers.getMapper(LegacyUnderframeComponentConverter.class);

    UnderframeComponentBo fromPo(LauUnderframeComponentPo po);

    LauUnderframeComponentPo toPo(UnderframeComponentBo bo);

    @Mapping(target = "ctime", expression = "java(bo.getCtime().getTime())")
    UnderframeComponent toRpcBo(UnderframeComponentBo bo);

    List<UnderframeComponent> toRpcBos(List<UnderframeComponentBo> bos);

    @Mapping(target = "ctime", ignore = true)
    UnderframeComponentBo fromRpcBo(UnderframeComponent rpcBo);

    LegacyQueryUnderframeComponentBo toListQueryBo(ListUnderframeComponentReq req);

    @Mapping(target = "ids", source = "componentIds")
    LegacyQueryUnderframeComponentBo toListQueryBo(GetUnderframeComponentsReq req);
}
