package sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauCreativeLandingPageBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Long id;
    private Integer accountId;
    private Integer campaignId;
    private Integer unitId;
    @CompareMeta(uk = true, comparable = false)
    private Integer creativeId;
    @CompareMeta(shadow = true)
    private Long containerPageId;
    @CompareMeta(shadow = true, comparable = true)
    private String containerUrl; // 变了
    @CompareMeta(shadow = true)
    private String containerSecondaryUrl;

}
