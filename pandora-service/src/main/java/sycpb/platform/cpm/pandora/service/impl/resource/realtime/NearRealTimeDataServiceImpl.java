package sycpb.platform.cpm.pandora.service.impl.resource.realtime;

import com.bapis.ad.pluto.service.AdStatsServiceGrpc;
import com.bapis.ad.pluto.service.CampaignSummaryStatReq;
import com.bapis.ad.pluto.service.UnitSummaryStatReq;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.resource.realtime.INearRealTimeDataService;
import sycpb.platform.cpm.pandora.service.api.resource.realtime.bo.QueryNearRealTimeDataBo;
import sycpb.platform.cpm.pandora.service.api.resource.realtime.bo.RealTimeDataBo;
import sycpb.platform.cpm.pandora.service.api.resource.realtime.bo.RealTimeQueryMode;
import sycpb.platform.cpm.pandora.service.proxy.CpmPlutoProxy;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.Objects;

@Service
public class NearRealTimeDataServiceImpl implements INearRealTimeDataService {
    @Resource
    private CpmPlutoProxy cpmPlutoProxy;

    @Override
    public RealTimeDataBo get(QueryNearRealTimeDataBo queryBo) {
        return cpmPlutoProxy.getSummaryStat(queryBo);
    }
}
