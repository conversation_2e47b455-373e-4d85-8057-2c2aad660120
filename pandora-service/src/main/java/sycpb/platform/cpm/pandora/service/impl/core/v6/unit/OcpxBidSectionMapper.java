package sycpb.platform.cpm.pandora.service.impl.core.v6.unit;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.infra.common.comma_split.CommaSplitMapper;
import sycpb.platform.cpm.pandora.infra.common.target_rule.TargetRuleMapper;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.OcpxBidSectionPo;
import sycpb.platform.cpm.pandora.infra.pandora_type.ValueWithDefaultValueMapper;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.OcpxBidSectionBo;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = {ValueWithDefaultValueMapper.class, TargetRuleMapper.class, CommaSplitMapper.class})
public interface OcpxBidSectionMapper {

    OcpxBidSectionMapper MAPPER = Mappers.getMapper(OcpxBidSectionMapper.class);

    OcpxBidSectionBo toBo(OcpxBidSectionPo po);
}
