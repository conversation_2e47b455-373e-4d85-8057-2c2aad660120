package sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow;

import lombok.Data;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @Description
 * @date 4/16/24
 **/
@Data
public class LauShadowCreativeBo {

    private Long      id;
    private Integer   accountId;
    private Integer   campaignId;
    private Integer   unitId;
    private Integer   creativeId;
    private Long      mgkPageId;
    private Integer   auditStatus;
    private String    reason;
    private String    shadowCreative;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   creativeStatus;
    private Long      pageGroupId;

    @CompareMeta(uk = true)
    public Integer uk() {
        return creativeId;
    }
}
