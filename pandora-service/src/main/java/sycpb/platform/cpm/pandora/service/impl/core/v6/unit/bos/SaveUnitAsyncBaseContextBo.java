package sycpb.platform.cpm.pandora.service.impl.core.v6.unit.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperatorBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.UnitBo;

/**
 * @ClassName SaveUnitBaseContextBo
 * <AUTHOR>
 * @Date 2024/8/4 4:46 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SaveUnitAsyncBaseContextBo {

    private OperatorBo operator;
    private UnitBo newVersion;
    private UnitAppPackageExtraBo appPackageExtraBo;

}
