package sycpb.platform.cpm.pandora.service.impl.core.v6.business.content;

import com.bapis.ad.account.category.CategoryDto;
import com.bapis.ad.account.category.QueryAccountCategoryResp;
import com.bapis.ad.account.category.QueryUnitIndustryResp;
import com.bapis.ad.account.category.UnitIndustryDto;
import com.bapis.ad.account.crm.acc.AccountBase;
import com.bapis.ad.account.crm.acc.AccountBaseReply;
import com.bapis.ad.account.product.QueryAccountProductResp;
import com.bapis.community.service.govern.UDFProviderGrpc;
import com.bilibili.business.content.api.content.enums.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauSubjectPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauProgrammaticCreativeDetailDao;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.*;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.constants.AdpVersion;
import sycpb.platform.cpm.pandora.service.constants.BusinessDomain;
import sycpb.platform.cpm.pandora.service.constants.GeneralVersion;
import sycpb.platform.cpm.pandora.service.constants.ProgrammaticStatus;
import sycpb.platform.cpm.pandora.service.databus.pub.BusinessContentPub;
import sycpb.platform.cpm.pandora.service.databus.pub.bo.BusinessContentProducerMessageBo;
import sycpb.platform.cpm.pandora.service.enums.LauSubjectTypeEnum;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.enums.PromotionPurposeType;
import sycpb.platform.cpm.pandora.service.proxy.CpmAdAccountProxy;
import sycpb.platform.cpm.pandora.service.proxy.CpmScvProxy;
import sycpb.platform.cpm.pandora.service.proxy.MainCommunityGovernServiceProxy;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauSubject.LAU_SUBJECT;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeArchive.LAU_CREATIVE_ARCHIVE;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnit.LAU_UNIT;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitCreative.LAU_UNIT_CREATIVE;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitExtra.LAU_UNIT_EXTRA;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/5/16
 **/
@Service
@Slf4j
public class BusinessContentService {

    @Resource(name = MySqlConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;

    @Resource(name = MySqlConfig.AD_DSL_CONTEXT)
    private DSLContext ad;

    @Resource
    private LauProgrammaticCreativeDetailDao lauProgrammaticCreativeDetailDao;

    @Resource
    private CpmScvProxy cpmScvProxy;

    @Resource
    private CpmAdAccountProxy cpmAdAccountProxy;

    @Resource
    private BusinessContentPub businessContentPub;

    @Resource
    private MainCommunityGovernServiceProxy mainCommunityGovernServiceProxy;


    private static final Map<Integer, FirstBusinessTypeEnum> BUSINESS_FIRST_CATE_MAP = new HashMap<Integer, FirstBusinessTypeEnum>() {
        {
            put(BusinessDomain.CPC, FirstBusinessTypeEnum.BI_XUAN);
            put(BusinessDomain.BUSINESS_FLY, FirstBusinessTypeEnum.QI_FEI);
            put(BusinessDomain.CONTENT_FLY, FirstBusinessTypeEnum.QI_FEI);
            put(BusinessDomain.PERSONAL_FLY, FirstBusinessTypeEnum.QI_FEI);
        }
    };

    private static final Map<Integer, SecondBusinessTypeEnum> BUSINESS_SECOND_CATE_MAP = new HashMap<Integer, SecondBusinessTypeEnum>() {
        {
            put(BusinessDomain.BUSINESS_FLY, SecondBusinessTypeEnum.SHANG_YE_QI_FEI);
            put(BusinessDomain.CONTENT_FLY, SecondBusinessTypeEnum.NEI_RONG_QI_FEI);
            put(BusinessDomain.PERSONAL_FLY, SecondBusinessTypeEnum.GE_REN_QI_FEI);
        }
    };

    private static final Map<Integer, ContentFromEnum> ADP_VERSION_CONTENTFROM_MAP = new HashMap<Integer, ContentFromEnum>() {
        {
            put(AdpVersion.LEGACY, ContentFromEnum.LAO_BI_XUAN);
            put(AdpVersion.ARCHIVE_MERGE, ContentFromEnum.SAN_LIAN);
            put(AdpVersion.MODEL_MERGE, ContentFromEnum.SAN_LIAN);
        }
    };


    public static final ArrayList<Integer> CONSUMER_TYPES = Lists.newArrayList(
            ConsumerTypeEnum.OTHER.getCode(),
            ConsumerTypeEnum.ZHI_LI_PING_TAI.getCode());


    public void pubById(Integer creativeId) {
        LauUnitCreativePo creativePo = adCore.select(LAU_UNIT_CREATIVE.CREATIVE_ID, LAU_UNIT_CREATIVE.UNIT_ID, LAU_UNIT_CREATIVE.VIDEO_ID, LAU_UNIT_CREATIVE.IS_PROGRAMMATIC)
                .from(LAU_UNIT_CREATIVE)
                .where(LAU_UNIT_CREATIVE.CREATIVE_ID.eq(creativeId))
                .limit(1)
                .fetchOneInto(LauUnitCreativePo.class);
        pub(creativePo);
    }

    public void pub(LauUnitCreativePo creativePo) {
        if (null == creativePo) return;

        Integer creativeId = creativePo.getCreativeId();
        Integer unitId = creativePo.getUnitId();
        LauUnitPo unitPo = adCore.select(LAU_UNIT.UNIT_ID, LAU_UNIT.SUBJECT_ID,
                        LAU_UNIT.BUSINESS_DOMAIN, LAU_UNIT.ADP_VERSION, LAU_UNIT.ACCOUNT_ID, LAU_UNIT.PROMOTION_PURPOSE_TYPE)
                .from(LAU_UNIT)
                .where(LAU_UNIT.UNIT_ID.eq(unitId))
                .limit(1)
                .fetchOneInto(LauUnitPo.class);
        if (null == unitPo || PromotionPurposeType.OGV.getCode() == unitPo.getPromotionPurposeType()) return;

        LauUnitExtraPo unitExtraPo = adCore.select(LAU_UNIT_EXTRA.IS_BILI_NATIVE, LAU_UNIT_EXTRA.GENERAL_VERSION).from(LAU_UNIT_EXTRA)
                .where(LAU_UNIT_EXTRA.UNIT_ID.eq(unitId)).limit(1).fetchOneInto(LauUnitExtraPo.class);
        if (null == unitExtraPo) {
            unitExtraPo = new LauUnitExtraPo();
            //默认非原生
            unitExtraPo.setIsBiliNative(0);
            unitExtraPo.setGeneralVersion(0);
        }

        Map<Long, LauProgrammaticCreativeDetailPo> programmaticCreativeDetailPoMap = Collections.EMPTY_MAP;
        if (NumberUtils.isPositive(creativePo.getIsProgrammatic())) {
            List<LauProgrammaticCreativeDetailPo> programmaticCreativeDetailPos = lauProgrammaticCreativeDetailDao.fetchByCreativeId(creativeId);
            programmaticCreativeDetailPoMap = programmaticCreativeDetailPos.stream()
                    .collect(Collectors.toMap(LauProgrammaticCreativeDetailPo::getMaterialId, r -> r));
        }

        //视频云
        List<LauCreativeArchivePo> lauCreativeArchivePos = adCore.selectFrom(LAU_CREATIVE_ARCHIVE)
                .where(LAU_CREATIVE_ARCHIVE.CREATIVE_ID.eq(creativeId))
                .fetchInto(LauCreativeArchivePo.class);
        LauUnitExtraPo finalUnitExtraPo = unitExtraPo;
        final Map<Long, LauProgrammaticCreativeDetailPo> finalProgrammaticCreativeDetailPoMap = programmaticCreativeDetailPoMap;
        lauCreativeArchivePos.forEach(item -> {
                    if (null != item) {
                        if (NumberUtils.isPositive(creativePo.getIsProgrammatic()) && GeneralVersion.GENERAL_VERSION_V1.equals(finalUnitExtraPo.getGeneralVersion())) {
                            if (Integer.valueOf(ProgrammaticStatus.AUDIT_PASSED)
                                    .equals(finalProgrammaticCreativeDetailPoMap.getOrDefault(item.getMaterialId(), new LauProgrammaticCreativeDetailPo()).getBizStatus())) {
                                pubVideo(item.getAvid(), unitPo, finalUnitExtraPo);
                            }
                        } else {
                            pubVideo(item.getAvid(), unitPo, finalUnitExtraPo);
                        }
                    }
                }
        );

        //直播间
        LauSubjectPo lauSubject = buildLauSubjectById(unitPo.getSubjectId(), LauSubjectTypeEnum.LIVE_ROOM);
        if (null != lauSubject) {
            pubLive(lauSubject, unitPo, unitExtraPo);
        }
    }

    public void pubVideo(Long avid, LauUnitPo unitPo, LauUnitExtraPo unitExtraPo) {
        if (!NumberUtils.isPositive(avid)) {
            return;
        }
        BusinessContentProducerMessageBo messageBo = BusinessContentProducerMessageBo.builder()
                .contentId(avid)
                .accountId(unitPo.getAccountId())
                //是否原生。1：否，2：是
                .isOriginalAd(NumberUtils.isPositive(unitExtraPo.getIsBiliNative()) ? 2 : 1)
                .contentType(ContentTypeEnum.GAO_JIAN.getCode())
                .governType(GovernTypeEnum.ADD.getCode())
                .consumerTypes(CONSUMER_TYPES)
                .firstBusinessType(BUSINESS_FIRST_CATE_MAP.getOrDefault(unitPo.getBusinessDomain(), FirstBusinessTypeEnum.UNKNOWN).getCode())
                .messageTime(System.currentTimeMillis())
                .controlEndTime(buildControlEndTime(System.currentTimeMillis(), 1))
                .build();
        buildSecondBusinessType(avid, unitPo, messageBo);
        messageBo.setContentFrom(ADP_VERSION_CONTENTFROM_MAP.getOrDefault(unitPo.getAdpVersion(), ContentFromEnum.SAN_LIAN).getCode());
        buildAccInfo(messageBo);
        businessContentPub.pub(messageBo);
    }

    public void pubDynamic(Long dynamicId, LauUnitPo unitPo, LauUnitExtraPo unitExtraPo) {
        if (!NumberUtils.isPositive(dynamicId)) {
            return;
        }
        BusinessContentProducerMessageBo messageBo = BusinessContentProducerMessageBo.builder()
                .contentId(dynamicId)
                .accountId(unitPo.getAccountId())
                //是否原生。1：否，2：是
                .isOriginalAd(NumberUtils.isPositive(unitExtraPo.getIsBiliNative()) ? 2 : 1)
                .contentType(ContentTypeEnum.DONG_TAI.getCode())
                .governType(GovernTypeEnum.ADD.getCode())
                .contentFrom(ContentFromEnum.SAN_LIAN.getCode())
                .consumerTypes(CONSUMER_TYPES)
                .firstBusinessType(BUSINESS_FIRST_CATE_MAP.getOrDefault(unitPo.getBusinessDomain(), FirstBusinessTypeEnum.UNKNOWN).getCode())
                .secondBusinessType(SecondBusinessTypeEnum.SHANG_YE_QI_FEI.getCode())
                .messageTime(System.currentTimeMillis())
                .controlEndTime(buildControlEndTime(System.currentTimeMillis(), 1))
                .build();
        buildSecondBusinessType(0L, unitPo, messageBo);
        buildAccInfo(messageBo);
        messageBo.setContentFrom(ADP_VERSION_CONTENTFROM_MAP.getOrDefault(unitPo.getAdpVersion(), ContentFromEnum.SAN_LIAN).getCode());
        businessContentPub.pub(messageBo);
    }

    public void pubLive(LauSubjectPo lauSubject, LauUnitPo unitPo, LauUnitExtraPo unitExtraPo) {
        if (null == lauSubject) return;
        Long roomId = Long.parseLong(lauSubject.getMaterialId());
        if (!NumberUtils.isPositive(roomId)) {
            return;
        }
        BusinessContentProducerMessageBo messageBo = BusinessContentProducerMessageBo.builder()
                .contentId(roomId)
                .accountId(unitPo.getAccountId())
                //是否原生。1：否，2：是
                .isOriginalAd(NumberUtils.isPositive(unitExtraPo.getIsBiliNative()) ? 2 : 1)
                .contentType(ContentTypeEnum.ZHI_BO.getCode())
                .governType(GovernTypeEnum.ADD.getCode())
                .contentFrom(ContentFromEnum.SAN_LIAN.getCode())
                .consumerTypes(CONSUMER_TYPES)
                .firstBusinessType(BUSINESS_FIRST_CATE_MAP.getOrDefault(unitPo.getBusinessDomain(), FirstBusinessTypeEnum.UNKNOWN).getCode())
                .secondBusinessType(SecondBusinessTypeEnum.SHANG_YE_QI_FEI.getCode())
                .messageTime(System.currentTimeMillis())
                .controlEndTime(buildControlEndTime(System.currentTimeMillis(), 1))
                .build();
        buildLiveSecondBusinessType(unitPo, messageBo);
        buildAccInfo(messageBo);
        //直播情况下  是否敏感mid也要传
        messageBo.setIsSensitive(mainCommunityGovernServiceProxy.queryMidIsMainSensitive(lauSubject.getMid()) ? 2 : 1);
        messageBo.setControlPriority(mainCommunityGovernServiceProxy.queryMidIsMainSensitive(lauSubject.getMid()) ? ControlPriorityEnum.HIGH_PRIORITY.getCode() : ControlPriorityEnum.COMMON.getCode());
        messageBo.setMid(lauSubject.getMid());
        messageBo.setContentFrom(ADP_VERSION_CONTENTFROM_MAP.getOrDefault(unitPo.getAdpVersion(), ContentFromEnum.SAN_LIAN).getCode());
        businessContentPub.pub(messageBo);
    }

    private void buildSecondBusinessType(Long avid, LauUnitPo unitPo, BusinessContentProducerMessageBo messageBo) {
        SecondBusinessTypeEnum secondBusinessTypeEnum = SecondBusinessTypeEnum.UNKNOWN;
        if (FirstBusinessTypeEnum.BI_XUAN.getCode().equals(messageBo.getFirstBusinessType()) && NumberUtils.isPositive(avid)) {
            secondBusinessTypeEnum = cpmScvProxy.archiveExists(avid) ? SecondBusinessTypeEnum.BI_XUAN_XIAO_HAO : SecondBusinessTypeEnum.BI_XUAN_FEI_XIAO_HAO;
        }
        if (FirstBusinessTypeEnum.QI_FEI.getCode().equals(messageBo.getFirstBusinessType())) {
            secondBusinessTypeEnum = BUSINESS_SECOND_CATE_MAP.getOrDefault(unitPo.getBusinessDomain(), SecondBusinessTypeEnum.UNKNOWN);
        }
        messageBo.setSecondBusinessType(secondBusinessTypeEnum.getCode());
    }

    private void buildLiveSecondBusinessType(LauUnitPo unitPo, BusinessContentProducerMessageBo messageBo) {
        SecondBusinessTypeEnum secondBusinessTypeEnum = SecondBusinessTypeEnum.UNKNOWN;
        if (FirstBusinessTypeEnum.BI_XUAN.getCode().equals(messageBo.getFirstBusinessType())) {
            secondBusinessTypeEnum = SecondBusinessTypeEnum.BI_XUAN_FEI_XIAO_HAO;
        }
        if (FirstBusinessTypeEnum.QI_FEI.getCode().equals(messageBo.getFirstBusinessType())) {
            secondBusinessTypeEnum = BUSINESS_SECOND_CATE_MAP.getOrDefault(unitPo.getBusinessDomain(), SecondBusinessTypeEnum.UNKNOWN);
        }
        messageBo.setSecondBusinessType(secondBusinessTypeEnum.getCode());
    }

    public LauSubjectPo buildLauSubjectById(Integer subjectId, LauSubjectTypeEnum lauSubjectTypeEnum) {
        return ad.selectFrom(LAU_SUBJECT)
                .where(LAU_SUBJECT.ID.eq(subjectId)
                        .and(LAU_SUBJECT.TYPE.eq(lauSubjectTypeEnum.getCode())))
                .limit(1)
                .fetchOneInto(LauSubjectPo.class);
    }


    private String buildControlEndTime(Long ts, Integer year) {
        return new Timestamp(ts + 3600 * 1000L * 24 * 365 * year).toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private void buildAccInfo(BusinessContentProducerMessageBo messageBo) {

        AccountBaseReply accountBase = cpmAdAccountProxy.getAccountBase(messageBo.getAccountId());
        if (!accountBase.hasData()) {
            return;
        }
        AccountBase accountBaseData = accountBase.getData();
        // 老行业
        int commerceCategoryFirstId = accountBaseData.getCommerceCategoryFirstId();
        int commerceCategorySecondId = accountBaseData.getCommerceCategorySecondId();
        QueryAccountCategoryResp accountCategoryByIds = cpmAdAccountProxy.queryAccountCategoryByIds(commerceCategoryFirstId,
                commerceCategorySecondId);

        if (!CollectionUtils.isEmpty(accountCategoryByIds.getDataList())) {
            List<CategoryDto> dataList = accountCategoryByIds.getDataList();
            Map<Integer, CategoryDto> categoryDtoMap = dataList.stream().collect(Collectors.toMap(CategoryDto::getId, Function.identity()));
            messageBo.setCategoryFirstName(categoryDtoMap.getOrDefault(commerceCategoryFirstId, CategoryDto.getDefaultInstance()).getName());
            messageBo.setCategorySecondName(categoryDtoMap.getOrDefault(commerceCategorySecondId, CategoryDto.getDefaultInstance()).getName());
        }

        // 新行业
        int unitedFirstIndustryId = accountBaseData.getUnitedFirstIndustryId();
        int unitedSecondIndustryId = accountBaseData.getUnitedSecondIndustryId();
        int unitedThirdIndustryId = accountBaseData.getUnitedThirdIndustryId();
        QueryUnitIndustryResp queryUnitIndustryResp = cpmAdAccountProxy.queryUnitIndustryByIds(unitedFirstIndustryId, unitedSecondIndustryId, unitedThirdIndustryId);
        List<UnitIndustryDto> unitIndustryDtos = queryUnitIndustryResp.getDataList();
        if (!CollectionUtils.isEmpty(unitIndustryDtos)) {
            Map<Integer, String> unitIndustryIdAndNameMap = unitIndustryDtos.stream().collect(Collectors.toMap(t -> t.getId(), t -> t.getName(), (t1, t2) -> t2));
            messageBo.setUnitedFirstIndustry(unitIndustryIdAndNameMap.getOrDefault(unitedFirstIndustryId, ""));
            messageBo.setUnitedSecondIndustry(unitIndustryIdAndNameMap.getOrDefault(unitedSecondIndustryId, ""));
            messageBo.setUnitedThirdIndustry(unitIndustryIdAndNameMap.getOrDefault(unitedThirdIndustryId, ""));
        }

        messageBo.setIsInner(NumberUtils.isPositive(accountBaseData.getIsInner()) ? 2 : 1);

        int productId = accountBaseData.getProductId();
        QueryAccountProductResp accountProductResp = cpmAdAccountProxy.queryAccountProductByIds(productId);
        if (!CollectionUtils.isEmpty(accountProductResp.getDataList())) {
            messageBo.setProductName(accountProductResp.getDataList().get(0).getName());
            messageBo.setProductId((long) productId);
        }
    }


}
