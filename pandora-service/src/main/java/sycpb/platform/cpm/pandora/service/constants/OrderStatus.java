package sycpb.platform.cpm.pandora.service.constants;

import java.util.Objects;

public class OrderStatus {
    public static final int WAITING = 0;
    public static final int EXECUTING = 1;
    public static final int RAN_OUT_OF_TIME = 2;
    public static final int RAN_OUT_OF_BUDGET = 3;
    public static final int FAILED_FREEZING_BUDGET = 4;
    public static final int RENEWAL_FAILURE = 5;

    public static boolean isTerminated(Integer x) {
        return Objects.equals(x, RAN_OUT_OF_TIME) || Objects.equals(x, RAN_OUT_OF_BUDGET);
    }

    public static boolean isExecuting(Integer x) {
        return Objects.equals(x, EXECUTING);
    }

    public static boolean isWaiting(Integer x) {
        return Objects.equals(x, WAITING);
    }
}
