package sycpb.platform.cpm.pandora.service.proxy;


import com.bapis.ad.account.category.*;
import com.bapis.ad.account.crm.acc.AccountBaseReply;
import com.bapis.ad.account.crm.acc.AccountIdReq;
import com.bapis.ad.account.crm.acc.CrmAccountServiceGrpc;
import com.bapis.ad.account.product.AccountProductServiceGrpc;
import com.bapis.ad.account.product.QueryAccountProductReq;
import com.bapis.ad.account.product.QueryAccountProductResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CpmAdAccountProxy {
    @RPCClient("sycpb.cpm.ad-account")
    private CrmAccountServiceGrpc.CrmAccountServiceBlockingStub accountServiceBlockingStub;
    @RPCClient("sycpb.cpm.ad-account")
    private AccountProductServiceGrpc.AccountProductServiceBlockingStub accountProductServiceBlockingStub;
    @RPCClient("sycpb.cpm.ad-account")
    private AccountCategoryServiceGrpc.AccountCategoryServiceBlockingStub accountCategoryServiceBlockingStub;

    private static final Long Deadline = 1000L;


    public AccountBaseReply getAccountBase(Integer accountId) {
        return accountServiceBlockingStub
                .withDeadlineAfter(Deadline, TimeUnit.MILLISECONDS)
                .getAccountBase(AccountIdReq.newBuilder().setAccountId(accountId).build());
    }

    public QueryAccountCategoryResp queryAccountCategoryByIds(int commerceCategoryFirstId, int commerceCategorySecondId) {
        return accountCategoryServiceBlockingStub
                .withDeadlineAfter(Deadline, TimeUnit.MILLISECONDS)
                .queryAccountCategoryByIds(QueryAccountCategoryReq.newBuilder()
                .addCategoryIds(commerceCategoryFirstId)
                .addCategoryIds(commerceCategorySecondId)
                .build());

    }


    public QueryUnitIndustryResp queryUnitIndustryByIds(int unitedFirstIndustryId, int unitedSecondIndustryId, int unitedThirdIndustryId) {
        return accountCategoryServiceBlockingStub
                .withDeadlineAfter(Deadline, TimeUnit.MILLISECONDS)
                .queryUnitIndustryByIds(QueryUnitIndustryReq.newBuilder()
                .addIndustryIds(unitedFirstIndustryId)
                .addIndustryIds(unitedSecondIndustryId)
                .addIndustryIds(unitedThirdIndustryId)
                .build());
    }


    public QueryAccountProductResp queryAccountProductByIds(int productId) {
        return accountProductServiceBlockingStub
                .withDeadlineAfter(Deadline, TimeUnit.MILLISECONDS)
                .queryAccountProductByIds(QueryAccountProductReq.newBuilder().addProductIds(productId).build());
    }


}
