package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauSplashScreenCreativeBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Long id;
    private Integer accountId;
    private Integer campaignId;
    private Integer unitId;
    @CompareMeta(uk = true, comparable = false, logGroupId = true)
    private Integer creativeId;

    private Integer duration;
    private Integer isSkip;
    private Integer navigationType;
    private Integer interactType;
    private Float   skipButtonHeight;
    private Integer markWithSkipStyle;
    private Integer clickArea;

    @CompareMeta
    private String jumpImageUrl;
    @CompareMeta
    private String jumpImageMd5;
    @CompareMeta
    private String schemaImageUrl;
    @CompareMeta
    private String schemaImageMd5;
    @CompareMeta
    private String jumpUrl;
    @CompareMeta
    private String jumpGuideContent;
    @CompareMeta
    private String schemaUrl;
    @CompareMeta
    private String schemaGuideContent;
    @CompareMeta
    private String appPackageName;

}
