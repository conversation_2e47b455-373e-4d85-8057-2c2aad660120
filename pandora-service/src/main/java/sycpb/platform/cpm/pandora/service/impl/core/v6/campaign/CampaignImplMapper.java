package sycpb.platform.cpm.pandora.service.impl.core.v6.campaign;


import com.bapis.ad.pandora.resource.BudgetType;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauCampaignNextdayBudgetPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCampaignPo;
import sycpb.platform.cpm.pandora.infra.pandora_type.ValueWithDefaultValueMapper;
import sycpb.platform.cpm.pandora.infra.pandora_type.numeric.decimal.DecimalValue;
import sycpb.platform.cpm.pandora.service.api.core.v6.campaign.bos.LauCampaignBo;
import sycpb.platform.cpm.pandora.service.impl.core.batch.bos.LauCampaignNextdayBudgetBo;

import java.util.Objects;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = {ValueWithDefaultValueMapper.class})
public interface CampaignImplMapper {
    CampaignImplMapper MAPPER = Mappers.getMapper(CampaignImplMapper.class);

    @Mapping(target = "budget", expression = "java(CampaignImplMapper.toBudget(x))")
    LauCampaignBo fromPo(LauCampaignPo x);
    LauCampaignPo toPo(LauCampaignBo bo);

    LauCampaignNextdayBudgetPo toPo(LauCampaignNextdayBudgetBo x);

    static DecimalValue toBudget(LauCampaignPo x) {
        if (Objects.isNull(x.getBudgetLimitType()) || Objects.isNull(x.getBudget())) return null;

        if (Objects.equals(x.getBudgetLimitType(), BudgetType.BUDGET_UNLIMITED_VALUE)) {
            return ValueWithDefaultValueMapper.toUnLimitedBudget();
        }
        return ValueWithDefaultValueMapper.toDecimalDefaultZero(x.getBudget());
    }
}
