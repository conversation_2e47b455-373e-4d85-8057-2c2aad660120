package sycpb.platform.cpm.pandora.service.impl.core.v6.strategy.template_group;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.CreativeBo;
import sycpb.platform.cpm.pandora.service.constants.TemplateGroup;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;

import java.util.List;

@Component
@RequiredArgsConstructor
public class TemplateGroupLive implements ITemplateGroupHandleStrategy {
    @Override
    public int getTemplateGroupId() {
        return TemplateGroup.LIVE;
    }

    @Override
    public void process(SaveUnitCreativeContextBo ctx, CreativeBo creativeBo) {
        handleTitles(ctx, creativeBo);
        handleImages(ctx, creativeBo);

        creativeBo.setLauCreativeArchiveBos(List.of());
    }
}
