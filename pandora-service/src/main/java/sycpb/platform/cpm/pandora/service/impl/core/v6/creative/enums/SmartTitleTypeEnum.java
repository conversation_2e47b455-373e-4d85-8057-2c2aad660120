package sycpb.platform.cpm.pandora.service.impl.core.v6.creative.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import sycpb.platform.cpm.pandora.infra.err.PandoraDataErr;

import java.util.Objects;

/**
 * @ClassName SmartTitleTypeEnum
 * <AUTHOR>
 * @Date 2024/6/5 2:50 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@Getter
public enum SmartTitleTypeEnum {

    AREA(1, "智能地域", 4, "{智能地域}"),
    GENDER(2, "智能性别", 1, "{智能性别}"),
    DEVICE(3, "智能设备", 2, "{智能设备}"),
    AGE(4, "智能年龄", 2, "{智能年龄}"),
    ;

    public static SmartTitleTypeEnum getByCode(Integer code) {
        SmartTitleTypeEnum[] values = values();

        for(SmartTitleTypeEnum value : values) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }

        throw new PandoraDataErr("未知的智能标题类型");
    }

    private final Integer code;
    private final String name;
    // 校验长度时 默认占位符的对应真实长度
    private final Integer validLength;
    // 校验时,占位符内容
    private final String placeHolder;
}
