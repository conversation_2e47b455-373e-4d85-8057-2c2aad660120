package sycpb.platform.cpm.pandora.service.proxy;


import com.alibaba.fastjson.JSON;
import com.bapis.ad.cmc.gateway.common.BaseRequestInfo;
import com.bapis.ad.cmc.gateway.common.CallerInfo;
import com.bapis.ad.cmc.goods.CmcGoodsQueryReqV2;
import com.bapis.ad.cmc.goods.CmcGoodsResponseV2;
import com.bapis.ad.cmc.goods.CmcGoodsResult;
import com.bapis.ad.cmc.goods.GoodsGrpc;
import com.bapis.ad.cmc.launchresource.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;
import sycpb.platform.cpm.pandora.service.api.resource.goods.GoodsMapper;
import sycpb.platform.cpm.pandora.service.api.resource.goods.bos.*;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CpmTavernPlatformProxy {
    @RPCClient("sycpb.cpm.tavern-platform")
    private GoodsGrpc.GoodsBlockingStub goodsBlockingStub;

    @RPCClient("sycpb.cpm.tavern-platform")
    private LaunchResourceServiceGrpc.LaunchResourceServiceBlockingStub launchResourceServiceBlockingStub;

    private static final Long Deadline = 3000L;

    private static final Long Deadline2s = 2000L;

    public List<CidGoodsContentBo> queryCidGoods(QueryCreativeGoodsContentBo bo) {
        final var req = GoodsMapper.toCmcCidRpcBo(bo);
        final var resp = goodsBlockingStub
                .withDeadlineAfter(Deadline, TimeUnit.MILLISECONDS)
                .cidGoodsQuery(req);

        return resp.getResultList().stream()
                .map(resultBo -> GoodsMapper.MAPPER.fromRpcBo(resultBo, req.getContentTypeValue()))
                .collect(Collectors.toList());
    }

    public List<CmcGoodsContentBo> queryCmcGoods(QueryCreativeGoodsContentBo bo) {
        final var req = GoodsMapper.toCmcRpcBoV2(bo);
        final var resp = goodsBlockingStub
                .withDeadlineAfter(Deadline, TimeUnit.MILLISECONDS)
                .queryCmcGoodsV2(req);
        return resp.getResultList().stream()
                .map(GoodsMapper.MAPPER::fromRpcBo)
                .collect(Collectors.toList());
    }

    public List<CmcGoodsResult> getTopCommentFromGoods(List<Long> avids) {
        CmcGoodsQueryReqV2 req = CmcGoodsQueryReqV2.newBuilder()
                .addAllContentIds(avids)
                .setContentType(com.bapis.ad.cmc.goods.ContentType.ARCHIVE)
                .addPlaceTypesRequireGoodsDetail(com.bapis.ad.cmc.goods.PlaceType.COMMENT)
                .setSearchTopComment(1)
                .build();

        CmcGoodsResponseV2 response;

        try {
            response = goodsBlockingStub
                    .withDeadlineAfter(Deadline2s, TimeUnit.MILLISECONDS)
                    .queryCmcGoodsV2(req);
        } catch (Exception e) {
            log.error("getTopCommentFromGoods error, avids={}", JSON.toJSONString(avids), e);
            return new ArrayList<>();
        }
        return response.getResultList();
    }


    public GoodsDetailBo queryGoodsDetail(QueryGoodsDetailBo bo) {
        final var req = GoodsMapper.MAPPER.toRpcBo(bo);
        final var resp = goodsBlockingStub
                .withDeadlineAfter(200, TimeUnit.MILLISECONDS)
                .queryGoodsDetail(req);
        return GoodsMapper.MAPPER.fromRpcBo(resp.getGoods());
    }


    public LaunchResourceGoodsInfoQueryResp queryLaunchResourceGoodsInfo(List<Long> avids) {
        List<ContentInfo> contentInfos = avids.stream()
                .map(avid -> ContentInfo.newBuilder()
                        // 只返回框下的
                        .setPlaceType(PlaceType.RECOMMEND)
                        .setContentId(String.valueOf(avid))
                        .build())
                .collect(Collectors.toList());

        BaseRequestInfo baseRequestInfo = BaseRequestInfo.newBuilder().build();
        CallerInfo callerInfo = CallerInfo.newBuilder()
                .setCallerId("sycpb.cpm.cpm-pandora")
                .build();

        LaunchResourceGoodsInfoQueryReq goodsInfoQueryReq = LaunchResourceGoodsInfoQueryReq.newBuilder()
                .setReqInfo(baseRequestInfo)
                .setCallerInfo(callerInfo)
                .addAllResourceInfo(contentInfos)
                .build();

        return launchResourceServiceBlockingStub
                .withDeadlineAfter(Deadline2s, TimeUnit.MILLISECONDS)
                .queryLaunchResourceGoodsInfo(goodsInfoQueryReq);

    }

}
