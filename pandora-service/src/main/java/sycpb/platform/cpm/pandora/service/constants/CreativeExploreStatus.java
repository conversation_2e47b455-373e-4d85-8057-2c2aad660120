package sycpb.platform.cpm.pandora.service.constants;

import com.google.common.collect.Lists;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName CreativeExploreStatus
 * @<PERSON>
 * @Date 2025/4/23 11:09 下午
 * @Version 1.0
 **/
public class CreativeExploreStatus {

    public static final int UNKNOWN = 0;
    public static final int VALID = 1;
    public static final int PAUSED = 2;
    public static final int DELETED = 3;

    public static final List<Integer> VALID_OR_PAUSED_STATUS_LIST = Lists.newArrayList(VALID, PAUSED);

    public static Integer fromSupportExploreCreative(Integer supportExploreCreative) {
        if (Objects.isNull(supportExploreCreative)) {
            return null;
        }

        if (NumberUtils.isPositive(supportExploreCreative)) {
            return VALID;
        } else {
            return PAUSED;
        }
    }
}
