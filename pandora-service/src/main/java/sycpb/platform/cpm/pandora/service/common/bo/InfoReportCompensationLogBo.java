package sycpb.platform.cpm.pandora.service.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfoReportCompensationLogBo {

    public static final int operation_type_1 = 1; //出价

    public static final int operation_type_2 = 2; //基础定向

    public static final int operation_type_3 = 3; //优化目标记录

    public static final int operation_type_4 = 4; //出价方式

    public static final int operation_type_5 = 5; //单元预算


    public static final int operation_type_6 = 6; //单元状态变更（暂停/删除/续投）



    public static final int operation_type_7 = 7; //计划预算


    public static final int operation_type_8 = 8; //计划状态变更（暂停）


    private boolean needReport;


    //需要传的数值 ; 单元和计划传一个就行
    private String unitId;
    private String campaignId;

    private String operator_id;


    //type1（出价）是不是需要上报
    private boolean needTyp1Report;
    //type1（出价） 类型 的数据
    private Type1Data type1Data;


    //type2（基础定向）是不是需要上报
    private boolean needTyp2Report;


    //type5（预算）是不是需要上报
    private boolean needTyp5Report;
    //type5（预算） 类型 的数据
    private Type5Data type5Data;


    //type6 单元状态变更（暂停/删除/续投） 是不是需要上报
    private boolean needTyp6Report;

    //type7 计划预算  是不是需要上报
    private boolean needTyp7Report;
    private Type7Data type7Data;


    //type8 计划状态变更（暂停）是不是需要上报
    private boolean needTyp8Report;


    @lombok.Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Type1Data {
        // 操作类型
        private Integer operation_type;
        private Type1DataValue old_value;
        private Type1DataValue new_value;
        private String extra;

    }


    @lombok.Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Type1DataValue {

        @JsonProperty(value = "cost_price")
        private Long cost_price;

        @JsonProperty(value = "two_stage_bid")
        private Long two_stage_bid;

        @JsonProperty(value = "ocpx_target_two_bid")
        private Long ocpx_target_two_bid;

        @JsonProperty(value = "search_price_coefficient")
        private Integer search_price_coefficient;

        @JsonProperty(value = "search_first_price_coefficient")
        private Integer search_first_price_coefficient;

    }


    @lombok.Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Type5Data {
        // 操作类型
        private Integer operation_type;
        private Type5DataValue old_value;
        private Type5DataValue new_value;
        private String extra;

    }


    @lombok.Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Type5DataValue {

        @JsonProperty(value = "budget")
        private Long budget;

    }


    @lombok.Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Type7Data {
        // 操作类型
        private Integer operation_type;
        private Type7DataValue old_value;
        private Type7DataValue new_value;
        private String extra;
    }



    @lombok.Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Type7DataValue {

        @JsonProperty(value = "budget")
        private Long budget;

        @JsonProperty(value = "budget_limit_type")
        private Integer budget_limit_type;

        @JsonProperty(value = "budget_type")
        private Integer budget_type;


    }

}
