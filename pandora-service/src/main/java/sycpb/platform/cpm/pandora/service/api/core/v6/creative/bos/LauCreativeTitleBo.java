package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LauCreativeTitleBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Long      id;
    private Integer   unitId;
    @CompareMeta(logGroupId = true, comparable = false)
    private Integer   creativeId;
    @CompareMeta(logDescription = "创意标题", shadow = true)
    private String    rawTitle;
    @CompareMeta(shadow = true)
    private String    title;
    @CompareMeta(shadow = true)
    private String    materialMd5;
    @CompareMeta(shadow = true)
    private Long      materialId;
    @CompareMeta
    private Integer isAigcReplace;

    private List<LauCreativeSmartTitleBo> lauCreativeSmartTitleBos;
    private String materialUk;

    @CompareMeta(uk = true)
    public String uk() {
        return creativeId + "-" + materialId;
    }

    @CompareMeta(shadowKeyGetterMethod = true)
    public Integer getShadowKeyGetterMethod() {
        return creativeId;
    }
}
