package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos.LauCreativeExploreCustomizeConditionDao;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauCreativeExploreCustomizeConditionPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauUnitCreativeDao;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.CreativeBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.CreativeLevelUnitBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauUnitSceneBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore.CreativeExploreExtraInfoBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName CreativeExploreExtraConditionService
 * <AUTHOR>
 * @Date 2025/3/30 11:48 下午
 * @Version 1.0
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class CreativeExploreExtraConditionService {

    private final LauCreativeExploreCustomizeConditionDao lauCreativeExploreCustomizeConditionDao;

    @Resource(name = MySqlConfig.AD_DSL_CONTEXT)
    private DSLContext ad;

    public CreativeExploreExtraInfoBo fetch(Integer unitId) {
        Assert.isTrue(NumberUtils.isPositive(unitId), "单元id不可为空");

        List<LauCreativeExploreCustomizeConditionPo> pos = lauCreativeExploreCustomizeConditionDao.fetchByUnitId(unitId);
        return CollectionUtils.isEmpty(pos) ? null : UnitCreativeImplMapper.MAPPER.fromPo(pos.get(0));
    }

    public void save(SaveUnitCreativeContextBo ctx) {
        final var currentVersion = ctx.getCurrentVersion().getUnit().getCreativeExploreExtraCondition();
        final var newVersion = ctx.getNewVersion().getUnit().getCreativeExploreExtraCondition();

        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(CreativeExploreExtraInfoBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeExploreCustomizeConditionDao)
                .setOpLog(ctx.genUnitCompareConsumer());
        JooqFunctions.save(currentVersion, newVersion, ctx0);
    }



}
