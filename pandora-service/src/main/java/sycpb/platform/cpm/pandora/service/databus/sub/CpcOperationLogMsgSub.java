package sycpb.platform.cpm.pandora.service.databus.sub;

import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.IOperationLogService;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperationLogLongContextBo;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/26
 */
@Slf4j
@Service
public class CpcOperationLogMsgSub implements MessageListener{

    private final String topic;
    private final String group;

    @Lazy
    @Resource
    private IOperationLogService operationLogService;
    private final ObjectMapper objectMapper;

    public CpcOperationLogMsgSub(
            DatabusProperties databusProperties) {

        DatabusProperty databusProperty = databusProperties.getProperties().get("cpc_operation_log");
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        this.objectMapper = new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                // FIXME jackson-databind 存在2.11 和2.13的版本冲突避免使用PropertyNamingStrategies
                .setPropertyNamingStrategy(new SnakeCaseStrategy())
                .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    }
    public String topic() {
        return topic;
    }

    @Override
    public String group() {
        return group;
    }

    @Override
    public boolean autoCommit() {
        return true;
    }


    @Override
    public void onMessage(AckableMessage ackableMessage) {

        try {
            String value = new String(ackableMessage.payload());

            log.info("On cpc-operation-log databus message={}",value );

            List<OperationLogLongContextBo> logs = objectMapper.readValue(value,
                    new TypeReference<List<OperationLogLongContextBo>>() {
                    });

            if (CollectionUtils.isEmpty(logs)) {
                return;
            }

            operationLogService.saveForLong(logs);


        } catch (Exception e) {
            log.error("CpcOperationLogMsgSub error", e);
        }
    }



}
