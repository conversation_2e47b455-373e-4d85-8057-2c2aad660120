package sycpb.platform.cpm.pandora.service.constants;

import com.bapis.ad.pandora.resource.CpaTarget;
import com.google.common.collect.Sets;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;

import java.util.Set;

public class OcpxTarget {

    public static final Set<Integer> ROI_TARGET_SET =
            Sets.newHashSet(CpaTarget.CPA_PAID_IN_24H_ROI_VALUE,
                    CpaTarget.CPA_FIRST_DAY_ROI_VALUE,
                    CpaTarget.CPA_IN_APP_CHARGE_24H_ROI_VALUE,
                    CpaTarget.CPA_PAID_IN_7D_ROI_VALUE,
                    CpaTarget.CPA_GAME_CHARGE_IN_24H_MIX_VALUE);

    public static int getBidPrecision(Integer x) {
        if (!NumberUtils.isPositive(x)) return 0;

        if (ROI_TARGET_SET.contains(x)) return 4;

        return 2;
    }
}
