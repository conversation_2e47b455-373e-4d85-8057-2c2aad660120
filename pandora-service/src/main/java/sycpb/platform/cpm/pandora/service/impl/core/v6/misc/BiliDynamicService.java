package sycpb.platform.cpm.pandora.service.impl.core.v6.misc;

import com.alibaba.fastjson.JSON;
import com.bapis.account.service.relation.StatReply;
import com.bapis.cosmo.conn.common.OpusCard;
import com.bapis.dynamic.service.feed.DynSimpleInfoOption;
import com.bapis.dynamic.service.feed.DynSimpleInfosReq;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.common.http.HttpConstants;
import sycpb.platform.cpm.pandora.infra.common.http.HttpResponse;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeFlyDynamicInfoPo;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraRemoteAccessErr;
import sycpb.platform.cpm.pandora.infra.exception.BusinessException;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.config.DynamicProperties;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.bos.*;
import sycpb.platform.cpm.pandora.service.proxy.AccountServiceRelationProxy;
import sycpb.platform.cpm.pandora.service.proxy.MainDynamicFeedServiceProxy;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO;

@Service
@RequiredArgsConstructor
@Slf4j
public class BiliDynamicService {
    // 图文
    public static final int DYN_TYPE_PIC_AND_TXT = 2;
    // 文字
    public static final int DYN_TYPE_TXT = 4;
    // 稿件
    public static final int DYN_TYPE_ARCHIVE = 8;


    @Resource
    private MainDynamicFeedServiceProxy mainDynamicFeedServiceProxy;
    @Resource
    private AccountServiceRelationProxy accountServiceRelationProxy;

    @Resource(name = "okHttpClient")
    private OkHttpClient okHttpClient;
    private final ObjectMapper objectMapper;

    @Autowired
    private DynamicProperties dynamicProperties;

    @Resource(name = MySqlConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;

    public Map<Long, BiliDynamicBo> getMap(Collection<Long> dynamicIds, boolean includeStats) {
        if (CollectionUtils.isEmpty(dynamicIds)) {
            return Collections.emptyMap();
        }
        final var reqBuilder = DynSimpleInfosReq.newBuilder()
                .addAllDynIds(dynamicIds);
        if (includeStats) {
            reqBuilder.setOption(DynSimpleInfoOption.newBuilder()
                    .setStats(true)
                    .build());
        }
        final var resp = mainDynamicFeedServiceProxy.getDynSimpleInfosMap(dynamicIds, includeStats);
        return resp.keySet()
                .stream()
                .collect(Collectors.toMap(Function.identity(),
                        key -> MiscMapper.MAPPER.fromRo(resp.get(key))));
    }

    /**
     * 获取动态封面信息
     *
     * @param dynamicIds
     * @return
     */
    @SneakyThrows
    public Map<Long, BiliDynamicWithPicBo> getDynamicWithPic(List<Long> dynamicIds) {
        if (CollectionUtils.isEmpty(dynamicIds)) {
            return Collections.emptyMap();
        }

        final var dynamicReqBo = new DynamicReqBo();
        dynamicReqBo.setDyn_ids(dynamicIds);
        final var call = okHttpClient.newCall(new Request.Builder()
                .url(dynamicProperties.getManagerHost() + dynamicProperties.getDynContentsUrl())
                .post(RequestBody.create(HttpConstants.JSON, objectMapper.writeValueAsBytes(dynamicReqBo)))
                .build());
        try (var resp = call.execute()) {
            List<BiliDynamicWithPicBo> biliDynamicWithPicBos = extractFromResponseWithPic(resp);
            return biliDynamicWithPicBos.stream().collect(Collectors.toMap(BiliDynamicWithPicBo::getDyn_id, Function.identity()));
        }
    }

    @SneakyThrows
    public List<DynamicResponseBo> getDynamicInfoByAvIds(Collection<Long> avIds) {
        if (CollectionUtils.isEmpty(avIds)) return List.of();

        final var revBos = avIds.stream()
                .map(x -> {
                    final var bo = new DynamicReqBo.RevBo();
                    bo.setRid(x);
                    bo.setType(DYN_TYPE_ARCHIVE);
                    return bo;
                }).collect(Collectors.toList());
        final var dynamicReqBo = new DynamicReqBo();
        dynamicReqBo.setRevs(revBos);
        final var call = okHttpClient.newCall(new Request.Builder()
                .url(dynamicProperties.getManagerHost() + dynamicProperties.getFetchByIdUrl())
                .post(RequestBody.create(HttpConstants.JSON, objectMapper.writeValueAsBytes(dynamicReqBo)))
                .build());
        try (var resp = call.execute()) {
            return extractFromResponse(resp);
        }
    }

    @SneakyThrows
    public List<DynamicResponseBo> getDynamicInfoByIds(List<Long> dynamicIds) {
        String url = dynamicProperties.getManagerHost() + dynamicProperties.getFetchByIdUrl();
        DynamicReqBo reqBo = DynamicReqBo.builder()
                .dyn_ids(dynamicIds)
                .build();
        RequestBody body = RequestBody.create(HttpConstants.JSON, objectMapper.writeValueAsString(reqBo));
        Request req = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        Response response = okHttpClient.newCall(req).execute();
        return extractFromResponse(response);
    }

    @SneakyThrows
    public List<DynamicResponseBo> getDynamicInfo(DynamicReqBo reqBo) {
        String url = dynamicProperties.getManagerHost() + dynamicProperties.getFetchByIdUrl();
        RequestBody body = RequestBody.create(HttpConstants.JSON, objectMapper.writeValueAsString(reqBo));
        Request req = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        Response response = okHttpClient.newCall(req).execute();
        return extractFromResponse(response);
    }

    public Map<Long, OpusCardBo> fetchOpusCardMap(Collection<Long> dynamicIds) {
        final Map<Long, OpusCardBo> map = new HashMap<>();
        for (var subDynamicIds : Lists.partition(new ArrayList<>(dynamicIds), 20)) {
            final var resp = mainDynamicFeedServiceProxy.opusCard(subDynamicIds);
            for (OpusCard opsCard : resp.values()) {
                final var bo = new OpusCardBo();
                bo.setDynamicId(opsCard.getOpusId());
                bo.setMusicId(opsCard.getSummary().getMusicId());
                map.put(bo.getDynamicId(), bo);
            }
        }
        return map;
    }

    @SneakyThrows
    private List<DynamicResponseBo> extractFromResponse(Response resp) {
        if (Objects.isNull(resp.body())) {
            throw PandoraRemoteAccessErr.noBody();
        }
        try {
            final HttpResponse<DynamicResponseListBo> dynamicListBo = objectMapper.readValue(resp.body().bytes(),
                    new TypeReference<>() {
                    });
            if (Objects.isNull(dynamicListBo)) {
                return List.of();
            }
            if (Objects.isNull(dynamicListBo.getData())) {
                return List.of();
            }
            if (CollectionUtils.isEmpty(dynamicListBo.getData().getDyn_infos())) {
                return List.of();
            }
            return dynamicListBo.getData().getDyn_infos();
        } catch (Exception e) {
            log.error("调用动态服务，返回数据异常", e);
            throw new BusinessException(ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR, "调用动态服务返回数据异常");
        }
    }


    @SneakyThrows
    private List<BiliDynamicWithPicBo> extractFromResponseWithPic(Response resp) {
        if (Objects.isNull(resp.body())) {
            throw PandoraRemoteAccessErr.noBody();
        }
        try {
            final HttpResponse<DynamicResponseWithPicListBo> dynamicListBo = objectMapper.readValue(resp.body().bytes(),
                    new TypeReference<>() {
                    });
            if (Objects.isNull(dynamicListBo)) {
                return List.of();
            }
            if (Objects.isNull(dynamicListBo.getData())) {
                return List.of();
            }
            if (CollectionUtils.isEmpty(dynamicListBo.getData().getContents())) {
                return List.of();
            }
            return dynamicListBo.getData().getContents().values().stream().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("调用动态服务，返回数据异常", e);
            throw new BusinessException(ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR, "调用动态服务返回数据异常");
        }
    }

    public void updateJointVideo(LauCreativeFlyDynamicInfoPo insertPo) {
        if (!NumberUtils.isPositive(insertPo.getDynamicId())) {
            updateSecondShowMidToDefault(insertPo);
            return;
        }

        List<DynamicResponseBo> dynamicResponseBos = getDynamicInfo(DynamicReqBo.builder()
                .need_shadow_info(true)
                .dyn_ids(Lists.newArrayList(insertPo.getDynamicId()))
                .build());
        if (CollectionUtils.isEmpty(dynamicResponseBos)) {
            updateSecondShowMidToDefault(insertPo);
            return;
        }

        DynamicResponseBo dynamicResponseBo = dynamicResponseBos.get(0);
        ShadowInfoBo shadowInfo = dynamicResponseBo.getShadow_info();
        if (null == shadowInfo || CollectionUtils.isEmpty(shadowInfo.getShadow_dyn_info())) {
            updateSecondShowMidToDefault(insertPo);
            return;
        }
        //非当前动态mid
        Long secondShowMid = 0L;
        if (!CollectionUtils.isEmpty(shadowInfo.getShadow_dyn_info()) && 1 == shadowInfo.getShadow_dyn_info().size()) {
            secondShowMid = shadowInfo.getShadow_dyn_info().get(0).getUid();
        }

        //多人联合投稿 取粉丝最少的展示
        if (shadowInfo.getShadow_dyn_info().size() > 1) {
            List<Long> jointVideoMids = shadowInfo.getShadow_dyn_info().stream().map(ShadowInfoItemBo::getUid).collect(Collectors.toList());
            Map<Long, StatReply> midFans = accountServiceRelationProxy.queryMidFans(jointVideoMids);
            List<StatReply> sortedFanList = midFans.values().stream().sorted(comparing(StatReply::getFollower)).collect(Collectors.toList());
            secondShowMid = sortedFanList.get(0).getMid();
        }
        adCore.update(LAU_CREATIVE_FLY_DYNAMIC_INFO)
                .set(LAU_CREATIVE_FLY_DYNAMIC_INFO.SECOND_SHOW_MID, secondShowMid)
                .set(LAU_CREATIVE_FLY_DYNAMIC_INFO.SHADOW_INFO, JSON.toJSONString(shadowInfo))
                .where(LAU_CREATIVE_FLY_DYNAMIC_INFO.ID.eq(insertPo.getId())
                        .and(LAU_CREATIVE_FLY_DYNAMIC_INFO.DYNAMIC_ID.eq(insertPo.getDynamicId()))
                ).execute();

    }

    private void updateSecondShowMidToDefault(LauCreativeFlyDynamicInfoPo insertPo) {
        adCore.update(LAU_CREATIVE_FLY_DYNAMIC_INFO)
                .set(LAU_CREATIVE_FLY_DYNAMIC_INFO.SECOND_SHOW_MID, 0L)
                .set(LAU_CREATIVE_FLY_DYNAMIC_INFO.SHADOW_INFO, "")
                .where(LAU_CREATIVE_FLY_DYNAMIC_INFO.ID.eq(insertPo.getId())
                        .and(LAU_CREATIVE_FLY_DYNAMIC_INFO.DYNAMIC_ID.eq(insertPo.getDynamicId()))
                ).execute();
    }
}
