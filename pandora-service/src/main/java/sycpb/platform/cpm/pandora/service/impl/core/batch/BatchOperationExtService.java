package sycpb.platform.cpm.pandora.service.impl.core.batch;

import com.bapis.ad.pandora.core.batch.BatchOperationType;
import com.bapis.ad.pandora.resource.AreaType;
import com.bapis.ad.pandora.resource.SpeedMode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.api.env.IEnvService;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos.LauBatchOperationDao;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos.LauBatchOperationDetailDao;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauBatchOperationPo;
import sycpb.platform.cpm.pandora.infra.pagination.Pagination;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.GetBatchOperationReqBo;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.LauBatchOperationBo;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.LauBatchOperationDetailBo;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.ListBatchOperationReqBo;
import sycpb.platform.cpm.pandora.service.api.resource.target.IResTargetItemService;
import sycpb.platform.cpm.pandora.service.api.resource.target.bos.ResTargetItemBo;
import sycpb.platform.cpm.pandora.service.constants.BatchOperationDetailStatus;
import sycpb.platform.cpm.pandora.service.constants.BatchOperationStatus;
import sycpb.platform.cpm.pandora.service.constants.BatchTargetType;
import sycpb.platform.cpm.pandora.service.constants.OperatorTypeConstants;
import sycpb.platform.cpm.pandora.service.databus.pub.LauBatchOperationDetailEventPub;
import sycpb.platform.cpm.pandora.service.impl.core.v6.unit.enums.DeviceAppStoreEnum;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.bapis.ad.pandora.core.batch.BatchOperationType.*;
import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauBatchOperation.LAU_BATCH_OPERATION;
import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauBatchOperationDetail.LAU_BATCH_OPERATION_DETAIL;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCampaign.LAU_CAMPAIGN;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnit.LAU_UNIT;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitCreative.LAU_UNIT_CREATIVE;

@Slf4j
@Service
@RequiredArgsConstructor
public class BatchOperationExtService {
    private final ObjectMapper objectMapper;
    private final LauBatchOperationDao lauBatchOperationDao;
    private final LauBatchOperationDetailDao lauBatchOperationDetailDao;
    private final ApplicationContext applicationContext;
    private final IResTargetItemService resTargetItemService;
    private final LauBatchOperationDetailEventPub lauBatchOperationDetailEventPub;

    @Resource(name = MySqlConfig.AD_DSL_CONTEXT)
    private DSLContext ad;

    @Resource(name = MySqlConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;


    public void updateFailedOperationDetail(LauBatchOperationDetailBo bo, String reason) {
        updateOperationDetail(bo, BatchOperationDetailStatus.FAILED, null, null, reason);
    }

    public void updateSucceededOperationDetail(LauBatchOperationDetailBo bo, String valueBefore, String valueAfter) {
        updateOperationDetail(bo, BatchOperationDetailStatus.SUCCEEDED, valueBefore, valueAfter, null);
    }

    public void saveBatchOperationDetail(List<LauBatchOperationDetailBo> bos) {
        if (!CollectionUtils.isEmpty(bos)) {
            lauBatchOperationDetailEventPub.pub(bos);
        }
    }

    public void insertBatchOperationDetail(List<LauBatchOperationDetailBo> bos) {
        if (!CollectionUtils.isEmpty(bos)) {
            final var pos = BatchImplMapper.MAPPER.toLauBatchOperationByTargetPos(bos);
            lauBatchOperationDetailDao.insert(pos);
        }
    }


    public LauBatchOperationBo getBatchOperation(Long operationId) {
        final var pos = lauBatchOperationDao.fetchByOperationId(operationId);
        if (CollectionUtils.isEmpty(pos)) return null;

        return BatchImplMapper.MAPPER.fromPo(pos.get(0));
    }

    public Pagination<LauBatchOperationBo> listBatchOperation(ListBatchOperationReqBo reqBo) {
        var condition = DSL.noCondition();
        if (NumberUtils.isPositive(reqBo.getOperationId())) {
            condition = condition.and(LAU_BATCH_OPERATION.OPERATION_ID.eq(reqBo.getOperationId()));
        } else if (!NumberUtils.isPositive(reqBo.getAccountId())) {
            return Pagination.emptyInstance();
        } else {
            condition = condition.and(LAU_BATCH_OPERATION.ACCOUNT_ID.eq(reqBo.getAccountId()));
        }
        if (!CollectionUtils.isEmpty(reqBo.getOperationStatus())) {
            condition = condition.and(LAU_BATCH_OPERATION.OPERATION_STATUS.in(reqBo.getOperationStatus()));
        }
        if (!CollectionUtils.isEmpty(reqBo.getOperationTargetType())) {
            condition = condition.and(LAU_BATCH_OPERATION.TARGET_TYPE.in(reqBo.getOperationTargetType()));
        }
        if (!CollectionUtils.isEmpty(reqBo.getOperationType())) {
            condition = condition.and(LAU_BATCH_OPERATION.OPERATION_TYPE.in(reqBo.getOperationType()));
        }
        condition = condition.and(LAU_BATCH_OPERATION.OPERATOR_TYPE.in(OperatorTypeConstants.OPERATOR_TYPE_LIST_WITHOUT_AUTO_SYSTEM));
        final var count = ad.selectCount()
                .from(LAU_BATCH_OPERATION)
                .where(condition)
                .fetchOne()
                .value1();
        if (!NumberUtils.isPositive(count)) {
            return Pagination.emptyInstance();
        }
        final var pos = ad.selectFrom(LAU_BATCH_OPERATION)
                .where(condition)
                .orderBy(LAU_BATCH_OPERATION.CTIME.desc())
                .offset((reqBo.getPageNo() - 1) * reqBo.getPageSize())
                .limit(reqBo.getPageSize())
                .fetch()
                .into(LauBatchOperationPo.class);
        final var lauBatchOperationBos = BatchImplMapper.MAPPER.toLauBatchOperationBos(pos)
                .stream()
                .sorted(Comparator.comparing(x -> -x.getOperationRegisterTime()))
                .collect(Collectors.toList());
        return new Pagination<>(lauBatchOperationBos, count);
    }

    public List<LauBatchOperationDetailBo> listBatchOperationDetails(Long operationId) {


        if (!NumberUtils.isPositive(operationId)) return List.of();

        final var lauBatchOperationBo = getBatchOperation(operationId);
        if (Objects.isNull(lauBatchOperationBo)) return List.of();

        final var pos = lauBatchOperationDetailDao.fetchByOperationId(operationId);
        if (CollectionUtils.isEmpty(pos)) return List.of();

        final var bos = BatchImplMapper.MAPPER.toLauBatchOperationDetailBos(pos);
        final List<LauBatchOperationDetailBo> unmarshalledBos = new ArrayList<>();
        final var targetDescMap = fetchTargetDescMap(lauBatchOperationBo.getTargetType(), bos.stream()
                .map(LauBatchOperationDetailBo::getTargetId)
                .collect(Collectors.toList()));
        for (LauBatchOperationDetailBo bo : bos) {
            try {
                unmarshalledBos.add(bo);
                bo.setTargetDesc(targetDescMap.getOrDefault(bo.getTargetId(), ""));
                if (!StringUtils.hasText(bo.getOperationExt())) continue;

                LauBatchOperationDetailBo.ExtBo extBo = objectMapper.readValue(bo.getOperationExt(), LauBatchOperationDetailBo.ExtBo.class);
                bo.setExtBo(extBo);
            } catch (Throwable t) {
                log.error("failed to unmarshal batch operation detail: target_id = {}", bo.getTargetId());
            }
        }

        try {
            warpper(lauBatchOperationBo.getOperationType(), unmarshalledBos);
        } catch (Exception e) {
            log.error("listBatchOperationDetails warpper err", e);
        }

        return unmarshalledBos.stream()
                .sorted(Comparator.comparing(LauBatchOperationDetailBo::getTargetId))
                .collect(Collectors.toList());
    }

    public void setOperationStatus(Integer accountId, Long operationId, Integer status) {
        var condition = LAU_BATCH_OPERATION.OPERATION_ID.eq(operationId);
        if (Objects.nonNull(accountId)) {
            condition = condition.and(LAU_BATCH_OPERATION.ACCOUNT_ID.eq(accountId));
        }
        ad.update(LAU_BATCH_OPERATION)
                .set(LAU_BATCH_OPERATION.OPERATION_STATUS, status)
                .where(condition)
                .execute();
    }

    public LauBatchOperationBo fetchNext(GetBatchOperationReqBo reqBo) {
        final var env = IEnvService.encodeEnv(IEnvService.getPandoraExecutorEnv(applicationContext.getEnvironment()));
        var condition = LAU_BATCH_OPERATION.OPERATION_ENV.eq(env)
                .and(LAU_BATCH_OPERATION.OPERATION_STATUS.in(BatchOperationStatus.WAITING));
        if (NumberUtils.isPositive(reqBo.getOperationId())) {
            condition = condition.and(LAU_BATCH_OPERATION.OPERATION_ID.eq(reqBo.getOperationId()));
        }
        if (!CollectionUtils.isEmpty(reqBo.getExcludedAccountIds())) {
            condition = condition.and(LAU_BATCH_OPERATION.ACCOUNT_ID.notIn(reqBo.getExcludedAccountIds()));
        }
        final var po = ad.selectFrom(LAU_BATCH_OPERATION)
                .where(condition)
                .orderBy(LAU_BATCH_OPERATION.CTIME.asc())
                .fetchAnyInto(LauBatchOperationPo.class);
        return BatchImplMapper.MAPPER.fromPo(po);
    }

    public void cleanUpBatchOperationDetail(Long operationId) {
        final var condition = LAU_BATCH_OPERATION_DETAIL.OPERATION_ID.eq(operationId);
        final var count = ad.selectCount()
                .from(LAU_BATCH_OPERATION_DETAIL)
                .where(condition)
                .fetchOneInto(Integer.class);
        if (NumberUtils.isPositive(count)) {
            JooqFunctions.safeDelete(ad, LAU_BATCH_OPERATION_DETAIL, condition);
        }
    }

    @SneakyThrows
    private void updateOperationDetail(LauBatchOperationDetailBo bo, Integer operationDetailStatus, String valueBefore, String valueAfter, String failureReason) {
        bo.setOperationStatus(operationDetailStatus);
        bo.getExtBo().setTime(System.currentTimeMillis());
        bo.getExtBo().setReason(failureReason);
        bo.getExtBo().setBefore(valueBefore);
        bo.getExtBo().setAfter(valueAfter);
        bo.setOperationExt(objectMapper.writeValueAsString(bo.getExtBo()));
    }

    private Map<Integer, String> fetchTargetDescMap(Integer targetType, Collection<Integer> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) return Map.of();

        switch (targetType) {
            case BatchTargetType.CAMPAIGN:
                return adCore.select(LAU_CAMPAIGN.CAMPAIGN_ID, LAU_CAMPAIGN.CAMPAIGN_NAME)
                        .from(LAU_CAMPAIGN)
                        .where(LAU_CAMPAIGN.CAMPAIGN_ID.in(targetIds))
                        .fetchMap(LAU_CAMPAIGN.CAMPAIGN_ID, LAU_CAMPAIGN.CAMPAIGN_NAME);
            case BatchTargetType.UNIT:
                return adCore.select(LAU_UNIT.UNIT_ID, LAU_UNIT.UNIT_NAME)
                        .from(LAU_UNIT)
                        .where(LAU_UNIT.UNIT_ID.in(targetIds))
                        .fetchMap(LAU_UNIT.UNIT_ID, LAU_UNIT.UNIT_NAME);
            case BatchTargetType.CREATIVE:
                return adCore.select(LAU_UNIT_CREATIVE.CREATIVE_ID, LAU_UNIT_CREATIVE.TITLE)
                        .from(LAU_UNIT_CREATIVE)
                        .where(LAU_UNIT_CREATIVE.CREATIVE_ID.in(targetIds))
                        .fetchMap(LAU_UNIT_CREATIVE.CREATIVE_ID, LAU_UNIT_CREATIVE.TITLE);

            case BatchTargetType.COMMENT_COMPONENT:
            case BatchTargetType.ANCHOR:
                return Collections.emptyMap();
            default:
                throw new IllegalArgumentException("无法识别批量操作目标类型: " + targetType);
        }
    }


    public static List<BatchOperationType> needTargetItemRootsList = Arrays.asList(BATCH_UPDATE_GENDER_TARGET, BATCH_UPDATE_AGE_TARGET);

    private void warpper(Integer operationTypeNum, List<LauBatchOperationDetailBo> bos) {
        final var operationType = BatchOperationType.forNumber(operationTypeNum);

        Map<Integer, String> itemNameMap;
        if (needTargetItemRootsList.contains(operationType)) {
            Set<Integer> totalItemIdSet = new HashSet<>();

            bos.forEach(t -> {
                totalItemIdSet.addAll(invokeSets(t.getExtBo().getBefore()));
                totalItemIdSet.addAll(invokeSets(t.getExtBo().getAfter()));
            });

            Set<Integer> totalItemIds = totalItemIdSet.stream().filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Integer, List<ResTargetItemBo>> targetItemRootsMap = resTargetItemService.getValidRootItemsMap(totalItemIds);
            itemNameMap = targetItemRootsMap.values().stream()
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(ResTargetItemBo::getId, ResTargetItemBo::getName));

            bos.forEach(t -> {
                if (Objects.nonNull(t.getExtBo().getAfter())) {
                    Set<Integer> nums = invokeSets(t.getExtBo().getAfter());
                    t.getExtBo().setAfter(replaceStr(t.getExtBo().getAfter(), nums, itemNameMap));
                }
                if (Objects.nonNull(t.getExtBo().getBefore())) {
                    Set<Integer> nums = invokeSets(t.getExtBo().getBefore());
                    t.getExtBo().setBefore(replaceStr(t.getExtBo().getBefore(), nums, itemNameMap));
                }
            });

        } else if (operationType.equals(BATCH_UPDATE_AREA_TARGET)) {
            Set<Integer> totalItemIdSet = new HashSet<>();

            bos.forEach(t -> {
                totalItemIdSet.addAll(invokeSetsArea(t.getExtBo().getBefore()));
                totalItemIdSet.addAll(invokeSetsArea(t.getExtBo().getAfter()));
            });

            Set<Integer> totalItemIds = totalItemIdSet.stream().filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Integer, List<ResTargetItemBo>> targetItemRootsMap = resTargetItemService.getValidRootItemsMap(totalItemIds);
            itemNameMap = targetItemRootsMap.values().stream()
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(ResTargetItemBo::getId, ResTargetItemBo::getName));

            bos.forEach(t -> {
                if (Objects.nonNull(t.getExtBo().getAfter())) {
                    t.getExtBo().setAfter(replaceAreaStr(t.getExtBo().getAfter(), itemNameMap));
                }
                if (Objects.nonNull(t.getExtBo().getBefore())) {
                    t.getExtBo().setBefore(replaceAreaStr(t.getExtBo().getBefore(), itemNameMap));
                }
            });

        } else if (operationType.equals(BATCH_UPDATE_PRICE_COEFFICIENT)) {
            bos.forEach(t -> {
                if (Objects.nonNull(t.getExtBo().getAfter())) {
                    t.getExtBo().setAfter(fromFenToYuan(new BigDecimal(Integer.parseInt(t.getExtBo().getAfter()))).toString());
                }
                if (Objects.nonNull(t.getExtBo().getBefore())) {
                    t.getExtBo().setBefore(fromFenToYuan(new BigDecimal(Integer.parseInt(t.getExtBo().getBefore()))).toString());
                }
            });

        } else if (operationType.equals(BATCH_UPDATE_SPEED_MODE)) {
            bos.forEach(t -> {
                if (Objects.nonNull(t.getExtBo().getAfter())) {
                    t.getExtBo().setAfter(speedModeDesc(Integer.parseInt(t.getExtBo().getAfter())));
                }
                if (Objects.nonNull(t.getExtBo().getBefore())) {
                    t.getExtBo().setBefore(speedModeDesc(Integer.parseInt(t.getExtBo().getBefore())));
                }
            });
        } else if (operationType.equals(BATCH_UPDATE_STORE_DIRECT_LAUNCH)) {
            bos.forEach(t -> {
                if (Objects.nonNull(t.getExtBo().getAfter())) {
                    t.getExtBo().setAfter(warpperStoreDirect(t.getExtBo().getAfter()));
                }
                if (Objects.nonNull(t.getExtBo().getBefore())) {
                    t.getExtBo().setBefore(warpperStoreDirect(t.getExtBo().getBefore()));
                }
            });

        }
    }

    private String warpperStoreDirect(String str) {
        List<String> list = Arrays.stream(str.split(",")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return str;
        }

        if (list.size() == 1 && StringUtils.isEmpty(list.get(0))) {
            return str;
        }


        StringBuilder result = new StringBuilder();
        for (String num : list) {
            result.append(DeviceAppStoreEnum.getAppStoreValue(num)).append(",");
        }


        return result.length() == 0 ? result.toString() : result.toString().substring(0, result.length() - 1);
    }


    private String replaceAreaStr(String str, Map<Integer, String> itemNameMap) {

        List<String> areaStr = Arrays.stream(str.split(";")).collect(Collectors.toList());
        String result = replaceStr(areaStr.get(0), invokeSets(areaStr.get(0)), itemNameMap) + replaceStr(areaStr.get(1), invokeSets(areaStr.get(1)), itemNameMap);
        String str2 = areaStr.get(2);
        if (str2.contains(":")) {
            Integer areaTy = Integer.valueOf(str2.substring(str2.indexOf(":") + 1).trim());
            str2 = str2.replaceAll(String.valueOf(areaTy), areaTypeDesc(areaTy));
        }
        return result + str2;
    }


    private String replaceStr(String str, Set<Integer> nums, Map<Integer, String> itemNameMap) {
        List<Integer> sorted = nums.stream().sorted(Collections.reverseOrder()).collect(Collectors.toList());
        for (Integer num : sorted) {
            log.info("itemNameMap.get(num)num={},nums= {}", num, itemNameMap.get(num));
            str = str.replaceAll(String.valueOf(num), itemNameMap.get(num));
        }
        return str;
    }


    private Set<Integer> invokeSetsArea(String str) {
        List<String> areaStr = Arrays.stream(str.split(";")).collect(Collectors.toList());

        Set<Integer> result = invokeSets(areaStr.get(0));
        result.addAll(invokeSets(areaStr.get(1)));

        return result;
    }


    private Set<Integer> invokeSets(String str) {
        str = str.replaceAll("\\[|\\]", "").replaceAll(" ", "");
        if (str.contains(":")) {
            str = str.substring(str.indexOf(":") + 1);
        }
        return Arrays.stream(str.split(",")).filter(t -> !StringUtils.isEmpty(t)).mapToInt(t -> Integer.parseInt(t.trim())).boxed().collect(Collectors.toSet());
    }


    private String areaTypeDesc(Integer x) {
        final var type = AreaType.forNumber(x);
        //0-全部，1-实时在此的用户，2-常住在此的用户，3-旅游在此的用户
        switch (type) {
            case ALL:
                return "全部";
            case CURRENT:
                return "实时在此的用户";
            case PERMANENT:
                return "常住在此的用户";
            case TRIP:
                return "旅游在此的用户";
            default:
                return "未知";
        }
    }


    private String speedModeDesc(Integer x) {
        final var speedMode = SpeedMode.forNumber(x);
        if (Objects.isNull(speedMode)) return "未知";
        switch (speedMode) {
            case SPEED_NORMAL:
                return "匀速";
            case SPEED_ACCELERATED:
                return "加速";
            default:
                return "未知";
        }
    }

    public static final BigDecimal HUNDRED = new BigDecimal(100);

    private static BigDecimal fromFenToYuan(final BigDecimal fen) {
        return null == fen ? BigDecimal.ZERO : fen.divide(HUNDRED, 2, RoundingMode.HALF_UP);
    }
}
