package sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauUnitGameBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Integer   id;
    private Integer   accountId;
    private Integer   unitId;
    @CompareMeta(logDescription = "游戏中心id")
    private Integer   gameBaseId;
    @CompareMeta(skipLog = true)
    private Integer   platformType;
    @CompareMeta(logDescription = "广告分包")
    private Integer   subPkg;
}
