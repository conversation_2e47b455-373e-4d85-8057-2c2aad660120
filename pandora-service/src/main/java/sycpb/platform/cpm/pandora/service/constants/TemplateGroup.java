package sycpb.platform.cpm.pandora.service.constants;

import sycpb.platform.cpm.pandora.infra.common.functions.CommonFunctions;

import java.util.List;
import java.util.Objects;

/**
 * 模板组ID：对应excel模版组配置
 */
public class TemplateGroup {
    public static final int IMAGE = 1; // 图片
    public static final int BANNER_IMAGE = 2; // 通栏图片
    public static final int BILI_ARCHIVE = 3; // 稿件
    public static final int CM_ARCHIVE = 4; // 视频
    public static final int DYNAMIC = 5; // 动态
    public static final int LIVE = 6; // 直播
    public static final int SPLASH_SCREEN = 7; // 闪屏图片
    public static final int GAME_CARD = 8; // 游戏卡

    public static final List<Integer> NATIVE_TEMPLATE_GROUPS = List.of(BILI_ARCHIVE, DYNAMIC, LIVE, IMAGE);

    public static boolean canCopy(Integer x) {
        return Objects.equals(x, IMAGE) || Objects.equals(x, BILI_ARCHIVE) || Objects.equals(x, BANNER_IMAGE);
    }

    public static boolean isImage(Integer x) {
        return CommonFunctions.in(x, IMAGE, BANNER_IMAGE);
    }

    public static boolean isSplashScreen(Integer x) {
        return Objects.equals(x, SPLASH_SCREEN);
    }

    public static boolean isBiliArchive(Integer x) {
        return Objects.equals(x, BILI_ARCHIVE);
    }

    public static boolean isCmArchive(Integer x) {
        return Objects.equals(x, CM_ARCHIVE);
    }

    public static boolean isDynamic(Integer x) {
        return Objects.equals(x, DYNAMIC);
    }

    public static boolean isLive(Integer x) {return Objects.equals(x, LIVE);}

    public static boolean isBannerImage(Integer x) {return Objects.equals(x, BANNER_IMAGE);}

    public static boolean isGameCard(Integer x) {
        return Objects.equals(x, GAME_CARD);
    }
}
