package sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.unit;

import com.bapis.ad.pandora.resource.AdType;
import com.bapis.ad.pandora.resource.BaseTarget;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.TableField;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.compare.OperationType;
import sycpb.platform.cpm.pandora.infra.compare.bos.ChangeLogBo;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCampaignPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitRecord;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.BidOperationBo;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.OperationBo;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.IOperationLogService;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperationLogContextBo;
import sycpb.platform.cpm.pandora.service.api.resource.ConfigurableResourceQueryBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.UnitBidBo;
import sycpb.platform.cpm.pandora.service.api.resource.unit.IResUnitService;
import sycpb.platform.cpm.pandora.service.common.InfoReportService;
import sycpb.platform.cpm.pandora.service.common.bo.InfoReportCompensationLogBo;
import sycpb.platform.cpm.pandora.service.constants.CommonConstants;
import sycpb.platform.cpm.pandora.service.constants.OcpxMode;
import sycpb.platform.cpm.pandora.service.constants.OcpxTarget;
import sycpb.platform.cpm.pandora.service.constants.OperatorTypeConstants;
import sycpb.platform.cpm.pandora.service.impl.core.batch.BatchBidType;
import sycpb.platform.cpm.pandora.service.impl.core.batch.BatchOperationExtService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.unit.UnitImplMapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCampaign.LAU_CAMPAIGN;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnit.LAU_UNIT;

@Slf4j
@Service
public class UnitBidHandler extends UnitTemplateHandler {
    private final IResUnitService resUnitService;
    private final InfoReportService infoReportService;

    public UnitBidHandler(IOperationLogService operationLogService,
                          BatchOperationExtService batchOperationExtService,
                          @Qualifier(MySqlConfig.AD_CORE_DSL_CONTEXT) DSLContext adCore,
                          IResUnitService resUnitService,
                          InfoReportService infoReportService) {
        super(operationLogService, batchOperationExtService, adCore);
        this.resUnitService = resUnitService;
        this.infoReportService = infoReportService;
    }

    @Override
    public List<OperationBo> reGroupOperations(final UnitContext ctx) {
        final Map<String, OperationBo> map = new HashMap<>();
        for (final var operationBo : ctx.getOperationBos()) {
            final var bidBo = operationBo.getBidGrey();
            final var bidType = bidBo.getBidType();
            final var bid = bidBo.getBid();
            for (final var targetId : operationBo.getTargetIds()) {
                final var lauUnitPo = ctx.getUnitMap().get(targetId);
                // 不存在就先跳过, 后续会有校验
                if (Objects.isNull(lauUnitPo)) continue;

                final var validationLauUnitPo = UnitImplMapper.MAPPER.copyPo(lauUnitPo);
                ctx.getValidationUnitMap().put(targetId, validationLauUnitPo);
                final int scale;
                final BiConsumer<LauUnitPo, Integer> applyBidValueFunc;
                final Integer curBidValue;
                final var changeLogBuilder = ChangeLogBo.builder();
                final TableField<LauUnitRecord, Integer> tableField;
                switch (bidType) {
                    case BatchBidType.BID:
                        scale = 2;
                        applyBidValueFunc = LauUnitPo::setCostPrice;
                        curBidValue = lauUnitPo.getCostPrice();
                        changeLogBuilder
                                .key("costPrice")
                                .desc("基础出价");
                        tableField = LAU_UNIT.COST_PRICE;
                        break;
                    case BatchBidType.CPA_BID:
                        scale = OcpxTarget.getBidPrecision(lauUnitPo.getOcpcTarget());
                        applyBidValueFunc = LauUnitPo::setTwoStageBid;
                        curBidValue = lauUnitPo.getTwoStageBid();
                        changeLogBuilder
                                .key("twoStageBid")
                                .desc("CPA出价");
                        tableField = LAU_UNIT.TWO_STAGE_BID;
                        break;
                    case BatchBidType.DEEP_CPA_BID:
                        scale = OcpxTarget.getBidPrecision(lauUnitPo.getOcpxTargetTwo());
                        applyBidValueFunc = LauUnitPo::setOcpxTargetTwoBid;
                        curBidValue = lauUnitPo.getOcpxTargetTwoBid();
                        changeLogBuilder
                                .key("ocpxTargetTwoBid")
                                .desc("深度CPA出价");
                        tableField = LAU_UNIT.OCPX_TARGET_TWO_BID;
                        break;
                    default:
                        throw new IllegalArgumentException("出价类型未知");
                }
                final var bidCtx = new UnitBidContext();
                bidCtx.setScale(scale);
                bidCtx.setBidValue(new BigDecimal(bid).multiply(BigDecimal.valueOf(10).pow(scale)).intValue());
                bidCtx.setCurBidValue(curBidValue);
                bidCtx.setChangeLogBo(changeLogBuilder.build());
                ctx.getUnitBidContextMap().put(genUnitBidUk(targetId, bidType), bidCtx);

                // 更新供校验使用的数据
                applyBidValueFunc.accept(validationLauUnitPo, bidCtx.getBidValue());

                final var uk = genReGroupUk(bidType, bidCtx.getBidValue());
                if (!map.containsKey(uk)) {
                    final var reGroupedOperationBo = new OperationBo();
                    reGroupedOperationBo.setTargetIds(new ArrayList<>());
                    reGroupedOperationBo.setBidGrey(new BidOperationBo());
                    reGroupedOperationBo.getBidGrey().setBid(bidBo.getBid());
                    reGroupedOperationBo.getBidGrey().setBidType(bidBo.getBidType());
                    reGroupedOperationBo.getBidGrey().setBidValue(bidCtx.getBidValue());
                    reGroupedOperationBo.getBidGrey().setTableField(tableField);
                    if (Objects.equals(bidType, BatchBidType.DEEP_CPA_BID)) {
                        if (NumberUtils.isPositive(bidCtx.getBidValue())) {
                            reGroupedOperationBo.getBidGrey().setOcpxMode(OcpxMode.DUAL_TARGET_MANUAL_BID);
                        } else {
                            reGroupedOperationBo.getBidGrey().setOcpxMode(OcpxMode.DUAL_TARGET_AUTO_BID);
                        }
                    }
                    map.put(uk, reGroupedOperationBo);
                }
                map.get(uk).getTargetIds().add(targetId);
            }
        }
        if (!map.isEmpty() && map.size() < ctx.getOperationBos().size()) {
            log.info("re-grouped batch bid operation dim from {} to {}", ctx.getOperationBos().size(), map.size());
        }
        return new ArrayList<>(map.values());
    }

    @Override
    public void handleRequest(UnitContext ctx) {
        super.handleRequest(ctx);
        final var campaignIds = ctx.getUnitMap()
                .values()
                .stream()
                .map(LauUnitPo::getCampaignId)
                .distinct()
                .collect(Collectors.toList());
        ctx.setCampaignMap(fetchCampaignMap(campaignIds));


        HashMap<Integer, UnitBidBo> unitBidMap = new HashMap<>();


        final var ocpcTargets = ctx.getUnitMap()
                .values()
                .stream()
                .map(LauUnitPo::getOcpcTarget)
                .distinct()
                .collect(Collectors.toList());

        ocpcTargets.forEach(t -> {
                    final var queryBo = new ConfigurableResourceQueryBo();
                    queryBo.setAccountId(ctx.getOperatorBo().getOperatorId());
                    queryBo.setCpaTarget(t);
                    final var unitBid = resUnitService.getUnitBid(queryBo);
                    unitBidMap.put(t, unitBid);
                }
        );

        ctx.setUnitBidMap(unitBidMap);
        ctx.setUnitBidContextMap(new HashMap<>());
        ctx.setValidationUnitMap(new HashMap<>());
    }

    @Override
    public void handleTarget(final UnitContext ctx) {
        super.handleTarget(ctx);
        ctx.setUnitBidContext(ctx.getUnitBidContextMap().get(genUnitBidUk(ctx.getTargetId(), ctx.getOperationBo().getBidGrey().getBidType())));
    }

    @Override
    public List<Function<UnitContext, String>> fetchValidators() {
        final var list = new ArrayList<>(super.fetchValidators());
        list.add(UnitTemplateHandler::validateNoBid);
        list.add(c -> {
            final var validationUnitPo = c.getValidationUnitMap().get(c.getTargetId());
            if (Objects.isNull(c.getUnitBidContext()) || Objects.isNull(validationUnitPo)) {
                return "单元id对应的数据不存在";
            }

            final var bidType = c.getOperationBo().getBidGrey().getBidType();
            final var bidValue = c.getUnitBidContext().getBidValue();
            if (Objects.equals(bidType, BatchBidType.BID)) {
                if (NumberUtils.isPositive(c.getLauUnitPo().getOcpcTarget())) {
                    return "仅CPC/CPM单元才能修改基础出价";
                }

                LauCampaignPo campaignPo = c.getCampaignMap().get(c.getLauUnitPo().getCampaignId());

                if (Objects.nonNull(campaignPo) && Objects.equals(AdType.ADTYPE_SPLASH_VALUE, campaignPo.getAdType())){
                    if (c.getLauUnitPo().getSalesType().equals(BaseTarget.BT_CPM_VALUE) && c.getUnitBidContext().getBidValue() < 1800L) {
                        return "闪屏CPM出价不可低于18.00元";
                    }else if (c.getLauUnitPo().getSalesType().equals(BaseTarget.BT_CPC_VALUE) && c.getUnitBidContext().getBidValue() < 180L) {
                        return "闪屏CPC出价不可低于1.80元";
                    }
                }

                Integer salesType = c.getLauUnitPo().getSalesType();
                if (BaseTarget.BT_CPC_VALUE == salesType) {
                    if (bidValue < 50L) {
                        return "CPC出价不可低于0.50元";
                    }

                } else if (bidValue < 100L) {
                    return "CPM出价不可低于1.00元";
                }
            } else if (Objects.equals(bidType, BatchBidType.CPA_BID)) {
                if (!NumberUtils.isPositive(validationUnitPo.getOcpcTarget())) {
                    return "仅CPA单元才能修改CPA出价";
                }

                // 子单元 不做深入校验
                if (NumberUtils.isPositive(validationUnitPo.getParentUnitId())) {
                    return null;
                }

                final var unitBid = c.getUnitBidMap().get(validationUnitPo.getOcpcTarget());

                if (Objects.isNull(unitBid)) {
                    return "无法获取CPA出价策略";
                }

                Integer minBid = unitBid.getCpaTargetMinBid();
                boolean personalUp = Objects.equals(OperatorTypeConstants.PERSON_FLY, c.getOperatorBo().getOperatorType());
                if (personalUp) {
                    minBid = CommonConstants.PERSONAL_UP_CPA_BID_MIN;
                }

                if (!NumberUtils.between(bidValue, minBid, unitBid.getCpaTargetMaxBid())) {
                    return MessageFormat.format("CPA出价{0}不在政策的范围之内, 最低{1}, 最高{2}",
                            formatBidValue(bidValue, c.getUnitBidContext().getScale()),
                            formatBidValue(minBid, c.getUnitBidContext().getScale()),
                            formatBidValue(unitBid.getCpaTargetMaxBid(), c.getUnitBidContext().getScale()));
                }

                final var msg = validateCpaBidAndDeepCpaBid(validationUnitPo);
                if (StringUtils.hasText(msg)) {
                    return msg;
                }
            } else if (Objects.equals(bidType, BatchBidType.DEEP_CPA_BID)) {
                if (!NumberUtils.isPositive(validationUnitPo.getOcpxTargetTwo())) {
                    return "仅深度CPA出价单元才能修改深度CPA出价";
                }

                if (Objects.equals(bidValue, 0)) {
                    validationUnitPo.setOcpxMode(OcpxMode.DUAL_TARGET_AUTO_BID);
                }

                // 子单元 不做深度出价大于浅层出价限制
                if (NumberUtils.isPositive(validationUnitPo.getParentUnitId())) {
                    return null;
                }

                final var msg = validateCpaBidAndDeepCpaBid(validationUnitPo);
                if (StringUtils.hasText(msg)) {
                    return msg;
                }
            }
            return null;
        });
        return list;
    }

    // 校验浅层出价不能高于深层出价
    private String validateCpaBidAndDeepCpaBid(LauUnitPo lauUnitPo) {
        // 没有深度优化目标 不需要校验
        if (!NumberUtils.isPositive(lauUnitPo.getOcpxTargetTwo())) {
            return null;
        }

        // roi类型优化目标 出价单位不一样 没法一起比较
        if (OcpxTarget.ROI_TARGET_SET.contains(lauUnitPo.getOcpcTarget())
                || OcpxTarget.ROI_TARGET_SET.contains(lauUnitPo.getOcpxTargetTwo())) {
            return null;
        }

        // 第二目标自动出价 不需要校验
        if (lauUnitPo.getOcpxMode().equals(OcpxMode.DUAL_TARGET_AUTO_BID)) {
            return null;
        }

        if (lauUnitPo.getOcpxTargetTwoBid() <= lauUnitPo.getTwoStageBid()) {
            return "单元" + lauUnitPo.getUnitId() + " ocpx第二目标出价必须大于二阶段出价";
        }

        return null;
    }

    @Override
    public CompareBo compare(UnitContext ctx) {
        final var compareBo = new CompareBo();
        var curBid = formatBidValue(ctx.getUnitBidContext().getCurBidValue(), ctx.getUnitBidContext().getScale());
        var newBid = new BigDecimal(ctx.getOperationBo().getBidGrey().getBid()).setScale(ctx.getUnitBidContext().getScale(), RoundingMode.HALF_EVEN).toString();
        boolean dirty = false;
        if (Objects.equals(ctx.getOperationBo().getBidGrey().getBidType(), BatchBidType.DEEP_CPA_BID)) {
            if (Objects.equals(ctx.getLauUnitPo().getOcpxMode(), OcpxMode.DUAL_TARGET_AUTO_BID)) {
                if (NumberUtils.isPositive(ctx.getUnitBidContext().getCurBidValue())) {
                    dirty = true;
                }
                curBid = "自动出价";
            }
            if (Objects.equals(ctx.getUnitBidContext().getBidValue(), 0)) {
                newBid = "自动出价";
            }
        }
        compareBo.setCurVersion(curBid);
        compareBo.setNewVersion(newBid);
        if (!Objects.equals(curBid, newBid)) {
            compareBo.setChanged(true);
            final var logContextBo = OperationLogContextBo.newContext(ctx.getOperatorBo(), "lau_unit");
            compareBo.setOperationLogContextBo(logContextBo);
            ctx.getUnitBidContext().getChangeLogBo().setOldValue(curBid);
            ctx.getUnitBidContext().getChangeLogBo().setNewValue(newBid);
            logContextBo.getChanges().add(ctx.getUnitBidContext().getChangeLogBo());
            logContextBo.updateContext(ctx.getTargetId(), OperationType.UPDATE);
        } else if (dirty) {
            compareBo.setChanged(true);
        }
        return compareBo;
    }

    @Override
    public void batchUpdateTarget(UnitContext ctx) {
        if (CollectionUtils.isEmpty(ctx.getUpdatingTargetIds())) return;

        var step = adCore.update(LAU_UNIT)
                .set(ctx.getOperationBo().getBidGrey().getTableField(), ctx.getOperationBo().getBidGrey().getBidValue());
        if (Objects.nonNull(ctx.getOperationBo().getBidGrey().getOcpxMode())) {
            step = step.set(LAU_UNIT.OCPX_MODE, ctx.getOperationBo().getBidGrey().getOcpxMode());
        }
        step.where(LAU_UNIT.UNIT_ID.in(ctx.getUpdatingTargetIds()))
                .execute();
    }

    @Override
    public void batchUpdate(UnitContext ctx) {
        ((UnitBidHandler) AopContext.currentProxy()).batchUpdateWithTx(ctx);

        try {
            infoReportCompensationLog(ctx);
        } catch (Exception ignored) {
        }
    }

    @Transactional(MySqlConfig.AD_CORE_TX_MGR)
    public void batchUpdateWithTx(UnitContext ctx) {
        super.batchUpdate(ctx);
    }

    private String formatBidValue(Integer bidValue, int scale) {
        return BigDecimal.valueOf(bidValue).divide(BigDecimal.valueOf(10).pow(scale), scale, RoundingMode.HALF_EVEN).toString();
    }


    private void infoReportCompensationLog(UnitContext ctx) {
        if (CollectionUtils.isEmpty(ctx.getOperationBos())) {
            return;
        }
        InfoReportCompensationLogBo infoReportCompensationLogBo = new InfoReportCompensationLogBo();
        infoReportCompensationLogBo.setNeedReport(true);
        infoReportCompensationLogBo.setNeedTyp1Report(true);
        infoReportCompensationLogBo.setOperator_id(String.valueOf(ctx.getOperatorBo().getOperatorId()));


        for (OperationLogContextBo operationLogContextBo : ctx.getOperationLogContextBos()) {
            infoReportCompensationLogBo.setUnitId(String.valueOf(operationLogContextBo.getObjId()));
            InfoReportCompensationLogBo.Type1Data type1Data = new InfoReportCompensationLogBo.Type1Data();

            if (CollectionUtils.isEmpty(operationLogContextBo.getChanges())) {
                continue;
            }


            InfoReportCompensationLogBo.Type1DataValue oldValue = new InfoReportCompensationLogBo.Type1DataValue();
            InfoReportCompensationLogBo.Type1DataValue newValue = new InfoReportCompensationLogBo.Type1DataValue();
            // 永远只有一条
            Long oldValueStr = StringUtils.isEmpty(operationLogContextBo.getChanges().get(0).getOldValue()) ? 0 :
                    Long.parseLong(operationLogContextBo.getChanges().get(0).getOldValue());

            Long newValueStr = StringUtils.isEmpty(operationLogContextBo.getChanges().get(0).getNewValue()) ? 0 :
                    Long.parseLong(operationLogContextBo.getChanges().get(0).getNewValue());

            switch (ctx.getOperationBo().getBidGrey().getBidType()) {
                case BatchBidType.BID:
                    newValue.setCost_price(newValueStr);
                    oldValue.setCost_price(oldValueStr);
                    break;
                case BatchBidType.CPA_BID:

                    newValue.setTwo_stage_bid(newValueStr);
                    oldValue.setTwo_stage_bid(oldValueStr);

                    break;
                case BatchBidType.DEEP_CPA_BID:
                    newValue.setOcpx_target_two_bid(newValueStr);
                    oldValue.setOcpx_target_two_bid(oldValueStr);
                    break;
            }
            type1Data.setOld_value(oldValue);
            type1Data.setNew_value(newValue);
            infoReportCompensationLogBo.setType1Data(type1Data);
            infoReportService.reportCompensationLog(infoReportCompensationLogBo);
        }
    }

    private String genUnitBidUk(Integer unitId, Integer bidType) {
        return unitId + "-" + bidType;
    }

    private String genReGroupUk(Integer bidType, Integer bidValue) {
        return bidType + "-" + bidValue;
    }


    private Map<Integer, LauCampaignPo> fetchCampaignMap(Collection<Integer> campaignIds) {
        if (CollectionUtils.isEmpty(campaignIds)) return Map.of();

        return adCore.select(LAU_CAMPAIGN.CAMPAIGN_ID,
                        LAU_CAMPAIGN.AD_TYPE)
                .from(LAU_CAMPAIGN)
                .where(LAU_CAMPAIGN.CAMPAIGN_ID.in(campaignIds))
                .fetchInto(LauCampaignPo.class)
                .stream()
                .collect(Collectors.toMap(LauCampaignPo::getCampaignId, Function.identity()));
    }
}
