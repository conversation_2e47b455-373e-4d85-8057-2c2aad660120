package sycpb.platform.cpm.pandora.service.impl.core.v6.campaign;

import lombok.Data;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperationLogContextBo;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperatorBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.campaign.bos.LauCampaignBo;
import sycpb.platform.cpm.pandora.service.api.resource.account.bos.AccountBo;
import sycpb.platform.cpm.pandora.service.common.bo.InfoReportCompensationLogBo;

import java.util.Set;


@Data
public class SaveCampaignContextBo {
    private OperatorBo operator;
    private LauCampaignBo newVersion;
    private LauCampaignBo currentVersion;
    private Set<Integer> accountLabelIds;
    private OperationLogContextBo operationLogContextBo;
    private AccountBo accountBo;
    private boolean personalUp;
    private InfoReportCompensationLogBo infoReportBo;


}