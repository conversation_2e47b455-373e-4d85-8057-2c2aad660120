package sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauUnitTargetTagBo {
	@CompareMeta(copyFromCurrentVersion = true, comparable = false)
	private Integer id;
	private Integer unitId;
	private Integer platformType;
	private Long tagId;

	@CompareMeta(uk = true)
	public String uk() {
		return unitId + "-" + platformType + "-" + tagId;
	}
}
