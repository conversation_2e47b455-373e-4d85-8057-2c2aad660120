package sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input;

import lombok.Data;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;

import java.util.List;
import java.util.Objects;

@Data
public class NativeParamBo {
    private List<Integer> promotionPurposeTypes;
    private List<Integer> promotionContentTypes;
    private List<Integer> accountLabelIds;
    private List<Integer> cpaTargetIds;
    private List<Integer> deepCpaTargetIds;
    private List<Integer> baseTargetIds;

    public void addSingleBaseTargetId(Integer x) {
        if (NumberUtils.isPositive(x)) {
            baseTargetIds = List.of(x);
        }
    }

    public void addSingleCpaTargetId(Integer x) {
        if (NumberUtils.isPositive(x)) {
            cpaTargetIds = List.of(x);
        }
    }

    public void addSinglePromotionPurposeType(Integer x) {
        if (NumberUtils.isPositive(x)) {
            promotionPurposeTypes = List.of(x);
        }
    }

    public void addSinglePromotionContentType(Integer x) {
        if (NumberUtils.isPositive(x)) {
            promotionContentTypes = List.of(x);
        }
    }

    public void addSingleDeepCpaTargetId(Integer x) {
        // 是否支持原生 二目标过滤 潇秦老师特别需求
        if (Objects.nonNull(x)) {
            deepCpaTargetIds = List.of(x);
        }
    }
}
