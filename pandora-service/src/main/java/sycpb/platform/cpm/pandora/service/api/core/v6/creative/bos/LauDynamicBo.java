package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauDynamicBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Long      id;
    @CompareMeta(logGroupId = true, comparable = false, uk = true)
    private Long      dynamicId;
    @CompareMeta
    private Long      dynamicUpMid;
    @CompareMeta
    private String    nickname;
    @CompareMeta
    private Long      sid;
    @CompareMeta
    private Integer   source;
    @CompareMeta
    private Long      secondShowMid;
    @CompareMeta
    private String    shadowInfo;
    @CompareMeta
    private String    cover;
}
