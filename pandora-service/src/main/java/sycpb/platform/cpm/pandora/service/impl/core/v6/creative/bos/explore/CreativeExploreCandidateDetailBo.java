package sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.explore;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName CreativeExploreCandidateDetailBo
 * <AUTHOR>
 * @Date 2025/3/30 5:41 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreativeExploreCandidateDetailBo {

    private Integer total;
    private List<CreativeExploreCandidateDetailEntityBo> entityList;
    private Integer progress;
}
