package sycpb.platform.cpm.pandora.service.impl.resource.location;

import com.bapis.ad.location.adp.v6.*;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;
import sycpb.platform.cpm.pandora.service.api.resource.location.ILocationService;
import sycpb.platform.cpm.pandora.service.api.resource.location.LocationMapper;
import sycpb.platform.cpm.pandora.service.api.resource.location.bos.*;
import sycpb.platform.cpm.pandora.service.constants.Channel;
import sycpb.platform.cpm.pandora.service.proxy.CpmLocationProxy;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@Service
@RequiredArgsConstructor
public class LocDimensionServiceImpl implements ILocationService {

    @Resource
    private CpmLocationProxy cpmLocationProxy;

    @Getter
    private volatile Map<Integer, ChannelBo> channelMap = new HashMap<>();
    @Getter
    private volatile Map<Integer, SceneBo> sceneMap = new HashMap<>();
    @Getter
    private volatile Map<Integer, Integer> templateTemplateGroupMap = new HashMap<>();
    @Getter
    private volatile Map<Integer, List<Integer>> templateGroupTemplateMap = new HashMap<>();
    @Getter
    private volatile Map<Integer, TemplateBo> templateMap = new HashMap<>();
    @Getter
    private volatile Map<Integer, SlotGroupBo> slotGroupMap = new HashMap<>();
    @Getter
    private volatile Map<Integer, TemplateGroupBo> templateGroupMap = new HashMap<>();
    @Getter
    private volatile Map<Integer, ButtonBo> buttonMap = new HashMap<>();

    @PostConstruct
    private void init() {
        final var executorService = Executors.newFixedThreadPool(10);
        Executors.newSingleThreadExecutor().execute(() -> run(executorService));
    }

    @SneakyThrows
    private void run(ExecutorService executorService) {
        while (true) {
            try {
                executorService.submit(() -> {
                    final var channelBos = loadChannels();
                    if (CollectionUtils.isEmpty(channelBos)) return;

                    final var map = new HashMap<Integer, ChannelBo>();
                    for (ChannelBo channelBo : channelBos) {
                        final int orderV2;
                        switch (channelBo.getChannelId()) {
                            case Channel.ALL:
                                orderV2 = 0;
                                break;
                            case Channel.MOBILE:
                                orderV2 = 1;
                                break;
                            case Channel.PC:
                                orderV2 = 2;
                                break;
                            default:
                                orderV2 = 100;
                        }
                        channelBo.setOrderV2(orderV2);
                        map.put(channelBo.getChannelId(), channelBo);
                    }
                    channelMap = map;
                });
                executorService.submit(() -> {
                    final var buttonBos = loadButtons();
                    if (CollectionUtils.isEmpty(buttonBos)) return;

                    final var map = new HashMap<Integer, ButtonBo>();
                    for (ButtonBo buttonBo : buttonBos) {
                        map.put(buttonBo.getButtonId(), buttonBo);
                    }
                    buttonMap = map;
                });
                executorService.submit(() -> {
                    final var sceneBos = loadScenes();
                    if (CollectionUtils.isEmpty(sceneBos)) return;

                    final var map = new HashMap<Integer, SceneBo>();
                    for (SceneBo sceneBo : sceneBos) {
                        map.put(sceneBo.getSceneId(), sceneBo);
                    }
                    sceneMap = map;
                });
                executorService.submit(() -> {
                    final var templateBos = loadTemplates();
                    if (CollectionUtils.isEmpty(templateBos)) return;

                    final var map = new HashMap<Integer, TemplateBo>();
                    for (TemplateBo templateBo : templateBos) {
                        map.put(templateBo.getTemplateId(), templateBo);
                    }
                    templateMap = map;
                });
                executorService.submit(() -> {
                    final var templateGroupTemplateMappingBos = loadTemplateGroupTemplateMappings();
                    if (CollectionUtils.isEmpty(templateGroupTemplateMappingBos)) return;

                    final var templateMappingMap = new HashMap<Integer, Integer>();
                    final var templateGroupMappingMap = new HashMap<Integer, List<Integer>>();
                    for (TemplateGroupTemplateMappingBo mappingBo : templateGroupTemplateMappingBos) {
                        for (Integer templateId : mappingBo.getTemplateIds()) {
                            templateMappingMap.put(templateId, mappingBo.getTemplateGroupId());
                        }
                        templateGroupMappingMap.put(mappingBo.getTemplateGroupId(), mappingBo.getTemplateIds());
                    }
                    templateTemplateGroupMap = templateMappingMap;
                    templateGroupTemplateMap = templateGroupMappingMap;
                });
                executorService.submit(() -> {
                    final var slotGroupBos = loadSlotGroups();
                    if (CollectionUtils.isEmpty(slotGroupBos)) return;

                    final var map = new HashMap<Integer, SlotGroupBo>();
                    for (SlotGroupBo slotGroupBo : slotGroupBos) {
                        map.put(slotGroupBo.getSlotGroupId(), slotGroupBo);
                    }
                    slotGroupMap = map;
                });
                executorService.submit(() -> {
                    final var templateGroupBos = loadTemplateGroups();
                    if (CollectionUtils.isEmpty(templateGroupBos)) return;

                    final var map = new HashMap<Integer, TemplateGroupBo>();
                    for (TemplateGroupBo templateGroupBo : templateGroupBos) {
                        map.put(templateGroupBo.getTemplateGroupId(), templateGroupBo);
                    }
                    templateGroupMap = map;
                });
            } catch (Throwable t) {
                log.error("ResCreativeService refresh failed", t);
            }
            TimeUnit.SECONDS.sleep(60);
        }
    }

    private List<TemplateGroupTemplateMappingBo> loadTemplateGroupTemplateMappings() {
        final Supplier<List<TemplateGroupTemplateMappingBo>> supplier = () -> {
            final var resp = cpmLocationProxy.templateGroupBindConfig();
            return LocationMapper.MAPPER.toTemplateGroupTemplateMappingBos(resp.getEntityList());
        };
        return exec(supplier);
    }

    private List<TemplateGroupBo> loadTemplateGroups() {
        final Supplier<List<TemplateGroupBo>> supplier = () -> {
            final var resp = cpmLocationProxy.templateGroupConfig();
            return LocationMapper.MAPPER.toTemplateGroupBos(resp.getEntityList());
        };
        return exec(supplier);
    }

    private List<TemplateBo> loadTemplates() {
        final Supplier<List<TemplateBo>> supplier = () -> {
            final var resp = cpmLocationProxy.listTemplate();
            return LocationMapper.MAPPER.toTemplateBos(resp.getEntityList());
        };
        return exec(supplier);
    }

    private List<SlotGroupBo> loadSlotGroups() {
        final Supplier<List<SlotGroupBo>> supplier = () -> {
            final var resp = cpmLocationProxy.listSlotGroup();
            return LocationMapper.MAPPER.toSlotGroupBos(resp.getEntityList());
        };
        return exec(supplier);
    }

    private List<SceneBo> loadScenes() {
        final Supplier<List<SceneBo>> supplier = () -> {
            final var resp = cpmLocationProxy.listScene();
            return LocationMapper.MAPPER.toSceneBos(resp.getEntityList());
        };
        return exec(supplier);
    }

    private List<ChannelBo> loadChannels() {
        final Supplier<List<ChannelBo>> supplier = () -> {
            final var resp = cpmLocationProxy.listChannel();
            return LocationMapper.MAPPER.toChannelBos(resp.getEntityList());
        };
        return exec(supplier);
    }

    private List<ButtonBo> loadButtons() {
        final Supplier<List<ButtonBo>> supplier = () -> {
            final var resp = cpmLocationProxy.listButton();
            return LocationMapper.MAPPER.toButtonBos(resp.getEntityList());
        };
        return exec(supplier);
    }

    private<T> List<T> exec(Supplier<List<T>> supplier) {
        try {
            return supplier.get();
        } catch (Throwable t) {
            log.error("LocDimensionServiceImpl", t);
            return List.of();
        }
    }
}
