package sycpb.platform.cpm.pandora.service.api.resource.creative;

import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.*;
import sycpb.platform.cpm.pandora.service.api.resource.location.bos.TemplateGroupBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.CreativeContentBo;

import java.util.List;

public interface IResCreativeService {
    CreativeContentBo getCreativeContent(QueryCreativeContentBo x);

    TemplateGroupBo getTemplateGroup(Integer templateGroupId);

    TemplateGroupDetailBo getTemplateGroupDetail(QueryTemplateGroupBo bo);

    CompoundTemplateGroupBo getCompoundTemplateGroup(QueryTemplateGroupBo bo);

    Integer getSupportBiliNative(GetSupportBiliNativeBo getSupportBiliNativeBo);

    List<Integer> getBusMarkIdsByTemplateIdAndAccountLabelIds(List<Integer> templateIds, List<Integer> accountLabelIds);
}
