package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import com.bapis.ad.pandora.resource.MonitorType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauUnitMonitoringDao;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitMonitoringPo;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraAssert;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauUnitMonitoringBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitGameBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.RequestParam;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitMonitoring.LAU_UNIT_MONITORING;

@Slf4j
@Service
@RequiredArgsConstructor
public class UnitMonitoringService {
    @Value("${game.monitor.domain:https://game-attribute.biligame.com/ad/api/click}")
    private String gameMonitorUrl = "https://game-attribute.biligame.com/ad/api/click";
    private final LauUnitMonitoringDao lauUnitMonitoringDao;

    @Resource(name = MySqlConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;

    public List<LauUnitMonitoringBo> get(Integer unitId) {
        List<LauUnitMonitoringPo> poList = adCore.selectFrom(LAU_UNIT_MONITORING)
                .where(LAU_UNIT_MONITORING.UNIT_ID.eq(unitId))
                .orderBy(LAU_UNIT_MONITORING.TYPE.asc())
                .fetch().into(LauUnitMonitoringPo.class);

        return UnitCreativeImplMapper.MAPPER.fromLauUnitMonitoringPos(poList);
    }

    public List<LauUnitMonitoringBo> list(List<Integer> unitIdList) {
        if (CollectionUtils.isEmpty(unitIdList)) {
            return Collections.emptyList();
        }

        List<LauUnitMonitoringPo> poList = adCore.selectFrom(LAU_UNIT_MONITORING)
                .where(LAU_UNIT_MONITORING.UNIT_ID.in(unitIdList))
                .orderBy(LAU_UNIT_MONITORING.TYPE.asc())
                .fetch().into(LauUnitMonitoringPo.class);

        return UnitCreativeImplMapper.MAPPER.fromLauUnitMonitoringPos(poList);
    }

    public void save(SaveUnitCreativeContextBo ctx) {
        final var currentVersions = ctx.getCurrentVersion().getUnit().getLauUnitMonitoringBos().stream().filter(bo -> bo.getType() != MonitorType.MONITOR_GAME_CLICK.getNumber()).collect(Collectors.toList());
        final var newVersions = ctx.getNewVersion().getUnit().getLauUnitMonitoringBos()
                .stream()
                .filter(x -> StringUtils.hasText(x.getUrl()))
                .filter(bo -> bo.getType() != MonitorType.MONITOR_GAME_CLICK.getNumber())
                .collect(Collectors.toList());
        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(LauUnitMonitoringBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauUnitMonitoringDao)
                .setDSLContext(adCore)
                .setTableImpl(LAU_UNIT_MONITORING)
                .setDatabasePrimaryKeyField(LAU_UNIT_MONITORING.ID)
                .setDatabasePrimaryKeyMapper(LauUnitMonitoringPo::getId)
                .setOpLog(ctx.genUnitCompareConsumer());
        JooqFunctions.save(currentVersions, removeDuplicated(newVersions), ctx0);

        saveGameMonitoring(ctx);
    }

    private List<LauUnitMonitoringBo> removeDuplicated(List<LauUnitMonitoringBo> bos) {
        return new ArrayList<>(bos.stream().collect(Collectors.toMap(LauUnitMonitoringBo::uk, Function.identity(), (x, y) -> x)).values());
    }

    private void saveGameMonitoring(SaveUnitCreativeContextBo ctx) {
        List<LauUnitMonitoringPo> lauUnitMonitoringPos = lauUnitMonitoringDao.fetchByUnitId(ctx.getUnitId());
        List<Integer> typeList = lauUnitMonitoringPos.stream().map(LauUnitMonitoringPo::getType).collect(Collectors.toList());
        if (typeList.contains(6)) {
            return;
        }

        LauUnitGameBo lauUnitGameBo = ctx.getLauUnitGameBo();
        if (lauUnitGameBo != null) {
            boolean isInner = ctx.getAccountBo() != null && Objects.equals(ctx.getAccountBo().getIsInner(), 1);
            LauUnitMonitoringPo lauUnitMonitoringPo = UnitCreativeImplMapper.MAPPER.toPo(genGameHiddenMonitoring(ctx.getUnitId(), lauUnitGameBo.getGameBaseId(), lauUnitGameBo.getPlatformType(), lauUnitGameBo.getSubPkg(), isInner));
            lauUnitMonitoringDao.insert(lauUnitMonitoringPo);
        }
    }


    public LauUnitMonitoringBo genGameHiddenMonitoring(Integer unitId, Integer gameBaseId, Integer gamePlatformType, Integer subPkg, boolean isInner) {
        LauUnitMonitoringBo lauUnitMonitoringBo = new LauUnitMonitoringBo();
        String url = getGameMonitorUrl().replace("game_base_id=&platform_type=",
                "game_base_id=" + gameBaseId + "&platform_type=" + gamePlatformType);
        // 内广增加sourcetype=inner参数
        if (isInner) {
            url = url + "&sourcetype=inner";
        }
        // 标记广告包的参数
        url += "&sycp_pkg_type=" + subPkg;
        url = LaunchUrlDecorationService.decorate(url, RequestParam.GAME_HIDDEN_MONITOR_PARAMS);
        url = UriComponentsBuilder.fromUriString(url)
                .replaceQueryParam("sycp_ip", "__IPV4__")
                .replaceQueryParam("sycp_ip_before", "__IP__")
                .replaceQueryParam("sycp_android_id", "__ANDROIDID__")
                .build(false).toString();

        lauUnitMonitoringBo.setUnitId(unitId);
        lauUnitMonitoringBo.setUrl(url);
        lauUnitMonitoringBo.setType(MonitorType.MONITOR_GAME_CLICK.getNumber());

        return lauUnitMonitoringBo;
    }

    public String getGameMonitorUrl() {
        return this.gameMonitorUrl + '?' + "game_base_id=&platform_type=&track_id=__TRACKID__&mid=__UID__&gamesdkuid=__GAMESDKUID__&device_type=__OS__&time=__TIME__&account_id=__ACCOUNTID__";
    }

    public static String getMonitorTypeDesc(Integer type) {
        PandoraAssert.notNull(type, "监控链接类型不能为空",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        final var monitorType = MonitorType.forNumber(type);
        PandoraAssert.notNull(monitorType, "监控链接类型无法识别",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        final String prefix;
        switch (monitorType) {
            case MONITOR_CLICK:
                prefix = "点击";
                break;
            case MONITOR_VIEW:
                prefix = "展示";
                break;
            case MONITOR_PLAY:
                prefix = "播放";
                break;
            case MONITOR_PLAY_3S:
                prefix = "播放3秒";
                break;
            case MONITOR_PLAY_5S:
                prefix = "播放5秒";
                break;
            case MONITOR_COMMENT_CLICK:
                prefix = "评论点击";
                break;
            case MONITOR_GAME_CLICK:
                prefix = "游戏点击";
                break;
            case MONITOR_PLAY_MONITORING:
                prefix = "播放监测";
                break;
            default:
                throw new IllegalArgumentException("监控链接类型无法识别");
        }
        return prefix + "监控链接";
    }
}
