package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore;

import lombok.Data;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauUnitMonitoringBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauUnitSceneBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.*;

import java.util.List;

/**
 * @ClassName CreativeExploreUnitInfoBo
 * <AUTHOR>
 * @Date 2025/3/20 10:11 下午
 * @Version 1.0
 **/
@Data
public class CreativeExploreUnitInfoBo {

    private Integer promotionPurposeType;
    private Integer promotionContentType;
    // 单元维度的status list 不是unit_status
    private List<Integer> unitStatusList;
    private Integer cpaTarget;
    private Integer baseTarget;
    private Long adProductId;
    private Integer gameBaseId;
    private Integer appPackageId;
    private Long liveRoomId;
    private Integer biliMiniGameBaseId;
    private Integer miniGameId;
    // 会员购商品
    private Integer shopGoodsId;
    // 淘天商品
    private Long itemId;
    // 直播预约id
    private Long liveReserveSid;
    private List<LauUnitMonitoringBo> lauUnitMonitoringBoList;
    private Integer channelId;
    private List<Integer> selectedScenes;
    private Integer generalVersion;
    private Integer isProgrammatic;
    private Integer isBiliNative;
}
