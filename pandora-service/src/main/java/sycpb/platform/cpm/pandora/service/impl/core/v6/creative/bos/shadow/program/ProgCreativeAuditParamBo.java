package sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.program;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperatorBo;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProgCreativeAuditParamBo {

    // 额外元素审核情况
    private MiscElemBo miscElemBo;

    // 主副元素列表审核情况
    private List<MaterialAuditBo> materialBos;

    private OperatorBo operatorBo;

    // 是否需要处理父创意
    private Boolean needProcessParentCreative;

    // 是否删除创意影子，调试用
    private Integer needDeleteCreativeShadow;

    // 是否为质检审核
    private Boolean recheckFlag;
}
