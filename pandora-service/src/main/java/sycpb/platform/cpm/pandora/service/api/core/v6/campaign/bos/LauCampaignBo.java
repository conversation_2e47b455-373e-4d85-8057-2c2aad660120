package sycpb.platform.cpm.pandora.service.api.core.v6.campaign.bos;

import com.bapis.ad.pandora.resource.BudgetType;
import lombok.Data;
import sycpb.platform.cpm.pandora.infra.pandora_type.numeric.decimal.DecimalValue;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

import java.sql.Timestamp;
import java.util.Objects;

@Data
public class LauCampaignBo {
    private Integer adpVersion;
    private Integer accountId;
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Integer campaignId;
    @CompareMeta(logDescription = "计划名称")
    private String campaignName;
    @CompareMeta(logDescription = "推广目的")
    private Integer promotionPurposeType;
    @CompareMeta
    private Integer budgetLimitType;
    @CompareMeta
    private Integer budgetType;
    @CompareMeta(logKey = "compositeBudget")
    private DecimalValue budget;
    @CompareMeta(logDescription = "广告类型")
    private Integer adType;
    @CompareMeta(logDescription = "是否自动投放")
    private Integer   supportAuto;

    @CompareMeta(logDescription = "状态")
    private Integer status;
    private Integer campaignStatus;
    private Integer salesType;

    @CompareMeta(logDescription = "是否mapi操作")
    private Integer flag;

    private Timestamp ctime;

    @CompareMeta(logDescription = "budget")
    public String budgedDesc() {
        if (Objects.equals(budgetLimitType, BudgetType.BUDGET_TOTAL_VALUE)) {
            return "总预算";
        } else {
            return "日预算";
        }
    }
}
