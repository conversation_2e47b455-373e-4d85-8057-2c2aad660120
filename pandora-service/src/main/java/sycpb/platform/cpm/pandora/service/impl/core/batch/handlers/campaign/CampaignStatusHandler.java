package sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.campaign;

import com.bapis.ad.pandora.core.batch.BatchOperationType;
import org.jooq.DSLContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.BatchOperationReqBo;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.OperationBo;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.IOperationLogService;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperationLogContextBo;
import sycpb.platform.cpm.pandora.service.common.InfoReportService;
import sycpb.platform.cpm.pandora.service.common.bo.InfoReportCompensationLogBo;
import sycpb.platform.cpm.pandora.service.constants.BatchTargetType;
import sycpb.platform.cpm.pandora.service.constants.CampaignStatus;
import sycpb.platform.cpm.pandora.service.constants.LaunchStatus;
import sycpb.platform.cpm.pandora.service.impl.core.batch.BatchOperationExtService;
import sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.misc.CompareFunctions;
import sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.unit.UnitStatusHandler;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCampaign.LAU_CAMPAIGN;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnit.LAU_UNIT;


@Service
public class CampaignStatusHandler extends CampaignTemplateHandler {
    @Resource
    private UnitStatusHandler updateUnitStatusHandler;

    public CampaignStatusHandler(BatchOperationExtService batchOperationExtService,
                                 IOperationLogService operationLogService,
                                 @Qualifier(MySqlConfig.AD_CORE_DSL_CONTEXT) DSLContext adCore) {
        super(operationLogService, batchOperationExtService, adCore);
    }

    @Override
    public CompareBo compare(CampaignContext ctx) {
        return CompareFunctions.compareStatus(ctx.getLauCampaignPo().getStatus(), ctx.getStatus(), ctx.getOperatorBo(), ctx.getTargetId(), "lau_campaign");
    }

    @Override
    public void batchUpdateTarget(CampaignContext ctx) {
        if (!CollectionUtils.isEmpty(ctx.getUpdatingTargetIds())) {
            final var campaignStatus = CampaignStatus.toCampaignStatus(ctx.getStatus());
            adCore.update(LAU_CAMPAIGN)
                    .set(LAU_CAMPAIGN.STATUS, ctx.getStatus())
                    .set(LAU_CAMPAIGN.CAMPAIGN_STATUS, campaignStatus)
                    .where(LAU_CAMPAIGN.CAMPAIGN_ID.in(ctx.getUpdatingTargetIds()))
                    .execute();
        }
        // 级联删除单元
        if (ctx.isDelete()) {
            final var unitIds = adCore.select(LAU_UNIT.UNIT_ID)
                    .from(LAU_UNIT)
                    .where(LAU_UNIT.CAMPAIGN_ID.in(ctx.getUpdatingTargetIds()))
                    .fetch(LAU_UNIT.UNIT_ID);
            if (!CollectionUtils.isEmpty(unitIds)) {
                final var unitOperationBo = new OperationBo();
                unitOperationBo.setTargetIds(unitIds);
                final var cascadeReqBo = new BatchOperationReqBo();
                cascadeReqBo.setOperator(ctx.getOperatorBo());
                cascadeReqBo.setOperationType(BatchOperationType.BATCH_DELETE_VALUE);
                cascadeReqBo.setTargetType(BatchTargetType.UNIT);
                cascadeReqBo.setOperations(List.of(unitOperationBo));
                cascadeReqBo.setStatus(ctx.getStatus());
                cascadeReqBo.setLauBatchOperationBo(ctx.getBatchOperationBo());
                cascadeReqBo.setIgnoreOperationDetail(true);
                cascadeReqBo.setFromUpperLevel(true);
                updateUnitStatusHandler.handle(cascadeReqBo);
            }
        }
    }

    @Override
    public void batchUpdate(CampaignContext ctx) {
        ((CampaignStatusHandler) AopContext.currentProxy()).batchUpdateWithTx(ctx);
    }

    @Resource
    private InfoReportService infoReportService;

    @Transactional(MySqlConfig.AD_CORE_TX_MGR)
    public void batchUpdateWithTx(CampaignContext ctx) {
        //暂停的需要赔付
        try {
            if (Objects.equals(ctx.getStatus(), LaunchStatus.PAUSED)) {
                InfoReportCompensationLogBo infoReportCompensationLogBo = new InfoReportCompensationLogBo();
                infoReportCompensationLogBo.setNeedTyp8Report(true);
                infoReportCompensationLogBo.setNeedReport(true);
                infoReportCompensationLogBo.setOperator_id(String.valueOf(ctx.getOperatorBo().getOperatorId()));
                for (OperationLogContextBo operationLogContextBo : ctx.getOperationLogContextBos()) {
                    infoReportCompensationLogBo.setCampaignId(String.valueOf(operationLogContextBo.getObjId()));
                    infoReportService.reportCompensationLog(infoReportCompensationLogBo);
                }
            }
        } catch (Exception e) {
        }

        super.batchUpdate(ctx);
    }
}
