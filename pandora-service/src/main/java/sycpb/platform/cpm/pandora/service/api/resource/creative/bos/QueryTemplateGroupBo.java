package sycpb.platform.cpm.pandora.service.api.resource.creative.bos;

import lombok.Data;

import java.util.List;

@Data
public class QueryTemplateGroupBo {
    private Integer accountId;
    private Integer unitId;
    private Integer isBiliNative;
    private Integer channelId;
    private List<Integer> sceneIds;
    private Integer isProgrammatic;
    private Integer isPreferScene;
    private Integer generalVersion;

    // 额外补充 替代计划单元对象
    private CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo;

    private Integer templateGroupId;
    private Integer isGif;
    private boolean removeStorySceneForDynamicTemplate;
    private Boolean isAutoPlay;
    private Boolean isPugv;
    private List<Integer> accountLabelIds;
    //个人起飞
    private boolean personalUp;


    public String genUUID() {
        return System.currentTimeMillis() + "-" + this.hashCode();
    }
}
