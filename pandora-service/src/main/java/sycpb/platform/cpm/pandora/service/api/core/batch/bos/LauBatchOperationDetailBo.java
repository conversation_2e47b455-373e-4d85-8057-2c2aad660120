package sycpb.platform.cpm.pandora.service.api.core.batch.bos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.service.constants.BatchOperationDetailStatus;

@Data
public class LauBatchOperationDetailBo {
    private Long      operationId;
    private Integer   targetId;
    private Integer   operationStatus;
    private String    operationExt;

    private ExtBo     extBo;
    private String    targetDesc;

    public static LauBatchOperationDetailBo newInstance(Long operationId, Integer targetId) {
        final var bo = new LauBatchOperationDetailBo();
        bo.setOperationId(operationId);
        bo.setTargetId(targetId);
        bo.setOperationStatus(BatchOperationDetailStatus.SUCCEEDED);
        bo.setExtBo(new ExtBo());
        return bo;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExtBo {
        private Long time;
        private String before;
        private String after;
        private String reason;
        // 评论组件、锚点时，avid、dynamicId保存在这
        private Long materialId;
    }
}
