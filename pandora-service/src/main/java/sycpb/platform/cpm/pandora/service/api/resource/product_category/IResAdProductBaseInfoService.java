package sycpb.platform.cpm.pandora.service.api.resource.product_category;

import sycpb.platform.cpm.pandora.service.api.resource.product_category.bos.AdProductBaseInfoBo;
import sycpb.platform.cpm.pandora.service.api.resource.product_category.bos.AdProductLibraryShareBo;

/**
 * @ClassName IResProductCategoryService
 * <AUTHOR>
 * @Date 2024/6/9 2:15 下午
 * @Version 1.0
 **/
public interface IResAdProductBaseInfoService {

    AdProductBaseInfoBo getById(Long id);

    AdProductBaseInfoBo getByIdV2(Long id);

    AdProductLibraryShareBo getByLibraryIdAndAccountId(Long libraryId, Integer accountId);

}
