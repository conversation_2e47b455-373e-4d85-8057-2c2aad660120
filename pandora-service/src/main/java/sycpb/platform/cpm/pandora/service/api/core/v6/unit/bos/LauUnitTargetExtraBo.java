package sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

import static sycpb.platform.cpm.pandora.service.constants.ExtraTargetType.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauUnitTargetExtraBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Integer   id;
    private Integer   unitId;
    @CompareMeta
    private Integer   unitTargetType;
    @CompareMeta
    private String    unitTargetValue;

    @CompareMeta(uk = true)
    public String uk() {
        return unitId + "-" + unitTargetType;
    }

    @CompareMeta(logKey = "unitTargetValue")
    public String logKey() {
        switch (unitTargetType) {
            case VIDEO_CATEGORY:
                return "videoCategory";
            case FANS_INCLUDE:
                return "fansInclude";
            case FANS_EXCLUDE:
                return "fansExclude";
            default:
                return "unknown";
        }
    }

    @CompareMeta(logDescription = "unitTargetValue")
    public String logDescription() {
        switch (unitTargetType) {
            case VIDEO_CATEGORY:
                return "视频二级分区";
            case FANS_INCLUDE:
                return "投放粉丝";
            case FANS_EXCLUDE:
                return "排除粉丝";
            default:
                return "未知";
        }
    }
}
