package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;


import com.bapis.ad.scv.anchor.SingleQueryAnchorRep;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.infra.common.comma_split.CommaSplitMapper;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.ArchiveBizAnchorBo;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = CommaSplitMapper.class)
public interface LauArchiveAnchorMapper {
    LauArchiveAnchorMapper MAPPER = Mappers.getMapper(LauArchiveAnchorMapper.class);

    @Mapping(target = "anchorSystemType", expression = "java(new Integer(com.bapis.ad.adp.anchor.AnchorSystemType.SANLIAN.getNumber()))")
    @Mapping(target = "avid", source = "aid")
    @Mapping(target = "anchorId", source = "id")
    @Mapping(target = "iosUrlPageId", source = "iosUrlPageId")
    @Mapping(target = "iosUrlType", expression = "java(singleQueryAnchorRep.getIosUrlType().getNumber())")
    @Mapping(target = "conversionUrlPageId", source = "conversionUrlPageId")
    @Mapping(target = "conversionUrlType", expression = "java(singleQueryAnchorRep.getConversionUrlType().getNumber())")
    @Mapping(target = "androidUrlPageId", source = "androidUrlPageId")
    @Mapping(target = "androidUrlPageType", expression = "java(singleQueryAnchorRep.getAndroidUrlTypeValue())")
    ArchiveBizAnchorBo toArchiveBizAnchorBo(SingleQueryAnchorRep singleQueryAnchorRep);
}
