package sycpb.platform.cpm.pandora.service.api.resource.recall;

import lombok.Data;

import java.util.List;

@Data
public class RecallResourceParamBo {
    // debug模式
    private boolean debug;

    // 账号标签
    private List<Integer> accountLabelIds;
    // 计划推广目的
    private Integer promotionPurposeType;
    // 单元推广内容
    private Integer promotionContentType;
    // 无目标出价类型
    private Integer baseTarget;
    // 浅层优化目标
    private Integer cpaTarget;
    // 计划类型, 目前仅对搜索广告生效
    // todo: 未来可以对闪屏广告生效, 这样就可以配置闪屏的资源位
    private Integer adType;
    // 流量类型
    private Integer channelId;
    // 场景
    private List<Integer> sceneIds;
    // 暗投
    private boolean allowHidden;
}
