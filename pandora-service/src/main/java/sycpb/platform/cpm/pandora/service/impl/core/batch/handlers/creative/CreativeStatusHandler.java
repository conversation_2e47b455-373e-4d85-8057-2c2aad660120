package sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.creative;

import com.bapis.ad.pandora.core.batch.BatchOperationType;
import org.jooq.DSLContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.IOperationLogService;
import sycpb.platform.cpm.pandora.service.constants.AdpVersion;
import sycpb.platform.cpm.pandora.service.constants.AuditStatus;
import sycpb.platform.cpm.pandora.service.constants.CreativeStatus;
import sycpb.platform.cpm.pandora.service.constants.LaunchStatus;
import sycpb.platform.cpm.pandora.service.impl.core.batch.BatchOperationExtService;
import sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.misc.CompareFunctions;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitCreative.LAU_UNIT_CREATIVE;
import static sycpb.platform.cpm.pandora.service.constants.AccountLabelIds.OLD_SANLIAN_ALLOW_EDIT_LABEL_ID;

@Service
public class CreativeStatusHandler extends CreativeTemplateHandler {
    public CreativeStatusHandler(IOperationLogService operationLogService,
                                 BatchOperationExtService batchOperationExtService,
                                 @Qualifier(MySqlConfig.AD_CORE_DSL_CONTEXT) DSLContext adCore) {
        super(operationLogService, batchOperationExtService, adCore);
    }

    @Override
    public CompareBo compare(CreativeContext ctx) {
        return CompareFunctions.compareStatus(ctx.getLauUnitCreativePo().getStatus(), ctx.getStatus(), ctx.getOperatorBo(), ctx.getTargetId(), "lau_unit_creative");
    }

    @Override
    public void batchUpdateTarget(CreativeContext ctx) {
        if (CollectionUtils.isEmpty(ctx.getUpdatingTargetIds())) return;

        final List<Integer> passedCreativeIds = new ArrayList<>();
        for (var updatingTargetId : ctx.getUpdatingTargetIds()) {
            final var po = ctx.getCreativeMap().get(updatingTargetId);
            if (Objects.isNull(po)) continue;

            if (Objects.equals(po.getAuditStatus(), AuditStatus.PASSED)) {
                passedCreativeIds.add(updatingTargetId);
            } else {
                // 因为这个状态的存在无法真正合并
                final var creativeStatus = CreativeStatus.toCreativeStatus(ctx.getStatus(), po.getAuditStatus());
                adCore.update(LAU_UNIT_CREATIVE)
                        .set(LAU_UNIT_CREATIVE.STATUS, ctx.getStatus())
                        .set(LAU_UNIT_CREATIVE.CREATIVE_STATUS, creativeStatus)
                        .where(LAU_UNIT_CREATIVE.CREATIVE_ID.eq(updatingTargetId))
                        .execute();
            }
        }
        if (!CollectionUtils.isEmpty(passedCreativeIds)) {
            final var creativeStatus = CreativeStatus.toCreativeStatus(ctx.getStatus());
            adCore.update(LAU_UNIT_CREATIVE)
                    .set(LAU_UNIT_CREATIVE.STATUS, ctx.getStatus())
                    .set(LAU_UNIT_CREATIVE.CREATIVE_STATUS, creativeStatus)
                    .where(LAU_UNIT_CREATIVE.CREATIVE_ID.in(passedCreativeIds))
                    .execute();
        }
    }

    @Override
    public void batchUpdate(CreativeContext ctx) {
        ((CreativeStatusHandler) AopContext.currentProxy()).batchUpdateWithTx(ctx);
    }

    @Transactional(MySqlConfig.AD_CORE_TX_MGR)
    public void batchUpdateWithTx(CreativeContext ctx) {
        super.batchUpdate(ctx);
    }
}
