package sycpb.platform.cpm.pandora.service.databus.sub;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeFlyDynamicInfoPo;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.databus.pub.bo.LauBatchOperationDetailEventMessage;
import sycpb.platform.cpm.pandora.service.impl.core.batch.BatchOperationExtService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.BiliDynamicService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/6/12
 **/
@Slf4j
@Service
public class LauBatchOperationDetailEventSub implements MessageListener {


    public static final String LAU_BATCH_OPERATION_DETAIL_EVENT = "lau-batch-operation-detail-event";
    private final String topic;
    private final String group;

    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private BatchOperationExtService batchOperationExtService;


    public LauBatchOperationDetailEventSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(LAU_BATCH_OPERATION_DETAIL_EVENT);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return true;
    }


    @Override
    public void onMessage(AckableMessage message) {
        log.info("wrap onMessage, LauBatchOperationDetailEventSub");
        try {
            LauBatchOperationDetailEventMessage dto = objectMapper.readValue(message.payload(), LauBatchOperationDetailEventMessage.class);
            batchOperationExtService.insertBatchOperationDetail(dto.getBos());
        } catch (Exception e) {
            log.error("LauBatchOperationDetailEventSub error", e);
        }
    }

}
