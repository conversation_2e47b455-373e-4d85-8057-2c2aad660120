package sycpb.platform.cpm.pandora.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.config.WatchedProperties;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
@Configuration
@PropertySource(value = "classpath:batch-operation.yaml", factory = PaladinPropertySourceFactory.class)
@WatchedProperties
@ConfigurationProperties(prefix = "batch-operation")
public class BatchOperationConfig {
    private Map<Integer, Map<Integer, TargetConfig>> maxConcurrentTasks = new ConcurrentHashMap<>();

    @Data
    public static class TargetConfig {
        private int maxConcurrent;
        private int maxTargetId;

    }

    public TargetConfig getTargetConfig(int batchOperationType, int batchOperationTargetType) {
        return getMaxConcurrentTasks().getOrDefault(batchOperationType, Map.of()).getOrDefault(batchOperationTargetType, new TargetConfig());
    }
}