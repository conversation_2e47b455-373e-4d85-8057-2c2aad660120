package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauCreativeQualificationBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Long      id;
    @CompareMeta(logGroupId = true, comparable = false)
    private Integer   creativeId;
    @CompareMeta(logDescription = "资质id")
    private Integer   qualificationId;

    @CompareMeta(uk = true)
    public String uk() {
        return creativeId + "-" + qualificationId;
    }
}
