package sycpb.platform.cpm.pandora.service.proxy;


import com.bapis.account.service.relation.MidsReq;
import com.bapis.account.service.relation.RelationGrpc;
import com.bapis.account.service.relation.StatReply;
import com.bapis.account.service.relation.StatsReply;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AccountServiceRelationProxy {
    @RPCClient("account.service.relation")
    private RelationGrpc.RelationBlockingStub relationBlockingStub;

    public Map<Long, StatReply> queryMidFans(List<Long> mids) {
        if (CollectionUtils.isEmpty(mids)) {
            return Collections.emptyMap();
        }

        MidsReq midsReq = MidsReq.newBuilder()
                .addAllMids(mids)
                .build();
        try {
            StatsReply reply = relationBlockingStub.withDeadlineAfter(2000, TimeUnit.MILLISECONDS)
                    .withWaitForReady()
                    .stats(midsReq);
            return reply != null && !CollectionUtils.isEmpty(reply.getStatReplyMapMap()) ?
                    reply.getStatReplyMapMap() : Collections.emptyMap();
        } catch (Exception e) {
            log.info("queryMidFans error! mids = {}", mids);
            log.error("queryMidFans error!", e);
            return Collections.emptyMap();
        }
    }

}
