package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauCreativeTabBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Integer   id;
    private Integer   accountId;
    private Integer   unitId;
    @CompareMeta(uk = true, comparable = false)
    private Long      creativeId;
    @CompareMeta(shadow = true)
    private Integer jumpType;
    @CompareMeta
    private String    jumpUrl;

    @CompareMeta(shadowKeyGetterMethod = true)
    public Long getShadowKeyGetterMethod() {
        return creativeId;
    }
}
