package sycpb.platform.cpm.pandora.service.impl.core.v6.unit;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauUnitTargetOsVersionUpgradeDao;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitTargetOsVersionUpgradeBo;
import sycpb.platform.cpm.pandora.service.common.InfoReportService;

import java.util.List;

@Service
@RequiredArgsConstructor
public class UnitTargetOsVersionService {
    private final LauUnitTargetOsVersionUpgradeDao lauUnitTargetOsVersionUpgradeDao;
    private final InfoReportService infoReportService;

    public void save(SaveUnitContextBo ctx) {
        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(LauUnitTargetOsVersionUpgradeBo.class, UnitImplMapper.MAPPER::toPo, lauUnitTargetOsVersionUpgradeDao)
                .setOpLog(ctx.genCompareConsumer());
        JooqFunctions.save(ctx.getCurrentVersion().getLauUnitTargetOsVersionUpgradeBos(), ctx.getNewVersion().getLauUnitTargetOsVersionUpgradeBos(), ctx0);
        infoReportService.checkAndUpdateCompensationLogType2(ctx.getInfoReportBo(), ctx0.getCompareBos());
    }

    public List<LauUnitTargetOsVersionUpgradeBo> fetch(Integer unitId) {
        return UnitImplMapper.MAPPER.fromOsVersionPos(lauUnitTargetOsVersionUpgradeDao.fetchByUnitId(unitId));
    }
}
