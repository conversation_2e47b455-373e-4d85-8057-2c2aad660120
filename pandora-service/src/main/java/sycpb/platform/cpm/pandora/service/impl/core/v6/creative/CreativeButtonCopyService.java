package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauCreativeButtonCopyDao;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeButtonCopyPo;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.CreativeBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauCreativeButtonCopyBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.ProgCreativeAuditContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.unit.SaveUnitContextBo;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY;

@Service
@RequiredArgsConstructor
public class CreativeButtonCopyService {
    private final LauCreativeButtonCopyDao lauCreativeButtonCopyDao;

    @Resource(name = MySqlConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;

    public List<LauCreativeButtonCopyBo> fetch(Integer unitId) {
        return UnitCreativeImplMapper.MAPPER.fromLauCreativeButtonCopyPos(lauCreativeButtonCopyDao.fetchByUnitId(unitId));
    }

    public void save(SaveUnitCreativeContextBo ctx) {
        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeButtonCopyBo);
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeButtonCopyBo)
                .stream()
                .filter(x -> NumberUtils.isPositive(x.getButtonCopyId()))
                .collect(Collectors.toList());
        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(LauCreativeButtonCopyBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeButtonCopyDao)
                .setDSLContext(adCore)
                .setTableImpl(LAU_CREATIVE_BUTTON_COPY)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_BUTTON_COPY.ID)
                .setDatabasePrimaryKeyMapper(LauCreativeButtonCopyPo::getId)
                .setOpLog(ctx.genCreativeCompareConsumer());
        JooqFunctions.save(currentVersions, newVersions, ctx0);
    }

    public void save(ProgCreativeAuditContextBo ctx) {
        final var currentVersions = ctx.getCurVersion().getLauCreativeButtonCopyBos();
        final var newVersions = ctx.getNewVersion().getLauCreativeButtonCopyBos();
        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(LauCreativeButtonCopyBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeButtonCopyDao)
                .setDSLContext(adCore)
                .setTableImpl(LAU_CREATIVE_BUTTON_COPY)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_BUTTON_COPY.ID)
                .setDatabasePrimaryKeyMapper(LauCreativeButtonCopyPo::getId)
                .setOpLog(ctx.genCreativeCompareConsumer());
        JooqFunctions.save(currentVersions, newVersions, ctx0);
    }

    public void saveByCreativeId(Integer creativeId, List<LauCreativeButtonCopyBo> buttons, SaveUnitCreativeContextBo ctx) {
        List<LauCreativeButtonCopyPo> lauCreativeButtonCopyPos = lauCreativeButtonCopyDao.fetchByCreativeId(creativeId);
        List<LauCreativeButtonCopyBo> lauCreativeButtonCopyBos = UnitCreativeImplMapper.MAPPER.fromLauCreativeButtonCopyPos(lauCreativeButtonCopyPos);
        JooqFunctions.save(lauCreativeButtonCopyBos, buttons,
                JooqFunctions.JooqSaveContext.minimum(LauCreativeButtonCopyBo.class,
                        UnitCreativeImplMapper.MAPPER::toPo, lauCreativeButtonCopyDao)
                        .setOpLog(ctx.genCreativeCompareConsumer()));
    }

    public void saveJumpUrlByUnitApp(SaveUnitContextBo ctx) {

        if (Objects.isNull(ctx.getAppPackageExtraBo())) {
            return;
        }

        Integer unitId = ctx.getUnitId();
        String appPackageJumpUrl = ctx.getAppPackageExtraBo().getButtonUrl();

        if (!NumberUtils.isPositive(unitId)) {
            return;
        }

        if (StringUtils.isEmpty(appPackageJumpUrl)) {
            return;
        }

        adCore.update(LAU_CREATIVE_BUTTON_COPY)
                .set(LAU_CREATIVE_BUTTON_COPY.JUMP_URL, appPackageJumpUrl)
                .where(LAU_CREATIVE_BUTTON_COPY.UNIT_ID.eq(unitId)
                .and(LAU_CREATIVE_BUTTON_COPY.IS_DELETED.eq(0)))
                .execute();
    }
}
