/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.service.api.core.batch.bos;


import lombok.Data;
import sycpb.platform.cpm.pandora.service.enums.ExecuteMode;

import java.util.List;

@Data
public class LauBatchOperationBo {
    private Long id;
    private Integer accountId;
    private Integer targetType;
    private Integer operatorType;
    private String operatorName;
    private Long operationId;
    private Integer operationType;
    private Integer operationStatus;
    private String operationExt;
    private Integer operationEnv;

    private Long operationRegisterTime;


    private ExecuteMode executeMode; // 1. 同步 2. 异步
    private List<String> syncErrMsg; // 1. 同步状态下错误文案
}
