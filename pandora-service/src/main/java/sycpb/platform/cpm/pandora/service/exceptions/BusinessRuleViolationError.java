package sycpb.platform.cpm.pandora.service.exceptions;

import lombok.Getter;

@Getter
public class BusinessRuleViolationError extends RuntimeException {
    private final int code;

    private BusinessRuleViolationError(int code, String msg) {
        super(msg);
        this.code = code;
    }

    public static BusinessRuleViolationError newInstance(int code, String msg) {
        return new BusinessRuleViolationError(code, msg);
    }

    public static BusinessRuleViolationError newInstance(String msg) {
        return newInstance(-500, msg);
    }
}
