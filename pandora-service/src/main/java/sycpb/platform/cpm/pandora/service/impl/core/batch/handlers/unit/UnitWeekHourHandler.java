package sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.unit;

import org.jooq.DSLContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.compare.OperationType;
import sycpb.platform.cpm.pandora.infra.compare.bos.ChangeLogBo;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.IOperationLogService;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperationLogContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.batch.BatchOperationExtService;

import java.util.Objects;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnit.LAU_UNIT;

@Service
public class UnitWeekHourHandler extends UnitTemplateHandler {
    public UnitWeekHourHandler(BatchOperationExtService batchOperationExtService,
                               IOperationLogService operationLogService,
                               @Qualifier(MySqlConfig.AD_CORE_DSL_CONTEXT) DSLContext adCore) {
        super(operationLogService, batchOperationExtService, adCore);
    }

    @Override
    public void handleOperation(UnitContext ctx) {
        ctx.setLaunchHourLog(genLogHour(ctx.getOperationBo().getLaunchTime()));
    }

    @Override
    public CompareBo compare(UnitContext ctx) {
        final var compareBo = new CompareBo();
        final var curLaunchHourLog = genLogHour(ctx.getLauUnitPo().getLaunchTime());
        compareBo.setCurVersion(curLaunchHourLog);
        compareBo.setNewVersion(ctx.getLaunchHourLog());
        if (!Objects.equals(ctx.getOperationBo().getLaunchTime(), ctx.getLauUnitPo().getLaunchTime())) {
            compareBo.setChanged(true);
            final var logContextBo = OperationLogContextBo.newContext(ctx.getOperatorBo(), "lau_unit");
            compareBo.setOperationLogContextBo(logContextBo);
            logContextBo.getChanges().add(ChangeLogBo.builder()
                    .key("launchTime")
                    .desc("投放时间")
                    .oldValue(curLaunchHourLog)
                    .newValue(ctx.getLaunchHourLog())
                    .build());
            logContextBo.updateContext(ctx.getTargetId(), OperationType.UPDATE);
        }
        return compareBo;
    }

    @Override
    public void batchUpdateTarget(UnitContext ctx) {
        if (CollectionUtils.isEmpty(ctx.getUpdatingTargetIds())) return;

        adCore.update(LAU_UNIT)
                .set(LAU_UNIT.LAUNCH_TIME, ctx.getOperationBo().getLaunchTime())
                .where(LAU_UNIT.UNIT_ID.in(ctx.getUpdatingTargetIds()))
                .execute();
    }

    @Override
    public void batchUpdate(UnitContext ctx) {
        ((UnitWeekHourHandler) AopContext.currentProxy()).batchUpdateWithTx(ctx);
    }

    @Transactional(MySqlConfig.AD_CORE_TX_MGR)
    public void batchUpdateWithTx(UnitContext ctx) {
        ctx.setNeedReportCompensationLogType6(true);
        super.batchUpdate(ctx);
    }

    private String genLogHour(String rawHours) {
        if (Objects.equals(rawHours, "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111")) {
            return "周一-周日:0 ~ 23;";
        }
        final var sb = new StringBuilder();
        for (var i = 0; i < 7; i++) {
            final var dailyHours = rawHours.substring(i * 24, i * 24 + 24);
            if (!dailyHours.contains("1")) continue;

            sb.append(index2WeekDay(i))
                    .append(":");
            Integer start = null, end = null;
            var k = 0;
            for (var j = 0; j < 24; j++) {
                final var cHour = dailyHours.charAt(j);
                if (cHour == '1') {
                    if (start == null) {
                        start = j;
                        k++;
                    }
                    end = j;
                } else if (cHour == '0') {
                    if (start != null) {
                        if (k > 1) {
                            sb.append(",");
                        }
                        if (start.equals(end)) {
                            sb.append(start);
                        } else {
                            sb.append(start)
                                    .append("~")
                                    .append(end);
                        }
                        start = null;
                        end = null;
                    }
                } else {
                    throw new IllegalArgumentException("时间定向的值非法");
                }
            }
            if (start != null) {
                if (k > 1) {
                    sb.append(",");
                }
                if (start.equals(end)) {
                    sb.append(start);
                } else {
                    sb.append(start)
                            .append("~")
                            .append(end);
                }
            }
            sb.append(";");
        }
        return sb.toString();
    }

    private String index2WeekDay(int x) {
        switch (x) {
            case 0:
                return "周一";
            case 1:
                return "周二";
            case 2:
                return "周三";
            case 3:
                return "周四";
            case 4:
                return "周五";
            case 5:
                return "周六";
            case 6:
                return "周日";
            default:
                throw new IllegalArgumentException("无法解析序号");
        }
    }
}
