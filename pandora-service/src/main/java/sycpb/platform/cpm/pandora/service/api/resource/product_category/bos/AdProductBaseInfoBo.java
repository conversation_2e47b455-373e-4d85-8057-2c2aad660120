package sycpb.platform.cpm.pandora.service.api.resource.product_category.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName AdProductCategoryBo
 * <AUTHOR>
 * @Date 2024/6/9 2:23 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdProductBaseInfoBo {

    private Long      id;
    private Integer   accountId;
    private Long      libraryId;
    private String    name;
    private Integer   bizStatus;
    private Long      firstCategoryCode;
    private Long      secondCategoryCode;
    private Long      thirdCategoryCode;
    private Long   skuCode;

}
