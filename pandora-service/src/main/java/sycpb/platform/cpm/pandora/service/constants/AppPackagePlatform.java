package sycpb.platform.cpm.pandora.service.constants;

import com.google.common.collect.Sets;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * @ClassName AppPackagePlatform
 * <AUTHOR>
 * @Date 2024/6/9 7:00 下午
 * @Version 1.0
 **/
public class AppPackagePlatform {

    public static final Integer IOS = 1;
    public static final Integer ANDROID = 2;
    public static final Integer IPHONE = 3;
    public static final Integer IPAD = 4;

    public static final Set<Integer> PLATFORM_IOS_SET = Sets.newHashSet(IOS, IPHONE, IPAD);
}
