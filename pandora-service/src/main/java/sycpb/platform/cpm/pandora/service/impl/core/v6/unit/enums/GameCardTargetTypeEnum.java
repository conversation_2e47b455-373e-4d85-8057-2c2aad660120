package sycpb.platform.cpm.pandora.service.impl.core.v6.unit.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GameCardTargetTypeEnum {

	TAG(1, "标签定向"),
	ARCHIVE(2, "视频定向"),
	CATEGORY(3, "分区定向");

	private Integer code;
	private String name;

	public static GameCardTargetTypeEnum getByCode(int code) {
		for (GameCardTargetTypeEnum bean : values()) {
			if (bean.getCode() == code) {
				return bean;
			}
		}
		throw new IllegalArgumentException("unknown code GameCardTargetTypeEnum " + code);
	}
}
