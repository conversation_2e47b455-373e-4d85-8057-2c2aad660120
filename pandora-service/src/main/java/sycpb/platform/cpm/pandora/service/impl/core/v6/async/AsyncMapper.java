package sycpb.platform.cpm.pandora.service.impl.core.v6.async;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveCreativeAsyncBaseContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;

/**
 * @ClassName AsyncMapper
 * <AUTHOR>
 * @Date 2024/8/5 8:50 下午
 * @Version 1.0
 **/
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AsyncMapper {

    AsyncMapper MAPPER = Mappers.getMapper(AsyncMapper.class);

    SaveUnitCreativeContextBo toSaveCtx(SaveCreativeAsyncBaseContextBo x);

}
