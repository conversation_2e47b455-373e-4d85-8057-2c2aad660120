package sycpb.platform.cpm.pandora.service.impl.core.batch;

import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import org.springframework.stereotype.Service;
import sycpb.platform.cpm.pandora.infra.databus.DatabusPrototypePublisher;

@Service
public class BatchPostOperationPub extends DatabusPrototypePublisher {
    public BatchPostOperationPub(DatabusProperties databusProperties, DatabusTemplate databusTemplate) {
        super("batch-post-op", databusProperties, databusTemplate);
    }
}
