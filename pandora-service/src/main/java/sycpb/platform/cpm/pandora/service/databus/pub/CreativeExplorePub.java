package sycpb.platform.cpm.pandora.service.databus.pub;

import com.alibaba.fastjson.JSON;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore.CreativeExploreBo;

import javax.annotation.Resource;

/**
 * @ClassName CreativeExplorePub
 * <AUTHOR>
 * @Date 2025/3/21 7:23 下午
 * @Version 1.0
 **/
@Slf4j
@Service
public class CreativeExplorePub {

    private final String topic;
    private final String group;

    public static final String CREATIVE_EXPLORE_DATABUS_KEY = "creative-explore";

    @Resource
    private DatabusTemplate databusTemplate;

    public CreativeExplorePub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(CREATIVE_EXPLORE_DATABUS_KEY);
        topic = databusProperty.getTopic();
        group = databusProperty.getPub().getGroup();
    }

    public void pub(String key, CreativeExploreBo exploreBo) {
        log.info("CreativeExplorePub pub msg, key={}, exploreBo = {}", key, JSON.toJSONString(exploreBo));
        Assert.notNull(exploreBo, "探索创意内容不可为空");
        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(key, exploreBo)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("CreativeExplorePub pub msg success, msg={}", JSON.toJSONString(exploreBo));
        } else {
            Throwable throwable = result.getThrowable();
            log.error("CreativeExplorePub pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }
    }

}
