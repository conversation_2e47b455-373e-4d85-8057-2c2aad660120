package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.DSLContext;
import org.jooq.Record2;
import org.jooq.Result;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Service;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos.LauCreativeExploreCandidateMappingDao;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauCreativeExploreCandidateMappingPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeImagePo;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauCreativeImageBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore.LauCreativeExploreCandidateMappingBo;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.count;
import static org.jooq.impl.DSL.max;
import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeImage.LAU_CREATIVE_IMAGE;

/**
 * @ClassName CreativeExploreCandidateService
 * <AUTHOR>
 * @Date 2025/3/27 10:39 下午
 * @Version 1.0
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class CreativeExploreCandidateService {

    private final LauCreativeExploreCandidateMappingDao lauCreativeExploreCandidateMappingDao;

    @Resource(name = MySqlConfig.AD_DSL_CONTEXT)
    private DSLContext ad;

    public Map<Integer, Integer> countByCreativeIds(List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.emptyMap();
        }

        Result<Record2<Integer, Integer>> result = ad.select(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CREATIVE_ID, count(DSL.one()))
                .from(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING)
                .where(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CANDIDATE_CREATIVE_ID.ne(0))
                .groupBy(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CREATIVE_ID)
                .fetch();

        return result.stream()
                .collect(Collectors.toMap(
                        record -> record.get(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CREATIVE_ID),
                        record -> record.get(DSL.count(DSL.one()))
                ));
    }

    public List<LauCreativeExploreCandidateMappingBo> fetch(Integer unitId) {
        List<LauCreativeExploreCandidateMappingPo> pos = lauCreativeExploreCandidateMappingDao.fetchByUnitId(unitId);
        return pos.stream()
                .map(UnitCreativeImplMapper.MAPPER::fromPo)
                .collect(Collectors.toList());
    }

    public List<LauCreativeExploreCandidateMappingBo> fetchByCreativeId(Integer creativeId) {
        List<LauCreativeExploreCandidateMappingPo> pos = lauCreativeExploreCandidateMappingDao.fetchByCreativeId(creativeId);
        return pos.stream()
                .map(UnitCreativeImplMapper.MAPPER::fromPo)
                .collect(Collectors.toList());
    }

    public List<LauCreativeExploreCandidateMappingBo> fetchActualCandidateMappingByCreativeId(Integer creativeId) {
        List<LauCreativeExploreCandidateMappingPo> pos = lauCreativeExploreCandidateMappingDao.fetchByCreativeId(creativeId);
        return pos.stream()
                .filter(po -> NumberUtils.isPositive(po.getCandidateCreativeId()))
                .map(UnitCreativeImplMapper.MAPPER::fromPo)
                .collect(Collectors.toList());
    }

    public Integer getLastFakeCreativeIdByUnitId(Integer unitId) {
        if (!NumberUtils.isPositive(unitId)) {
            return null;
        }

        List<LauCreativeExploreCandidateMappingBo> bos = ad.selectFrom(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING)
                .where(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.UNIT_ID.eq(unitId)
                        .and(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CANDIDATE_CREATIVE_ID.eq(0)))
                .orderBy(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.ID.desc())
                .limit(1)
                .fetch().into(LauCreativeExploreCandidateMappingBo.class);


        if (CollectionUtils.isEmpty(bos)) {
            return null;
        }

        return bos.get(0).getCreativeId();
    }

    public void save(List<LauCreativeExploreCandidateMappingBo> currentVersions,
                     List<LauCreativeExploreCandidateMappingBo> newVersions) {

        log.info("creative explore candidate save mapping, currentVersions:{}, newVersions:{}",
                JSON.toJSONString(currentVersions), JSON.toJSONString(newVersions));

        final var ctx0 =
                JooqFunctions.JooqSaveContext.minimum(LauCreativeExploreCandidateMappingBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeExploreCandidateMappingDao)
                .setDSLContext(ad)
                .setTableImpl(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.ID)
                .setDatabasePrimaryKeyMapper(LauCreativeExploreCandidateMappingPo::getId);
        JooqFunctions.save(currentVersions, newVersions, ctx0);
    }

    public List<LauCreativeExploreCandidateMappingBo> getPageInOrder(Long startId, Integer limit) {
        List<LauCreativeExploreCandidateMappingPo> poList = ad.selectFrom(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING)
                .where(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.ID.gt(startId))
                .orderBy(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.ID.asc())
                .limit(limit)
                .fetch().into(LauCreativeExploreCandidateMappingPo.class);

        return poList.stream()
                .map(UnitCreativeImplMapper.MAPPER::fromPo)
                .collect(Collectors.toList());
    }

    public void batchDeleteByCreativeIds(List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return;
        }

        log.info("CreativeExploreCandidateService batchDeleteByCreativeIds creativeIds:{}", JSON.toJSONString(creativeIds));
        ad.deleteFrom(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING)
                .where(LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CREATIVE_ID.in(creativeIds))
                .execute();
    }
}
