package sycpb.platform.cpm.pandora.service.api.core.legacy.underframe_component;

import sycpb.platform.cpm.pandora.service.api.core.creative_component.UnderframeComponentBo;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.LegacyOperationContextBo;

import java.util.List;

public interface LegacyUnderframeComponentService {
    UnderframeComponentBo get(int accountId, long componentId);

    long save(LegacyOperationContextBo ctx, UnderframeComponentBo bo);

    List<UnderframeComponentBo> list(LegacyQueryUnderframeComponentBo query);

    void delete(LegacyOperationContextBo ctx, int accountId, long componentId);
}
