package sycpb.platform.cpm.pandora.service.impl.core.v6.unit;

import com.bapis.ad.account.crm.acc.AccountBaseReply;
import com.bapis.ad.account.crm.acc.AccountIdReq;
import com.bapis.ad.account.crm.acc.CrmAccountServiceGrpc;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.OcpxBidSectionPo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.OcpxBidSectionBo;
import sycpb.platform.cpm.pandora.service.proxy.CpmAdAccountProxy;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TOcpxBidSection.OCPX_BID_SECTION;


@Service
@RequiredArgsConstructor
public class OcpxBidSectionService {

    @Resource(name = MySqlConfig.AD_DSL_CONTEXT)
    private DSLContext ad;

    @Resource()
    private CpmAdAccountProxy cpmAdAccountProxy;


    @Value("${ocpx.effective.nums:200}")
    private Integer effectiveNums;

    public OcpxBidSectionBo getOcpxBidSectionBoByAccountId(Integer ocpxTarget, Integer accountId) {
        if (ocpxTarget == 0) {
            return null;
        }
        AccountBaseReply accountBase = cpmAdAccountProxy.getAccountBase(accountId);
        int categoryFirstId = Optional.of(accountBase.getData().getCommerceCategoryFirstId()).orElse(0);
        int categorySecondId = Optional.of(accountBase.getData().getCommerceCategorySecondId()).orElse(0);

        Condition condition = OCPX_BID_SECTION.OCPX_TARGET_ID.eq(ocpxTarget)
                .and(OCPX_BID_SECTION.CATEGORY_FIRST_ID.in(List.of(0, categoryFirstId)))
                .and(OCPX_BID_SECTION.CATEGORY_SECOND_ID.in(List.of(0, categorySecondId)))
                .and(OCPX_BID_SECTION.EFFECTIVE_NUMS.gt(effectiveNums));
        List<OcpxBidSectionPo> pos = ad.fetch(OCPX_BID_SECTION, condition).into(OcpxBidSectionPo.class);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        OcpxBidSectionPo po = null;
        for (OcpxBidSectionPo x : pos) {
            // 大于有效个数
            if (x.getEffectiveNums() > effectiveNums) {
                // 优先取细化到二级行业的
                if (po == null || (po.getCategorySecondId() == 0 && x.getCategorySecondId() != 0)) {
                    po = x;
                }
                if (po.getCategoryFirstId() == 0 && x.getCategoryFirstId() != 0) {
                    po = x;
                }
            }
        }
        if (po == null) {
            return null;
        }
        return OcpxBidSectionMapper.MAPPER.toBo(po);
    }
}
