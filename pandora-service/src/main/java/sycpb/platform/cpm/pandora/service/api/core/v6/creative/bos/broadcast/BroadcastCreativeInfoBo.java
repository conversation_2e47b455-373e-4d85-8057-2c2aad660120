package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.broadcast;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BroadcastCreativeInfoBo {
    private String creative_id;

    private Integer is_dpa;

    private Integer ad_type;


    private Integer adp_version;

    private Integer version;

    //private Long mtime;


    private List<BroadcastCreativeInfoTitleBo> titles;

    private List<BroadcastCreativeInfoDescriptionBo> descriptions;

    private List<BroadcastCreativeInfoImageBo> images;

    private List<BroadcastCreativeInfoArchiveBo> archives;

    private List<BroadcastCreativeInfoDynamicBo> dynamics;


    private List<BroadcastCreativeInfoSubjectBo> subjects;

    private List<BroadcastCreativeInfoLandingPageGroupBo> landing_page_groups;

    private List<BroadcastCreativeInfoRawJumpUrlBo> raw_jump_urls;

    private List<BroadcastCreativeInfoTabUrlBo> tab_urls;

    private List<BroadcastCreativeInfoLinkReplaceBo> link_replaces;

    private List<BroadcastCreativeInfoComponentBo> components;

    private List<BroadcastCreativeInfoGameCardBo> game_cards;


}
