package sycpb.platform.cpm.pandora.service.databus.pub.bo.parent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 自定义创意消息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ParentChildProgramCreativeDatabusMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    // 共有元素审核结果
    private ParentChildProgramElementAuditDatabusMsg common_audit;

    // 主元素副元素审核结果
    private List<ParentChildProgramElementAuditDatabusMsg> element_audit;
}