package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

import java.sql.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauUnitCreativeBo {
    @CompareMeta(uk = true, comparable = false, copyFromCurrentVersion = true, logGroupId = true)
    private Integer creativeId;
    private Integer accountId;
    private Integer campaignId;
    private Integer unitId;
    @CompareMeta(logDescription = "创意名称")
    private String creativeName;
    @CompareMeta(shadow = true)
    private String promotionPurposeContent;
    @CompareMeta(shadow = true)
    private String title;
    @CompareMeta(logDescription = "创意描述", shadow = true)
    private String description;
    @CompareMeta
    private String imageUrl;
    @CompareMeta
    private String imageMd5;
    @CompareMeta(shadow = true)
    private Long videoId;
    @CompareMeta
    private String reason;
    @CompareMeta(logDescription = "审核状态")
    private Integer auditStatus;
    @CompareMeta
    private Integer status;
    @CompareMeta(logDescription = "旧版商业标")
    private Integer cmMark;
    @CompareMeta(logDescription = "按钮文案")
    private String buttonCopy;
    @CompareMeta
    private Integer creativeStatus;
    @CompareMeta(logDescription = "唤起链接")
    private String    schemeUrl;
    @CompareMeta
    private Integer   adVersionControllId;
    @CompareMeta(shadow = true, logDescription = "建站落地页id")
    private Long mgkPageId;
    @CompareMeta(logDescription = "新版商业标id")
    private Integer busMarkId;
    private Integer adpVersion;
    @CompareMeta(logDescription = "模板组id")
    private Integer templateGroupId;
    @CompareMeta
    private Integer isProgrammatic;
    @CompareMeta
    private Integer progAuditStatus;
    @CompareMeta
    private Integer progMiscElemAuditStatus;
    @CompareMeta(logDescription = "唤起链接是否自动填充")
    private Integer isAutoFill;
    @CompareMeta(shadow = true)
    private String promotionPurposeContentSecondary;
    @CompareMeta
    private String trackadf;
    private Integer advertisingMode;
    @CompareMeta
    private Integer isPageGroup;

    @CompareMeta(logDescription = "是否mapi操作")
    private Integer flag;
    @CompareMeta(logDescription = "母创意id")
    private Integer parentCreativeId;

    private Date beginTime;
    private Date endTime;
    private Integer isHistory;
    @CompareMeta(shadow = true)
    private Integer jumpType;
    private Long titleId;
    private Long materialId;
    private Integer creativeType;
    private Integer isRecheck;
    private Integer bilibiliUserId;
    private Long ctime;
    private Integer isManaged;


    private String gameCardAccessUrl;

    @CompareMeta(shadowKeyGetterMethod = true)
    public Integer getShadowKeyGetterMethod() {
        return creativeId;
    }
}
