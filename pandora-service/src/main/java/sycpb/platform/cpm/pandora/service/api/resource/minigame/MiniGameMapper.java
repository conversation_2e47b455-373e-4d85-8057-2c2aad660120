package sycpb.platform.cpm.pandora.service.api.resource.minigame;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauMiniGamePo;
import sycpb.platform.cpm.pandora.service.api.resource.minigame.bos.MiniGameBo;

import java.util.List;

/**
 * @ClassName MiniGameMapper
 * <AUTHOR>
 * @Date 2024/6/24 5:10 下午
 * @Version 1.0
 **/
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MiniGameMapper {

    MiniGameMapper MAPPER = Mappers.getMapper(MiniGameMapper.class);

    MiniGameBo fromPo(LauMiniGamePo po);

    List<MiniGameBo> fromPos(List<LauMiniGamePo> pos);

}
