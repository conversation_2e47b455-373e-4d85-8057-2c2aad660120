package sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.unit;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.DSLContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.compare.OperationType;
import sycpb.platform.cpm.pandora.infra.compare.bos.ChangeLogBo;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.IOperationLogService;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperationLogContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.batch.BatchOperationExtService;

import javax.annotation.Resource;
import java.util.List;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitTargetRule.LAU_UNIT_TARGET_RULE;

@Service
public class UnitGenderHandler extends UnitTargetTemplateHandler {
    @Resource
    private ObjectMapper om;

    public UnitGenderHandler(IOperationLogService operationLogService,
                             BatchOperationExtService batchOperationExtService,
                             @Qualifier(MySqlConfig.AD_CORE_DSL_CONTEXT) DSLContext adCore) {
        super(operationLogService, batchOperationExtService, adCore);
    }

    @Override
    public CompareBo compare(UnitContext ctx) {
        final var compareBo = new CompareBo();
        final var curGender = UnitTargetTemplateHandler.parseTarget(ctx.getLauUnitTargetRulePo().getGender(), om);
        final var newGender = ctx.getOperationBo().getTarget().getGender();
        compareBo.setCurVersion(curGender.toString());
        compareBo.setNewVersion(newGender.toString());
        if (!UnitTargetTemplateHandler.equals(curGender, newGender)) {
            compareBo.setChanged(true);
            final var logContextBo = OperationLogContextBo.newContext(ctx.getOperatorBo(), "lau_unit");
            compareBo.setOperationLogContextBo(logContextBo);
            logContextBo.setChanges(List.of(ChangeLogBo.builder()
                    .key("gender")
                    .desc("性别")
                    .oldValue(curGender.toString())
                    .newValue(newGender.toString())
                    .build()));
            logContextBo.updateContext(ctx.getTargetId(), OperationType.UPDATE);
        }
        return compareBo;
    }

    @Override
    public void batchUpdateTarget(UnitContext ctx) {
        if (CollectionUtils.isEmpty(ctx.getUpdatingTargetIds())) return;

        adCore.update(LAU_UNIT_TARGET_RULE)
                .set(LAU_UNIT_TARGET_RULE.GENDER, UnitTargetTemplateHandler.formatTarget(om, ctx.getOperationBo().getTarget().getGender()))
                .where(LAU_UNIT_TARGET_RULE.UNIT_ID.in(ctx.getUpdatingTargetIds()))
                .execute();
    }

    @Override
    public void batchUpdate(UnitContext ctx) {
        ((UnitGenderHandler) AopContext.currentProxy()).batchUpdateWithTx(ctx);
    }

    @Transactional(MySqlConfig.AD_CORE_TX_MGR)
    public void batchUpdateWithTx(UnitContext ctx) {
        ctx.setNeedReportCompensationLogType2(true);
        super.batchUpdate(ctx);
    }
}
