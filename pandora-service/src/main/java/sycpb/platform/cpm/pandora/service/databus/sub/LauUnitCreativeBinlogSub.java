package sycpb.platform.cpm.pandora.service.databus.sub;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.config.BizProperties;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitCreativePo;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.constants.*;
import sycpb.platform.cpm.pandora.service.databus.pub.bo.CreativeBinlogBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.business.content.BusinessContentService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.NativeCreativeRelativityChangeVideoService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.NativeDarkCreativeCpsReplaceLinkService;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/5/20
 **/
@Slf4j
@Service
public class LauUnitCreativeBinlogSub implements MessageListener {

    @Resource
    private BusinessContentService businessContentService;
    @Resource
    private NativeDarkCreativeCpsReplaceLinkService nativeDarkCreativeCpsReplaceLinkService;
    @Resource
    private BizProperties bizProperties;
    @Resource
    private NativeCreativeRelativityChangeVideoService nativeCreativeRelativityChangeVideoService;


    public static final String CREATIVE_BINLOG = "creative-binlog";
    private final String topic;
    private final String group;


    public LauUnitCreativeBinlogSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(CREATIVE_BINLOG);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
    }

    private void handleMsg(JSON json) {
        JSONObject jsonObject = (JSONObject) json;
        String action = jsonObject.getString(BinlogConstant.ACTION);
        if (StringUtils.isEmpty(action)) {
            return;
        }
        handleBusinessContent(jsonObject, action);

        // 处理原生暗投的创意的信息保存到替链表
        handleNativeDarkCreativeCpsReplaceLink(jsonObject, action);

        // 处理原生相关性换稿件情况
        handleNativeCreativeRelativity(jsonObject, action);
    }

    private void handleNativeCreativeRelativity(JSONObject jsonObject, String action) {

        JSONObject oldObject = jsonObject.getJSONObject(BinlogConstant.OLD);
        CreativeBinlogBo oldBo = deserializeCreativeBinlogDto(oldObject);
        JSONObject newObject = jsonObject.getJSONObject(BinlogConstant.NEW);
        CreativeBinlogBo newBo = deserializeCreativeBinlogDto(newObject);

        if (Objects.nonNull(newBo)
                && Objects.equals(newBo.getIsManaged(), IsManaged.EXPLORE)
                && !NumberUtils.isPositive(newBo.getParentCreativeId())) {
            return;
        }

        // 新三连 & 只处理编辑，并且是审核通过/驳回 & videoId大于0 & videoId 发生了变化
        if (AdpVersion.MODEL_MERGE == newBo.getAdpVersion() && (AuditStatus.isPassed(newBo.getAuditStatus()) || AuditStatus.isRejected(newBo.getAuditStatus()))
                && NumberUtils.isPositive(newBo.getVideoId()) && !Objects.equals(oldBo.getVideoId(), newBo.getVideoId())) {
            nativeCreativeRelativityChangeVideoService.process(newBo);
        }
    }


    private void handleBusinessContent(JSONObject jsonObject, String action) {
        JSONObject oldObject = jsonObject.getJSONObject(BinlogConstant.OLD);
        CreativeBinlogBo oldBo = deserializeCreativeBinlogDto(oldObject);
        JSONObject newObject = jsonObject.getJSONObject(BinlogConstant.NEW);
        CreativeBinlogBo newBo = deserializeCreativeBinlogDto(newObject);

        if (Objects.nonNull(newBo)
                && Objects.equals(newBo.getIsManaged(), IsManaged.EXPLORE)
                && !NumberUtils.isPositive(newBo.getParentCreativeId())) {
            return;
        }

        if ((null == oldBo && AuditStatus.PASSED == newBo.getAuditStatus())
                || (null != oldBo && AuditStatus.PASSED != oldBo.getAuditStatus() && AuditStatus.PASSED == newBo.getAuditStatus())) {
            //新三连创意走Pandora
            if (AdpVersion.MODEL_MERGE == newBo.getAdpVersion()) {
                LauUnitCreativePo po = new LauUnitCreativePo();
                BeanUtils.copyProperties(newBo, po);
                businessContentService.pub(po);
            }
        }
    }

    /**
     * 将原生暗投的创意的信息保存到替链表
     * @param jsonObject
     * @param action
     */
    private void handleNativeDarkCreativeCpsReplaceLink(JSONObject jsonObject, String action) {
        if (!BinlogConstant.INSERT.equals(action) && !BinlogConstant.UPDATE.equals(action)) {
            return;
        }
        JSONObject oldObject = jsonObject.getJSONObject(BinlogConstant.OLD);
        CreativeBinlogBo oldBo = deserializeCreativeBinlogDto(oldObject);
        JSONObject newObject = jsonObject.getJSONObject(BinlogConstant.NEW);
        CreativeBinlogBo newBo = deserializeCreativeBinlogDto(newObject);

        // 探索 假母创意
        if (Objects.nonNull(newBo)
                && Objects.equals(newBo.getIsManaged(), IsManaged.EXPLORE)
                && !NumberUtils.isPositive(newBo.getParentCreativeId())) {
            return;
        }

        // 新三连 & 审核通过 & 创意有效 & 视频id大于0 && && 稿件模板组
        if (AdpVersion.MODEL_MERGE == newBo.getAdpVersion() && AuditStatus.PASSED == newBo.getAuditStatus()
                && CreativeStatus.VALID == newBo.getCreativeStatus() && NumberUtils.isPositive(newBo.getVideoId())
                 && TemplateGroup.isBiliArchive(newBo.getTemplateGroupId())) {
            if (NumberUtils.isPositive(bizProperties.getCpsLinkReplaceBinlogSwitch())) {
                nativeDarkCreativeCpsReplaceLinkService.process(newBo.getCreativeId());
            }
        }
    }

    private CreativeBinlogBo deserializeCreativeBinlogDto(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        CreativeBinlogBo creativeBinlogBo = null;
        try {
            creativeBinlogBo = insertObject.toJavaObject(CreativeBinlogBo.class);
        } catch (Exception e) {
            log.error("deserializeCreativeBinlogDto error, insertObject:{}", insertObject, e);
        }
        return creativeBinlogBo;
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return true;
    }


    @Override
    public void onMessage(AckableMessage ackableMessage) {
        log.info("wrap onMessage, ackableMessage={}", JSON.toJSONString(ackableMessage));

        try {
            String value = new String(ackableMessage.payload());
            JSONObject msg = JSONObject.parseObject(value);
            handleMsg(msg);
        } catch (Exception e) {
            log.error("CreativeBinlogSubTask error", e);
        }
    }

}
