package sycpb.platform.cpm.pandora.service.api.resource.rule;

import com.bapis.ad.location.adp.v6.*;
import com.bapis.ad.pandora.resource.BusMarkInfo;
import com.bapis.ad.pandora.resource.CreativeContentConfigInfo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.infra.common.EnumMapper;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.BusMarkParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.CreativeContentParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.NativeParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.*;

import java.util.List;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = {EnumMapper.class, StdRuleInputMapper.class})
public interface CreativeRuleMapper {
    CreativeRuleMapper MAPPER = Mappers.getMapper(CreativeRuleMapper.class);

    @Mapping(target = "templateIds", source = "templateId")
    SlotGroupTemplateMappingBo fromRo(SlotGroupMappingConfigInfo x);
    @Mapping(target = "score", expression = "java(StdRuleInputMapper.calScore(x.getInput()))")
    StdRuleBo<SlotGroupTemplateMappingBo> fromRo(SlotGroupMappingConfigEntity x);
    List<StdRuleBo<SlotGroupTemplateMappingBo>> toSlotGroupTemplateMappingBos(List<SlotGroupMappingConfigEntity> x);

    @Mapping(target = "promotionPurposeTypes", source = "promotionPurposeType")
    @Mapping(target = "promotionContentTypes", source = "promotionContentType")
    @Mapping(target = "deepCpaTargetIds", source = "deepCpaTarget")
    @Mapping(target = "cpaTargetIds", source = "cpaTarget")
    @Mapping(target = "accountLabelIds", source = "accountLabelId")
    @Mapping(target = "baseTargetIds", source = "baseTarget")
    NativeParamBo fromRo(SupportNativeArcConfigInput x);
    @Mapping(target = "score", expression = "java(StdRuleInputMapper.calScore(x.getInput()))")
    RuleBo<NativeParamBo, NativeBo> fromRo(SupportNativeArcConfigEntity x);
    List<RuleBo<NativeParamBo, NativeBo>> toSupportBiliNativeRules(List<SupportNativeArcConfigEntity> x);

    @Mapping(target = "cardTypeIds", source = "cardTypeId")
    @Mapping(target = "accountLabelIds", source = "accountLabelId")
    BusMarkParamBo fromRo(BusMarkConfigInput x);
    @Mapping(target = "score", expression = "java(StdRuleInputMapper.calScore(x.getInput()))")
    RuleBo<BusMarkParamBo, BusMarkBo> fromRo(BusMarkConfigEntity x);
    List<RuleBo<BusMarkParamBo, BusMarkBo>> toBusMarkRules(List<BusMarkConfigEntity> x);
    @Mapping(target = "busMarkStyleList", source = "busMarkStyle")
    BusMarkBo fromRo(BusMarkInfo x);

    @Mapping(target = "sceneIds", source = "sceneId")
    @Mapping(target = "launchModes", source = "launchMode")
    @Mapping(target = "cpaTargets", source = "cpaTarget")
    @Mapping(target = "promotionPurposeTypes", source = "promotionPurposeType")
    @Mapping(target = "promotionContentTypes", source = "promotionContentType")
    @Mapping(target = "accountLabelIds", source = "accountLabelId")
    CreativeContentParamBo fromRo(CreativeContentConfigInput x);
    @Mapping(target = "supportYellowCarComponentType", source = "supportYellowCarComponentType")
    CreativeContentBo fromRo(CreativeContentConfigInfo x);
    @Mapping(target = "score", expression = "java(StdRuleInputMapper.calScore(x.getInput()))")
    RuleBo<CreativeContentParamBo, CreativeContentBo> fromRo(CreativeContentConfigEntity x);
    List<RuleBo<CreativeContentParamBo, CreativeContentBo>> toCreativeContentRules(List<CreativeContentConfigEntity> x);

    CreativeContentBo copy(CreativeContentBo bo);
}
