package sycpb.platform.cpm.pandora.service.impl.resource.qualification;

import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauQualificationPackagePo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauQualificationPo;
import sycpb.platform.cpm.pandora.service.api.resource.qualification.LauQualificationMapper;
import sycpb.platform.cpm.pandora.service.api.resource.qualification.bos.LauQualificationBo;
import sycpb.platform.cpm.pandora.service.api.resource.qualification.bos.LauQualificationPackageBo;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauQualification.LAU_QUALIFICATION;
import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE;

/**
 * @ClassName ResQualificationService
 * <AUTHOR>
 * @Date 2024/6/24 5:53 下午
 * @Version 1.0
 **/
@Service
public class ResQualificationService {

    @Resource(name = MySqlConfig.AD_DSL_CONTEXT)
    private DSLContext ad;

    public List<LauQualificationBo> fetchByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<LauQualificationPo> pos = ad.selectFrom(LAU_QUALIFICATION)
                .where(LAU_QUALIFICATION.ID.in(ids))
                .and(LAU_QUALIFICATION.IS_DELETED.eq(0))
                .fetch().into(LauQualificationPo.class);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return LauQualificationMapper.MAPPER.fromPos(pos);
    }

    public List<LauQualificationPackageBo> fetchPackageByPackageIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<LauQualificationPackagePo> pos = ad.selectFrom(LAU_QUALIFICATION_PACKAGE)
                .where(LAU_QUALIFICATION_PACKAGE.ID.in(ids))
                .and(LAU_QUALIFICATION_PACKAGE.IS_DELETED.eq(0))
                .fetch().into(LauQualificationPackagePo.class);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return LauQualificationMapper.MAPPER.fromPackagePos(pos);
    }
}
