package sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.program.MaterialAuditBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.program.MiscElemBo;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProgCreativeAuditBo {

    // 额外元素审核信息
    private MiscElemBo miscElemBo;

    // 主副元素审核列表
    private List<MaterialAuditBo> materialBos;

    // 是否需要审核父创意
    private Boolean needProcessParentCreative;
}
