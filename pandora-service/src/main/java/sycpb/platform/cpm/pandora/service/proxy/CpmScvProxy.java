package sycpb.platform.cpm.pandora.service.proxy;


import com.bapis.ad.archive.*;
import com.bapis.ad.component.*;
import com.bapis.ad.ott.AdOttServiceGrpc;
import com.bapis.ad.ott.BatchVideoOttAuditReq;
import com.bapis.ad.ott.OttAuditApp;
import com.bapis.ad.ott.OttAuditStatus;
import com.bapis.ad.scv.anchor.NativeAnchorServiceGrpc;
import com.bapis.ad.scv.anchor.QueryAnchorRep;
import com.bapis.ad.scv.anchor.QueryAnchorReq;
import com.bapis.ad.scv.anchor.SingleQueryAnchorRep;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraAssert;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauCreativeComponentBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.AnchorInfoBo;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CpmScvProxy {
    @RPCClient("sycpb.cpm.scv")
    private CommentComponentServiceGrpc.CommentComponentServiceBlockingStub commentComponentServiceBlockingStub;

    @RPCClient("sycpb.cpm.scv")
    private CmArchiveServiceGrpc.CmArchiveServiceBlockingStub cmArchiveServiceBlockingStub;

    @RPCClient("sycpb.cpm.scv")
    private NativeAnchorServiceGrpc.NativeAnchorServiceBlockingStub nativeAnchorServiceBlockingStub;

    @RPCClient("sycpb.cpm.scv")
    private StoryComponentServiceGrpc.StoryComponentServiceBlockingStub storyComponentServiceBlockingStub;

    @RPCClient("sycpb.cpm.scv")
    private AdOttServiceGrpc.AdOttServiceBlockingStub adOttServiceBlockingStub;

    public List<CommentComponent> componentsTop(List<Long> avids, int pn, int ps) {
        ComponentsReply components = commentComponentServiceBlockingStub.components(ComponentsReq.newBuilder()
                .addAllAid(avids).addAllStatusValue(Lists.newArrayList(CommentComponentStatus.TOP.getNumber())).setPn(pn)
                .setPs(ps).build());
        return components.getComponentsList();
    }


    public List<CommentComponent> components(List<Long> avids, int pn, int ps) {
        ComponentsReply components = commentComponentServiceBlockingStub.components(ComponentsReq.newBuilder().addAllAid(avids).setPn(pn)
                .setPs(ps).build());
        return components.getComponentsList();
    }

    public Map<Long, CommentComponent> getComponentsMap(List<Long> avids, int pn, int ps) {
        return  components(avids, pn, ps).stream().collect(Collectors.toMap(CommentComponent::getAid, t -> t));
    }

    /**
     * 获取小号稿件的id
     *
     * @param avIds
     * @return
     */
    public Set<Long> getCmVideoSet(List<Long> avIds) {
        QueryArchiveExistsReq.Builder reqBuilder = QueryArchiveExistsReq.newBuilder()
                .addAllAvids(avIds);
        QueryArchiveExistsReply resp = cmArchiveServiceBlockingStub.queryArchiveExists(reqBuilder.build());
        if (resp.getListCount() == 0) {
            return new HashSet<>();
        }
        return resp.getListList().stream().filter(SingleQueryArchiveExistsReply::getExist).map(SingleQueryArchiveExistsReply::getAvid).collect(Collectors.toSet());
    }

    public Boolean archiveExists(Long avid) {
        QueryArchiveExistsReq req = QueryArchiveExistsReq.newBuilder().addAvids(avid).build();
        QueryArchiveExistsReply archiveExistsReply = cmArchiveServiceBlockingStub.withDeadlineAfter(1, TimeUnit.SECONDS)
                .queryArchiveExists(req);
        return archiveExistsReply.getListList().stream().map(SingleQueryArchiveExistsReply::getExist).collect(Collectors.toList()).contains(true);
    }

    public Map<Long, SingleQueryAnchorRep> getBizAnchorResMap(List<Long> avids) {
        QueryAnchorReq queryAnchorReq = QueryAnchorReq.newBuilder().addAllAid(avids).build();
        QueryAnchorRep queryAnchorRep = nativeAnchorServiceBlockingStub.queryAnchorList(queryAnchorReq);
        List<SingleQueryAnchorRep> singleQueryAnchorReps = queryAnchorRep.getDataList();
        return singleQueryAnchorReps.stream().collect(Collectors.toMap(SingleQueryAnchorRep::getAid, t -> t));
    }


    public List<AnchorInfoBo> querySanlianAnchors(List<Long> avids) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(avids)) {
            return Collections.emptyList();
        }

        List<AnchorInfoBo> anchorInfoBos = new ArrayList<>();
        try {
            QueryAnchorReq queryAnchorReq = QueryAnchorReq.newBuilder()
                    .addAllAid(avids)
                    .build();
            QueryAnchorRep queryAnchorRep = nativeAnchorServiceBlockingStub
                    .withDeadlineAfter(2000, TimeUnit.MILLISECONDS)
                    .queryAnchorList(queryAnchorReq);

            queryAnchorRep.getDataList().forEach(anchorRep -> {
                AnchorInfoBo anchorInfoBo = AnchorInfoBo.builder()
                        .aid(anchorRep.getAid())
                        .build();
                anchorInfoBos.add(anchorInfoBo);
            });
        } catch (Exception e) {
            log.error("querySanlianAnchors error, avids={}", avids, e);
        }
        return anchorInfoBos;
    }


    public List<Long> getCmVideoAvids(Integer accountId, List<Long> avids) {
        if (CollectionUtils.isEmpty(avids)) {
            return Collections.emptyList();
        }

        PandoraAssert.isTrue(avids.size() <= 100, "单次处理稿件上限为100",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        ListCmArchiveReq req = ListCmArchiveReq.newBuilder()
                .setArchiveModeValue(CmArchiveMode.CM_SPACE_VALUE)
                .addAccountIdSet(accountId)
                .addAllAvIdSet(avids)
                .addIsAuditPassedSet(true)
                .setPageNo(1)
                .setPageSize(avids.size())
                .build();

        ListCmArchiveResp resp = cmArchiveServiceBlockingStub.withWaitForReady()
                .withDeadlineAfter(1000L, TimeUnit.MILLISECONDS)
                .listCmArchive(req);

        return resp.getCmArchiveSetList().stream()
                .map(CmArchiveInfo::getUgc)
                .map(CmArchiveUgcInfo::getAvid)
                .collect(Collectors.toList());
    }


    public StoryComponent getStoryComponent(LauCreativeComponentBo componentBo) {
        StoryComponentIdAndType request = StoryComponentIdAndType.newBuilder().setId(componentBo.getComponentId()).setType(componentBo.getComponentType()).build();
        return storyComponentServiceBlockingStub.get(request);
    }


    public void sendAuditOttAvids(List<Long> avids) {
        if (CollectionUtils.isEmpty(avids)) {
            return;
        }

        List<Long> notifyAvids = avids.stream()
                .filter(NumberUtils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notifyAvids)) {
            return;
        }

        log.info("sendAuditOttAvids avids:{}", avids);

        BatchVideoOttAuditReq req = BatchVideoOttAuditReq.newBuilder()
                .addAllAid(avids)
                //商业起飞稿件
                .setAppValue(OttAuditApp.CPM_VALUE)
                //要入库 + 送审 + 增加标记
                .setStatusValue(OttAuditStatus.ADD_MARK_VALUE)
                .build();
        adOttServiceBlockingStub.batchVideoOttAudit(req);
    }


}
