package sycpb.platform.cpm.pandora.service.databus.sub;

import com.alibaba.fastjson.JSON;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.ICreativeExploreService;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore.CreativeExploreBo;

/**
 * @ClassName CreativeExplorePreviewSub
 * <AUTHOR>
 * @Date 2025/4/9 5:54 下午
 * @Version 1.0
 **/
@Slf4j
@Service
public class CreativeExplorePreviewSub implements MessageListener {

    public static final String CREATIVE_EXPLORE_PREVIEW_DATABUS_KEY = "creative-explore-preview";

    private final String topic;
    private final String group;

    @Autowired
    private ICreativeExploreService creativeExploreService;

    public CreativeExplorePreviewSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(CREATIVE_EXPLORE_PREVIEW_DATABUS_KEY);
        topic = databusProperty.getTopic();
        group = databusProperty.getSub().getGroup();
    }

    @Override
    public String topic() {
        return topic;
    }

    @Override
    public String group() {
        return group;
    }

    @Override
    public boolean autoCommit() {
        return true;
    }

    @Override
    public void onMessage(AckableMessage message) {
        String messageStr = new String(message.payload());
        log.info("CreativeExplorePreviewSub on message:{}", messageStr);
        handleMessage(messageStr);
    }

    private void handleMessage(String messageStr) {
        CreativeExploreBo creativeExploreBo = JSON.parseObject(messageStr, CreativeExploreBo.class);
        creativeExploreService.executeExploreCreative(creativeExploreBo);
    }

}
