package sycpb.platform.cpm.pandora.service.constants;

import java.util.Objects;

public class Device {
    public static final int ANDROID = 399;
    public static final int IPHONE = 398;
    public static final int IPAD = 421;

    public static boolean isAndroid(Integer x) {
        return Objects.equals(x, ANDROID);
    }

    public static boolean isIOS(Integer x) {
        return Objects.equals(x, IPHONE) || Objects.equals(x, IPAD);
    }
}
