package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauCreativeFlyDynamicInfoDao;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeFlyDynamicInfoPo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.CreativeBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauCreativeFlyDynamicInfoBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.ProgCreativeAuditContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO;

@Service
@RequiredArgsConstructor
public class CreativeFlyDynamicInfoService {
    private final LauCreativeFlyDynamicInfoDao lauCreativeFlyDynamicInfoDao;

    @Resource(name = MySqlConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;

    public List<LauCreativeFlyDynamicInfoBo> fetch(Integer unitId) {
        return UnitCreativeImplMapper.MAPPER.fromLauCreativeFlyDynamicInfoPos(lauCreativeFlyDynamicInfoDao.fetchByUnitId(unitId));
    }

    public List<LauCreativeFlyDynamicInfoBo> list(List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.emptyList();
        }

        List<LauCreativeFlyDynamicInfoPo> lauCreativeFlyDynamicInfoPos = lauCreativeFlyDynamicInfoDao.fetchByCreativeId(creativeIds.toArray(new Integer[0]));
        return UnitCreativeImplMapper.MAPPER.fromLauCreativeFlyDynamicInfoPos(lauCreativeFlyDynamicInfoPos);
    }

    public void save(SaveUnitCreativeContextBo ctx) {
        final var currentVersion = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeFlyDynamicInfoBo);
        final var newVersion = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeFlyDynamicInfoBo);
        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(LauCreativeFlyDynamicInfoBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeFlyDynamicInfoDao)
                .setOpLog(ctx.genCreativeCompareConsumer())
                .setDSLContext(adCore)
                .setTableImpl(LAU_CREATIVE_FLY_DYNAMIC_INFO)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_FLY_DYNAMIC_INFO.ID)
                .setDatabasePrimaryKeyMapper(LauCreativeFlyDynamicInfoPo::getId);
        JooqFunctions.save(currentVersion, newVersion, ctx0);
    }

    public void save(ProgCreativeAuditContextBo ctx) {
        final var currentVersion = ctx.getCurVersion().getLauCreativeFlyDynamicInfoBos();
        final var newVersion = ctx.getNewVersion().getLauCreativeFlyDynamicInfoBos();
        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(LauCreativeFlyDynamicInfoBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeFlyDynamicInfoDao)
                .setOpLog(ctx.genCreativeCompareConsumer())
                .setDSLContext(adCore)
                .setTableImpl(LAU_CREATIVE_FLY_DYNAMIC_INFO)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_FLY_DYNAMIC_INFO.ID)
                .setDatabasePrimaryKeyMapper(LauCreativeFlyDynamicInfoPo::getId);
        JooqFunctions.save(currentVersion, newVersion, ctx0);
    }
}
