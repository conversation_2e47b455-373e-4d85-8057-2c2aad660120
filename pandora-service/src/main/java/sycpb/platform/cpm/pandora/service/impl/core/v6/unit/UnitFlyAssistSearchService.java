package sycpb.platform.cpm.pandora.service.impl.core.v6.unit;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.FlyAssistSearchDao;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.FlyAssistSearchPo;

import java.util.Objects;

@Service
@RequiredArgsConstructor
public class UnitFlyAssistSearchService {
    private final FlyAssistSearchDao flyAssistSearchDao;

    public void saveIfNotExists(Integer unitId) {
        var po = flyAssistSearchDao.fetchOneByUnitId(unitId);
        if (Objects.nonNull(po)) return;

        po = new FlyAssistSearchPo();
        po.setUnitId(unitId);
        flyAssistSearchDao.insert(po);
    }
}
