package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauCreativeTabDao;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeTabPo;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.CreativeBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauCreativeTabBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.ProgCreativeAuditContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;

import javax.annotation.Resource;
import java.util.List;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeTab.LAU_CREATIVE_TAB;

@Service
@RequiredArgsConstructor
public class CreativeTabService {
    private final LauCreativeTabDao lauCreativeTabDao;

    @Resource(name = MySqlConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;

    public List<LauCreativeTabBo> fetch(Integer unitId) {
        return UnitCreativeImplMapper.MAPPER.fromLauCreativeTabPos(lauCreativeTabDao.fetchByUnitId(unitId));
    }

    public LauCreativeTabBo fetchByCreativeId(Integer creativeId) {
        if (!NumberUtils.isPositive(creativeId)) {
            return null;
        }

        List<LauCreativeTabBo> lauCreativeTabBos = UnitCreativeImplMapper.MAPPER.fromLauCreativeTabPos(lauCreativeTabDao.fetchByCreativeId(creativeId.longValue()));
        if (CollectionUtils.isEmpty(lauCreativeTabBos)) {
            return null;
        }

        return lauCreativeTabBos.get(0);
    }

    public void save(SaveUnitCreativeContextBo ctx) {
        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeTabBo);
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeTabBo);
        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(LauCreativeTabBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeTabDao)
                .setDSLContext(adCore)
                .setTableImpl(LAU_CREATIVE_TAB)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_TAB.ID)
                .setDatabasePrimaryKeyMapper(LauCreativeTabPo::getId);
        JooqFunctions.save(currentVersions, newVersions, ctx0);
    }

    public void save(ProgCreativeAuditContextBo ctx) {
        final var currentVersions = ctx.getCurVersion().getLauCreativeTabBos();
        final var newVersions = ctx.getCurVersion().getLauCreativeTabBos();
        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(LauCreativeTabBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeTabDao)
                .setDSLContext(adCore)
                .setTableImpl(LAU_CREATIVE_TAB)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_TAB.ID)
                .setDatabasePrimaryKeyMapper(LauCreativeTabPo::getId);
        JooqFunctions.save(currentVersions, newVersions, ctx0);
    }

    public void saveByCreativeId(Integer creativeId, List<LauCreativeTabBo> tabs, SaveUnitCreativeContextBo ctx) {
        List<LauCreativeTabPo> lauCreativeTabPos = lauCreativeTabDao.fetchByCreativeId(Long.parseLong(String.valueOf(creativeId)));
        List<LauCreativeTabBo> lauCreativeTabBos = UnitCreativeImplMapper.MAPPER.fromLauCreativeTabPos(lauCreativeTabPos);
        JooqFunctions.save(lauCreativeTabBos, tabs, JooqFunctions.JooqSaveContext.minimum(LauCreativeTabBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeTabDao)
                .setOpLog(ctx.genCreativeCompareConsumer())
        );
    }
}
