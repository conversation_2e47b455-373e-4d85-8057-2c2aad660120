package sycpb.platform.cpm.pandora.service.api.core.batch.bos;

import com.bapis.ad.pandora.resource.BudgetType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.pandora_type.numeric.decimal.UnLimitedBudget;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BudgetOperationBo {
    private String  budget;
    private Long    budgetValue;
    private Integer budgetLimitType;
    private Integer isRepeat;

    public static BudgetOperationBo newInstance(String budget, Integer budgetLimitType, Integer isRepeat) {
        final var bo = new BudgetOperationBo();
        bo.setBudgetLimitType(budgetLimitType);
        bo.setIsRepeat(isRepeat);
        switch (budgetLimitType) {
            case BudgetType.BUDGET_UNLIMITED_VALUE:
                bo.setBudget("");
                bo.setBudgetValue(UnLimitedBudget.UNLIMITED_VALUE);
                break;
            case BudgetType.BUDGET_DAILY_VALUE:
            case BudgetType.BUDGET_TOTAL_VALUE:
                bo.setBudget(budget);
                Assert.isTrue(StringUtils.hasText(budget), "预算的值不存在");
                bo.setBudgetValue(new BigDecimal(budget).multiply(BigDecimal.valueOf(100)).longValue());
                break;
            default:
                throw new IllegalArgumentException("预算类型无法识别");
        }
        return bo;
    }

    public static BudgetOperationBo newInstance(Long budgetValue, Integer budgetLimitType, Integer isRepeat) {
        final var bo = new BudgetOperationBo();
        bo.setBudgetValue(budgetValue);
        bo.setBudgetLimitType(budgetLimitType);
        bo.setIsRepeat(isRepeat);
        switch (budgetLimitType) {
            case BudgetType.BUDGET_UNLIMITED_VALUE:
                bo.setBudget("");
                break;
            case BudgetType.BUDGET_DAILY_VALUE:
            case BudgetType.BUDGET_TOTAL_VALUE:
                bo.setBudget(new BigDecimal(budgetValue).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_EVEN).toString());
                break;
            default:
                throw new IllegalArgumentException("预算类型无法识别");
        }
        return bo;
    }

    public String desc() {
        final String postfix;
        switch (budgetLimitType) {
            case BudgetType.BUDGET_UNLIMITED_VALUE:
                return "不限预算";
            case BudgetType.BUDGET_DAILY_VALUE:
                postfix = "日预算";
                break;
            case BudgetType.BUDGET_TOTAL_VALUE:
                postfix = "总预算";
                break;
            default:
                throw new IllegalArgumentException("预算类型无法识别");
        }
        return postfix + ": " + budget + (NumberUtils.isPositive(isRepeat) ? ", 每日重复" : "");
    }
}
