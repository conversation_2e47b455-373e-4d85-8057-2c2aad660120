package sycpb.platform.cpm.pandora.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProductWildcardType {
    NO_URL(1, "非链接"),
    URL(2, "链接"),
    IMG(3, "图片");

    private final Integer code;
    private final String desc;

    public static ProductWildcardType getByCode(Integer code) {
        for (ProductWildcardType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown code ProductWildcardType " + code);
    }

    public static ProductWildcardType getByCodeWithoutValidation(Integer code) {
        for (ProductWildcardType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}