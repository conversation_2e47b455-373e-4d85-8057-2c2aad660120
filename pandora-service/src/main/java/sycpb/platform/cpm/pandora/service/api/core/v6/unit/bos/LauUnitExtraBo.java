package sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.common.comma_split.CommaSplitString;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LauUnitExtraBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Long id;
    private Integer accountId;
    private Integer unitId;
    @CompareMeta(logDescription = "是否原生")
    private Integer isBiliNative;
    @CompareMeta(logDescription = "广告标签")
    private CommaSplitString tags;

    @CompareMeta(logDescription = "支持的安卓App Store")
    private String deviceAppStore;

    private Boolean needSmartKeyWordLog;

    @CompareMeta(skipLog = true)
    private Integer smartKeyWord;

    @CompareMeta(logDescription = "安卓应用包id")
    private Integer androidAppPackageId;

    @CompareMeta(logDescription = "ios应用包id")
    private Integer iosAppPackageId;

    @CompareMeta(logDescription = "首位出价系数")
    private Integer searchFirstPriceCoefficient;


    @CompareMeta(logDescription = "是否允许aigc助审")
    private Integer allowAigcReplaceMaterial;

    private Integer parentUnitId;

    // 单元版本，0-未知 1-自定义&程序化 2-融合程序化
    @CompareMeta(logDescription = "单元通用版本")
    private Integer generalVersion;


    @CompareMeta(logValue = "smartKeyWord")
    public Integer getSmartKeyWordLogValue() {
        if (!Objects.equals(Boolean.TRUE, needSmartKeyWordLog)) {
            return null;
        }
        return smartKeyWord;
    }

    @CompareMeta(logDescription = "smartKeyWord")
    public String getSmartKeyWordLogDescription() {
        return "智能拓词开关";
    }

}
