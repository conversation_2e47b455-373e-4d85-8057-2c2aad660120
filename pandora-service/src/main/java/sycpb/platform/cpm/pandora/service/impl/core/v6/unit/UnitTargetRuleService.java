package sycpb.platform.cpm.pandora.service.impl.core.v6.unit;

import com.bapis.ad.pandora.resource.AreaType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.common.target_rule.TargetRule;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauUnitTargetRuleDao;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitTargetRuleBo;
import sycpb.platform.cpm.pandora.service.common.InfoReportService;

import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UnitTargetRuleService {
    private final LauUnitTargetRuleDao lauUnitTargetRuleDao;
    private final InfoReportService infoReportService;

    public void save(SaveUnitContextBo ctx) {
        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(LauUnitTargetRuleBo.class, UnitImplMapper.MAPPER::toPo, lauUnitTargetRuleDao)
                .setOpLog(ctx.genCompareConsumer())
                .setValueMapper((k, v) -> {
                    switch (k) {
                        case "area":
                        case "gender":
                        case "age":
                        case "os":
                        case "network":
                        case "deviceBrand":
                        case "convertedUserFilter":
                        case "intelligentMass":
                        case "videoPartition":
                        case "phonePrice":
                        case "areaLevel":
                        case "category":
                        case "appCategory":
                            final var ruleIds = ((TargetRule) v).getRuleIds();
                            if (CollectionUtils.isEmpty(ruleIds)) return "不限";

                            return ruleIds.stream()
                                    .map(x -> ctx.getTargetItemDescMap().getOrDefault(x, "未知"))
                                    .collect(Collectors.joining(","));
                        case "areaType":
                            final var areaTypeIds = ((TargetRule) v).getRuleIds();
                            if (CollectionUtils.isEmpty(areaTypeIds)) return "不限";
                            return areaTypeIds.stream()
                                    .map(this::getAreaTypeDesc)
                                    .collect(Collectors.joining(","));
                        default:
                            return v.toString();
                    }
                });
        JooqFunctions.save(ctx.getCurrentVersion().getLauUnitTargetRuleBo(), ctx.getNewVersion().getLauUnitTargetRuleBo(), ctx0);
        infoReportService.checkAndUpdateCompensationLogType2(ctx.getInfoReportBo(), ctx0.getCompareBos());
    }

    public LauUnitTargetRuleBo get(Integer unitId) {
        return UnitImplMapper.MAPPER.fromPo(lauUnitTargetRuleDao.fetchOneByUnitId(unitId));
    }

    private String getAreaTypeDesc(Integer areaType) {
        if (Objects.equals(areaType, AreaType.ALL.getNumber())) {
            return "全部";
        }
        if (Objects.equals(areaType, AreaType.CURRENT.getNumber())) {
            return "实时在此的用户";
        }
        if (Objects.equals(areaType, AreaType.PERMANENT.getNumber())) {
            return "常住在此的用户";
        }
        if (Objects.equals(areaType, AreaType.TRIP.getNumber())) {
            return "旅游在此的用户";
        }
        return "未知";

    }
}
