package sycpb.platform.cpm.pandora.service.api.resource.game;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName GameBo
 * <AUTHOR>
 * @Date 2024/6/12 7:30 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResGameBo {

    @JsonProperty("is_online")
    private Boolean isOnline;
    @JsonProperty("game_base_id")
    private Integer gameBaseId;
    @JsonProperty("game_name")
    private String gameName;
    @JsonProperty("game_icon")
    private String gameIcon;
    @JsonProperty("game_status")
    private Integer gameStatus;
    @JsonProperty("game_link")
    private String gameLink;
    @JsonProperty("grade_status")
    private Integer gradeStatus;
    @JsonProperty("grade")
    private Double grade;
    @JsonProperty("game_tags")
    private String gameTags;

}
