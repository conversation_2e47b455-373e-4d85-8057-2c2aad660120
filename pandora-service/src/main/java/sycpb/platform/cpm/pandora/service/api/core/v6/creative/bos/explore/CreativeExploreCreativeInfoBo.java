package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.explore;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.CreativeCpsReplaceLinkBo;

import java.util.List;

/**
 * @ClassName CreativeExploreCreativeInfoBo
 * <AUTHOR>
 * @Date 2025/3/20 10:49 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreativeExploreCreativeInfoBo {

    private Integer templateGroupId;
    private List<Long> avidList;
    private Long dynamicId;
    private CreativeExploreLandingPageInfoBo landingPage;
    private CreativeCpsReplaceLinkBo cpsReplaceLinkInfo;
    private Integer miniGameId;
    private String schemeUrl;

}
