package sycpb.platform.cpm.pandora.service.impl.resource.creative;

import com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.bapis.ad.pandora.resource.*;
import io.vavr.<PERSON>ple;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.common.functions.CommonFunctions;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraAssert;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.campaign.ICampaignService;
import sycpb.platform.cpm.pandora.service.api.resource.account.IAccountLabelService;
import sycpb.platform.cpm.pandora.service.api.resource.creative.IResCreativeService;
import sycpb.platform.cpm.pandora.service.api.resource.creative.ResCreativeApiMapper;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.*;
import sycpb.platform.cpm.pandora.service.api.resource.location.ILocationService;
import sycpb.platform.cpm.pandora.service.api.resource.location.LocationMapper;
import sycpb.platform.cpm.pandora.service.api.resource.location.bos.*;
import sycpb.platform.cpm.pandora.service.api.resource.rule.CreativeRuleMapper;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.BusMarkParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.CreativeContentParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.NativeParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.StdParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.*;
import sycpb.platform.cpm.pandora.service.constants.*;
import sycpb.platform.cpm.pandora.service.impl.core.v6.unit.UnitExtService;
import sycpb.platform.cpm.pandora.service.impl.resource.rule.RuleFilterService;
import sycpb.platform.cpm.pandora.service.utils.BusinessDomainUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ResCreativeServiceImpl implements IResCreativeService {
    private static final List<Integer> DYNAMIC_CARD_TYPES = List.of(65, 66, 73, 115, 116);
    private static final int SCHEME_URL_LABEL_ID = 38;
    private static final int BUS_MARK_LABEL_ID = 808;
    private static final int MINI_GAME_LABEL_ID = 394;
    private static final int PLAY_MONITORING_LABEL_ID = 875;

    private static final int OGV_ADD_PARAM_LABEL_ID = 931;
    private static final int STORY_COMPONENT_CONTAIN_GOODS_LABEL_ID = 938;

    public static final int SPLASH_SLOT_GROUP_ID_1 = 581;

    public static final int SPLASH_SLOT_GROUP_ID_2 = 589;

    public static final int SPLASH_TWIST_TEMPLATE_ID = 592;

    public static final int SPLASH_SLIDE_TEMPLATE_ID = 593;
    public static final int SPLASH_CLICK_TEMPLATE_ID = 593;

    private static final int SPLASH_TEMPLATE_GROUP_ID = 7;


    private final ILocationService locDimensionService;
    private final RuleFilterService ruleFilterService;
    private final IAccountLabelService accountLabelService;
    private final ICampaignService campaignService;
    private final PersonalExcelDataLoader personalExcelDataLoader;

    @Resource
    private UnitExtService unitExtService;

    @Override
    public CreativeContentBo getCreativeContent(QueryCreativeContentBo x) {
        final var paramBo = new CreativeContentParamBo();

        var accountLabelIds = x.getAccountLabelIds();
        Integer unitId = x.getUnitId();
        CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo = x.getCreativeConfigCampaignUnitInfoBo();

        PandoraAssert.isTrue(Objects.nonNull(x.getCreativeConfigCampaignUnitInfoBo())
                || NumberUtils.isPositive(unitId), "单元id和计划单元信息对象在查询创意填写内容配置时不可同时为空",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        if (NumberUtils.isPositive(unitId)) {
            var lauUnitBo = unitExtService.getWithCheck(x.getUnitId(), x.getAccountId());
            var lauCampaignBo = campaignService.get(x.getAccountId(), lauUnitBo.getCampaignId());
            creativeConfigCampaignUnitInfoBo = ResCreativeApiMapper.MAPPER.fromCampaignAndUnitBo(lauCampaignBo, lauUnitBo);
        }

        if (CollectionUtils.isEmpty(accountLabelIds)) {
            accountLabelIds = accountLabelService.list(x.getAccountId());
        }

        paramBo.setAdType(creativeConfigCampaignUnitInfoBo.getAdType());
        paramBo.setAccountLabelIds(accountLabelIds);
        paramBo.setTemplateGroupId(x.getTemplateGroupId());
        paramBo.setPromotionPurposeTypes(List.of(creativeConfigCampaignUnitInfoBo.getPromotionPurposeType()));
        paramBo.setPromotionContentTypes(List.of(creativeConfigCampaignUnitInfoBo.getPromotionContentType()));
        final var cpaTarget = creativeConfigCampaignUnitInfoBo.getOcpcTarget();
        paramBo.setCpaTargets(NumberUtils.isPositive(cpaTarget) ? List.of(cpaTarget) : List.of());
        paramBo.setLaunchModes(List.of(x.getLaunchMode()));
        paramBo.setSceneIds(x.getSceneIds());


        CreativeContentBo creativeContentBo = null;
        if (Objects.equals(AdType.ADTYPE_SPLASH_VALUE, paramBo.getAdType())) {
            creativeContentBo = genSplashCreativeContentBo();
        } else {
            creativeContentBo = ruleFilterService.getCreativeContent(paramBo);
        }
        if (Objects.isNull(creativeContentBo)) return null;

        final var copyBo = CreativeRuleMapper.MAPPER.copy(creativeContentBo);
        // 视频/稿件模板组支持播放监测选填
        final var templateGroupBo = getTemplateGroup(x.getTemplateGroupId());
        copyBo.setPlayMonitoringAccessibility(templateGroupBo != null
                && (NumberUtils.isPositive(templateGroupBo.getIsSupportVideo()) || NumberUtils.isPositive(templateGroupBo.getIsSupportArchive()))
                ? Accessibility.ACCESS_OPTIONAL_VALUE : Accessibility.ACCESS_INVALID_VALUE);
        copyBo.setOgvHighlightAccessibility(Accessibility.ACCESS_INVALID_VALUE);
        if (!accountLabelIds.contains(SCHEME_URL_LABEL_ID)) {
            copyBo.setSchemeUrlAccessibility(Accessibility.ACCESS_INVALID_VALUE);
        }
        if (!accountLabelIds.contains(BUS_MARK_LABEL_ID)) {
            copyBo.setBusMarkIdAccessibility(Accessibility.ACCESS_INVALID_VALUE);
        }
        if (!accountLabelIds.contains(MINI_GAME_LABEL_ID)) {
            copyBo.setWechatMiniGameAccessibility(Accessibility.ACCESS_INVALID_VALUE);
        }
        if (!accountLabelIds.contains(PLAY_MONITORING_LABEL_ID)) {
            copyBo.setPlayMonitoringAccessibility(Accessibility.ACCESS_INVALID_VALUE);
        }

        if (accountLabelIds.contains(OGV_ADD_PARAM_LABEL_ID) && Objects.equals(PromotionContentType.PPC_OGV_PROMOTION_VALUE, creativeConfigCampaignUnitInfoBo.getPromotionContentType())){
            copyBo.setOgvHighlightAccessibility(Accessibility.ACCESS_OPTIONAL_VALUE);
        }
        if (Objects.equals(PromotionContentType.PPC_GOODS_CONTENT_VALUE, creativeConfigCampaignUnitInfoBo.getPromotionContentType()) &&
                Objects.equals(TemplateGroup.BILI_ARCHIVE, x.getTemplateGroupId())) {
            if (accountLabelIds.contains(STORY_COMPONENT_CONTAIN_GOODS_LABEL_ID)) {
                copyBo.setYellowCarComponentAccessibility(Accessibility.ACCESS_INVALID_VALUE);
                copyBo.setSupportYellowCarComponentType(Collections.emptyList());
            } else {
                copyBo.setStoryComponentAccessibility(Accessibility.ACCESS_INVALID_VALUE);
            }
        }
        return copyBo;
    }

    @Override
    public TemplateGroupBo getTemplateGroup(Integer templateGroupId) {
        return locDimensionService.getTemplateGroupMap().get(templateGroupId);
    }

    @Override
    public TemplateGroupDetailBo getTemplateGroupDetail(QueryTemplateGroupBo bo) {
        PandoraAssert.isTrue(NumberUtils.isPositive(bo.getTemplateGroupId()),"模板组id必须大于0",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        final var ctx = genTemplateGroupContext(bo);
        final var nested = new TemplateGroupDetailBo();
        final var key = bo.genUUID();
        log.info("param={}, key={}", bo, key);

        // 步骤0: 召回, 如果参数包含模板组id则过滤
        recall(ctx);
        nested.setTemplateGroupBo(ctx.getTemplateGroupBo());
        logDetailFunnel(ctx.getSlotGroupTemplateDetailBos(), key, 0, "recall");
        if (CollectionUtils.isEmpty(ctx.getSlotGroupTemplateDetailBos())) return nested;

        // 步骤1: 过滤其他
        filterGivenTemplateGroupId(ctx);
        logMapFunnel(ctx.getFilterMap(), key, 1);
        if (CollectionUtils.isEmpty(ctx.getSlotGroupTemplateDetailBos())) return nested;

        nested.setSlotGroupTemplateDetailBos(ctx.getSlotGroupTemplateDetailBos());
        nested.setSupportDynamic(ctx.getSlotGroupTemplateDetailBos().stream().anyMatch(x -> DYNAMIC_CARD_TYPES.contains(x.getTemplateBo().getCardType())));

        final var busMarksMap = genBusMarksMap(ctx);
        TemplateGroupBo templateGroupBo = nested.getTemplateGroupBo();
        templateGroupBo.setBusMarks(
                busMarksMap.getOrDefault(templateGroupBo.getTemplateGroupId(),
                        Collections.emptyList()));
        return nested;
    }

    @Override
    public CompoundTemplateGroupBo getCompoundTemplateGroup(QueryTemplateGroupBo bo) {
        final var ctx = genTemplateGroupContext(bo);
        log.info("param={}, key={}", bo, ctx.getKey());
        final var compoundTemplateGroupBo = getCompoundTemplateGroup(ctx);
        if (!ctx.getAccountLabelIds().contains(AccountLabelIds.SCENE_NOT_MERGED_LABEL_ID) && !CollectionUtils.isEmpty(compoundTemplateGroupBo.getChannels())) {
            compoundTemplateGroupBo.setChannels(compoundTemplateGroupBo.getChannels()
                    .stream()
                    .sorted(Comparator.comparing(ChannelBo::getOrderV2))
                    .collect(Collectors.toList()));
        }
        // 透传
        compoundTemplateGroupBo.setGeneralVersion(bo.getGeneralVersion());
        return compoundTemplateGroupBo;
    }

    private CreativeContentBo genSplashCreativeContentBo() {
        final var resBuilder = CreativeContentBo.builder();
        resBuilder.jumpUrlAccessibility(Accessibility.ACCESS_REQUIRED_VALUE)
                .schemeUrlAccessibility(Accessibility.ACCESS_OPTIONAL_VALUE)
                .qualificationAccessibility(Accessibility.ACCESS_OPTIONAL_VALUE)
                .playMonitoringAccessibility(Accessibility.ACCESS_OPTIONAL_VALUE);
        return resBuilder.build();
    }
    private CompoundTemplateGroupBo getCompoundTemplateGroup(TemplateGroupContextBo ctx) {
        final var compound = new CompoundTemplateGroupBo();
        compound.setIsSupportBiliNative(ctx.getIsSupportNative());
        // 步骤0: 召回, 如果参数包含模板组id则过滤
        recall(ctx);
        logDetailFunnel(ctx.getSlotGroupTemplateDetailBos(), ctx.getKey(), 0, "recall");
        if (CollectionUtils.isEmpty(ctx.getSlotGroupTemplateDetailBos())) return compound;

        // 步骤1: 过滤暗投模板, 暗投模板不影响模板组接口
        ctx.setSlotGroupTemplateDetailBos(
                ctx.getSlotGroupTemplateDetailBos()
                        .stream()
                        .filter(x -> Objects.equals(x.getTemplateBo().getIsHidden(), 0))
                        .collect(Collectors.toList())
        );

        // 步骤2: 召回流量类型和场景
        if (NumberUtils.isPositive(ctx.getTemplateGroupId())) {
            // 指定模板组, 返回模板组可用的场景
            filterGivenTemplateGroupId(ctx);
            final Set<Integer> channelIdSet = new HashSet<>();
            final Set<Integer> sceneIdSet = new HashSet<>();
            boolean isSupportPreferScene = false;
            boolean isSupportProgrammatic = false;
            for (SlotGroupTemplateDetailBo slotGroupTemplateDetailBo : ctx.getSlotGroupTemplateDetailBos()) {
                channelIdSet.addAll(slotGroupTemplateDetailBo.getSlotGroupBo().getChannelIds());
                sceneIdSet.addAll(slotGroupTemplateDetailBo.getSlotGroupBo().getSceneIds());
                if (NumberUtils.isPositive(slotGroupTemplateDetailBo.getSlotGroupBo().getIsSupportPreferScene())) {
                    isSupportPreferScene = true;
                }
                if (NumberUtils.isPositive(slotGroupTemplateDetailBo.getTemplateGroupBo().getIsSupportProgrammatic())) {
                    isSupportProgrammatic = true;
                }
            }
            final var channelBos = channelIdSet.stream()
                    .map(x -> locDimensionService.getChannelMap().get(x))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            final var sceneBos = sceneIdSet.stream()
                    .map(x -> locDimensionService.getSceneMap().get(x))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            compound.setChannels(channelBos);
            compound.setScenes(sceneBos);
            compound.setIsSupportPreferScene(NumberUtils.boolean2Integer(isSupportPreferScene));
            compound.setIsSupportProgrammatic(NumberUtils.boolean2Integer(isSupportProgrammatic));
        } else {
            // 不指定模板组, 先过滤确定字段
            var detailBos = new ArrayList<SlotGroupTemplateDetailBo>();
            for (var slotGroupTemplateDetailBo : ctx.getSlotGroupTemplateDetailBos()) {
                if (searchFailed(ctx, slotGroupTemplateDetailBo)) {
                    addFilterMap(ctx, slotGroupTemplateDetailBo, "search");
                    continue;
                }
                if (noBidFailed(ctx, slotGroupTemplateDetailBo)) {
                    addFilterMap(ctx, slotGroupTemplateDetailBo, "noBid");
                    continue;
                }
                if (accountLabelFailed(ctx, slotGroupTemplateDetailBo)) {
                    addFilterMap(ctx, slotGroupTemplateDetailBo, "accountLabel");
                    continue;
                }

                if (nativeFailed(ctx, slotGroupTemplateDetailBo)) {
                    addFilterMap(ctx, slotGroupTemplateDetailBo, "native");
                    continue;
                }

                detailBos.add(slotGroupTemplateDetailBo);
            }
            ctx.setSlotGroupTemplateDetailBos(detailBos);
            var step = logMapFunnel(ctx.getFilterMap(), ctx.getKey(), 1);
            if (CollectionUtils.isEmpty(ctx.getSlotGroupTemplateDetailBos())) return compound;

            // 召回流量类型
            compound.setChannels(genChannelBos(ctx));

            // 过滤流量类型
            ctx.setFilterMap(new HashMap<>());
            detailBos = new ArrayList<>();
            for (var slotGroupTemplateDetailBo : ctx.getSlotGroupTemplateDetailBos()) {
                if (channelFailed(ctx, slotGroupTemplateDetailBo)) {
                    addFilterMap(ctx, slotGroupTemplateDetailBo, "channel");
                    continue;
                }

                detailBos.add(slotGroupTemplateDetailBo);
            }
            ctx.setSlotGroupTemplateDetailBos(detailBos);
            step = logMapFunnel(ctx.getFilterMap(), ctx.getKey(), step);
            if (CollectionUtils.isEmpty(ctx.getSlotGroupTemplateDetailBos())) return compound;

            // 召回场景
            final var tuple2 = genSceneBos(ctx);
            compound.setScenes(tuple2._2);
            compound.setIsSupportPreferScene(NumberUtils.boolean2Integer(tuple2._1));

            // 过滤场景
            ctx.setFilterMap(new HashMap<>());
            detailBos = new ArrayList<>();
            for (var slotGroupTemplateDetailBo : ctx.getSlotGroupTemplateDetailBos()) {
                if (sceneFailed(ctx, slotGroupTemplateDetailBo)) {
                    addFilterMap(ctx, slotGroupTemplateDetailBo, "scene");
                    continue;
                }

                detailBos.add(slotGroupTemplateDetailBo);
            }
            ctx.setSlotGroupTemplateDetailBos(detailBos);
            step = logMapFunnel(ctx.getFilterMap(), ctx.getKey(), step);
            if (CollectionUtils.isEmpty(ctx.getSlotGroupTemplateDetailBos())) return compound;

            // 召回是否程序化
            compound.setIsSupportProgrammatic(NumberUtils.boolean2Integer(genIsSupportProgrammatic(ctx)));

            // 过滤程序化
            ctx.setFilterMap(new HashMap<>());
            detailBos = new ArrayList<>();
            for (var slotGroupTemplateDetailBo : ctx.getSlotGroupTemplateDetailBos()) {
                if (NumberUtils.isPositive(ctx.getIsProgrammatic()) && !NumberUtils.isPositive(slotGroupTemplateDetailBo.getTemplateGroupBo().getIsSupportProgrammatic())) {
                    addFilterMap(ctx, slotGroupTemplateDetailBo, "programmatic");
                    continue;
                }

                detailBos.add(slotGroupTemplateDetailBo);
            }
            ctx.setSlotGroupTemplateDetailBos(detailBos);
            logMapFunnel(ctx.getFilterMap(), ctx.getKey(), step);
            if (CollectionUtils.isEmpty(ctx.getSlotGroupTemplateDetailBos())) return compound;
        }

        final var busMarksMap = genBusMarksMap(ctx);
        final var buttonsMap = genButtonsMap(ctx);
        final Set<Integer> tgSet = new HashSet<>();
        final var templateGroupBos = ctx.getSlotGroupTemplateDetailBos()
                .stream()
                .map(SlotGroupTemplateDetailBo::getTemplateGroupBo)
                .filter(x -> {
                    if (tgSet.contains(x.getTemplateGroupId())) return false;

                    tgSet.add(x.getTemplateGroupId());
                    return true;
                }).peek(x -> {
                    x.setBusMarks(busMarksMap.get(x.getTemplateGroupId()));
                    x.setButtons(buttonsMap.get(x.getTemplateGroupId()));
                }).collect(Collectors.toList());
        compound.setTemplateGroups(templateGroupBos);
        return compound;
    }

    @Override
    public List<Integer> getBusMarkIdsByTemplateIdAndAccountLabelIds(List<Integer> templateIds, List<Integer> accountLabelIds) {
        List<TemplateBo> nonHiddenTemplateBoList = templateIds.stream().map(templateId -> locDimensionService.getTemplateMap().get(templateId))
                .filter(Objects::nonNull)
                .filter(templateBo -> !NumberUtils.isPositive(templateBo.getIsHidden()))
                .collect(Collectors.toList());

        // 暗投 不能作为召回广告标的依据 否则逻辑会和页面逻辑不一致......
        List<Integer> nonHiddenTemplateIds = nonHiddenTemplateBoList.stream()
                .map(TemplateBo::getTemplateId)
                .collect(Collectors.toList());

        List<Integer> cardTypes = getCardTypesByTemplateIds(nonHiddenTemplateIds);

        if (CollectionUtils.isEmpty(cardTypes)) {
            return Collections.emptyList();
        }

        final var paramBo = new BusMarkParamBo();
        paramBo.setAccountLabelIds(accountLabelIds);
        paramBo.setCardTypeIds(cardTypes);
        return ruleFilterService.getBusMarks(paramBo).stream()
                .map(BusMarkBo::getBusMarkId)
                .collect(Collectors.toList());
    }

    private List<Integer> getCardTypesByTemplateIds(List<Integer> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }

        return templateIds.stream()
                .map(templateId -> locDimensionService.getTemplateMap().get(templateId))
                .filter(Objects::nonNull)
                .map(TemplateBo::getCardType)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Integer getSupportBiliNative(GetSupportBiliNativeBo getSupportBiliNativeBo) {
        CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo = getSupportBiliNativeBo.getCreativeConfigCampaignUnitInfoBo();
        Integer unitId = getSupportBiliNativeBo.getUnitId();
        Integer accountId = getSupportBiliNativeBo.getAccountId();
        final var accountLabelIds = accountLabelService.list(accountId);

        PandoraAssert.isTrue(Objects.nonNull(creativeConfigCampaignUnitInfoBo)
                || NumberUtils.isPositive(unitId), "单元id和计划单元信息对象在查询创意支持原生投放配置时不可同时为空",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        if (NumberUtils.isPositive(getSupportBiliNativeBo.getUnitId())) {
            final var lauUnitBo = unitExtService.getWithCheck(unitId, accountId);
            final var lauCampaignBo = campaignService.get(null, lauUnitBo.getCampaignId());
            PandoraAssert.notNull(lauCampaignBo, "对应的计划不存在",
                    ErrorCodeEnum.DomainType.CAMPAIGN, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.CAMPAIGN_NOT_FOUND);
            creativeConfigCampaignUnitInfoBo =
                    ResCreativeApiMapper.MAPPER.fromCampaignAndUnitBo(lauCampaignBo, lauUnitBo);
        }

        return isSupportBiliNative(creativeConfigCampaignUnitInfoBo, accountLabelIds);
    }

    private Boolean genIsSupportProgrammatic(TemplateGroupContextBo ctx) {
        if (NumberUtils.isPositive(ctx.getIsBiliNative())) {
            return ctx.getGeneralVersion().equals(GeneralVersion.GENERAL_VERSION_V2);
        }
        boolean isSupportProgrammatic = false;
        for (SlotGroupTemplateDetailBo slotGroupTemplateDetailBo : ctx.getSlotGroupTemplateDetailBos()) {
            if (NumberUtils.isPositive(slotGroupTemplateDetailBo.getTemplateGroupBo().getIsSupportProgrammatic())) {
                isSupportProgrammatic = true;
            }
        }
        return isSupportProgrammatic;
    }

    private Tuple2<Boolean, List<SceneBo>> genSceneBos(TemplateGroupContextBo ctx) {
        boolean isSupportPreferScene = false;
        final Set<Integer> sceneIdSet = new HashSet<>();
        for (SlotGroupTemplateDetailBo slotGroupTemplateDetailBo : ctx.getSlotGroupTemplateDetailBos()) {
            sceneIdSet.addAll(slotGroupTemplateDetailBo.getSlotGroupBo().getSceneIds());
            if (NumberUtils.isPositive(slotGroupTemplateDetailBo.getSlotGroupBo().getIsSupportPreferScene())) {
                isSupportPreferScene = true;
            }
        }
        final var sceneBos = sceneIdSet.stream()
                .map(x -> locDimensionService.getSceneMap().get(x))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return Tuple.of(isSupportPreferScene, sceneBos);
    }

    private List<ChannelBo> genChannelBos(TemplateGroupContextBo ctx) {
        final Set<Integer> channelIdSet = new HashSet<>();
        for (SlotGroupTemplateDetailBo slotGroupTemplateDetailBo : ctx.getSlotGroupTemplateDetailBos()) {
            channelIdSet.addAll(slotGroupTemplateDetailBo.getSlotGroupBo().getChannelIds());
        }
        return channelIdSet.stream()
                .map(x -> locDimensionService.getChannelMap().get(x))
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(ChannelBo::getOrder))
                .collect(Collectors.toList());
    }

    private void filterGivenTemplateGroupId(TemplateGroupContextBo ctx) {
        if (ctx.isPersonalUp()){
            return;
        }
        final var detailBos = new ArrayList<SlotGroupTemplateDetailBo>();
        for (var slotGroupTemplateDetailBo : ctx.getSlotGroupTemplateDetailBos()) {
            if (channelFailed(ctx, slotGroupTemplateDetailBo)) {
                addFilterMap(ctx, slotGroupTemplateDetailBo, "channel");
                continue;
            }
            if (sceneFailed(ctx, slotGroupTemplateDetailBo)) {
                addFilterMap(ctx, slotGroupTemplateDetailBo, "scene");
                continue;
            }
            if (searchFailed(ctx, slotGroupTemplateDetailBo)) {
                addFilterMap(ctx, slotGroupTemplateDetailBo, "search");
                continue;
            }
            if (noBidFailed(ctx, slotGroupTemplateDetailBo)) {
                addFilterMap(ctx, slotGroupTemplateDetailBo, "noBid");
                continue;
            }
            if (accountLabelFailed(ctx, slotGroupTemplateDetailBo)) {
                addFilterMap(ctx, slotGroupTemplateDetailBo, "accountLabel");
                continue;
            }
            if (imageTypeFailed(ctx, slotGroupTemplateDetailBo)) {
                addFilterMap(ctx, slotGroupTemplateDetailBo, "gif");
                continue;
            }
            if (nativeFailed(ctx, slotGroupTemplateDetailBo)) {
                addFilterMap(ctx, slotGroupTemplateDetailBo, "native");
                continue;
            }
            if (autoPlayFailed(ctx, slotGroupTemplateDetailBo)) {
                addFilterMap(ctx, slotGroupTemplateDetailBo, "autoPlay");
                continue;
            }
            if (pugvFailed(ctx, slotGroupTemplateDetailBo)) {
                addFilterMap(ctx, slotGroupTemplateDetailBo, "notPugv");
                continue;
            }
            detailBos.add(slotGroupTemplateDetailBo);
        }
        ctx.setSlotGroupTemplateDetailBos(detailBos);
    }

    private TemplateGroupContextBo genTemplateGroupContext(QueryTemplateGroupBo bo) {
        final var ctx = new TemplateGroupContextBo();
        PandoraAssert.isPositive(bo.getAccountId(), "账户id");
        ctx.setAccountId(bo.getAccountId());
        ctx.setUnitId(bo.getUnitId());
        ctx.setKey(bo.genUUID());
        ctx.setPersonalUp(bo.isPersonalUp());

        PandoraAssert.isTrue(Objects.nonNull(bo.getCreativeConfigCampaignUnitInfoBo())
                || NumberUtils.isPositive(bo.getUnitId()), "单元id和计划单元信息对象在召回模板时不可同时为空",
                ErrorCodeEnum.DomainType.CREATIVE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        if (NumberUtils.isPositive(bo.getUnitId())) {
            final var lauUnitBo = unitExtService.getWithCheck(ctx.getUnitId(), ctx.getAccountId());
            final var lauCampaignBo = campaignService.get(null, lauUnitBo.getCampaignId());
            ctx.setCreativeConfigCampaignUnitInfoBo(ResCreativeApiMapper.MAPPER.fromCampaignAndUnitBo(lauCampaignBo, lauUnitBo));
        } else {
            ctx.setCreativeConfigCampaignUnitInfoBo(bo.getCreativeConfigCampaignUnitInfoBo());
        }

        if (CollectionUtils.isEmpty(bo.getAccountLabelIds())) {
            List<Integer> accountLabelIds = accountLabelService.list(ctx.getAccountId());
            ctx.setAccountLabelIds(accountLabelIds);
        } else {
            ctx.setAccountLabelIds(bo.getAccountLabelIds());
        }

        ctx.setIsSupportNative(isSupportBiliNative(ctx.getCreativeConfigCampaignUnitInfoBo(), ctx.getAccountLabelIds()));
        if (Objects.isNull(bo.getGeneralVersion())) {
            bo.setGeneralVersion(1);
        }
        if (Objects.isNull(bo.getIsBiliNative())) {
            if (NumberUtils.isPositive(ctx.getIsSupportNative())) {
                bo.setIsBiliNative(1);
            } else {
                bo.setIsBiliNative(0);
            }
        }
        if (NumberUtils.isPositive(bo.getIsBiliNative())) {
            PandoraAssert.isTrue(NumberUtils.isPositive(ctx.getIsSupportNative()), "当前单元不支持原生投放",
                    ErrorCodeEnum.DomainType.UNIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            if (!bo.getGeneralVersion().equals(GeneralVersion.GENERAL_VERSION_V2) && Objects.nonNull(bo.getIsProgrammatic())) {
                PandoraAssert.isTrue(!NumberUtils.isPositive(bo.getIsProgrammatic()), "原生模式仅支持自定义创意",
                        ErrorCodeEnum.DomainType.UNIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            }
        }
        if (Objects.isNull(bo.getChannelId())) {
            bo.setChannelId(Channel.MOBILE);
        }
        if (Objects.isNull(bo.getIsProgrammatic())) {
            bo.setIsProgrammatic(0);
        }
        if (Objects.isNull(bo.getIsPreferScene()) && CollectionUtils.isEmpty(bo.getSceneIds())) {
            bo.setIsPreferScene(1);
        }

        ctx.setIsBiliNative(bo.getIsBiliNative());
        ctx.setGeneralVersion(bo.getGeneralVersion());
        ctx.setChannelId(bo.getChannelId());
        ctx.setIsPreferScene(bo.getIsPreferScene());
        ctx.setSceneIds(bo.getSceneIds());
        ctx.setIsProgrammatic(bo.getIsProgrammatic());
        ctx.setTemplateGroupId(bo.getTemplateGroupId());

        ctx.setSlotGroupTemplateDetailBos(new ArrayList<>());
        ctx.setFilterMap(new HashMap<>());
        ctx.setIsGif(bo.getIsGif());
        ctx.setRemoveStorySceneForDynamicTemplate(bo.isRemoveStorySceneForDynamicTemplate());
        ctx.setIsAutoPlay(bo.getIsAutoPlay());
        ctx.setIsPugv(bo.getIsPugv());

        Integer isCpcNative2Fly = genIsCpcNative2Fly(ctx.getCreativeConfigCampaignUnitInfoBo(), ctx.getIsBiliNative(), ctx.isPersonalUp());
        ctx.setIsCpcNative2Fly(isCpcNative2Fly);
        return ctx;
    }

    private Integer genIsCpcNative2Fly(CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo,
                                       Integer isBiliNative, boolean personalUp) {

        Integer ppt = creativeConfigCampaignUnitInfoBo.getPromotionPurposeType();
        Integer ppc = creativeConfigCampaignUnitInfoBo.getPromotionContentType();
        Integer ocpcTarget = creativeConfigCampaignUnitInfoBo.getOcpcTarget();
        Integer businessDomainWithoutNative =
                BusinessDomainUtil.genBusinessDomain(ppt, ppc, ocpcTarget, 0, null, personalUp);

        if (!Objects.equals(businessDomainWithoutNative, BusinessDomainType.BD_CPC_VALUE)) {
            return 0;
        }

        if (!NumberUtils.isPositive(isBiliNative)) {
            return 0;
        }

//        NativeBo nativeConfig = getSupportBiliNativeConfig(locationCampaignUnitInfoBo, accountLabelIds);
//        // 是否存在原生配置
//        if (Objects.isNull(nativeConfig)) {
//            return 0;
//        }

        // 是否需要根据模板的支持原生字段对模板进行过滤
//        if (!NumberUtils.isPositive(nativeConfig.getIsNeedTemplateFilter())) {
//            return 0;
//        }

        return 1;
    }

    private Map<Integer, List<ButtonBo>> genButtonsMap(TemplateGroupContextBo ctx) {
        final var templateGroupButtonMap = new HashMap<Integer, Map<Integer, ButtonBo>>();
        for (var slotGroupTemplateDetailBo : ctx.getSlotGroupTemplateDetailBos()) {
            final var templateGroupId = slotGroupTemplateDetailBo.getTemplateGroupBo().getTemplateGroupId();
            templateGroupButtonMap.putIfAbsent(templateGroupId, new HashMap<>());
            for (ButtonBo buttonBo : slotGroupTemplateDetailBo.getTemplateBo().getButtons()) {
                templateGroupButtonMap.get(templateGroupId).put(buttonBo.getButtonId(), buttonBo);
            }
        }
        final var map = new HashMap<Integer, List<ButtonBo>>();
        for (Map.Entry<Integer, Map<Integer, ButtonBo>> entry : templateGroupButtonMap.entrySet()) {
            map.put(entry.getKey(), new ArrayList<>(entry.getValue().values()));
        }
        return map;
    }

    private Map<Integer, List<BusMarkBo>> genBusMarksMap(TemplateGroupContextBo ctx) {
        CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo = ctx.getCreativeConfigCampaignUnitInfoBo();
        Integer ppt = creativeConfigCampaignUnitInfoBo.getPromotionPurposeType();
        Integer ppc = creativeConfigCampaignUnitInfoBo.getPromotionContentType();
        Integer ocpcTarget = creativeConfigCampaignUnitInfoBo.getOcpcTarget();
        boolean personalUp = ctx.isPersonalUp();
        Integer businessDomainWithoutNative =
                BusinessDomainUtil.genBusinessDomain(ppt, ppc, ocpcTarget, 0, null, personalUp);

        if (Objects.equals(businessDomainWithoutNative, BusinessDomainType.BD_CPC_VALUE)
                && !NumberUtils.isPositive(ctx.getIsCpcNative2Fly())) {
            final var templateGroupCardTypeMap = new HashMap<Integer, Set<Integer>>();
            for (var slotGroupTemplateDetailBo : ctx.getSlotGroupTemplateDetailBos()) {
                if (NumberUtils.isPositive(slotGroupTemplateDetailBo.getTemplateBo().getIsHidden())) {
                    continue;
                }
                final var templateGroupId = slotGroupTemplateDetailBo.getTemplateGroupBo().getTemplateGroupId();
                templateGroupCardTypeMap.putIfAbsent(templateGroupId, new HashSet<>());
                templateGroupCardTypeMap.get(templateGroupId).add(slotGroupTemplateDetailBo.getTemplateBo().getCardType());
            }
            final var map = new HashMap<Integer, List<BusMarkBo>>();
            for (Map.Entry<Integer, Set<Integer>> entry : templateGroupCardTypeMap.entrySet()) {
                final var paramBo = new BusMarkParamBo();
                paramBo.setAccountLabelIds(ctx.getAccountLabelIds());
                paramBo.setCardTypeIds(new ArrayList<>(entry.getValue()));
                map.put(entry.getKey(), ruleFilterService.getBusMarks(paramBo));
            }
            return map;
        } else {
            return Map.of();
        }
    }

    private Integer isSupportBiliNative(CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo, List<Integer> accountLabelIds) {
        final var nativeConfig = getSupportBiliNativeConfig(creativeConfigCampaignUnitInfoBo, accountLabelIds);
        if (Objects.equals(AdType.ADTYPE_SPLASH_VALUE, creativeConfigCampaignUnitInfoBo.getAdType())) {
            return 0;
        }
        return Objects.isNull(nativeConfig) ? 0 : nativeConfig.getIsSupportNative();
    }

    private NativeBo getSupportBiliNativeConfig(CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo, List<Integer> accountLabelIds) {
        final var param = new NativeParamBo();
        param.setAccountLabelIds(accountLabelIds);
        param.addSinglePromotionPurposeType(creativeConfigCampaignUnitInfoBo.getPromotionPurposeType());
        param.addSinglePromotionContentType(creativeConfigCampaignUnitInfoBo.getPromotionContentType());
        if (NumberUtils.isPositive(creativeConfigCampaignUnitInfoBo.getOcpcTarget())) {
            param.addSingleCpaTargetId(creativeConfigCampaignUnitInfoBo.getOcpcTarget());
        } else {
            param.addSingleBaseTargetId(creativeConfigCampaignUnitInfoBo.getSalesType());
        }
        param.addSingleDeepCpaTargetId(creativeConfigCampaignUnitInfoBo.getOcpxTargetTwo());
        return ruleFilterService.getSupportBiliNative(param);
    }

    private void recall(TemplateGroupContextBo ctx) {
        CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo = ctx.getCreativeConfigCampaignUnitInfoBo();
        if (NumberUtils.isPositive(ctx.getTemplateGroupId())) {
            TemplateGroupBo resourceTemplateGroupBo =
                    locDimensionService.getTemplateGroupMap().get(ctx.getTemplateGroupId());
            if (Objects.equals(SPLASH_TEMPLATE_GROUP_ID, ctx.getTemplateGroupId())) {
                resourceTemplateGroupBo = genSplashSlotGroupTemplateDetailBo().get(0).getTemplateGroupBo();
            }
            ctx.setTemplateGroupBo(LocationMapper.MAPPER.copyTemplateGroupBo(resourceTemplateGroupBo));
            PandoraAssert.exists(ctx.getTemplateGroupBo(), ctx.getTemplateGroupId(), "模板组");
            if (!isGeneralVersionV2AndNeedProgrammatic(ctx.getGeneralVersion(), ctx.getTemplateGroupId())) {
                PandoraAssert.isTrue(NumberUtils.isPositive(ctx.getTemplateGroupBo().getIsSupportProgrammatic()) || !NumberUtils.isPositive(ctx.getIsProgrammatic()),
                        "选择的模板组不支持程序化创意",
                        ErrorCodeEnum.DomainType.CONFIG, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
            } else if (Objects.equals(creativeConfigCampaignUnitInfoBo.getPromotionContentType(),
                    PromotionContentType.PPC_DYNAMIC_GOODS_VALUE)) {
                // DPA使用图片模版组，但不支持程序化
                ctx.getTemplateGroupBo().setIsSupportProgrammatic(0);
            } else {
                ctx.getTemplateGroupBo().setIsSupportProgrammatic(1);
            }
        }

        final var detailBos = new ArrayList<SlotGroupTemplateDetailBo>();
        if (ctx.isPersonalUp()){
            //个人起飞模版召回
            List<SlotGroupTemplateDetailBo> slotGroupTemplateDetailBos = personalUpRecall(ctx.getTemplateGroupId(),
                    ctx.getCreativeConfigCampaignUnitInfoBo().getPromotionContentType(),
                    ctx.getCreativeConfigCampaignUnitInfoBo().getOcpcTarget(),
                    ctx.getSceneIds());
            TemplateGroupBo templateGroupBo = ctx.getTemplateGroupBo();
            //模版组标题长度0-80、描述0-40
            templateGroupBo.setTitleMinLength(0);
            templateGroupBo.setTitleMaxLength(80);
            templateGroupBo.setDescriptionMinLength(0);
            templateGroupBo.setDescriptionMaxLength(40);
            ctx.setTemplateGroupBo(templateGroupBo);
            detailBos.addAll(slotGroupTemplateDetailBos);
            log.info("personal up recall size:{}", JSON.toJSONString(slotGroupTemplateDetailBos));
            ctx.setSlotGroupTemplateDetailBos(detailBos);
            return;
        }
        // 闪屏不走通用召回逻辑
        if (!Objects.equals(AdType.ADTYPE_SPLASH_VALUE, creativeConfigCampaignUnitInfoBo.getAdType())) {
            final var param = new StdParamBo();
            param.addSinglePromotionPurposeType(creativeConfigCampaignUnitInfoBo.getPromotionPurposeType());
            param.addSinglePromotionContentType(creativeConfigCampaignUnitInfoBo.getPromotionContentType());
            param.setAccountLabelIds(ctx.getAccountLabelIds());
            param.addSingleCpaTargetId(creativeConfigCampaignUnitInfoBo.getOcpcTarget());
            if (!NumberUtils.isPositive(creativeConfigCampaignUnitInfoBo.getOcpcTarget())) {
                param.addSingleBaseTargetId(creativeConfigCampaignUnitInfoBo.getSalesType());
            }
            final var slotGroupTemplateFlatMappingBos = ruleFilterService.getSlotGroupTemplateMapping(param);
            for (SlotGroupTemplateFlatMappingBo slotGroupTemplateFlatMappingBo : slotGroupTemplateFlatMappingBos) {
                final var tgId = locDimensionService.getTemplateTemplateGroupMap().get(slotGroupTemplateFlatMappingBo.getTemplateId());
                if (Objects.isNull(tgId)) continue;

                if (NumberUtils.isPositive(ctx.getTemplateGroupId()) && !Objects.equals(ctx.getTemplateGroupId(), tgId))
                    continue;

                final var resourceTemplateGroupBo = locDimensionService.getTemplateGroupMap().get(tgId);
                if (Objects.isNull(resourceTemplateGroupBo)) continue;

                final var slotGroupBo = locDimensionService.getSlotGroupMap().get(slotGroupTemplateFlatMappingBo.getSlotGroupId());
                if (Objects.isNull(slotGroupBo)) continue;

                final var templateBo = locDimensionService.getTemplateMap().get(slotGroupTemplateFlatMappingBo.getTemplateId());
                if (Objects.isNull(templateBo)) continue;

                final var templateGroupBo = LocationMapper.MAPPER.copyTemplateGroupBo(resourceTemplateGroupBo);
                if (isGeneralVersionV2AndNeedProgrammatic(ctx.getGeneralVersion(), templateGroupBo.getTemplateGroupId())) {
                    templateGroupBo.setIsSupportProgrammatic(1);
                }
                // DPA使用图片模版组，但不支持程序化
                if (Objects.equals(creativeConfigCampaignUnitInfoBo.getPromotionContentType(),
                        PromotionContentType.PPC_DYNAMIC_GOODS_VALUE)) {
                    templateGroupBo.setIsSupportProgrammatic(0);
                }
                detailBos.add(new SlotGroupTemplateDetailBo(templateGroupBo, templateBo, slotGroupBo));
            }
        } else {
            detailBos.addAll(genSplashSlotGroupTemplateDetailBo());
        }
        ctx.setSlotGroupTemplateDetailBos(detailBos);
    }

    private List<SlotGroupTemplateDetailBo> personalUpRecall(Integer templateGroupId,
                                                             Integer ppc,
                                                             Integer ocpxTarget,
                                                             List<Integer> sceneId){
        List<PersonalExcelDataLoader.PersonalExcelRow> personalExcelRows = personalExcelDataLoader.filterData(ppc, ocpxTarget, sceneId);
        Map<Integer, SlotGroupBo> slotGroupMap = locDimensionService.getSlotGroupMap();
        Map<Integer, TemplateBo> templateMap = locDimensionService.getTemplateMap();
        Map<Integer, TemplateGroupBo> templateGroupMap = locDimensionService.getTemplateGroupMap();
        Map<Integer, Integer> templateTemplateGroupMap = locDimensionService.getTemplateTemplateGroupMap();

        List<SlotGroupTemplateDetailBo> result = new ArrayList<>();

        for (PersonalExcelDataLoader.PersonalExcelRow row : personalExcelRows) {
            Integer slotGroupId = row.getSlotGroupId();
            Integer templateId = row.getTemplateId();

            // Get SlotGroupBo and TemplateBo from maps
            SlotGroupBo slotGroupBo = slotGroupMap.get(slotGroupId);
            TemplateBo templateBo = templateMap.get(templateId);

            // Skip if either is null
            if (slotGroupBo == null || templateBo == null) {
                continue;
            }

            // Get template group from template group map
            TemplateGroupBo resourceTemplateGroupBo = templateGroupMap.get(templateGroupId);
            if (resourceTemplateGroupBo == null) {
                continue;
            }

            // Make a copy of the template group to avoid modifying the original
            TemplateGroupBo templateGroupBo = LocationMapper.MAPPER.copyTemplateGroupBo(resourceTemplateGroupBo);
            //模版组标题长度0-80、描述0-40
            templateGroupBo.setTitleMinLength(0);
            templateGroupBo.setTitleMaxLength(80);
            templateGroupBo.setDescriptionMinLength(0);
            templateGroupBo.setDescriptionMaxLength(40);

            // Check if need to set programmatic support for general version V2
            if (isGeneralVersionV2AndNeedProgrammatic(GeneralVersion.GENERAL_VERSION_V2, templateGroupBo.getTemplateGroupId())) {
                templateGroupBo.setIsSupportProgrammatic(1);
            }

            // Add to result
            result.add(new SlotGroupTemplateDetailBo(templateGroupBo, templateBo, slotGroupBo));
        }

        return result;
    }

    // generalVersion=2 && 通栏或直播模版组支持程序化，放量阶段
    private boolean isGeneralVersionV2AndNeedProgrammatic(Integer generalVersion, Integer templateGroupId) {
        return (TemplateGroup.isBannerImage(templateGroupId) || TemplateGroup.isLive(templateGroupId)) &&
                GeneralVersion.GENERAL_VERSION_V2.equals(generalVersion);
    }

    private List<SlotGroupTemplateDetailBo> genSplashSlotGroupTemplateDetailBo() {
        final var templateGroupImageBo = TemplateGroupImageBo.builder()
                .maxSizeKb(300)
                .ratioType(ImageRatioType.RATIO_1_2_VALUE)
                .build();
        final var splashTemplateGroupBo = TemplateGroupBo.builder()
                .templateGroupId(SPLASH_TEMPLATE_GROUP_ID)
                .templateGroupName("闪屏")
                .isSupportImage(1)
                .isSupportBiliNative(0)
                .isSupportBiliDynamic(0)
                .isSupportArchive(0)
                .isSupportVideo(0)
                .isSupportProgrammatic(0)
                .templateGroupImages(List.of(templateGroupImageBo))
                .build();
        final var templateBo1 = locDimensionService.getTemplateMap().get(SPLASH_TWIST_TEMPLATE_ID);
        final var templateBo2 = locDimensionService.getTemplateMap().get(SPLASH_SLIDE_TEMPLATE_ID);
        final var splashSlotGroup1 = locDimensionService.getSlotGroupMap().get(SPLASH_SLOT_GROUP_ID_1);
        final var splashSlotGroup2 = locDimensionService.getSlotGroupMap().get(SPLASH_SLOT_GROUP_ID_2);

        return List.of(new SlotGroupTemplateDetailBo(splashTemplateGroupBo, templateBo1, splashSlotGroup1),
                new SlotGroupTemplateDetailBo(splashTemplateGroupBo, templateBo2, splashSlotGroup2));
    }
    private boolean autoPlayFailed(TemplateGroupContextBo ctx, SlotGroupTemplateDetailBo bo) {
        return Objects.nonNull(ctx.getIsAutoPlay()) && !ctx.getIsAutoPlay() && bo.getTemplateBo().isRequireAutoPlay();
    }
    private boolean pugvFailed(TemplateGroupContextBo ctx, SlotGroupTemplateDetailBo bo) {

        //过滤PC流量位置模版，过滤非1卡、6卡模版,过滤非信息流小卡=1，播放页=3模版
        return Objects.nonNull(ctx.getIsPugv()) && ctx.getIsPugv() &&
                (
                        bo.getTemplateBo().isNotPugvCard()
                                || Collections.disjoint(bo.getSlotGroupBo().getSceneIds(), CommonConstants.PUGV_SCENE_ID_LIST)
                                || bo.getSlotGroupBo().getChannelIds().contains(Channel.PC)
                );
    }

    private boolean channelFailed(TemplateGroupContextBo ctx, SlotGroupTemplateDetailBo bo) {
        return !Objects.equals(ctx.getChannelId(), Channel.ALL) && !bo.getSlotGroupBo().getChannelIds().contains(ctx.getChannelId());
    }

    private boolean sceneFailed(TemplateGroupContextBo ctx, SlotGroupTemplateDetailBo bo) {
        if (NumberUtils.isPositive(bo.getTemplateBo().getIsHidden())) return false;
        if (NumberUtils.isPositive(ctx.getIsPreferScene()) && !NumberUtils.isPositive(bo.getSlotGroupBo().getIsSupportPreferScene())) return true;
        if (!CollectionUtils.isEmpty(ctx.getSceneIds()) && !CommonFunctions.hasCommonElement(ctx.getSceneIds(), bo.getSlotGroupBo().getSceneIds())) return true;
        if (ctx.isRemoveStorySceneForDynamicTemplate() && bo.getSlotGroupBo().getSceneIds().contains(Scene.STORY)) return true;

        return false;
    }

    private boolean searchFailed(TemplateGroupContextBo ctx, SlotGroupTemplateDetailBo bo) {
        return !NumberUtils.isPositive(bo.getSlotGroupBo().getIsSupportSearchAd()) && isSearch(ctx.getCreativeConfigCampaignUnitInfoBo());
    }

    private boolean noBidFailed(TemplateGroupContextBo ctx, SlotGroupTemplateDetailBo bo) {
        //原代码如下. bo.getSlotGroupBo().getIsSupportNoBid() 为0时，表示不支持无竞价，为1时表示支持无竞价.现在需求放开后 永远为1
        // return !NumberUtils.isPositive(bo.getSlotGroupBo().getIsSupportNoBid()) && isNoBid(ctx.getLauUnitBo());
        return false;
    }

    private boolean accountLabelFailed(TemplateGroupContextBo ctx, SlotGroupTemplateDetailBo bo) {
        return (!CollectionUtils.isEmpty(bo.getSlotGroupBo().getAccountLabelIds()) && !CommonFunctions.hasCommonElement(bo.getSlotGroupBo().getAccountLabelIds(), ctx.getAccountLabelIds()))
                || (!CollectionUtils.isEmpty(bo.getTemplateBo().getAccountLabelIds()) && !CommonFunctions.hasCommonElement(bo.getTemplateBo().getAccountLabelIds(), ctx.getAccountLabelIds()));
    }

    private boolean imageTypeFailed(TemplateGroupContextBo ctx, SlotGroupTemplateDetailBo bo) {
        return Objects.nonNull(ctx.getIsGif()) && !Objects.equals(ctx.getIsGif(), bo.getTemplateBo().getIsGif());
    }

    private boolean nativeFailed(TemplateGroupContextBo ctx, SlotGroupTemplateDetailBo bo) {
        // 原生只支持稿件模板组
        if (NumberUtils.isPositive(ctx.getIsBiliNative())
            && !bo.getTemplateGroupBo().getTemplateGroupId().equals(TemplateGroup.BILI_ARCHIVE)) {
            return true;
        }

        NativeBo nativeConfig = getSupportBiliNativeConfig(ctx.getCreativeConfigCampaignUnitInfoBo(), ctx.getAccountLabelIds());
        // 是否存在原生配置
        if (Objects.isNull(nativeConfig)) {
            return false;
        }

        // 是否需要根据模板的支持原生字段对模板进行过滤
        if (!NumberUtils.isPositive(nativeConfig.getIsNeedTemplateFilter())) {
            return false;
        }

        return !ctx.getIsBiliNative().equals(bo.getTemplateBo().getIsSupportNative());
    }

    private void addFilterMap(TemplateGroupContextBo ctx, SlotGroupTemplateDetailBo bo, String key) {
        ctx.getFilterMap().putIfAbsent(key, new ArrayList<>());
        final var flatBo = new SlotGroupTemplateFlatMappingBo();
        flatBo.setSlotGroupId(bo.getSlotGroupBo().getSlotGroupId());
        flatBo.setTemplateId(bo.getTemplateBo().getTemplateId());
        ctx.getFilterMap().get(key).add(flatBo);
    }

    private boolean isSearch(CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo) {
        return Objects.equals(creativeConfigCampaignUnitInfoBo.getAdType(), AdType.ADTYPE_SEARCH_VALUE);
    }

    private boolean isNoBid(CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo) {
        return NumberUtils.isPositive(creativeConfigCampaignUnitInfoBo.getIsNoBid());
    }

    private int logMapFunnel(Map<String, List<SlotGroupTemplateFlatMappingBo>> filterMap, String key, int step) {
        var i = step;
        for (Map.Entry<String, List<SlotGroupTemplateFlatMappingBo>> entry : filterMap.entrySet()) {
            logFunnel(entry.getValue(), key, i, entry.getKey());
            i++;
        }
        return i;
    }

    private void logDetailFunnel(List<SlotGroupTemplateDetailBo> bos, String key, int step, String desc) {
        final var flatMappingBos = bos.stream()
                .map(x -> {
                    final var bo = new SlotGroupTemplateFlatMappingBo();
                    bo.setTemplateId(x.getTemplateBo().getTemplateId());
                    bo.setSlotGroupId(x.getSlotGroupBo().getSlotGroupId());
                    return bo;
                }).collect(Collectors.toList());
        logFunnel(flatMappingBos, key, step, desc);
    }

    private void logFunnel(List<SlotGroupTemplateFlatMappingBo> bos, String key, int step, String desc) {
        log.info("filter slot_group and template, key={}, {}-{}, size={}, details={}", key, step, desc, bos.size(), bos);
    }
}
