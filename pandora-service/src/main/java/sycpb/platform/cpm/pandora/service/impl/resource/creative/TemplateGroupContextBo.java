package sycpb.platform.cpm.pandora.service.impl.resource.creative;

import lombok.Data;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.CreativeConfigCampaignUnitInfoBo;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.SlotGroupTemplateDetailBo;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.SlotGroupTemplateFlatMappingBo;
import sycpb.platform.cpm.pandora.service.api.resource.location.bos.TemplateGroupBo;

import java.util.List;
import java.util.Map;

@Data
public class TemplateGroupContextBo {
    private String key;
    private Integer accountId;
    private Integer unitId;
    private Integer isBiliNative;
    private Integer channelId;
    private Integer isPreferScene;
    private List<Integer> sceneIds;
    private Integer isProgrammatic;
    private Integer templateGroupId;

    private CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo;

    private List<Integer> accountLabelIds;
    private TemplateGroupBo templateGroupBo;
    private List<SlotGroupTemplateDetailBo> slotGroupTemplateDetailBos;
    private Map<String, List<SlotGroupTemplateFlatMappingBo>> filterMap;

    private Integer isGif;
    private Integer isSupportNative;
    private Integer isCpcNative2Fly;
    private boolean removeStorySceneForDynamicTemplate;
    private Boolean isAutoPlay;
    private Integer generalVersion;
    private Boolean isPugv;
    //个人起飞
    private boolean personalUp;
}
