package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauCreativeLandingPageDao;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeLandingPagePo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.CreativeBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.LauCreativeLandingPageBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.ProgCreativeAuditContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE;

@Service
@RequiredArgsConstructor
@Slf4j
public class CreativeLandingPageService {
    private final LauCreativeLandingPageDao lauCreativeLandingPageDao;

    @Resource(name = MySqlConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;

    public void save(SaveUnitCreativeContextBo ctx) {
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeLandingPageBo);
        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeLandingPageBo);
        JooqFunctions.save(currentVersions, newVersions, JooqFunctions.JooqSaveContext.minimum(LauCreativeLandingPageBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeLandingPageDao)
                .setDSLContext(adCore)
                .setTableImpl(LAU_CREATIVE_LANDING_PAGE)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_LANDING_PAGE.ID)
                .setDatabasePrimaryKeyMapper(LauCreativeLandingPagePo::getId));
    }

    public void save(ProgCreativeAuditContextBo ctx) {
        final var newVersions = ctx.getCurVersion().getLauCreativeLandingPageBos();
        final var currentVersions = ctx.getNewVersion().getLauCreativeLandingPageBos();
        JooqFunctions.save(currentVersions, newVersions, JooqFunctions.JooqSaveContext.minimum(LauCreativeLandingPageBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativeLandingPageDao)
                .setDSLContext(adCore)
                .setTableImpl(LAU_CREATIVE_LANDING_PAGE)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_LANDING_PAGE.ID)
                .setDatabasePrimaryKeyMapper(LauCreativeLandingPagePo::getId));
    }

    public List<LauCreativeLandingPageBo> fetch(Collection<Integer> creativeIds) {
        return UnitCreativeImplMapper.MAPPER.fromLauCreativeLandingPagePos(lauCreativeLandingPageDao.fetchByCreativeId(creativeIds.toArray(creativeIds.toArray(new Integer[0]))));
    }

    public List<LauCreativeLandingPageBo> getPageByStartCreativeId(Integer startCreativeId, Integer limit) {
        List<LauCreativeLandingPagePo> poList = adCore.selectFrom(LAU_CREATIVE_LANDING_PAGE)
                .where(LAU_CREATIVE_LANDING_PAGE.CREATIVE_ID.gt(startCreativeId)
                        .and(LAU_CREATIVE_LANDING_PAGE.IS_DELETED.eq(0)))
                .orderBy(LAU_CREATIVE_LANDING_PAGE.CREATIVE_ID.asc())
                .limit(limit)
                .fetch().into(LauCreativeLandingPagePo.class);
        return UnitCreativeImplMapper.MAPPER.fromLauCreativeLandingPagePos(poList);
    }

    @SneakyThrows
    public void batchUpdateCreativeLandingPageUrl(List<LauCreativeLandingPageBo> boList) {
        if (CollectionUtils.isEmpty(boList)) {
            return;
        }

        boList.forEach(bo -> {
            Integer creativeId = bo.getCreativeId();
            adCore.update(LAU_CREATIVE_LANDING_PAGE)
                    .set(LAU_CREATIVE_LANDING_PAGE.CONTAINER_URL, bo.getContainerUrl())
                    .set(LAU_CREATIVE_LANDING_PAGE.CONTAINER_SECONDARY_URL, bo.getContainerSecondaryUrl())
                    .where(LAU_CREATIVE_LANDING_PAGE.CREATIVE_ID.eq(creativeId))
                    .execute();
            log.info("batchUpdateCreativeLandingPageUrl creative_id:{}, containerUrl:{}, containerSecondaryUrl:{}",
                    creativeId, bo.getContainerUrl(), bo.getContainerSecondaryUrl());
        });

        Thread.sleep(boList.size() * 30L);
    }
}
