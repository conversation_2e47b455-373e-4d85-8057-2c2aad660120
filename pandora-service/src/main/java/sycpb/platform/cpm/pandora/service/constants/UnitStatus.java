package sycpb.platform.cpm.pandora.service.constants;

import org.springframework.util.Assert;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;

import java.util.Arrays;
import java.util.List;

public class UnitStatus {
    public static final int VALID = 1;
    public static final int PAUSED = 2;
    public static final int DELETED = 4;

    public static final int BUDGET_EXCEED = 6;
    public static final int COMPLETED = 8;
    public static final int FINISHED = 3;
    public static final int NOT_IN_LAUNCH_TIME = 7;
    public static final int NOT_START = 5;
    public static final int APP_REFRESH = 9;
    public static final int CREATING = 10;

    public static final List<Integer> NON_DELETED_UNIT_STATUS_LIST = Arrays.asList(BUDGET_EXCEED, COMPLETED, FINISHED, NOT_IN_LAUNCH_TIME, NOT_START, PAUSED, VALID, APP_REFRESH);

    public static int toUnitStatus(Integer status) {
        Assert.isTrue(NumberUtils.isPositive(status), "状态不能为空");
        switch (status) {
            case LaunchStatus.VALID:
                return VALID;
            case LaunchStatus.PAUSED:
                return PAUSED;
            case LaunchStatus.DELETED:
                return DELETED;
            default:
                throw new IllegalArgumentException("状态无法识别");
        }
    }
}
