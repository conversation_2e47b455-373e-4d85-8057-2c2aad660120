package sycpb.platform.cpm.pandora.service.impl.core.v6.misc.bos;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class ResAppPackageBo {
    private Integer   id;
    private Integer   accountId;
    private String    name;
    private String    url;
    private String    packageName;
    private String    appName;
    private Integer   platform;
    private String    version;
    private Integer   size;
    private String    md5;
    private String    iconUrl;
    private String    internalUrl;
    private Integer   status;
    private Integer   platformStatus;
    private String    developerName;
    private String    authorityUrl;
    private String    authCodeList;
    private Timestamp apkUpdateTime;
    private String    privacyPolicy;
    private Integer   dmpAppId;
    private Integer   isNewFly;
    private String    description;
    private String    subTitle;
    private Integer   isIconValid;
    private String    deviceAppStore;
}
