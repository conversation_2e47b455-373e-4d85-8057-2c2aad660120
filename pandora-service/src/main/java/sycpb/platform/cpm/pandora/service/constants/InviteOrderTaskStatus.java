package sycpb.platform.cpm.pandora.service.constants;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @ClassName InviteOrderTaskStatus
 * <AUTHOR>
 * @Date 2025/3/25 10:24 下午
 * @Version 1.0
 **/
public class InviteOrderTaskStatus {

    public static final int EXPIRED = 5;
    public static final int DELETED = 6;

    public static List<Integer> LAUNCH_AVOID_INVITE_INVALID_STATUS_LIST = Lists.newArrayList(EXPIRED, DELETED);
}
