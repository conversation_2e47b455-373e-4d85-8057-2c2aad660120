package sycpb.platform.cpm.pandora.service.api.resource.unit.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompoundTargetMo {
    private List<Integer> area;
    private List<Integer> areaLevel;
    private Integer areaType;
    private List<OsTargetMo> os;
    private List<Integer> network;
    private List<Integer> gender;
    private List<Integer> age;
    private List<Integer> category;
    private List<Integer> appCategory;
    private CrowdPackTargetMo crowdPack;
    private TagTargetMo arcTagInterest;
    private TagTargetMo arcTag;
    private InstalledUserFilterTargetMo installedUserFilter;
    private Integer convertedUserFilter;
    private IntelligentMassTargetMo intelligentMass;
    private List<Integer> videoPartition;
    private Integer isProfessionAuto;
    private List<Integer> professionInterestCrowdPack;
    private List<Integer> phonePrice;
    private ArchiveContentTargetMo archiveContent;

    public static List<Integer> extractItemIds(CompoundTargetMo targetMo) {

        if (Objects.isNull(targetMo)) {
            return Collections.emptyList();
        }

        List<OsTargetMo> osTargetMoList = targetMo.getOs();
        List<Integer> osList = CollectionUtils.isEmpty(osTargetMoList) ?
                Collections.emptyList() : osTargetMoList.stream()
                .map(OsTargetMo::getOsTarget)
                .distinct()
                .collect(Collectors.toList());
        List<Integer> deviceBrandList = CollectionUtils.isEmpty(osTargetMoList) ?
                Collections.emptyList() : osTargetMoList.stream()
                .map(OsTargetMo::getDeviceBrand)
                .filter(deviceBrand -> !CollectionUtils.isEmpty(deviceBrand))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        List<Integer> intelligentMassTypeList = Objects.isNull(targetMo.getIntelligentMass()) ?
                Collections.emptyList() : targetMo.getIntelligentMass().getIntelligentMassTargetType();

        return Stream.of(targetMo.getArea(), targetMo.getAreaLevel(), targetMo.getAge(), targetMo.getGender(), osList,
                targetMo.getNetwork(), targetMo.getCategory(), targetMo.getAppCategory(), deviceBrandList,
                targetMo.getVideoPartition(), targetMo.getPhonePrice(), intelligentMassTypeList)
                .filter(list -> !CollectionUtils.isEmpty(list))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

    }
}
