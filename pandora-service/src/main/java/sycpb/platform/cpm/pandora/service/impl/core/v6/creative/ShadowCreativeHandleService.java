package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import com.bapis.ad.pandora.resource.AdType;
import com.bapis.ad.pandora.resource.PromotionContentType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import sycpb.platform.cpm.pandora.infra.common.functions.ShadowFunctions;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.*;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.LauCreativeLandingPageBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName ShadowCreativeHandleService
 * <AUTHOR>
 * @Date 2024/7/25 9:31 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class ShadowCreativeHandleService {

    public void handle(SaveUnitCreativeContextBo ctx) {
        if (Objects.equals(AdType.ADTYPE_SPLASH_VALUE, ctx.getCampaignBo().getAdType()) || Objects.equals(PromotionContentType.PPC_GAME_CARD_VALUE, ctx.getLauUnitBo().getPromotionPurposeType())) {
            return;
        }

        handleLauUnitCreative(ctx);
        handleLauCreativeImage(ctx);
        handleLauCreativeTitle(ctx);
        handleLauCreativeButton(ctx);
        handleLauCreativeComponent(ctx);
        handleLauCreativeExtra(ctx);
        handleCreativeCpsReplaceLink(ctx);

//        handleLauCreativeTab(ctx);
//        handleLauCreativeArchive(ctx);
//        handleLauCreativeDynamic(ctx);
//        handleLauCreativeLandingPage(ctx);
//        handleLauCreativeLandingPageGroup(ctx);
//        handleLauCreativePgcArchive(ctx);

        // 处理 details
        handleProgrammaticCreativeDetail(ctx);

    }

    private void handleProgrammaticCreativeDetail(SaveUnitCreativeContextBo ctx) {

        if (!ctx.isProgrammatic()) {
            return;
        }
        ShadowFunctions.ShadowContext<LauProgrammaticCreativeDetailBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauProgrammaticCreativeDetailBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setOpLog(ctx.genCreativeCompareConsumer());
        final var currentVersions = UnitCreativeImplMapper.extractCollectionFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauProgrammaticCreativeDetailBos);
        final var newVersions = UnitCreativeImplMapper.extractCollectionFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauProgrammaticCreativeDetailBos);

        // 获取影子数据，此时是基于 newVersion 中的 shadow 注解部分是 curVersion 的值
        List<LauProgrammaticCreativeDetailBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, List<LauProgrammaticCreativeDetailBo>> shadowCreativeIdMap = shadowVersions.stream()
                .collect(Collectors.groupingBy(LauProgrammaticCreativeDetailBo::getCreativeId));

        StringBuilder replaceCreativeIds = new StringBuilder();
        // 将 newVersion 里的 shadow 注解部分替换为 curVersion 的，相当于保存时，将 shadow 的数据换回 cur
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauProgrammaticCreativeDetailBos(shadowCreativeIdMap.get(creativeId));
                replaceCreativeIds.append(creativeId).append(",");
            }
        });
        log.info("program shadow handleProgrammaticCreativeDetail end, 用curVersion替换shadow注解字段, 替换creativeIds: {}", replaceCreativeIds.toString());
    }

    private void handleLauUnitCreative(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<LauUnitCreativeBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauUnitCreativeBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setValueMapper(UnitCreativeExtService.VALUE_MAPPER)
                .setOpLog(ctx.genCreativeCompareConsumer());

        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauUnitCreativeBo);
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauUnitCreativeBo);
        List<LauUnitCreativeBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, LauUnitCreativeBo> shadowCreativeIdMap = shadowVersions.stream()
                .collect(Collectors.toMap(LauUnitCreativeBo::getCreativeId, Function.identity()));
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauUnitCreativeBo(shadowCreativeIdMap.get(creativeId));
            }
        });
    }

    private void handleLauCreativeImage(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<LauCreativeImageBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativeImageBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setOpLog(ctx.genCreativeCompareConsumer());
        final var currentVersions = UnitCreativeImplMapper.extractCollectionFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeImageBos);
        final var newVersions = UnitCreativeImplMapper.extractCollectionFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeImageBos);
        List<LauCreativeImageBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, List<LauCreativeImageBo>> shadowCreativeIdMap = shadowVersions.stream().collect(Collectors.groupingBy(LauCreativeImageBo::getCreativeId));
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauCreativeImageBos(shadowCreativeIdMap.get(creativeId));
            }
        });
    }

    private void handleLauCreativeArchive(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<LauCreativeArchiveBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativeArchiveBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setOpLog(ctx.genCreativeCompareConsumer());
        final var currentVersions = UnitCreativeImplMapper.extractCollectionFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeArchiveBos);
        final var newVersions = UnitCreativeImplMapper.extractCollectionFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeArchiveBos);
        List<LauCreativeArchiveBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, List<LauCreativeArchiveBo>> shadowCreativeIdMap = shadowVersions.stream().collect(Collectors.groupingBy(LauCreativeArchiveBo::getCreativeId));
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauCreativeArchiveBos(shadowCreativeIdMap.get(creativeId));
            }
        });
    }

    private void handleLauCreativeDynamic(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<LauCreativeFlyDynamicInfoBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativeFlyDynamicInfoBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setValueMapper(UnitCreativeExtService.VALUE_MAPPER)
                .setOpLog(ctx.genCreativeCompareConsumer());

        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeFlyDynamicInfoBo);
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeFlyDynamicInfoBo);
        List<LauCreativeFlyDynamicInfoBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, LauCreativeFlyDynamicInfoBo> shadowCreativeIdMap = shadowVersions.stream()
                .collect(Collectors.toMap(LauCreativeFlyDynamicInfoBo::getCreativeId, Function.identity()));
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauCreativeFlyDynamicInfoBo(shadowCreativeIdMap.get(creativeId));
            }
        });
    }

    private void handleLauCreativeTab(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<LauCreativeTabBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativeTabBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setValueMapper(UnitCreativeExtService.VALUE_MAPPER)
                .setOpLog(ctx.genCreativeCompareConsumer());

        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeTabBo);
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeTabBo);
        List<LauCreativeTabBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Long, LauCreativeTabBo> shadowCreativeIdMap = shadowVersions.stream().collect(Collectors.toMap(LauCreativeTabBo::getCreativeId, Function.identity()));
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Long creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId().longValue();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauCreativeTabBo(shadowCreativeIdMap.get(creativeId));
            }
        });
    }

    private void handleLauCreativeLandingPage(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<LauCreativeLandingPageBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativeLandingPageBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setValueMapper(UnitCreativeExtService.VALUE_MAPPER)
                .setOpLog(ctx.genCreativeCompareConsumer());

        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeLandingPageBo);
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeLandingPageBo);
        List<LauCreativeLandingPageBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, LauCreativeLandingPageBo> shadowCreativeIdMap = shadowVersions.stream()
                .collect(Collectors.toMap(LauCreativeLandingPageBo::getCreativeId, Function.identity()));
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauCreativeLandingPageBo(shadowCreativeIdMap.get(creativeId));
            }
        });
    }

    private void handleLauCreativeLandingPageGroup(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<LauCreativeLandingPageGroupBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativeLandingPageGroupBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setValueMapper(UnitCreativeExtService.VALUE_MAPPER)
                .setOpLog(ctx.genCreativeCompareConsumer());

        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeLandingPageGroupBo);
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeLandingPageGroupBo);
        List<LauCreativeLandingPageGroupBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, LauCreativeLandingPageGroupBo> shadowCreativeIdMap = shadowVersions.stream()
                .collect(Collectors.toMap(LauCreativeLandingPageGroupBo::getCreativeId, Function.identity()));
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauCreativeLandingPageGroupBo(shadowCreativeIdMap.get(creativeId));
            }
        });
    }

    private void handleLauCreativePgcArchive(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<LauCreativePgcArchiveBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativePgcArchiveBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setValueMapper(UnitCreativeExtService.VALUE_MAPPER)
                .setOpLog(ctx.genCreativeCompareConsumer());

        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativePgcArchiveBo);
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativePgcArchiveBo);
        List<LauCreativePgcArchiveBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, LauCreativePgcArchiveBo> shadowCreativeIdMap = shadowVersions.stream()
                .collect(Collectors.toMap(LauCreativePgcArchiveBo::getCreativeId, Function.identity()));
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauCreativePgcArchiveBo(shadowCreativeIdMap.get(creativeId));
            }
        });
    }

    private void handleLauCreativeTitle(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<LauCreativeTitleBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativeTitleBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setOpLog(ctx.genCreativeCompareConsumer());

        ShadowFunctions.ShadowContext<LauCreativeSmartTitleBo> shadowSmartCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativeSmartTitleBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setOpLog(ctx.genCreativeCompareConsumer());

        final var lauCreativeTitleBos = new ArrayList<LauCreativeTitleBo>();
        final var lauCreativeSmartTitleBos = new ArrayList<LauCreativeSmartTitleBo>();
        for (CreativeBo creativeBo : ctx.getNewVersion().getCreatives()) {
            for (LauCreativeTitleBo lauCreativeTitleBo : creativeBo.getLauCreativeTitleBos()) {
                lauCreativeTitleBos.add(lauCreativeTitleBo);
                lauCreativeSmartTitleBos.addAll(lauCreativeTitleBo.getLauCreativeSmartTitleBos());
            }
        }
        final var existingBos = UnitCreativeImplMapper.extractCollectionFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeTitleBos);
        final var existingSmartBos = existingBos
                .stream()
                .flatMap(x -> Optional.ofNullable(x.getLauCreativeSmartTitleBos()).stream().flatMap(Collection::stream))
                .collect(Collectors.toList());

        List<LauCreativeTitleBo> shadowTitleVersions = ShadowFunctions.handleAndGetShadowVersions(existingBos, lauCreativeTitleBos, shadowCtx);
        List<LauCreativeSmartTitleBo> shadowSmartTitleVersions = ShadowFunctions.handleAndGetShadowVersions(existingSmartBos, lauCreativeSmartTitleBos, shadowSmartCtx);

        Map<Integer, List<LauCreativeTitleBo>> shadowCreativeIdMap = shadowTitleVersions.stream()
                .collect(Collectors.groupingBy(LauCreativeTitleBo::getCreativeId));
        Map<String, List<LauCreativeSmartTitleBo>> shadowSmartTitleMap = shadowSmartTitleVersions.stream()
                .collect(Collectors.groupingBy(LauCreativeSmartTitleBo::getTitleKey));

        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (!shadowCreativeIdMap.containsKey(creativeId)) {
                return;
            }
            creativeBo.setLauCreativeTitleBos(shadowCreativeIdMap.get(creativeId));
            creativeBo.getLauCreativeTitleBos().forEach(titleBo -> {
                String titleKey = titleBo.uk();
                titleBo.setLauCreativeSmartTitleBos(shadowSmartTitleMap.getOrDefault(titleKey, Collections.emptyList()));
            });
        });

    }

    private void handleLauCreativeButton(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<LauCreativeButtonCopyBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativeButtonCopyBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setOpLog(ctx.genCreativeCompareConsumer());

        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeButtonCopyBo);
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeButtonCopyBo)
                .stream()
                .filter(x -> NumberUtils.isPositive(x.getButtonCopyId()))
                .collect(Collectors.toList());

        List<LauCreativeButtonCopyBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, LauCreativeButtonCopyBo> shadowCreativeIdMap = shadowVersions.stream()
                .collect(Collectors.toMap(LauCreativeButtonCopyBo::getCreativeId, Function.identity()));
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauCreativeButtonCopyBo(shadowCreativeIdMap.get(creativeId));
            }
        });
    }

    private void handleLauCreativeComponent(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<LauCreativeComponentBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativeComponentBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setOpLog(ctx.genCreativeCompareConsumer());
        final var currentVersions = UnitCreativeImplMapper.extractCollectionFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeComponentBos);
        final var newVersions = UnitCreativeImplMapper.extractCollectionFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeComponentBos);

        List<LauCreativeComponentBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, List<LauCreativeComponentBo>> shadowCreativeIdMap = shadowVersions.stream()
                .collect(Collectors.groupingBy(LauCreativeComponentBo::getCreativeId));

        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauCreativeComponentBos(shadowCreativeIdMap.get(creativeId));
            }
        });
    }

    private void handleLauCreativeExtra(SaveUnitCreativeContextBo ctx) {
        // 处理 pptType 字段，  @CompareMeta(logDescription = "落地页类型", shadow = true)
        ShadowFunctions.ShadowContext<LauCreativeExtraBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(LauCreativeExtraBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setOpLog(ctx.genCreativeCompareConsumer());
        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getLauCreativeExtraBo);
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getLauCreativeExtraBo);
        List<LauCreativeExtraBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, LauCreativeExtraBo> shadowCreativeIdMap = shadowVersions.stream()
                .collect(Collectors.toMap(LauCreativeExtraBo::getCreativeId, Function.identity()));
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setLauCreativeExtraBo(shadowCreativeIdMap.get(creativeId));
            }
        });
    }

    private void handleCreativeCpsReplaceLink(SaveUnitCreativeContextBo ctx) {
        ShadowFunctions.ShadowContext<CreativeCpsReplaceLinkBo> shadowCtx = ShadowFunctions.ShadowContext.createWithClazz(CreativeCpsReplaceLinkBo.class)
                .setNeedShadowAuditMap(ctx.getNeedShadowAuditMap())
                .setOpLog(ctx.genCreativeCompareConsumer());
        // 替链，新增时也需要替换
        shadowCtx.setNeedCoverForAdd(true);
        final var currentVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getCurrentVersion(), CreativeBo::getCpsReplaceLinkInfo);
        final var newVersions = UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(ctx.getNewVersion(), CreativeBo::getCpsReplaceLinkInfo);
        List<CreativeCpsReplaceLinkBo> shadowVersions = ShadowFunctions.handleAndGetShadowVersions(currentVersions, newVersions, shadowCtx);
        Map<Integer, CreativeCpsReplaceLinkBo> shadowCreativeIdMap = shadowVersions.stream()
                .collect(Collectors.toMap(CreativeCpsReplaceLinkBo::getCreativeId, Function.identity()));
        ctx.getNewVersion().getCreatives().forEach(creativeBo -> {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            if (shadowCreativeIdMap.containsKey(creativeId)) {
                creativeBo.setCpsReplaceLinkInfo(shadowCreativeIdMap.get(creativeId));
            }
            // 新增的情况，需要替换为空，不能立马更新
            else if (shadowCtx.getShadowAddMap().containsKey(creativeId)) {
                creativeBo.setCpsReplaceLinkInfo(null);

            }
        });
    }
}
