package sycpb.platform.cpm.pandora.service.api.core.operation_log;

import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.bos.ChangeLogBo;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class OperationLogContextBo {
    private String tableName;
    private Integer objId;
    private Integer operationType;
    private OperatorBo operator;
    private List<ChangeLogBo> changes = new ArrayList<>();
    private String value;

    public static OperationLogContextBo newContext(OperatorBo operator, String tableName) {
        final var bo = new OperationLogContextBo();
        bo.setOperator(operator);
        bo.setTableName(tableName);
        return bo;
    }

    public void updateContext(Integer id, Integer operationType) {
        objId = id;
        this.operationType = operationType;
    }
}
