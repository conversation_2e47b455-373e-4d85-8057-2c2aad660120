package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import com.bapis.ad.mgk.MgkPageStatus;
import com.bapis.ad.mgk.TemplatePage;
import com.bapis.ad.mgk.TemplatePagesReply;
import com.bapis.ad.mgk.page.group.PageGroupStatus;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.plexus.util.ExceptionUtils;
import org.jooq.DSLContext;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.compare.OperationType;
import sycpb.platform.cpm.pandora.infra.compare.bos.ChangeLogBo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos.LauImageAuditLogDao;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauImageAuditLogPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.*;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.*;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraAssert;
import sycpb.platform.cpm.pandora.infra.metrics.CustomMetrics;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.IOperationLogService;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperationLogContextBo;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperatorBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.*;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.LauUnitExtraBo;
import sycpb.platform.cpm.pandora.service.api.resource.account.IAccountService;
import sycpb.platform.cpm.pandora.service.api.resource.account.bos.AccountBo;
import sycpb.platform.cpm.pandora.service.api.resource.material.IResImageService;
import sycpb.platform.cpm.pandora.service.api.resource.material.IResMaterialService;
import sycpb.platform.cpm.pandora.service.api.resource.material.LauMaterialBo;
import sycpb.platform.cpm.pandora.service.api.resource.material.ResImageBo;
import sycpb.platform.cpm.pandora.service.api.resource.mgk.bos.LandingPageBo;
import sycpb.platform.cpm.pandora.service.api.resource.mgk.bos.LandingPageGroupBo;
import sycpb.platform.cpm.pandora.service.constants.*;
import sycpb.platform.cpm.pandora.service.enums.CreativeAuditEvent;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.*;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.CreativeButtonBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.CreativeComponentBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.ShadowCreativeBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.TemplatePageBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.program.MaterialAuditBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.program.MiscElemBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.program.ProgCreativeAuditParamBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.shadow.program.ProgCreativeNeedAuditResultBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.enums.AdvertisingMode;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.enums.CreativeIsRecheckType;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.enums.IsDeleted;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.landing_page.LandingPageService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.unit.UnitImplMapper;
import sycpb.platform.cpm.pandora.service.impl.service.shadow.ShadowConverter;
import sycpb.platform.cpm.pandora.service.proxy.CpmMgkPortalProxy;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig.AD_CORE_DSL_CONTEXT;
import static sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig.AD_CORE_TX_MGR;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeLandingPageGroup.LAU_CREATIVE_LANDING_PAGE_GROUP;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauShadowCreative.LAU_SHADOW_CREATIVE;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitCreative.LAU_UNIT_CREATIVE;

/**
 * 程序化创意影子 service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProgrammaticShadowCreativeService {

    @Resource(name = AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;
    @Resource(name = "mainCluster")
    private RedissonClient redissonClient;

    private final ObjectMapper objectMapper;

    private final LauShadowCreativeDao lauShadowCreativeDao;
    private final LauCreativeImageDao lauCreativeImageDao;
    private final LauCreativeButtonCopyDao lauCreativeButtonCopyDao;
    private final LauCreativeComponentDao lauCreativeComponentDao;
    private final LauCreativeTabDao lauCreativeTabDao;
    private final LauCreativeTitleDao lauCreativeTitleDao;
    private final LauCreativeLandingPageDao lauCreativeLandingPageDao;
    private final LauCreativeExtraDao lauCreativeExtraDao;
    private final LauCreativeAuditStatDao lauCreativeAuditStatDao;
    private final LauImageAuditLogDao lauImageAuditLogDao;
    private final LauUnitExtraDao lauUnitExtraDao;
    private final LauCreativeArchiveDao lauCreativeArchiveDao;
    private final LauCreativeFlyDynamicInfoDao lauCreativeFlyDynamicInfoDao;

    private final UnitCreativeExtService unitCreativeExtService;
    private final IAccountService accountService;
    private final ShadowCreativeService shadowCreativeService;
    private final IOperationLogService operationLogService;
    private final CreativeTitleService creativeTitleService;
    private final CreativeImageService creativeImageService;
    private final CreativeButtonCopyService creativeButtonCopyService;
    private final CreativeComponentService creativeComponentService;
    private final AigcAuditService aigcAuditService;
    private final ProgramElementAndCreativeStatusService programElementAndCreativeStatusService;
    private final ParentChildCreativeAuditService parentChildCreativeAuditService;
    private final CreativeProgrammaticDetailService creativeProgrammaticDetailService;
    private final CreativeCpsReplaceLinkService creativeReplaceLinkService;
    private final IResMaterialService iResMaterialService;
    private final IResImageService iResImageService;
    private final AuditService auditService;

    private final CustomMetrics pandoraMetrics;
    private final CpmMgkPortalProxy cpmMgkPortalProxy;

    private static final String PROG_CREATIVE_AUDIT_LOCK_KEY = "progCreative.audit.key.%d";

    /**
     * 程序化元素审核
     * 1. 回填触审字段和元素审核状态
     * 2. 根据最新的元素状态(额外，主副)等-> 创意状态
     */
    @Transactional(value = AD_CORE_TX_MGR, rollbackFor = Exception.class)
    @SneakyThrows
    public ProgCreativeAuditContextBo doAuditProgElements(ProgCreativeAuditParamBo progCreativeAuditParamBo) {
        log.info("audit prog elements, param: {}", objectMapper.writeValueAsString(progCreativeAuditParamBo));
        checkParams(progCreativeAuditParamBo);
        ProgCreativeAuditContextBo ctx = new ProgCreativeAuditContextBo(progCreativeAuditParamBo);

        String lockKey = String.format(PROG_CREATIVE_AUDIT_LOCK_KEY, progCreativeAuditParamBo.getMiscElemBo().getCreativeId());
        RLock lock = redissonClient.getLock(lockKey);

        boolean execOk = false;
        boolean ok = false;
        try {
            ok = lock.tryLock(1, 5, TimeUnit.SECONDS);
            if (!ok) {
                throw new IllegalArgumentException("当前创意正在审核中，请刷新查看");
            }

            setDefaultValue(ctx);
            // 准备上线文其他信息
            preProcess(ctx);

            // 逻辑处理(更新内存数据)
            postProcess(ctx);

            validateAfterPostProcess(ctx);

            // 操作库(各个业务表)
            doSave2Db(ctx);

            saveLogs(ctx);
            execOk = true;
        } catch (InterruptedException e) {
            log.error("程序化创意审核异常，creativeId:{}, e:{}", ctx.getCreativeId(), ExceptionUtils.getStackTrace(e));
            execOk = false;
            throw new IllegalArgumentException("程序化创意审核异常，请重试");
        } finally {
            if (ok && lock.isLocked()) {
                lock.unlock();
            }
        }
        return ctx;
    }

    private void saveLogs(ProgCreativeAuditContextBo ctx) {
        final var opLogCtxBos = new ArrayList<OperationLogContextBo>();
        for (Map.Entry<Integer, List<ChangeLogBo>> entry : ctx.getChangeLogsMap().entrySet()) {
            final var logCtx = OperationLogContextBo.newContext(ctx.getOperator(), "lau_unit_creative");
            opLogCtxBos.add(logCtx);
            logCtx.updateContext(entry.getKey(), ctx.getOperationTypeMap().get(entry.getKey()));
            logCtx.setChanges(entry.getValue());
        }
        operationLogService.save(opLogCtxBos);
    }

    private void setDefaultValue(ProgCreativeAuditContextBo ctx) {

        if (ctx.getProgCreativeAuditParamBo().getNeedProcessParentCreative() == null) {
            ctx.getProgCreativeAuditParamBo().setNeedProcessParentCreative(true);
        }
        if (ctx.getProgCreativeAuditParamBo().getNeedDeleteCreativeShadow() == null) {
            ctx.getProgCreativeAuditParamBo().setNeedDeleteCreativeShadow(1);
        }

    }

    private void validateAfterPostProcess(ProgCreativeAuditContextBo ctx) {

        MiscElemBo miscElemBo = ctx.getProgCreativeAuditParamBo().getMiscElemBo();
        // details 的元素是否合法
        List<MaterialAuditBo> materialBos = ctx.getProgCreativeAuditParamBo().getMaterialBos();
        PandoraAssert.isTrue(materialBos.size() == ctx.getNewVersion().getLauProgrammaticCreativeDetailBos().size(), "审核主副元素数量不一致",
                ErrorCodeEnum.DomainType.CREATIVE_AUDIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);

        // 影子替换后才能校验
        for (LauProgrammaticCreativeDetailBo detailBo : ctx.getNewVersion().getLauProgrammaticCreativeDetailBos()) {
            Optional<MaterialAuditBo> first = materialBos.stream().filter(x -> Objects.equals(x.getMaterialId(), detailBo.getMaterialId())).findFirst();
            if (first.isPresent()) {
                detailBo.setBizStatus(first.get().getStatus());
                detailBo.setRejectedReason(first.get().getReason());
            } else if (Objects.equals(ProgrammaticElementAuditStatus.AUDIT_REJECTED, miscElemBo.getStatus())
                    && CollectionUtils.isEmpty(ctx.getProgCreativeAuditParamBo().getMaterialBos())
                    && Objects.isNull(detailBo.getBizStatus())) {
                // 审核拒绝 并且请求没有带任何元素审核信息 并且发现有新元素 那么认为是创意其他信息导致拒审
                // 回填影子 并且只把完全未审核的新元素置为待审核
                detailBo.setBizStatus(ProgrammaticElementAuditStatus.AUDITING);
            } else {
                throw new IllegalArgumentException("存在主副元素未审核, materialId=" + detailBo.getMaterialId());
            }
        }

    }

    private void postProcess(ProgCreativeAuditContextBo ctx) {
        ProgCreativeAuditParamBo progCreativeAuditParamBo = ctx.getProgCreativeAuditParamBo();
        MiscElemBo miscElemBo = progCreativeAuditParamBo.getMiscElemBo();
        ProgCreativeAuditSaveBo curVersion = ctx.getCurVersion();
        ProgCreativeAuditSaveBo newVersion = ctx.getNewVersion();

        // 影子回填(只是内存更新)
        writeBackFromShadow(ctx);

        // 填充审核的数据
        ctx.getNewVersion().getLauProgrammaticCreativeDetailBos().forEach(detailBo -> {
            MaterialAuditBo materialAuditBo = ctx.getMaterialAuditBoMap().get(detailBo.getMaterialId());
            if (materialAuditBo != null) {
                detailBo.setBizStatus(materialAuditBo.getStatus());
                detailBo.setRejectedReason(materialAuditBo.getReason());
            }
        });

        // 根据额外元素和主副元素关系获取创意审核状态
        ProgCreativeAuditResultBo progCreativeAuditResultBo = programElementAndCreativeStatusService.judgeProgCreativeAuditStatusByElements(miscElemBo, ctx, newVersion);
        log.info("audit prog elements, creativeId:{}, progCreativeAuditResultBo:{}", ctx.getCreativeId(), progCreativeAuditResultBo);
        ctx.setProgCreativeAuditResultBo(progCreativeAuditResultBo);
        // 创意状态更新
        newVersion.getLauUnitCreativeBo().setCreativeStatus(progCreativeAuditResultBo.getCreativeStatus());
        newVersion.getLauUnitCreativeBo().setAuditStatus(progCreativeAuditResultBo.getAuditStatus());
        newVersion.getLauUnitCreativeBo().setProgMiscElemAuditStatus(progCreativeAuditResultBo.getProgMiscElemAuditStatus());
        newVersion.getLauUnitCreativeBo().setProgAuditStatus(progCreativeAuditResultBo.getProgAuditStatus());
        newVersion.getLauUnitCreativeBo().setReason(progCreativeAuditResultBo.getReason());
        if (Boolean.TRUE.equals(progCreativeAuditParamBo.getRecheckFlag())) {
            newVersion.getLauUnitCreativeBo().setIsRecheck(CreativeIsRecheckType.RECHECK.getCode());
        }

        // 创意审核通过的话，一些校验
        if (Objects.equals(AuditStatus.PASSED, progCreativeAuditResultBo.getAuditStatus())) {
            if (NumberUtils.isPositive(newVersion.getLauUnitCreativeBo().getMgkPageId())) {
                cpmMgkPortalProxy.validateMgkPages(Collections.singletonList(newVersion.getLauUnitCreativeBo().getMgkPageId()));
            }

            validateMgkLandingPageGroup(miscElemBo.getCreativeId());

            List<LauCreativeArchiveBo> creativeArchiveBos = newVersion.getLauCreativeArchiveBos();
            if (!CollectionUtils.isEmpty(creativeArchiveBos) && NumberUtils.isPositive(creativeArchiveBos.get(0).getAvid())) {
                shadowCreativeService.validateArcs(Collections.singletonList(creativeArchiveBos.get(0).getAvid()));
            }

        }

        // 当前创意状态不是审核驳回、审核中、有效状态时，不更新创意状态
        Integer curCreativeStatus = curVersion.getLauUnitCreativeBo().getCreativeStatus();
        if (!Objects.equals(curCreativeStatus, CreativeStatus.AUDIT_REJECTED)
                && !Objects.equals(curCreativeStatus, CreativeStatus.AUDITING)
                && !Objects.equals(curCreativeStatus, CreativeStatus.VALID)) {
            newVersion.getLauUnitCreativeBo().setCreativeStatus(curCreativeStatus);
        }

        CreativeAuditResultBo creativeAuditResultBo = CreativeAuditResultBo.builder()
                .creativeId(ctx.getCreativeId()).auditStatus(progCreativeAuditResultBo.getAuditStatus()).reason(progCreativeAuditResultBo.getReason())
                .parentCreativeId(ctx.getParentCreativeId())
                .build();
        ctx.setCreativeAuditResultBo(creativeAuditResultBo);

        // 父子创意处理
        Integer parentFlag = parentChildCreativeAuditService.process(ctx);

        // aigc 助审
        Integer aigcFlag = aigcAuditService.auditCreative(ctx);
    }

    /**
     * 元素更新 db
     *
     * @param ctx
     */
    private void doSave2Db(ProgCreativeAuditContextBo ctx) {

        unitCreativeExtService.saveForProgCreativeAudit(ctx);
        creativeProgrammaticDetailService.saveForAuditProgram(ctx);

        // 存在影子
        if (ctx.getShadowCreativeBo() != null) {
            creativeTitleService.save(ctx);
            creativeImageService.save(ctx);
            creativeButtonCopyService.save(ctx);
            creativeComponentService.save(ctx);

//            creativeTabService.save(ctx);
//            saveCreativeLandPageForProgAudit(ctx);
//            creativeLandingPageService.save(ctx);
            // lau_creative_fly_dynamic_info
//            creativeFlyDynamicInfoService.save(ctx);

            // 替链
            creativeReplaceLinkService.save(ctx);

            Boolean needDeleteCreativeShadow = NumberUtils.isPositive(ctx.getProgCreativeAuditParamBo().getNeedDeleteCreativeShadow());
            if (needDeleteCreativeShadow) {
                adCore.delete(LAU_SHADOW_CREATIVE).where(LAU_SHADOW_CREATIVE.CREATIVE_ID.eq(ctx.getCreativeId())).execute();
            }
        }

        ProgCreativeAuditResultBo progCreativeAuditResultBo = ctx.getProgCreativeAuditResultBo();
        OperatorBo operatorBo = ctx.getOperator();
        // creative audit log
        LauUnitCreativePo lauUnitCreativePo = UnitCreativeImplMapper.MAPPER.toPo(ctx.getNewVersion().getLauUnitCreativeBo());
        // 元素审核日志
        saveCreativeAuditLog(ctx);

        // lau_image_audit_log
        List<LauProgrammaticCreativeDetailPo> creativeDetailPos = UnitCreativeImplMapper.MAPPER.toPos(ctx.getNewVersion().getLauProgrammaticCreativeDetailBos());
        Set<String> imageAuditLogMd5Set = ctx.getLauImageAuditLogBos().stream().map(t -> t.getImageMd5()).collect(Collectors.toSet());
        List<LauProgrammaticCreativeDetailPo> imageDetailPos = creativeDetailPos.stream().filter(t -> MaterialType.IMAGE_TYPES.contains(t.getMaterialType()) && !imageAuditLogMd5Set.contains(t.getMaterialMd5())).collect(Collectors.toList());
        saveCreativeImageAuditLog(imageDetailPos);

        // addCreativeAuditStat
        CreativeAuditEvent creativeAuditEvent = AuditStatus.PASSED == progCreativeAuditResultBo.getAuditStatus() ? CreativeAuditEvent.AUDITED : CreativeAuditEvent.REJECTED;
        addCreativeAuditStat(operatorBo.getOperatorName(), lauUnitCreativePo, creativeAuditEvent);
    }

    /**
     * 此处的影子回填仅仅是更新到内存，还没有写入 db
     *
     * @param ctx
     */
    private void writeBackFromShadow(ProgCreativeAuditContextBo ctx) {
        ShadowCreativeBo shadowCreativeBo = ctx.getShadowCreativeBo();
        if (shadowCreativeBo != null) {
            log.info("audit prog elements, shadow write back start, creativeId:{}", ctx.getCreativeId());

            // 主表
            writeBackCreativeFromShadow(ctx.getNewVersion().getLauUnitCreativeBo(), shadowCreativeBo);

            // 创意其他附表
            writeBackCreativeExtraInfoFromShadow(ctx);
        }
    }

    private void writeBackCreativeExtraInfoFromShadow(ProgCreativeAuditContextBo ctx) {

        ShadowCreativeBo shadowCreativeBo = ctx.getShadowCreativeBo();

        // creative title
        List<LauCreativeTitleBo> lauCreativeTitleBos = shadowCreativeBo.getLauCreativeTitleBos();
        ctx.getNewVersion().setLauCreativeTitleBos(lauCreativeTitleBos);

        // creative image
        List<LauCreativeImageBo> lauCreativeImageBos = shadowCreativeBo.getImages().stream()
                .filter(Objects::nonNull)
                .map(image -> ShadowConverter.MAPPER.shadowImgBoToLauImgBo(ctx.getUnitId(), ctx.getCreativeId(), image))
                .collect(Collectors.toList());
        ctx.getNewVersion().setLauCreativeImageBos(lauCreativeImageBos);

        // creative button copy
        List<LauCreativeButtonCopyBo> lauCreativeButtonCopyBos = Stream.of(shadowCreativeBo.getButton())
                .filter(Objects::nonNull)
                .map(r -> ShadowConverter.MAPPER.shadowButtonBoToLau(ctx.getCreativeId(), r))
                .collect(Collectors.toList());
        ctx.getNewVersion().setLauCreativeButtonCopyBos(lauCreativeButtonCopyBos);

        // creative component
        List<LauCreativeComponentBo> lauCreativeComponentBos = shadowCreativeBo.getComponents()
                .stream()
                .filter(Objects::nonNull)
                .map(component -> ShadowConverter.MAPPER.shadowComponentBoToLau(ctx.getAccountId(), ctx.getCampaignId(), ctx.getUnitId(), ctx.getCreativeId(), component))
                .collect(Collectors.toList());
        ctx.getNewVersion().setLauCreativeComponentBos(lauCreativeComponentBos);

        // creative tab
        if (AdvertisingMode.nativeContentMode(shadowCreativeBo.getAdvertisingMode())) {
            List<LauCreativeTabBo> lauCreativeTabBos = Stream.of(shadowCreativeBo.getTab())
                    .filter(Objects::nonNull)
                    .map(tab -> ShadowConverter.MAPPER.shadowTabBoToLau(ctx.getAccountId(), ctx.getUnitId(), (long) ctx.getCreativeId(), tab))
                    .collect(Collectors.toList());
            ctx.getNewVersion().setLauCreativeTabBos(lauCreativeTabBos);
        }

        // creative details
        List<LauProgrammaticCreativeDetailBo> creativeDetailBos = shadowCreativeBo.getCreativeDetailBos()
                .stream()
                .filter(Objects::nonNull)
                .map(detailBo -> ShadowConverter.MAPPER.shadowProgDetailBoToLau(ctx.getAccountId(), ctx.getCampaignId(), ctx.getUnitId(), ctx.getCreativeId(), detailBo))
                .collect(Collectors.toList());
        creativeDetailBos.stream().forEach(creativeDetailBo -> {
            creativeDetailBo.setAccountId(ctx.getAccountId());
            creativeDetailBo.setCampaignId(ctx.getCampaignId());
            creativeDetailBo.setUnitId(ctx.getUnitId());
        });
        ctx.getNewVersion().setLauProgrammaticCreativeDetailBos(creativeDetailBos);

        CreativeCpsReplaceLinkBo cpsReplaceLink = shadowCreativeBo.getCpsReplaceLink();
        if (cpsReplaceLink != null) {
            ctx.getNewVersion().setCreativeCpsReplaceLinkBo(cpsReplaceLink);
        }

//        // creative archive
//        List<LauCreativeArchiveBo> lauCreativeArchiveBos = Stream.of(shadowCreativeBo.getCreativeArchiveBo())
//                .filter(Objects::nonNull)
//                .map(archiveBo -> ShadowConverter.MAPPER.shadowArchiveBoToLauArchiveBo(ctx.getUnitId(), ctx.getCreativeId(), archiveBo))
//                .collect(Collectors.toList());
//        ctx.getNewVersion().setLauCreativeArchiveBos(lauCreativeArchiveBos);
//
//        // creative dynamic
//        List<LauCreativeFlyDynamicInfoBo> creativeFlyDynamicInfoBos = Stream.of(shadowCreativeBo.getCreativeFlyDynamicInfoBo())
//                .filter(Objects::nonNull)
//                .map(dynamicInfoBo -> ShadowConverter.MAPPER.shadowFlyDynamicInfoBoToLauDynamicBo(ctx.getUnitId(), ctx.getCreativeId(), dynamicInfoBo))
//                .collect(Collectors.toList());
//        ctx.getNewVersion().setLauCreativeFlyDynamicInfoBos(creativeFlyDynamicInfoBos);
//
//        // creative landing page
//        List<LauCreativeLandingPageBo> creativeLandingPageBos = Stream.of(shadowCreativeBo.getLandingPage())
//                .filter(Objects::nonNull)
//                .map(creativeLandingPageBo -> ShadowConverter.MAPPER.shadowLandingPageToLauBo(ctx.getUnitId(), ctx.getCreativeId(), creativeLandingPageBo))
//                .collect(Collectors.toList());
//        ctx.getNewVersion().setLauCreativeLandingPageBos(creativeLandingPageBos);
//
//        // creative landing page group
//        List<LauCreativeLandingPageGroupBo> landingPageGroupBos = Stream.of(shadowCreativeBo.getLandingPageGroupBo())
//                .filter(Objects::nonNull)
//                .map(landingPageGroupBo -> ShadowConverter.MAPPER.shadowLandPageGroupToLauBo(ctx.getUnitId(), ctx.getCreativeId(), landingPageGroupBo))
//                .collect(Collectors.toList());
//        ctx.getNewVersion().setLauCreativeLandingPageGroupBos(landingPageGroupBos);

    }

    @Deprecated
    private void saveCreativeLandPageForProgAudit(ProgCreativeAuditContextBo ctx) {
        ShadowCreativeBo shadowCreativeBo = ctx.getShadowCreativeBo();

//        final boolean needSaveExtraLandingPage = Objects.equals(PromotionPurposeType.LANDING_PAGE.getCode(), shadowCreativeBo.getPromotionPurposeType())
//                || Objects.equals(PromotionPurposeType.ON_SHELF_GAME.getCode(), shadowCreativeBo.getPromotionPurposeType())
//                || Objects.equals(PromotionPurposeType.APP_DOWNLOAD.getCode(), shadowCreativeBo.getPromotionPurposeType())
//                || Objects.equals(PromotionPurposeType.SALE_GOODS.getCode(), shadowCreativeBo.getPromotionPurposeType());
//        if (needSaveExtraLandingPage) {
//            //创意联投落地页 page id
//            Long extraLandingPageId = Optional.ofNullable(shadowCreativeBo.getLandingPage()).map(CreativeLandingPageBo::getContainerPageId).orElse(0L);
//            TemplatePageBo extraLandPageTemplatePageBo = ctx.getTemplatePageBoMap().get(extraLandingPageId);
//
//            shadowCreativeService.saveLandingPageExtraInfo(ctx.getAccountId(), ctx.getCampaignId(), ctx.getUnitId(), ctx.getCreativeId(), extraLandPageTemplatePageBo);
//        }
    }

    /**
     * @param creative         创意
     * @param shadowCreativeBo 影子创意
     */
    public void writeBackCreativeFromShadow(LauUnitCreativeBo creative, ShadowCreativeBo shadowCreativeBo) {
        creative.setTitle(shadowCreativeBo.getTitle());
        creative.setCmMark(shadowCreativeBo.getCmMark());
        creative.setBusMarkId(shadowCreativeBo.getBusMark());
        creative.setDescription(shadowCreativeBo.getDescription());
//        creative.setExtDescription(shadowCreativeBo.getExtDescription());
        creative.setMgkPageId(shadowCreativeBo.getMgkPageId());
        creative.setJumpType(shadowCreativeBo.getJumpType());
        creative.setPromotionPurposeContent(shadowCreativeBo.getJumpUrl());
        creative.setPromotionPurposeContentSecondary(shadowCreativeBo.getJumpUrlSecondary());
        creative.setTrackadf(shadowCreativeBo.getTrackadf());
        // 不置信 视频模板组不写lau_creative_image
//        List<CreativeImageBo> images = shadowCreativeBo.getImages();
//        CreativeImageBo image = !CollectionUtils.isEmpty(images) ? images.get(0) : CreativeImageBo.builder().imageUrl(StringUtils.EMPTY).imageMd5(StringUtils.EMPTY).build();
//        creative.setImageUrl(image.getImageUrl());
//        creative.setImageMd5(image.getImageMd5());
        creative.setIsPageGroup(shadowCreativeBo.getIsPageGroup());
        creative.setButtonCopy(Optional.ofNullable(shadowCreativeBo.getButton()).map(CreativeButtonBo::getButtonContent).orElse(StringUtils.EMPTY));
        creative.setVideoId(shadowCreativeBo.getVideoId());
    }

    private static void checkParams(ProgCreativeAuditParamBo progCreativeAuditParamBo) {
        PandoraAssert.notNull(progCreativeAuditParamBo, "程序化审核参数缺失",
                ErrorCodeEnum.DomainType.CREATIVE_AUDIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);

        MiscElemBo miscElemBo = progCreativeAuditParamBo.getMiscElemBo();
        PandoraAssert.notNull(miscElemBo, "程序化创意审核其他元素不能为空",
                ErrorCodeEnum.DomainType.CREATIVE_AUDIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);
        PandoraAssert.isTrue(NumberUtils.isPositive(miscElemBo.getCreativeId()), "额外元素创意id缺失",
                ErrorCodeEnum.DomainType.CREATIVE_AUDIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);
        PandoraAssert.notNull(miscElemBo.getStatus(), "额外元素审核状态缺失",
                ErrorCodeEnum.DomainType.CREATIVE_AUDIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);

        PandoraAssert.notNull(progCreativeAuditParamBo.getOperatorBo(), "程序化审核操作人参数缺失",
                ErrorCodeEnum.DomainType.CREATIVE_AUDIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);

        // 其他元素拒绝时 创意必然不可投放 不一定需要审核主副元素 适用于其他元素(稿件/落地页)原因拒审创意
        PandoraAssert.isTrue(Objects.equals(miscElemBo.getStatus(), AuditStatus.REJECTED)
                        || !CollectionUtils.isEmpty(progCreativeAuditParamBo.getMaterialBos()), "程序化创意审核主副元素不能为空",
                ErrorCodeEnum.DomainType.CREATIVE_AUDIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);

        for (MaterialAuditBo materialBo : progCreativeAuditParamBo.getMaterialBos()) {
            PandoraAssert.notNull(materialBo, "程序化创意审核主副元素缺失",
                    ErrorCodeEnum.DomainType.CREATIVE_AUDIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);
            PandoraAssert.isTrue(NumberUtils.isPositive(materialBo.getMaterialId()), "程序化创意审核主副元素materialId缺失",
                    ErrorCodeEnum.DomainType.CREATIVE_AUDIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);
            PandoraAssert.notNull(materialBo.getStatus(), "程序化创意审核主副元素状态缺失",
                    ErrorCodeEnum.DomainType.CREATIVE_AUDIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);
        }
    }

    /**
     * 查询需要用到的信息放入 context
     *
     * @param ctx
     */
    private void preProcess(ProgCreativeAuditContextBo ctx) {

        ProgCreativeAuditSaveBo curVersion = ctx.getCurVersion();
        ProgCreativeAuditSaveBo newVersion = ctx.getNewVersion();

        // ================================ 两个版本信息 ================================

        // creative extra
        LauCreativeExtraPo creativeExtraPo = lauCreativeExtraDao.fetchOneByCreativeId(ctx.getCreativeId());
        LauCreativeExtraBo lauCreativeExtraBo = UnitCreativeImplMapper.MAPPER.fromPo(creativeExtraPo);
        ctx.setLauCreativeExtraBo(lauCreativeExtraBo);
        if (lauCreativeExtraBo != null) {
            ctx.setParentCreativeId(lauCreativeExtraBo.getParentCreativeId());
            if (NumberUtils.isPositive(lauCreativeExtraBo.getParentCreativeId())) {
                ctx.getOperator().setOperatorType(OperatorTypeConstants.EFFECT_AUTO_LAUNCH_SYSTEM);
            }
        }

        List<Integer> creativeIdsParentAndChild = new ArrayList<>();
        creativeIdsParentAndChild.add(ctx.getCreativeId());
        Optional.ofNullable(ctx.getParentCreativeId()).ifPresent(creativeIdsParentAndChild::add);

        // lau_unit_creative(父子创意一起查)
        List<LauUnitCreativeBo> parentChildCreativeBos = unitCreativeExtService.fetch(creativeIdsParentAndChild);
        Map<Integer, LauUnitCreativeBo> parentChildCreativeBoMap = parentChildCreativeBos.stream().collect(Collectors.toMap(LauUnitCreativeBo::getCreativeId, Function.identity()));

        LauUnitCreativeBo curChildUnitCreativeBo = parentChildCreativeBoMap.get(ctx.getCreativeId());
        PandoraAssert.isTrue(Objects.equals(curChildUnitCreativeBo.getIsProgrammatic(), 1), "非程序化创意",
                ErrorCodeEnum.DomainType.CREATIVE_AUDIT, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.DATA_INVALID);

        ctx.setUnitId(curChildUnitCreativeBo.getUnitId());
        ctx.setCampaignId(curChildUnitCreativeBo.getCampaignId());
        ctx.setAccountId(curChildUnitCreativeBo.getAccountId());
        curVersion.setLauUnitCreativeBo(curChildUnitCreativeBo);
        newVersion.setLauUnitCreativeBo(UnitCreativeImplMapper.MAPPER.copyUnitCreativeBo(curChildUnitCreativeBo));
        ctx.setParentUnitCreativeBo(parentChildCreativeBoMap.get(ctx.getParentCreativeId()));

        // creative detail(父子创意的一起查)
        List<LauProgrammaticCreativeDetailBo> parentChildProgCreativeDetailBos = creativeProgrammaticDetailService.fetch(creativeIdsParentAndChild);
        Map<Integer, List<LauProgrammaticCreativeDetailBo>> parentChildCreativeDetailBoMap = parentChildProgCreativeDetailBos.stream().collect(Collectors.groupingBy(LauProgrammaticCreativeDetailBo::getCreativeId));

        List<LauProgrammaticCreativeDetailBo> curChildCreativeDetailBos = parentChildCreativeDetailBoMap.getOrDefault(ctx.getCreativeId(), new ArrayList<>());
        curVersion.setLauProgrammaticCreativeDetailBos(curChildCreativeDetailBos);
        newVersion.setLauProgrammaticCreativeDetailBos(UnitCreativeImplMapper.MAPPER.copyProgramCreativeDetailBos(curChildCreativeDetailBos));

        List<LauProgrammaticCreativeDetailBo> parentCreativeDetailBos = parentChildCreativeDetailBoMap.getOrDefault(ctx.getParentCreativeId(), new ArrayList<>());
        ctx.setParentCreativeDetailBos(parentCreativeDetailBos);

        // 收集 material ids(回填后的影子里的也需要，查完后放入 map)
        Set<Long> allMaterialIds = curChildCreativeDetailBos.stream().map(LauProgrammaticCreativeDetailBo::getMaterialId).collect(Collectors.toSet());
        Set<String> allMaterialMd5 = curChildCreativeDetailBos.stream().map(LauProgrammaticCreativeDetailBo::getMaterialMd5).collect(Collectors.toSet());

        // creative archive
        List<LauCreativeArchivePo> lauCreativeArchivePos = lauCreativeArchiveDao.fetchByCreativeId(ctx.getCreativeId());
        List<LauCreativeArchiveBo> lauCreativeArchiveBos = UnitCreativeImplMapper.MAPPER.fromLauCreativeArchivePos(lauCreativeArchivePos);
        curVersion.setLauCreativeArchiveBos(lauCreativeArchiveBos);
        newVersion.setLauCreativeArchiveBos(UnitCreativeImplMapper.MAPPER.copyLauCreativeArchivePos(lauCreativeArchiveBos));
        if (!CollectionUtils.isEmpty(lauCreativeArchivePos)) {
            allMaterialIds.addAll(lauCreativeArchivePos.stream().map(LauCreativeArchivePo::getMaterialId).collect(Collectors.toSet()));
        }

        // creative titles
        List<LauCreativeTitlePo> lauCreativeTitlePos = lauCreativeTitleDao.fetchByCreativeId(ctx.getCreativeId());
        List<LauCreativeTitleBo> lauCreativeTitleBos = UnitCreativeImplMapper.MAPPER.fromLauCreativeTitlePos(lauCreativeTitlePos);
        curVersion.setLauCreativeTitleBos(lauCreativeTitleBos);
        newVersion.setLauCreativeTitleBos(UnitCreativeImplMapper.MAPPER.copyCreativeTitles(lauCreativeTitleBos));
        if (!CollectionUtils.isEmpty(lauCreativeTitlePos)) {
            allMaterialIds.addAll(lauCreativeTitlePos.stream().map(LauCreativeTitlePo::getMaterialId).collect(Collectors.toSet()));
            allMaterialMd5.addAll(lauCreativeTitlePos.stream().map(LauCreativeTitlePo::getMaterialMd5).collect(Collectors.toSet()));
        }

        // creative images
        List<LauCreativeImagePo> lauCreativeImagePos = lauCreativeImageDao.fetchByCreativeId(ctx.getCreativeId());
        List<LauCreativeImageBo> lauCreativeImageBos = UnitCreativeImplMapper.MAPPER.fromLauCreativeImagePos(lauCreativeImagePos);
        curVersion.setLauCreativeImageBos(lauCreativeImageBos);
        newVersion.setLauCreativeImageBos(UnitCreativeImplMapper.MAPPER.copyLauCreativeImageBos(lauCreativeImageBos));
        if (!CollectionUtils.isEmpty(lauCreativeImagePos)) {
            allMaterialIds.addAll(lauCreativeImagePos.stream().map(LauCreativeImagePo::getMaterialId).collect(Collectors.toSet()));
            allMaterialMd5.addAll(lauCreativeImagePos.stream().map(LauCreativeImagePo::getImageMd5).collect(Collectors.toSet()));
        }

        // 影子创意
        Set<Long> allPageIds = new HashSet<>();
        if (NumberUtils.isPositive(curChildUnitCreativeBo.getMgkPageId())) {
            allPageIds.add(curChildUnitCreativeBo.getMgkPageId());
        }
        List<LauShadowCreativePo> lauShadowCreativePos = lauShadowCreativeDao.fetchByCreativeId(ctx.getCreativeId());
        if (!CollectionUtils.isEmpty(lauShadowCreativePos)) {
            ShadowCreativeBo shadowCreativeBo = shadowCreativeService.convertShadowPo2Bo(lauShadowCreativePos.get(0));
            ctx.setShadowCreativeBo(shadowCreativeBo);
            log.info("audit prog elements, shadow exist, creativeId:{}", ctx.getCreativeId());

            // 后面需要校验影子里的落地页是否 ok
            if (shadowCreativeBo != null) {
                List<Long> pageIds = new ArrayList<>();
                if (shadowCreativeBo.getLandingPage() != null) {
                    pageIds.add(shadowCreativeBo.getLandingPage().getContainerPageId());
                }
                if (NumberUtils.isPositive(shadowCreativeBo.getMgkPageId())) {
                    pageIds.add(shadowCreativeBo.getMgkPageId());
                    allPageIds.add(shadowCreativeBo.getMgkPageId());
                }

            }
            if (!CollectionUtils.isEmpty(shadowCreativeBo.getCreativeDetailBos())) {
                allMaterialIds.addAll(shadowCreativeBo.getCreativeDetailBos().stream().map(LauProgrammaticCreativeDetailBo::getMaterialId).collect(Collectors.toSet()));
                allMaterialMd5.addAll(shadowCreativeBo.getCreativeDetailBos().stream().map(LauProgrammaticCreativeDetailBo::getMaterialMd5).collect(Collectors.toSet()));
            }

            // 影子存在才需要查询影子替换的数据做覆盖

            // creative fly dynamic info
            List<LauCreativeFlyDynamicInfoPo> lauCreativeFlyDynamicInfoPos = lauCreativeFlyDynamicInfoDao.fetchByCreativeId(ctx.getCreativeId());
            List<LauCreativeFlyDynamicInfoBo> lauCreativeFlyDynamicInfoBos = UnitCreativeImplMapper.MAPPER.fromLauCreativeFlyDynamicInfoPos(lauCreativeFlyDynamicInfoPos);
            curVersion.setLauCreativeFlyDynamicInfoBos(lauCreativeFlyDynamicInfoBos);
            newVersion.setLauCreativeFlyDynamicInfoBos(UnitCreativeImplMapper.MAPPER.copyLauCreativeFlyDynamicInfoBos(lauCreativeFlyDynamicInfoBos));

            // creative button copy
            List<LauCreativeButtonCopyPo> lauCreativeButtonCopyPos = lauCreativeButtonCopyDao.fetchByCreativeId(ctx.getCreativeId());
            List<LauCreativeButtonCopyBo> lauCreativeButtonCopyBos = UnitCreativeImplMapper.MAPPER.fromLauCreativeButtonCopyPos(lauCreativeButtonCopyPos);
            curVersion.setLauCreativeButtonCopyBos(lauCreativeButtonCopyBos);
            newVersion.setLauCreativeButtonCopyBos(UnitCreativeImplMapper.MAPPER.copyLauCreativeButtonCopyBos(lauCreativeButtonCopyBos));

            // creative component
            List<LauCreativeComponentPo> lauCreativeComponentPos = lauCreativeComponentDao.fetchByCreativeId(ctx.getCreativeId());
            List<LauCreativeComponentBo> lauCreativeComponentBos = UnitCreativeImplMapper.MAPPER.fromLauCreativeComponentPos(lauCreativeComponentPos);
            curVersion.setLauCreativeComponentBos(lauCreativeComponentBos);
            newVersion.setLauCreativeComponentBos(UnitCreativeImplMapper.MAPPER.copyLauCreativeComponentBos(lauCreativeComponentBos));

            // creative tab
            List<LauCreativeTabPo> lauCreativeTabPos = lauCreativeTabDao.fetchByCreativeId(ctx.getCreativeId().longValue());
            List<LauCreativeTabBo> lauCreativeTabBos = UnitCreativeImplMapper.MAPPER.fromLauCreativeTabPos(lauCreativeTabPos);
            curVersion.setLauCreativeTabBos(lauCreativeTabBos);
            newVersion.setLauCreativeTabBos(UnitCreativeImplMapper.MAPPER.copyLauCreativeTabBos(lauCreativeTabBos));

            // creative landing page
            List<LauCreativeLandingPagePo> lauCreativeLandingPagePos = lauCreativeLandingPageDao.fetchByCreativeId(ctx.getCreativeId());
            List<LauCreativeLandingPageBo> lauCreativeLandingPageBos = UnitCreativeImplMapper.MAPPER.fromLauCreativeLandingPagePos(lauCreativeLandingPagePos);
            curVersion.setLauCreativeLandingPageBos(lauCreativeLandingPageBos);
            newVersion.setLauCreativeLandingPageBos(UnitCreativeImplMapper.MAPPER.copyLauCreativeLandingPageBos(lauCreativeLandingPageBos));

            // 替链
            CreativeCpsReplaceLinkBo creativeCpsReplaceLinkBo = creativeReplaceLinkService.fetchByCreativeId(ctx.getCreativeId());
            curVersion.setCreativeCpsReplaceLinkBo(creativeCpsReplaceLinkBo);
            newVersion.setCreativeCpsReplaceLinkBo(UnitCreativeImplMapper.MAPPER.copy(creativeCpsReplaceLinkBo));
        }

        // unit extra
        LauUnitExtraPo lauUnitExtraPo = lauUnitExtraDao.fetchOneByUnitId(curChildUnitCreativeBo.getUnitId());
        LauUnitExtraBo lauUnitExtraBo = UnitImplMapper.MAPPER.fromPo(lauUnitExtraPo);
        ctx.setLauUnitExtraBo(lauUnitExtraBo);

        // lau_image_audit_log
        if (!CollectionUtils.isEmpty(allMaterialMd5)) {
            List<LauImageAuditLogPo> imageAuditLogPos = lauImageAuditLogDao.fetchByImageMd5(allMaterialMd5.toArray(new String[0]));
            ctx.setLauImageAuditLogBos(UnitCreativeImplMapper.MAPPER.toBos(imageAuditLogPos));
        }


        // accountBo
        AccountBo accountBo = accountService.get(ctx.getAccountId());

        Map<Long, LauMaterialBo> lauMaterialBoMap = iResMaterialService.fetchMap(allMaterialIds);
        ctx.setLauMaterialBoMap(lauMaterialBoMap);
        ctx.setAccountBo(accountBo);

        Map<Long, ResImageBo> resImageBoMap = iResImageService.fetchMap(allMaterialIds);
        ctx.setResImageBoMap(resImageBoMap);

        // 落地页
        // 为了aaa 不能做账户id校验
        final var pageIdMap = cpmMgkPortalProxy.fetchLandingPages(0, new ArrayList<>(allPageIds))
                .stream()
                .collect(Collectors.toMap(LandingPageBo::getPageId, Function.identity()));
        ctx.setLandingPageBoMap(pageIdMap);
    }

    private static TemplatePageBo convertTemplatePage2Bo(TemplatePage templatePage) {
        return TemplatePageBo.builder()
                .mgkPageId(templatePage.getMgkPageId())
                .launchUrl(templatePage.getJumpUrl())
                .launchUrlSecondary(templatePage.getJumpUrlSecondary())
                .build();
    }

    private void validateMgkLandingPageGroup(Integer creativeId) {
        List<LauCreativeLandingPageGroupPo> creativeLandingPageGroupPos = adCore.selectFrom(LAU_CREATIVE_LANDING_PAGE_GROUP)
                .where(LAU_CREATIVE_LANDING_PAGE_GROUP.IS_DELETED.eq(IsDeleted.VALID.getCode()).and(LAU_CREATIVE_LANDING_PAGE_GROUP.CREATIVE_ID.in(Lists.newArrayList(creativeId))))
                .fetch().into(LauCreativeLandingPageGroupPo.class);
        List<Long> groupIdList = creativeLandingPageGroupPos.stream()
                .map(LauCreativeLandingPageGroupPo::getGroupId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, LandingPageGroupBo> pageGroupBoMap = cpmMgkPortalProxy.fetchPageGroupMap(groupIdList);
        List<Long> invalidPageGroupIdList = pageGroupBoMap.values().stream()
                .filter(pageGroupBaseDto -> PageGroupStatus.VALID.getNumber() != pageGroupBaseDto.getGroupStatus())
                .map(LandingPageGroupBo::getGroupId)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(groupIdList)
                && invalidPageGroupIdList.contains(groupIdList.get(0))) {
            throw new IllegalArgumentException("当前程序化创意关联程序化落地页组不可用,审核通过失败");
        }
    }

    private void addCreativeAuditStat(String operatorName, LauUnitCreativePo po, CreativeAuditEvent event) {
        LauCreativeAuditStatPo lauCreativeAuditStatPo = new LauCreativeAuditStatPo();
        lauCreativeAuditStatPo.setCreativeId(po.getCreativeId());
        lauCreativeAuditStatPo.setCampaignId(po.getCampaignId());
        lauCreativeAuditStatPo.setUnitId(po.getUnitId());
        lauCreativeAuditStatPo.setAccountId(po.getAccountId());
        lauCreativeAuditStatPo.setEvent(event.getCode());
        lauCreativeAuditStatPo.setOperatorUsername(operatorName);
        lauCreativeAuditStatDao.insert(lauCreativeAuditStatPo);
    }

    private void saveCreativeImageAuditLog(List<LauProgrammaticCreativeDetailPo> creativeDetailPos) {

        if (CollectionUtils.isEmpty(creativeDetailPos)) {
            return;
        }

        try {
            List<LauImageAuditLogPo> imageAuditLogPos = new ArrayList<>();
            for (LauProgrammaticCreativeDetailPo x : creativeDetailPos) {
                int auditStatusForLog = AuditStatus.AUDITING;
                if (Objects.equals(ProgrammaticElementAuditStatus.PASSED, x.getBizStatus())) {
                    auditStatusForLog = AuditStatus.PASSED;
                } else if (Objects.equals(ProgrammaticElementAuditStatus.AUDIT_REJECTED, x.getBizStatus())) {
                    auditStatusForLog = AuditStatus.REJECTED;
                }

                LauImageAuditLogPo imageAuditLogPo = new LauImageAuditLogPo();
                imageAuditLogPo.setImageId(x.getMaterialId().intValue());
                imageAuditLogPo.setCreativeId(x.getCreativeId());
                imageAuditLogPo.setAuditStatus(auditStatusForLog);
                imageAuditLogPo.setImageMd5(x.getMaterialMd5());
                imageAuditLogPo.setReason(x.getRejectedReason() != null ? x.getRejectedReason() : "");
                imageAuditLogPo.setIsDeleted(IsDeleted.VALID.getCode());
                imageAuditLogPo.setMtime(new Timestamp(System.currentTimeMillis()));
                imageAuditLogPo.setCtime(x.getCtime());
                imageAuditLogPos.add(imageAuditLogPo);
            }
            lauImageAuditLogDao.insert(imageAuditLogPos);
        } catch (Exception e) {
            log.error("saveCreativeImageAuditLog error", e);
        }
    }

    private void saveCreativeAuditLog(ProgCreativeAuditContextBo ctx) {

        MiscElemBo miscElemBo = ctx.getProgCreativeAuditParamBo().getMiscElemBo();
        List<MaterialAuditBo> materialBos = ctx.getProgCreativeAuditParamBo().getMaterialBos();
        Integer curAuditStatus = ctx.getCurVersion().getLauUnitCreativeBo().getAuditStatus();
        LauUnitCreativeBo newUnitCreativeBo = ctx.getNewVersion().getLauUnitCreativeBo();
        Integer newAuditStatus = newUnitCreativeBo.getAuditStatus();
        OperatorBo operatorBo = ShadowConverter.MAPPER.copy(ctx.getOperator());
        // 设置对了 accountId 投放端历史才能显示
        operatorBo.setOperatorId(ctx.getAccountId());

        Map<Long, LauProgrammaticCreativeDetailBo> creativeDetailBoMap = ctx.getNewVersion().getLauProgrammaticCreativeDetailBos().stream().collect(Collectors.toMap(t -> t.getMaterialId(), t -> t));
        Map<Long, LauMaterialBo> lauMaterialBoMap = ctx.getLauMaterialBoMap();
        List<LauCreativeArchiveBo> lauCreativeArchiveBos = ctx.getCurVersion().getLauCreativeArchiveBos();
        Map<Long, LauCreativeArchiveBo> lauCreativeArchiveBoMap = lauCreativeArchiveBos.stream().collect(Collectors.toMap(LauCreativeArchiveBo::getMaterialId, Function.identity(), (k1, k2) -> k1));

        // 创意审核日志
        OperationLogContextBo logContextBo = OperationLogContextBo.newContext(operatorBo, "lau_unit_creative");
        List<ChangeLogBo> changeBos = new ArrayList();
        ChangeLogBo auditChangeBo = ChangeLogBo.builder()
                .key(LAU_UNIT_CREATIVE.AUDIT_STATUS.getName())
                .desc("审核状态")
                .oldValue(AuditStatus.auditingStatusMapper(curAuditStatus))
                .newValue(AuditStatus.auditingStatusMapper(newAuditStatus))
                .build();
        changeBos.add(auditChangeBo);
        ChangeLogBo miscChangeBo = ChangeLogBo.builder()
                .key("misc_elem")
                .desc("其他元素")
                .oldValue(ProgrammaticElementAuditStatus.auditingStatusMapper(newUnitCreativeBo.getProgMiscElemAuditStatus()))
                .newValue(ProgrammaticElementAuditStatus.auditingStatusMapper(miscElemBo.getStatus()))
                .build();
        changeBos.add(miscChangeBo);

        List<ChangeLogBo> modifyReasonChangeBos = new ArrayList();
        final List<String> passedList = new LinkedList<>();
        final List<String> rejectedList = new LinkedList<>();
        for (MaterialAuditBo x : materialBos) {

            LauMaterialBo lauMaterialBo = lauMaterialBoMap.get(x.getMaterialId());
            if (Objects.isNull(lauMaterialBo)) continue;

            String content = lauMaterialBo.getContent();
            LauCreativeArchiveBo lauCreativeArchiveBo = lauCreativeArchiveBoMap.get(x.getMaterialId());
            // creative archive 格式不一样: bo.mgkVideoMedia.getMgkVideoUrl() + "-" + bo.mgkVideoMedia.getVideoCover().getUrl() ==
            if (Objects.equals(MaterialType.ARCHIVE_AND_COVER, lauMaterialBo.getType()) && lauCreativeArchiveBo != null) {
                content = LandingPageService.genPlayPageUrl(lauCreativeArchiveBo.getAvid()) + "-" + lauCreativeArchiveBo.getCoverUrl();
            }

            if (Objects.equals(x.getStatus(), ProgrammaticElementAuditStatus.PASSED)) {
                passedList.add(content);
            } else if (Objects.equals(x.getStatus(), ProgrammaticElementAuditStatus.AUDIT_REJECTED)) {
                rejectedList.add(content);
                LauProgrammaticCreativeDetailBo creativeDetailPo = creativeDetailBoMap.get(x.getMaterialId());
                if (creativeDetailPo == null && !Optional.ofNullable(x.getReason()).orElse("").equals(creativeDetailPo.getRejectedReason())) {
                    modifyReasonChangeBos.add(ChangeLogBo.builder()
                            .key(x.getMaterialId().toString())
                            .desc("修改驳回理由")
                            .oldValue(creativeDetailPo.getRejectedReason())
                            .newValue(x.getReason())
                            .build());
                }
            } else {
                throw new IllegalArgumentException("更新程序化创意审核状态: 审核结果只能是通过和拒绝");
            }
        }

        ChangeLogBo passChangeBo = ChangeLogBo.builder()
                .key("pass_elem")
                .desc("通过的元素")
                .oldValue("")
                .newValue(passedList.toString())
                .build();
        changeBos.add(passChangeBo);

        ChangeLogBo rejectedChangeBo = ChangeLogBo.builder()
                .key("reject_elem")
                .desc("拒绝的元素")
                .oldValue("")
                .newValue(rejectedList.toString())
                .build();
        changeBos.add(rejectedChangeBo);

        logContextBo.setChanges(changeBos);
        logContextBo.updateContext(miscElemBo.getCreativeId(), OperationType.UPDATE);
        operationLogService.save(Lists.newArrayList(logContextBo));
    }

    public void processForSaveCreative(SaveUnitCreativeContextBo creativeContextBo) {

        if (!creativeContextBo.isProgrammatic()) return;

        boolean hasV2ProgAuth = creativeProgrammaticDetailService.hasV2ProgShadowAuth(creativeContextBo);
        Set<Integer> newCreativeIds = creativeContextBo.getCurrentVersion().getCreatives().stream().map(t -> t.getLauUnitCreativeBo().getCreativeId()).collect(Collectors.toSet());
        log.info("process program shadow creatives start, unitId:{},hasV2ProgAuth={},accountId={},newCreativeIds={}", creativeContextBo.getUnitId(), hasV2ProgAuth, creativeContextBo.getAccountId(), newCreativeIds);

        List<CreativeBo> curCreativeBos = creativeContextBo.getCurrentVersion().getCreatives();
        UnitCreativeBo newUnitCreativeBo = creativeContextBo.getNewVersion();
        Map<Integer, CreativeBo> curUpdateCreativeBosMap = curCreativeBos.stream().filter(t -> NumberUtils.isPositive(t.getLauUnitCreativeBo().getCreativeId())).collect(Collectors.toMap(t -> t.getLauUnitCreativeBo().getCreativeId(), Function.identity(), (k1, k2) -> k1));
        for (CreativeBo creativeBo : newUnitCreativeBo.getCreatives()) {
            // update 时, details 的 bizStatus, rejectedReason 需要回填；因为需要保存到影子里
            updateDetailsBizStatusAndReason(creativeBo, curUpdateCreativeBosMap);
        }
        Map<Integer, CreativeBo> preCreativeBos = curCreativeBos.stream().collect(Collectors.toMap(x -> x.getLauUnitCreativeBo().getCreativeId(), Function.identity()));

        // 要求同时有程序化v2和影子创意标签，才有影子功能
        if (!hasV2ProgAuth) {
            processCreativeInfoChangeForWithoutShadow(creativeContextBo, newUnitCreativeBo, preCreativeBos);
            return;
        }

        // 单元下各个创意的影子创意
        final Map<Integer, ShadowCreativeBo> shadowCreativeMap = shadowCreativeService.shadowCreatives(creativeContextBo.getUnitId());

        for (CreativeBo creativeBo : newUnitCreativeBo.getCreatives()) {
            try {
                Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
                LauUnitCreativeBo lauUnitCreativeBo = creativeBo.getLauUnitCreativeBo();
                CreativeBo preCreativeBo = preCreativeBos.get(lauUnitCreativeBo.getCreativeId());

                // 新建不会有影子
                final var isCreation = !NumberUtils.isPositive(creativeBo.getLauUnitCreativeBo().getCreativeId());
                if (isCreation) {
                    continue;
                }
//                // 自动投放父子创意，不需要影子创意
//                if (NumberUtils.isPositive(creativeBo.getLauCreativeExtraBo().getParentCreativeId())) {
//                    continue;
//                }

                ShadowCreativeBo prevShadowCreative = shadowCreativeMap.get(creativeId);
                final boolean existedShadowCreative = Objects.nonNull(prevShadowCreative);

                //是否需要推审
                ProgCreativeNeedAuditResultBo needAuditResultBo = null;
                if (!existedShadowCreative) {
                    needAuditResultBo = auditService.programCreativeNeedAudit(creativeBo, preCreativeBo);
                } else {
                    CreativeBo prevShadowCreativeBo = shadowCreativeService.shadowCreativeBoToCreativeBo(prevShadowCreative);
                    // 对不起 中秋后影子创意需要整体梳理
                    prevShadowCreativeBo.getLauUnitCreativeBo().setImageUrl(
                            preCreativeBo.getLauUnitCreativeBo().getImageUrl());
                    prevShadowCreativeBo.getLauUnitCreativeBo().setImageMd5(
                            preCreativeBo.getLauUnitCreativeBo().getImageMd5());
                    needAuditResultBo = auditService.programCreativeNeedAudit(creativeBo, prevShadowCreativeBo);
                    //last影子创意merge 到currentVersion
                    mergeCreativeBo(prevShadowCreativeBo, preCreativeBo);
                }
                Integer originalAuditStatus = Optional.ofNullable(preCreativeBo)
                        .map(r -> r.getLauUnitCreativeBo().getAuditStatus())
                        .orElse(AuditStatus.UNKNOWN);
                // 影子创意是否需要保存: 1.审核通过且需要重新审核 2.存在影子创意且影子创意处于审核中
                final boolean needSaveShadowCreative = (originalAuditStatus == AuditStatus.PASSED && needAuditResultBo.needReAudit())
                        || (Objects.nonNull(prevShadowCreative) && Objects.equals(prevShadowCreative.getAuditStatus(), AuditStatus.AUDITING));

                // 此处仅仅设置了是否触审
                creativeBo.setMiscElemChanged(needAuditResultBo.isMiscElemChanged());
                creativeBo.setProgMaterialChanged(needAuditResultBo.isMaterialChanged());
                creativeBo.setTriggerAudit(needAuditResultBo.needReAudit());
                // 是否仅仅替链发生变更
                creativeContextBo.getOnlyCpsLinkReplaceUpdateMap().put(creativeId, needAuditResultBo.getOnlyCpsLinkReplaceUpdate());

                // update 时, details 的 bizStatus, rejectedReason 需要回填；因为需要保存到影子里
                updateDetailsBizStatusAndReason(creativeBo, curUpdateCreativeBosMap);
                if (!needSaveShadowCreative) {
                    continue;
                }

                creativeContextBo.getNeedShadowAuditMap().put(creativeId, true);

                // 有程序化影子权限 && 需要影子创意，影子创意快照构建(auditStatus=待审)
                ShadowCreativeBo shadowCreativeBo = buildShadowCreativeBo(creativeContextBo, creativeBo, preCreativeBo);
                String shadowCreative = "";
                try {
                    shadowCreative = objectMapper.writeValueAsString(shadowCreativeBo);
                } catch (Exception e) {
                    log.error("program shadow shadowCreativeBo parse error ", e);
                }
                //保存影子创意
                LauShadowCreativePo lauShadowCreativePo = ShadowConverter.MAPPER.bo2Po(shadowCreativeBo, shadowCreative);
                if (Objects.nonNull(prevShadowCreative)) {
                    lauShadowCreativePo.setId(prevShadowCreative.getId());
                    lauShadowCreativeDao.update(lauShadowCreativePo);
                    log.info("update program shadow creative, creativeId:{}", creativeId);
                } else {
                    lauShadowCreativeDao.insert(lauShadowCreativePo);
                    log.info("insert program shadow creative, creativeId:{}", creativeId);
                }
            } catch (Exception e) {
                log.error("creative program shadow process fail,creativeBo={}", creativeBo, e);
                throw e;
            }
        }

        log.info("process program shadow creatives end, unitId:{},hasV2ProgAuth={},accountId={},needShadowAuditMap={},newCreativeIds={}",
                creativeContextBo.getUnitId(), hasV2ProgAuth, creativeContextBo.getAccountId(), creativeContextBo.getNeedShadowAuditMap(), newCreativeIds);
    }

    /**
     * 对于影子的创意处理创意信息变更
     *
     * @param creativeContextBo
     * @param newUnitCreativeBo
     * @param preCreativeBos
     */
    public void processCreativeInfoChangeForWithoutShadow(SaveUnitCreativeContextBo creativeContextBo, UnitCreativeBo newUnitCreativeBo, Map<Integer, CreativeBo> preCreativeBos) {
        for (CreativeBo creativeBo : newUnitCreativeBo.getCreatives()) {
            Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
            LauUnitCreativeBo lauUnitCreativeBo = creativeBo.getLauUnitCreativeBo();
            CreativeBo preCreativeBo = preCreativeBos.get(lauUnitCreativeBo.getCreativeId());

            // 新建不会有影子
            final var isCreation = !NumberUtils.isPositive(creativeBo.getLauUnitCreativeBo().getCreativeId());
            if (isCreation) {
                continue;
            }
            // 自动投放父子创意，不需要影子创意
//            if (NumberUtils.isPositive(creativeBo.getLauCreativeExtraBo().getParentCreativeId())) {
//                continue;
//            }

            //是否需要推审
            ProgCreativeNeedAuditResultBo needAuditResultBo = auditService.programCreativeNeedAudit(creativeBo, preCreativeBo);
            // 是否仅仅替链发生变更
            creativeContextBo.getOnlyCpsLinkReplaceUpdateMap().put(creativeId, needAuditResultBo.getOnlyCpsLinkReplaceUpdate());
        }
    }

    public void updateDetailsBizStatusAndReason(CreativeBo creativeBo, Map<Integer, CreativeBo> curUpdateCreativeBosMap) {
        if (CollectionUtils.isEmpty(creativeBo.getLauProgrammaticCreativeDetailBos()) || curUpdateCreativeBosMap == null) {
            return;
        }

        for (LauProgrammaticCreativeDetailBo newCreativeDetailBo : creativeBo.getLauProgrammaticCreativeDetailBos()) {
            CreativeBo curCreativeBo = curUpdateCreativeBosMap.get(creativeBo.getLauUnitCreativeBo().getCreativeId());
            if (Objects.isNull(curCreativeBo)) {
                continue;
            }
            List<LauProgrammaticCreativeDetailBo> curCreativeDetailBos = curCreativeBo.getLauProgrammaticCreativeDetailBos();
            Map<Long, LauProgrammaticCreativeDetailBo> creativeDetailBoMapOfCurCreative = curCreativeDetailBos.stream().collect(Collectors.toMap(t -> t.getMaterialId(), t -> t, (k1, k2) -> k1));
            LauProgrammaticCreativeDetailBo curCreativeDetailBo = creativeDetailBoMapOfCurCreative.get(newCreativeDetailBo.getMaterialId());
            if (Objects.isNull(curCreativeDetailBo)) {
                continue;
            }
            newCreativeDetailBo.setBizStatus(curCreativeDetailBo.getBizStatus());
            newCreativeDetailBo.setRejectedReason(curCreativeDetailBo.getRejectedReason());
        }
    }

    public ShadowCreativeBo buildShadowCreativeBo(SaveUnitCreativeContextBo creativeContextBo, CreativeBo creativeBo, CreativeBo preCreativeBo) {
        Integer unitId = creativeContextBo.getUnitId();
        Integer campaignId = creativeContextBo.getCampaignId();
        Integer accountId = creativeContextBo.getAccountId();
        Integer creativeId = creativeBo.getLauUnitCreativeBo().getCreativeId();
        Long pageGroupId = Objects.isNull(creativeBo.getLauCreativeLandingPageGroupBo()) ? null : creativeBo.getLauCreativeLandingPageGroupBo().getGroupId();

        LauUnitCreativeBo lauUnitCreativeBo = creativeBo.getLauUnitCreativeBo();

        ShadowCreativeBo.ShadowCreativeBoBuilder shadowCreativeBuilder = ShadowCreativeBo.builder();
        shadowCreativeBuilder.images(creativeBo.getLauCreativeImageBos().stream()
                .map(r -> ShadowConverter.MAPPER.lauImgBoToShadowImgBo(unitId, creativeId, r))
                .collect(Collectors.toList()));
        shadowCreativeBuilder.button(ShadowConverter.MAPPER.lauButtonBoToShadow(creativeBo.getLauCreativeButtonCopyBo()));
        List<CreativeComponentBo> components = ShadowConverter.MAPPER.lauComponentBoToShadows(creativeBo.getLauCreativeComponentBos());
        components.forEach(r -> r.setCreativeId(creativeId));
        shadowCreativeBuilder.components(components);
//        shadowCreativeBuilder.flyExtInfo(ShadowConverter.MAPPER.lauFlyExtToShadow(creativeBo.getLauCreativeFlyExtInfoBo()));
        shadowCreativeBuilder.tab(ShadowConverter.MAPPER.lauTabBoToShadow(creativeBo.getLauCreativeTabBo()));
        shadowCreativeBuilder.accountId(accountId);
        shadowCreativeBuilder.campaignId(campaignId);
        shadowCreativeBuilder.unitId(unitId);
        shadowCreativeBuilder.creativeId(creativeId);
        shadowCreativeBuilder.promotionPurposeType(creativeContextBo.getLauUnitBo().getPromotionPurposeType());
        shadowCreativeBuilder.busMark(lauUnitCreativeBo.getBusMarkId());
        shadowCreativeBuilder.cmMark(lauUnitCreativeBo.getCmMark());
        shadowCreativeBuilder.description(lauUnitCreativeBo.getDescription());
        shadowCreativeBuilder.jumpType(lauUnitCreativeBo.getJumpType());
        shadowCreativeBuilder.jumpUrl(lauUnitCreativeBo.getPromotionPurposeContent());
        shadowCreativeBuilder.jumpUrlSecondary(creativeBo.getLauUnitCreativeBo().getPromotionPurposeContentSecondary());
        shadowCreativeBuilder.ppcType(creativeBo.getLauCreativeExtraBo().getPpcType());
        shadowCreativeBuilder.title(Optional.ofNullable(lauUnitCreativeBo.getTitle()).orElse(StringUtils.EMPTY));
        shadowCreativeBuilder.videoId(creativeBo.getLauUnitCreativeBo().getVideoId());
        shadowCreativeBuilder.canJointLaunchPageGroup(0);
        shadowCreativeBuilder.advertisingMode(creativeBo.getLauUnitCreativeBo().getAdvertisingMode());
        shadowCreativeBuilder.pageGroupId(pageGroupId);
        shadowCreativeBuilder.isPageGroup(NumberUtils.isPositive(pageGroupId) ? 1 : 0);
        shadowCreativeBuilder.extDescription("");

        creativeBo.getLauCreativeTitleBos().forEach(r -> {
            r.setCreativeId(creativeId);
            if (!CollectionUtils.isEmpty(r.getLauCreativeSmartTitleBos())) {
                r.getLauCreativeSmartTitleBos().forEach(smart -> smart.setCreativeId(creativeId));
            }
        });
        shadowCreativeBuilder.lauCreativeTitleBos(creativeBo.getLauCreativeTitleBos());
        shadowCreativeBuilder.mgkPageId(lauUnitCreativeBo.getMgkPageId());
        // 替链
        shadowCreativeBuilder.cpsReplaceLink(creativeBo.getCpsReplaceLinkInfo());

        // 程序化元素
        shadowCreativeBuilder.creativeDetailBos(creativeBo.getLauProgrammaticCreativeDetailBos());

        // 待审
        shadowCreativeBuilder.auditStatus(AuditStatus.AUDITING);
        shadowCreativeBuilder.progAuditStatus(1); // 待审是1，非待审0
        // 创意状态，默认审核中；如果有联投落地页，创意状态为联投落地页审核中
        int creativeStatus = CreativeStatus.AUDITING;
        if (NumberUtils.isPositive(lauUnitCreativeBo.getMgkPageId())) {
            LandingPageBo landingPageBo = creativeContextBo.getLandingPageContext().getLandingPageBoMap().get(lauUnitCreativeBo.getMgkPageId());
            if (Objects.nonNull(landingPageBo)
                    && Objects.nonNull(landingPageBo.getPageStatus())
                    && landingPageBo.getPageStatus().equals(MgkPageStatus.WAIT_AUDIT_VALUE)) {
                creativeStatus = CreativeStatus.LANDING_PAGE_AUDITING;
            }
        }
        shadowCreativeBuilder.creativeStatus(creativeStatus);

        // 额外元素的状态
        if (creativeBo.isMiscElemChanged()) {
            shadowCreativeBuilder.progMiscElemAuditStatus(ProgrammaticElementAuditStatus.AUDITING);
        } else {
            shadowCreativeBuilder.progMiscElemAuditStatus(preCreativeBo.getLauUnitCreativeBo().getProgMiscElemAuditStatus());
        }
        return shadowCreativeBuilder.build();
    }

    public void mergeCreativeBo(CreativeBo src, CreativeBo target) {
        target.setLauCreativeImageBos(src.getLauCreativeImageBos());
        target.setLauCreativeButtonCopyBo(src.getLauCreativeButtonCopyBo());
        target.setLauCreativeComponentBos(src.getLauCreativeComponentBos());
        LauCreativeTabBo tabBo = src.getLauCreativeTabBo();
        if (Objects.nonNull(tabBo)) {
            tabBo.setId(target.getLauCreativeTabBo().getId());
        }
        target.setLauCreativeTabBo(tabBo);
//        target.setNativeLandingPage();
        target.getLauUnitCreativeBo().setTitle(src.getLauUnitCreativeBo().getTitle());
        target.getLauUnitCreativeBo().setDescription(src.getLauUnitCreativeBo().getDescription());
        target.getLauUnitCreativeBo().setPromotionPurposeContent(src.getLauUnitCreativeBo().getPromotionPurposeContent());
        target.getLauUnitCreativeBo().setBusMarkId(src.getLauUnitCreativeBo().getBusMarkId());
//        target.setLauCreativeFlyExtInfoBo(src.getLauCreativeFlyExtInfoBo());
        target.setLauCreativeTitleBos(src.getLauCreativeTitleBos());

        // 直接用影子的程序化元素替换创意的
        if (!CollectionUtils.isEmpty(src.getLauProgrammaticCreativeDetailBos())) {
            target.setLauProgrammaticCreativeDetailBos(src.getLauProgrammaticCreativeDetailBos());
        }
    }
}
