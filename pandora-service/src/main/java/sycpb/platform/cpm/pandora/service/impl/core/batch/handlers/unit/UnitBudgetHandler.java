package sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.unit;

import com.bapis.ad.pandora.resource.BudgetType;
import org.jooq.DSLContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.compare.bos.ChangeLogBo;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.BudgetOperationBo;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.IOperationLogService;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperationLogContextBo;
import sycpb.platform.cpm.pandora.service.api.resource.realtime.INearRealTimeDataService;
import sycpb.platform.cpm.pandora.service.api.resource.realtime.IRealTimeDataService;
import sycpb.platform.cpm.pandora.service.api.resource.realtime.bo.QueryNearRealTimeDataBo;
import sycpb.platform.cpm.pandora.service.api.resource.realtime.bo.QueryRealTimeDataBo;
import sycpb.platform.cpm.pandora.service.api.resource.realtime.bo.RealTimeQueryMode;
import sycpb.platform.cpm.pandora.service.common.InfoReportService;
import sycpb.platform.cpm.pandora.service.common.bo.InfoReportCompensationLogBo;
import sycpb.platform.cpm.pandora.service.impl.core.batch.BatchOperationExtService;
import sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.misc.CompareFunctions;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnit.LAU_UNIT;

@Service
public class UnitBudgetHandler extends UnitBudgetTemplateHandler {
    @Resource
    private INearRealTimeDataService nearRealTimeDataService;
    @Resource
    private IRealTimeDataService realTimeDataService;

    @Resource
    private InfoReportService infoReportService;

    public UnitBudgetHandler(BatchOperationExtService batchOperationExtService,
                             IOperationLogService operationLogService,
                             @Qualifier(MySqlConfig.AD_CORE_DSL_CONTEXT) DSLContext adCore) {
        super(operationLogService, batchOperationExtService, adCore);
    }

    ;

    @Override
    public void handleRequest(UnitContext ctx) {
        super.handleRequest(ctx);
    }

    @Override
    public void handleOperation(UnitContext ctx) {
        super.handleOperation(ctx);
        final Integer queryMode;
        if (Objects.equals(ctx.getOperationBo().getBudget().getBudgetLimitType(), BudgetType.BUDGET_DAILY_VALUE)) {
            queryMode = RealTimeQueryMode.DAILY;
        } else if (Objects.equals(ctx.getOperationBo().getBudget().getBudgetLimitType(), BudgetType.BUDGET_TOTAL_VALUE)) {
            queryMode = RealTimeQueryMode.TOTAL;
        } else {
            queryMode = null;
        }
        ctx.setRealTimeDataQueryMode(queryMode);
    }

    @Override
    public List<Function<UnitContext, String>> fetchValidators() {
        final var list = new ArrayList<>(super.fetchValidators());
        list.add(c -> {
            final var lauCampaignPo = c.getCampaignMap().get(c.getLauUnitPo().getCampaignId());
            if (Objects.isNull(lauCampaignPo)) return "单元对应的计划不存在";

            return lauCampaignPo.getBudget() >= c.getOperationBo().getBudget().getBudgetValue() ? null : "单元预算不能超过计划预算";
        });
        list.add(c -> {
            // 不限预算无需校验
            if (Objects.isNull(c.getRealTimeDataQueryMode())) return null;

            final var lauCampaignPo = c.getCampaignMap().get(c.getLauUnitPo().getCampaignId());
            if (Objects.isNull(lauCampaignPo)) return "单元对应的计划不存在";

            // 预算没有调小 不需要看消耗
            if (c.getOperationBo().getBudget().getBudgetValue() >= c.getLauUnitPo().getBudget()) {
                return null;
            }

            if (Objects.equals(lauCampaignPo.getAdType(), 2)) {
                // 闪屏计划无法从实时数据中获取
                final var queryBo = new QueryNearRealTimeDataBo();
                queryBo.setAccountId(c.getLauUnitPo().getAccountId());
                queryBo.setUnitId(c.getTargetId());
                queryBo.setQueryMode(c.getRealTimeDataQueryMode());
                final var realTimeDataBo = nearRealTimeDataService.get(queryBo);
                return realTimeDataBo.getCost() * 1.2 <= c.getOperationBo().getBudget().getBudgetValue() * 1.0 ? null : "预算不能低于当前消耗的120%";
            } else {
                final var queryBo = new QueryRealTimeDataBo();
                queryBo.setCampaignId(c.getLauUnitPo().getCampaignId());
                queryBo.setUnitId(c.getTargetId());
                queryBo.setSalesType(c.getLauUnitPo().getSalesType());
                queryBo.setQueryMode(c.getRealTimeDataQueryMode());
                final var realTimeDataBo = realTimeDataService.get(queryBo);
                return realTimeDataBo.getCost() * 1.2 <= c.getOperationBo().getBudget().getBudgetValue() * 1.0 ? null : "预算不能低于当前消耗的120%";
            }
        });
        return list;
    }

    @Override
    public CompareBo compare(UnitContext ctx) {
        final var curBudgetBo = BudgetOperationBo.newInstance(ctx.getLauUnitPo().getBudget(), BudgetType.BUDGET_DAILY_VALUE, null);
        return CompareFunctions.compareBudget(curBudgetBo, ctx.getOperationBo().getBudget(), ctx.getOperatorBo(), ctx.getTargetId(), "lau_unit");
    }

    @Override
    public void batchUpdateTarget(UnitContext ctx) {
        if (CollectionUtils.isEmpty(ctx.getUpdatingTargetIds())) return;

        adCore.update(LAU_UNIT)
                .set(LAU_UNIT.BUDGET, ctx.getOperationBo().getBudget().getBudgetValue())
                .where(LAU_UNIT.UNIT_ID.in(ctx.getUpdatingTargetIds()))
                .execute();
    }

    @Override
    public void batchUpdate(UnitContext ctx) {
        ((UnitBudgetHandler) AopContext.currentProxy()).batchUpdateWithTx(ctx);
    }

    @Transactional(MySqlConfig.AD_CORE_TX_MGR)
    public void batchUpdateWithTx(UnitContext ctx) {
        super.batchUpdate(ctx);
        try {
            infoReportCompensationLog(ctx);
        } catch (Exception ignored) {
        }

    }


    private void infoReportCompensationLog(UnitContext ctx) {
        if (CollectionUtils.isEmpty(ctx.getOperationLogContextBos())) {
            return;
        }

        InfoReportCompensationLogBo infoReportCompensationLogBo = new InfoReportCompensationLogBo();
        infoReportCompensationLogBo.setNeedReport(true);
        infoReportCompensationLogBo.setNeedTyp5Report(true);
        infoReportCompensationLogBo.setOperator_id(String.valueOf(ctx.getOperatorBo().getOperatorId()));

        for (OperationLogContextBo operationLogContextBo : ctx.getOperationLogContextBos()) {
            infoReportCompensationLogBo.setUnitId(String.valueOf(operationLogContextBo.getObjId()));
            InfoReportCompensationLogBo.Type5Data type5Data = new InfoReportCompensationLogBo.Type5Data();

            if (CollectionUtils.isEmpty(operationLogContextBo.getChanges())) {
                continue;
            }


            InfoReportCompensationLogBo.Type5DataValue oldValue = new InfoReportCompensationLogBo.Type5DataValue();
            InfoReportCompensationLogBo.Type5DataValue newValue = new InfoReportCompensationLogBo.Type5DataValue();


            List<ChangeLogBo> curBudgets =
                    operationLogContextBo.getChanges().stream().filter(t -> t.getKey().equals("curBudget")).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(curBudgets)) {
                continue;
            }

            Long oldValueStr = StringUtils.isEmpty(curBudgets.get(0).getOldValue()) ? 0 :
                    Long.parseLong(curBudgets.get(0).getOldValue());

            Long newValueStr = StringUtils.isEmpty(curBudgets.get(0).getNewValue()) ? 0 :
                    Long.parseLong(curBudgets.get(0).getNewValue());


            newValue.setBudget(newValueStr);
            oldValue.setBudget(oldValueStr);

            type5Data.setOld_value(oldValue);
            type5Data.setNew_value(newValue);
            infoReportCompensationLogBo.setType5Data(type5Data);
            infoReportService.reportCompensationLog(infoReportCompensationLogBo);
        }

    }
}
