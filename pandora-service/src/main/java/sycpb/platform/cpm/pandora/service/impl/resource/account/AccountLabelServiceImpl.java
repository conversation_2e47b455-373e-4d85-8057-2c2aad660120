package sycpb.platform.cpm.pandora.service.impl.resource.account;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.resource.account.IAccountLabelService;
import sycpb.platform.cpm.pandora.service.proxy.CpmCrmPortalProxy;

import javax.annotation.Resource;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class AccountLabelServiceImpl implements IAccountLabelService {
    @Resource
    private CpmCrmPortalProxy cpmCrmPortalProxy;

    @Override
    public List<Integer> list(Integer accountId) {
        if (!NumberUtils.isPositive(accountId)) return List.of();
        List<Integer> labelIdList = cpmCrmPortalProxy.accountLabelList(accountId);
        log.info("AccountLabelServiceImpl list accountId:{}, labelIdList:{}", accountId, labelIdList);
        return labelIdList;
    }

    @Override
    public boolean checkLabel(Integer accountId, Integer labelId) {
        if (!NumberUtils.isPositive(accountId)
                || !NumberUtils.isPositive(labelId)) {
            return false;
        }

        return cpmCrmPortalProxy.checkLabel(accountId, labelId);
    }
}
