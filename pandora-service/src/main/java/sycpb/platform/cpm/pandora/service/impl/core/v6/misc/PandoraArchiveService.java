package sycpb.platform.cpm.pandora.service.impl.core.v6.misc;

import com.bapis.ad.pandora.resource.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.api.archive.BiliArchiveBo;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.CreativeBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauCreativeArchiveBo;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.CreativeContentGoodsBo;
import sycpb.platform.cpm.pandora.service.api.resource.goods.IResCreativeGoodsContentService;
import sycpb.platform.cpm.pandora.service.api.resource.goods.bos.QueryCreativeGoodsContentBo;
import sycpb.platform.cpm.pandora.service.constants.TemplateGroup;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.bos.PugvArchiveBo;
import sycpb.platform.cpm.pandora.service.proxy.CheeseSeasonServiceProxy;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName AdOttService
 * <AUTHOR>
 * @Date 2024/10/29 8:59 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class PandoraArchiveService {

    @Resource
    private IResCreativeGoodsContentService resCreativeGoodsContentService;
    @Resource
    private CheeseSeasonServiceProxy cheeseSeasonServiceProxy;


    // 起飞统一装填是否带货稿件信息
    public void prepareArchiveGoodsContext(SaveUnitCreativeContextBo ctx) {
        if (Objects.equals(ctx.getNewVersion().getUnit().getLauUnitBo().getBusinessDomain(), BusinessDomainType.BD_CPC_VALUE)) {
            ctx.setCreativeContentGoodsBoMap(new HashMap<>());
            return;
        }

        List<CreativeBo> creativeBos = ctx.getNewVersion().getCreatives();
        List<Long> totalBiliArcAvids = creativeBos.stream()
                .filter(creativeBo -> Objects.equals(TemplateGroup.BILI_ARCHIVE, creativeBo.getLauUnitCreativeBo().getTemplateGroupId()))
                .map(CreativeBo::getLauCreativeArchiveBos)
                .filter(archiveBo -> !CollectionUtils.isEmpty(archiveBo))
                .flatMap(Collection::stream)
                .map(LauCreativeArchiveBo::getAvid)
                .filter(NumberUtils::isPositive)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(totalBiliArcAvids)) {
            ctx.setCreativeContentGoodsBoMap(new HashMap<>());
            return;
        }

        QueryCreativeGoodsContentBo queryBo = new QueryCreativeGoodsContentBo();
        queryBo.setAvidList(totalBiliArcAvids);
        List<CreativeContentGoodsBo> goodsContentBos = resCreativeGoodsContentService.getGoodsContent(queryBo);
        Map<Long, CreativeContentGoodsBo> goodsContentMap = goodsContentBos
                .stream()
                .collect(Collectors.toMap(CreativeContentGoodsBo::getContentId, Function.identity()));

        // 需要全量带货稿件信息 起飞非带货稿件要ott入库
        ctx.setCreativeContentGoodsBoMap(goodsContentMap);
    }

    public Map<Long, PugvArchiveBo> handlePugvArchive(Map<Long, BiliArchiveBo> biliArchiveMap) {
        if (CollectionUtils.isEmpty(biliArchiveMap)) {
            return Map.of();
        }
        List<Long> pugvAvids = biliArchiveMap.values().stream()
                .filter(BiliArchiveBo::getIsPugv)
                .map(BiliArchiveBo::getAvid)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pugvAvids)) {
            return Map.of();
        }
        log.info("课堂稿件 PandoraArchiveService handlePugvArchive success pugvAvids={}", pugvAvids);
        try {
            List<PugvArchiveBo> seasonArcs = cheeseSeasonServiceProxy.getSeasonListByAvids(pugvAvids);
            //转成map，存放在ctx中
            return seasonArcs.stream().collect(Collectors.toMap(PugvArchiveBo::getAvid, Function.identity()));
        } catch (Exception e) {
            log.error("课堂稿件 PandoraArchiveService handlePugvArchive error", e);
        }
        return Map.of();
    }

}
