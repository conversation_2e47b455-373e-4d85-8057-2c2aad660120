/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitLiveReserve;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitLiveReservePo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitLiveReserveRecord;


/**
 * 单元直播预约表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauUnitLiveReserveDao extends DAOImpl<LauUnitLiveReserveRecord, LauUnitLiveReservePo, Integer> {

    /**
     * Create a new LauUnitLiveReserveDao without any configuration
     */
    public LauUnitLiveReserveDao() {
        super(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE, LauUnitLiveReservePo.class);
    }

    /**
     * Create a new LauUnitLiveReserveDao with an attached configuration
     */
    @Autowired
    public LauUnitLiveReserveDao(Configuration configuration) {
        super(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE, LauUnitLiveReservePo.class, configuration);
    }

    @Override
    public Integer getId(LauUnitLiveReservePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitLiveReservePo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauUnitLiveReservePo> fetchById(Integer... values) {
        return fetch(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauUnitLiveReservePo fetchOneById(Integer value) {
        return fetchOne(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.ID, value);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitLiveReservePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauUnitLiveReservePo> fetchByCtime(Timestamp... values) {
        return fetch(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitLiveReservePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauUnitLiveReservePo> fetchByMtime(Timestamp... values) {
        return fetch(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.MTIME, values);
    }

    /**
     * Fetch records that have <code>sid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitLiveReservePo> fetchRangeOfSid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.SID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sid IN (values)</code>
     */
    public List<LauUnitLiveReservePo> fetchBySid(Long... values) {
        return fetch(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.SID, values);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitLiveReservePo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauUnitLiveReservePo> fetchByUnitId(Integer... values) {
        return fetch(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.UNIT_ID, values);
    }

    /**
     * Fetch a unique record that has <code>unit_id = value</code>
     */
    public LauUnitLiveReservePo fetchOneByUnitId(Integer value) {
        return fetchOne(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.UNIT_ID, value);
    }
}
