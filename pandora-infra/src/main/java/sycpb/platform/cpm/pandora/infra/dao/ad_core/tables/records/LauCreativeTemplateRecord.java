/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeTemplate;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeTemplatePo;


/**
 * 创意与广告位组及模板关联表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeTemplateRecord extends UpdatableRecordImpl<LauCreativeTemplateRecord> implements Record14<Long, Inte<PERSON>, Inte<PERSON>, <PERSON>, Integer, Integer, Timestamp, Timestamp, Integer, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>te<PERSON>, Integer, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_template.id</code>. 自增ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_template.id</code>. 自增ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_creative_template.account_id</code>. 账号ID
     */
    public void setAccountId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_creative_template.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_creative_template.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_creative_template.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_creative_template.creative_id</code>. 创意ID
     */
    public void setCreativeId(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_creative_template.creative_id</code>. 创意ID
     */
    public Long getCreativeId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>lau_creative_template.slot_group_id</code>. 广告位组ID
     */
    public void setSlotGroupId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_creative_template.slot_group_id</code>. 广告位组ID
     */
    public Integer getSlotGroupId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_creative_template.template_id</code>. 模板ID
     */
    public void setTemplateId(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_creative_template.template_id</code>. 模板ID
     */
    public Integer getTemplateId() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_creative_template.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_creative_template.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(6);
    }

    /**
     * Setter for <code>lau_creative_template.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_creative_template.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(7);
    }

    /**
     * Setter for <code>lau_creative_template.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_creative_template.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>lau_creative_template.biz_status</code>. 0-正常, 1-低于保留价
     */
    public void setBizStatus(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_creative_template.biz_status</code>. 0-正常, 1-低于保留价
     */
    public Integer getBizStatus() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>lau_creative_template.reserved_price</code>. 保留价
     */
    public void setReservedPrice(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_creative_template.reserved_price</code>. 保留价
     */
    public Integer getReservedPrice() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>lau_creative_template.reserve_rule_id</code>. 决定保留价的规则id
     */
    public void setReserveRuleId(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_creative_template.reserve_rule_id</code>. 决定保留价的规则id
     */
    public Integer getReserveRuleId() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>lau_creative_template.creative_style</code>.
     * 创意形态(视频版位合并下移至模板维度)
     */
    public void setCreativeStyle(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_creative_template.creative_style</code>.
     * 创意形态(视频版位合并下移至模板维度)
     */
    public Integer getCreativeStyle() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>lau_creative_template.bus_mark_id</code>.
     * 商业标ID(视频版位合并下移至模板维度)
     */
    public void setBusMarkId(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_creative_template.bus_mark_id</code>.
     * 商业标ID(视频版位合并下移至模板维度)
     */
    public Integer getBusMarkId() {
        return (Integer) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<Long, Integer, Integer, Long, Integer, Integer, Timestamp, Timestamp, Integer, Integer, Integer, Integer, Integer, Integer> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<Long, Integer, Integer, Long, Integer, Integer, Timestamp, Timestamp, Integer, Integer, Integer, Integer, Integer, Integer> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.ACCOUNT_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.UNIT_ID;
    }

    @Override
    public Field<Long> field4() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.CREATIVE_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.SLOT_GROUP_ID;
    }

    @Override
    public Field<Integer> field6() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.TEMPLATE_ID;
    }

    @Override
    public Field<Timestamp> field7() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.CTIME;
    }

    @Override
    public Field<Timestamp> field8() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.MTIME;
    }

    @Override
    public Field<Integer> field9() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.IS_DELETED;
    }

    @Override
    public Field<Integer> field10() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.BIZ_STATUS;
    }

    @Override
    public Field<Integer> field11() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.RESERVED_PRICE;
    }

    @Override
    public Field<Integer> field12() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.RESERVE_RULE_ID;
    }

    @Override
    public Field<Integer> field13() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.CREATIVE_STYLE;
    }

    @Override
    public Field<Integer> field14() {
        return TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.BUS_MARK_ID;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getAccountId();
    }

    @Override
    public Integer component3() {
        return getUnitId();
    }

    @Override
    public Long component4() {
        return getCreativeId();
    }

    @Override
    public Integer component5() {
        return getSlotGroupId();
    }

    @Override
    public Integer component6() {
        return getTemplateId();
    }

    @Override
    public Timestamp component7() {
        return getCtime();
    }

    @Override
    public Timestamp component8() {
        return getMtime();
    }

    @Override
    public Integer component9() {
        return getIsDeleted();
    }

    @Override
    public Integer component10() {
        return getBizStatus();
    }

    @Override
    public Integer component11() {
        return getReservedPrice();
    }

    @Override
    public Integer component12() {
        return getReserveRuleId();
    }

    @Override
    public Integer component13() {
        return getCreativeStyle();
    }

    @Override
    public Integer component14() {
        return getBusMarkId();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getAccountId();
    }

    @Override
    public Integer value3() {
        return getUnitId();
    }

    @Override
    public Long value4() {
        return getCreativeId();
    }

    @Override
    public Integer value5() {
        return getSlotGroupId();
    }

    @Override
    public Integer value6() {
        return getTemplateId();
    }

    @Override
    public Timestamp value7() {
        return getCtime();
    }

    @Override
    public Timestamp value8() {
        return getMtime();
    }

    @Override
    public Integer value9() {
        return getIsDeleted();
    }

    @Override
    public Integer value10() {
        return getBizStatus();
    }

    @Override
    public Integer value11() {
        return getReservedPrice();
    }

    @Override
    public Integer value12() {
        return getReserveRuleId();
    }

    @Override
    public Integer value13() {
        return getCreativeStyle();
    }

    @Override
    public Integer value14() {
        return getBusMarkId();
    }

    @Override
    public LauCreativeTemplateRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value2(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value3(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value4(Long value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value5(Integer value) {
        setSlotGroupId(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value6(Integer value) {
        setTemplateId(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value7(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value8(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value9(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value10(Integer value) {
        setBizStatus(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value11(Integer value) {
        setReservedPrice(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value12(Integer value) {
        setReserveRuleId(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value13(Integer value) {
        setCreativeStyle(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord value14(Integer value) {
        setBusMarkId(value);
        return this;
    }

    @Override
    public LauCreativeTemplateRecord values(Long value1, Integer value2, Integer value3, Long value4, Integer value5, Integer value6, Timestamp value7, Timestamp value8, Integer value9, Integer value10, Integer value11, Integer value12, Integer value13, Integer value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativeTemplateRecord
     */
    public LauCreativeTemplateRecord() {
        super(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE);
    }

    /**
     * Create a detached, initialised LauCreativeTemplateRecord
     */
    public LauCreativeTemplateRecord(Long id, Integer accountId, Integer unitId, Long creativeId, Integer slotGroupId, Integer templateId, Timestamp ctime, Timestamp mtime, Integer isDeleted, Integer bizStatus, Integer reservedPrice, Integer reserveRuleId, Integer creativeStyle, Integer busMarkId) {
        super(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE);

        setId(id);
        setAccountId(accountId);
        setUnitId(unitId);
        setCreativeId(creativeId);
        setSlotGroupId(slotGroupId);
        setTemplateId(templateId);
        setCtime(ctime);
        setMtime(mtime);
        setIsDeleted(isDeleted);
        setBizStatus(bizStatus);
        setReservedPrice(reservedPrice);
        setReserveRuleId(reserveRuleId);
        setCreativeStyle(creativeStyle);
        setBusMarkId(busMarkId);
    }

    /**
     * Create a detached, initialised LauCreativeTemplateRecord
     */
    public LauCreativeTemplateRecord(LauCreativeTemplatePo value) {
        super(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE);

        if (value != null) {
            setId(value.getId());
            setAccountId(value.getAccountId());
            setUnitId(value.getUnitId());
            setCreativeId(value.getCreativeId());
            setSlotGroupId(value.getSlotGroupId());
            setTemplateId(value.getTemplateId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setIsDeleted(value.getIsDeleted());
            setBizStatus(value.getBizStatus());
            setReservedPrice(value.getReservedPrice());
            setReserveRuleId(value.getReserveRuleId());
            setCreativeStyle(value.getCreativeStyle());
            setBusMarkId(value.getBusMarkId());
        }
    }
}
