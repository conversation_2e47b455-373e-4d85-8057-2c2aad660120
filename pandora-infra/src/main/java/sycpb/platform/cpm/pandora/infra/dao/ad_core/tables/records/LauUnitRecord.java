/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnit;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitPo;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitRecord extends UpdatableRecordImpl<LauUnitRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_unit.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_unit.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_unit.account_id</code>. 账户ID
     */
    public void setAccountId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_unit.account_id</code>. 账户ID
     */
    public Integer getAccountId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_unit.campaign_id</code>. 推广计划ID
     */
    public void setCampaignId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_unit.campaign_id</code>. 推广计划ID
     */
    public Integer getCampaignId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_unit.unit_name</code>. 单元名称
     */
    public void setUnitName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_unit.unit_name</code>. 单元名称
     */
    public String getUnitName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>lau_unit.promotion_purpose_type</code>. 推广目的类型（1-APP推广
     * 2-落地页 3-视频, 4-应用下载 5-会员购 6-上架游戏 7-投稿内容 8-直播间 9-电商）
     */
    public void setPromotionPurposeType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_unit.promotion_purpose_type</code>. 推广目的类型（1-APP推广
     * 2-落地页 3-视频, 4-应用下载 5-会员购 6-上架游戏 7-投稿内容 8-直播间 9-电商）
     */
    public Integer getPromotionPurposeType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_unit.channel_id</code>. 渠道ID
     */
    public void setChannelId(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_unit.channel_id</code>. 渠道ID
     */
    public Integer getChannelId() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_unit.slot_group</code>. 广告位组
     */
    public void setSlotGroup(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_unit.slot_group</code>. 广告位组
     */
    public Integer getSlotGroup() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lau_unit.launch_begin_date</code>. 投放开始日期
     */
    public void setLaunchBeginDate(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_unit.launch_begin_date</code>. 投放开始日期
     */
    public String getLaunchBeginDate() {
        return (String) get(7);
    }

    /**
     * Setter for <code>lau_unit.launch_end_date</code>.
     * 投放结束日期（可以为空，为空时默认在开始时间上加了100年）
     */
    public void setLaunchEndDate(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_unit.launch_end_date</code>.
     * 投放结束日期（可以为空，为空时默认在开始时间上加了100年）
     */
    public String getLaunchEndDate() {
        return (String) get(8);
    }

    /**
     * Setter for <code>lau_unit.launch_time</code>.
     * 投放时间（7*24个01字符表示,从左到右每24个字符表示周一到周末的0点至23点）
     */
    public void setLaunchTime(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_unit.launch_time</code>.
     * 投放时间（7*24个01字符表示,从左到右每24个字符表示周一到周末的0点至23点）
     */
    public String getLaunchTime() {
        return (String) get(9);
    }

    /**
     * Setter for <code>lau_unit.cost_price</code>. 出价（单位分）
     */
    public void setCostPrice(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_unit.cost_price</code>. 出价（单位分）
     */
    public Integer getCostPrice() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>lau_unit.budget_type</code>. 预算类型：1-日预算、2-总预算
     */
    public void setBudgetType(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_unit.budget_type</code>. 预算类型：1-日预算、2-总预算
     */
    public Integer getBudgetType() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>lau_unit.budget</code>. 单元预算
     */
    public void setBudget(Long value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_unit.budget</code>. 单元预算
     */
    public Long getBudget() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>lau_unit.frequency_unit</code>. 频次单元（1-日 2-周 3-月）
     */
    public void setFrequencyUnit(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_unit.frequency_unit</code>. 频次单元（1-日 2-周 3-月）
     */
    public Integer getFrequencyUnit() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>lau_unit.frequency_limit</code>. 频次限制（大于1）
     */
    public void setFrequencyLimit(Integer value) {
        set(14, value);
    }

    /**
     * Getter for <code>lau_unit.frequency_limit</code>. 频次限制（大于1）
     */
    public Integer getFrequencyLimit() {
        return (Integer) get(14);
    }

    /**
     * Setter for <code>lau_unit.creative_display_mode</code>. 创意展示方式（1-优播模式
     * 2-轮播模式）
     */
    public void setCreativeDisplayMode(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>lau_unit.creative_display_mode</code>. 创意展示方式（1-优播模式
     * 2-轮播模式）
     */
    public Integer getCreativeDisplayMode() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>lau_unit.status</code>. 状态（1-有效，2-暂停,
     * 3-删除,4-已完成,5-修改待下线,6-已下线）
     */
    public void setStatus(Integer value) {
        set(16, value);
    }

    /**
     * Getter for <code>lau_unit.status</code>. 状态（1-有效，2-暂停,
     * 3-删除,4-已完成,5-修改待下线,6-已下线）
     */
    public Integer getStatus() {
        return (Integer) get(16);
    }

    /**
     * Setter for <code>lau_unit.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer value) {
        set(17, value);
    }

    /**
     * Getter for <code>lau_unit.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(17);
    }

    /**
     * Setter for <code>lau_unit.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(18, value);
    }

    /**
     * Getter for <code>lau_unit.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(18);
    }

    /**
     * Setter for <code>lau_unit.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(19, value);
    }

    /**
     * Getter for <code>lau_unit.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(19);
    }

    /**
     * Setter for <code>lau_unit.sales_type</code>. 售卖类型 11-CPM, 12-CPC, 21-GD
     */
    public void setSalesType(Integer value) {
        set(20, value);
    }

    /**
     * Getter for <code>lau_unit.sales_type</code>. 售卖类型 11-CPM, 12-CPC, 21-GD
     */
    public Integer getSalesType() {
        return (Integer) get(20);
    }

    /**
     * Setter for <code>lau_unit.slot_id</code>. 广告位ID res_slot.slot_id, 对GD广告有效
     */
    public void setSlotId(Integer value) {
        set(21, value);
    }

    /**
     * Getter for <code>lau_unit.slot_id</code>. 广告位ID res_slot.slot_id, 对GD广告有效
     */
    public Integer getSlotId() {
        return (Integer) get(21);
    }

    /**
     * Setter for <code>lau_unit.unit_status</code>.
     * 单元状态:1-有效,2-已暂停,3-已结束,4-已删除,5-未开始,6-预算超限,7-不在投放时段,8-已完成
     */
    public void setUnitStatus(Integer value) {
        set(22, value);
    }

    /**
     * Getter for <code>lau_unit.unit_status</code>.
     * 单元状态:1-有效,2-已暂停,3-已结束,4-已删除,5-未开始,6-预算超限,7-不在投放时段,8-已完成
     */
    public Integer getUnitStatus() {
        return (Integer) get(22);
    }

    /**
     * Setter for <code>lau_unit.unit_status_mtime</code>. 单元状态更新时间
     */
    public void setUnitStatusMtime(Timestamp value) {
        set(23, value);
    }

    /**
     * Getter for <code>lau_unit.unit_status_mtime</code>. 单元状态更新时间
     */
    public Timestamp getUnitStatusMtime() {
        return (Timestamp) get(23);
    }

    /**
     * Setter for <code>lau_unit.repeat_flag</code>. 重复的单元ID,多个以逗号分隔
     */
    public void setRepeatFlag(String value) {
        set(24, value);
    }

    /**
     * Getter for <code>lau_unit.repeat_flag</code>. 重复的单元ID,多个以逗号分隔
     */
    public String getRepeatFlag() {
        return (String) get(24);
    }

    /**
     * Setter for <code>lau_unit.tags</code>. 单元标签
     */
    public void setTags(String value) {
        set(25, value);
    }

    /**
     * Getter for <code>lau_unit.tags</code>. 单元标签
     */
    public String getTags() {
        return (String) get(25);
    }

    /**
     * Setter for <code>lau_unit.app_package_id</code>. 应用包id
     */
    public void setAppPackageId(Integer value) {
        set(26, value);
    }

    /**
     * Getter for <code>lau_unit.app_package_id</code>. 应用包id
     */
    public Integer getAppPackageId() {
        return (Integer) get(26);
    }

    /**
     * Setter for <code>lau_unit.daily_budget_type</code>. 日预算类型: 1-自动, 2-手动
     */
    public void setDailyBudgetType(Integer value) {
        set(27, value);
    }

    /**
     * Getter for <code>lau_unit.daily_budget_type</code>. 日预算类型: 1-自动, 2-手动
     */
    public Integer getDailyBudgetType() {
        return (Integer) get(27);
    }

    /**
     * Setter for <code>lau_unit.is_history</code>. 是否是历史单元: 0-否 1-是
     */
    public void setIsHistory(Integer value) {
        set(28, value);
    }

    /**
     * Getter for <code>lau_unit.is_history</code>. 是否是历史单元: 0-否 1-是
     */
    public Integer getIsHistory() {
        return (Integer) get(28);
    }

    /**
     * Setter for <code>lau_unit.ocpc_target</code>. oCPC(M)优化目标: 1-安卓下载
     */
    public void setOcpcTarget(Integer value) {
        set(29, value);
    }

    /**
     * Getter for <code>lau_unit.ocpc_target</code>. oCPC(M)优化目标: 1-安卓下载
     */
    public Integer getOcpcTarget() {
        return (Integer) get(29);
    }

    /**
     * Setter for <code>lau_unit.two_stage_bid</code>. oCPC(M)二阶段出价(单位分)
     */
    public void setTwoStageBid(Integer value) {
        set(30, value);
    }

    /**
     * Getter for <code>lau_unit.two_stage_bid</code>. oCPC(M)二阶段出价(单位分)
     */
    public Integer getTwoStageBid() {
        return (Integer) get(30);
    }

    /**
     * Setter for <code>lau_unit.subject_id</code>. 物料id
     */
    public void setSubjectId(Integer value) {
        set(31, value);
    }

    /**
     * Getter for <code>lau_unit.subject_id</code>. 物料id
     */
    public Integer getSubjectId() {
        return (Integer) get(31);
    }

    /**
     * Setter for <code>lau_unit.smart_increase</code>. 智能起量：0-关闭 1-开启
     */
    public void setSmartIncrease(Integer value) {
        set(32, value);
    }

    /**
     * Getter for <code>lau_unit.smart_increase</code>. 智能起量：0-关闭 1-开启
     */
    public Integer getSmartIncrease() {
        return (Integer) get(32);
    }

    /**
     * Setter for <code>lau_unit.flow_type</code>. 流量类型 -1 未知， 0-外广流量 1-内光流量
     */
    public void setFlowType(Integer value) {
        set(33, value);
    }

    /**
     * Getter for <code>lau_unit.flow_type</code>. 流量类型 -1 未知， 0-外广流量 1-内光流量
     */
    public Integer getFlowType() {
        return (Integer) get(33);
    }

    /**
     * Setter for <code>lau_unit.adp_version</code>. 广告平台版本号 0-老版 1-新版
     */
    public void setAdpVersion(Integer value) {
        set(34, value);
    }

    /**
     * Getter for <code>lau_unit.adp_version</code>. 广告平台版本号 0-老版 1-新版
     */
    public Integer getAdpVersion() {
        return (Integer) get(34);
    }

    /**
     * Setter for <code>lau_unit.speed_mode</code>. 投放模式: 1-匀速投放，2-加速投放
     */
    public void setSpeedMode(Integer value) {
        set(35, value);
    }

    /**
     * Getter for <code>lau_unit.speed_mode</code>. 投放模式: 1-匀速投放，2-加速投放
     */
    public Integer getSpeedMode() {
        return (Integer) get(35);
    }

    /**
     * Setter for <code>lau_unit.is_new_fly</code>. 新起飞：0-否 1-是
     */
    public void setIsNewFly(Integer value) {
        set(36, value);
    }

    /**
     * Getter for <code>lau_unit.is_new_fly</code>. 新起飞：0-否 1-是
     */
    public Integer getIsNewFly() {
        return (Integer) get(36);
    }

    /**
     * Setter for <code>lau_unit.is_store_direct_launch</code>. 是否应用商店直投:
     * 0-否，1-是
     */
    public void setIsStoreDirectLaunch(Integer value) {
        set(37, value);
    }

    /**
     * Getter for <code>lau_unit.is_store_direct_launch</code>. 是否应用商店直投:
     * 0-否，1-是
     */
    public Integer getIsStoreDirectLaunch() {
        return (Integer) get(37);
    }

    /**
     * Setter for <code>lau_unit.product_group_id</code>. 商品组id
     */
    public void setProductGroupId(Integer value) {
        set(38, value);
    }

    /**
     * Getter for <code>lau_unit.product_group_id</code>. 商品组id
     */
    public Integer getProductGroupId() {
        return (Integer) get(38);
    }

    /**
     * Setter for <code>lau_unit.is_programmatic</code>. 是否程序化创意 (0-否, 1-是)
     */
    public void setIsProgrammatic(Integer value) {
        set(39, value);
    }

    /**
     * Getter for <code>lau_unit.is_programmatic</code>. 是否程序化创意 (0-否, 1-是)
     */
    public Integer getIsProgrammatic() {
        return (Integer) get(39);
    }

    /**
     * Setter for <code>lau_unit.brand_info_id</code>. 品牌信息id
     */
    public void setBrandInfoId(Integer value) {
        set(40, value);
    }

    /**
     * Getter for <code>lau_unit.brand_info_id</code>. 品牌信息id
     */
    public Integer getBrandInfoId() {
        return (Integer) get(40);
    }

    /**
     * Setter for <code>lau_unit.flag</code>. 多种类型、枚举等通用字段。 0~100 来源
     */
    public void setFlag(Integer value) {
        set(41, value);
    }

    /**
     * Getter for <code>lau_unit.flag</code>. 多种类型、枚举等通用字段。 0~100 来源
     */
    public Integer getFlag() {
        return (Integer) get(41);
    }

    /**
     * Setter for <code>lau_unit.enterprise_mid</code>. 企业号mid
     */
    public void setEnterpriseMid(Long value) {
        set(42, value);
    }

    /**
     * Getter for <code>lau_unit.enterprise_mid</code>. 企业号mid
     */
    public Long getEnterpriseMid() {
        return (Long) get(42);
    }

    /**
     * Setter for <code>lau_unit.is_long_term_launch</code>. 是否长期投放（起飞专用）:0-否
     * 1-是
     */
    public void setIsLongTermLaunch(Integer value) {
        set(43, value);
    }

    /**
     * Getter for <code>lau_unit.is_long_term_launch</code>. 是否长期投放（起飞专用）:0-否
     * 1-是
     */
    public Integer getIsLongTermLaunch() {
        return (Integer) get(43);
    }

    /**
     * Setter for <code>lau_unit.ocpx_target_two</code>. oCPX第二目标(适用于双目标的深层目标)
     */
    public void setOcpxTargetTwo(Integer value) {
        set(44, value);
    }

    /**
     * Getter for <code>lau_unit.ocpx_target_two</code>. oCPX第二目标(适用于双目标的深层目标)
     */
    public Integer getOcpxTargetTwo() {
        return (Integer) get(44);
    }

    /**
     * Setter for <code>lau_unit.ocpx_target_two_bid</code>. oCPX第二目标出价
     */
    public void setOcpxTargetTwoBid(Integer value) {
        set(45, value);
    }

    /**
     * Getter for <code>lau_unit.ocpx_target_two_bid</code>. oCPX第二目标出价
     */
    public Integer getOcpxTargetTwoBid() {
        return (Integer) get(45);
    }

    /**
     * Setter for <code>lau_unit.ocpx_mode</code>. oCPX模式: 0-非oCPX, 1-oCPX单目标,
     * 2-oCPX第二目标自动出价, 3-oCPX双出价
     */
    public void setOcpxMode(Integer value) {
        set(46, value);
    }

    /**
     * Getter for <code>lau_unit.ocpx_mode</code>. oCPX模式: 0-非oCPX, 1-oCPX单目标,
     * 2-oCPX第二目标自动出价, 3-oCPX双出价
     */
    public Integer getOcpxMode() {
        return (Integer) get(46);
    }

    /**
     * Setter for <code>lau_unit.is_managed</code>. 是否是专业托管的单元
     */
    public void setIsManaged(Integer value) {
        set(47, value);
    }

    /**
     * Getter for <code>lau_unit.is_managed</code>. 是否是专业托管的单元
     */
    public Integer getIsManaged() {
        return (Integer) get(47);
    }

    /**
     * Setter for <code>lau_unit.is_gd_plus</code>. 是否gd+：0-否 1-是
     */
    public void setIsGdPlus(Integer value) {
        set(48, value);
    }

    /**
     * Getter for <code>lau_unit.is_gd_plus</code>. 是否gd+：0-否 1-是
     */
    public Integer getIsGdPlus() {
        return (Integer) get(48);
    }

    /**
     * Setter for <code>lau_unit.gd_plus_optimize_model</code>. 优化模式：0-系统优化
     * 1-手动优化
     */
    public void setGdPlusOptimizeModel(Integer value) {
        set(49, value);
    }

    /**
     * Getter for <code>lau_unit.gd_plus_optimize_model</code>. 优化模式：0-系统优化
     * 1-手动优化
     */
    public Integer getGdPlusOptimizeModel() {
        return (Integer) get(49);
    }

    /**
     * Setter for <code>lau_unit.gd_plus_optimize_target</code>.
     * 优化目标(仅优化模式为系统优化时有效)：0-播放成本 1-涨粉成本
     */
    public void setGdPlusOptimizeTarget(Integer value) {
        set(50, value);
    }

    /**
     * Getter for <code>lau_unit.gd_plus_optimize_target</code>.
     * 优化目标(仅优化模式为系统优化时有效)：0-播放成本 1-涨粉成本
     */
    public Integer getGdPlusOptimizeTarget() {
        return (Integer) get(50);
    }

    /**
     * Setter for <code>lau_unit.gd_plus_finish_flag</code>. GD+排期是否创建完成 0-创建完成
     * 1-创建中 2-创建失败
     */
    public void setGdPlusFinishFlag(Integer value) {
        set(51, value);
    }

    /**
     * Getter for <code>lau_unit.gd_plus_finish_flag</code>. GD+排期是否创建完成 0-创建完成
     * 1-创建中 2-创建失败
     */
    public Integer getGdPlusFinishFlag() {
        return (Integer) get(51);
    }

    /**
     * Setter for <code>lau_unit.gd_plus_fail_msg</code>. GD+排期创建失败原因
     */
    public void setGdPlusFailMsg(String value) {
        set(52, value);
    }

    /**
     * Getter for <code>lau_unit.gd_plus_fail_msg</code>. GD+排期创建失败原因
     */
    public String getGdPlusFailMsg() {
        return (String) get(52);
    }

    /**
     * Setter for <code>lau_unit.gd_plus_area_group_id</code>. GD+地域组id
     */
    public void setGdPlusAreaGroupId(Integer value) {
        set(53, value);
    }

    /**
     * Getter for <code>lau_unit.gd_plus_area_group_id</code>. GD+地域组id
     */
    public Integer getGdPlusAreaGroupId() {
        return (Integer) get(53);
    }

    /**
     * Setter for <code>lau_unit.gd_plus_is_today</code>. GD+单元是否当日：0-否 1-是
     */
    public void setGdPlusIsToday(Integer value) {
        set(54, value);
    }

    /**
     * Getter for <code>lau_unit.gd_plus_is_today</code>. GD+单元是否当日：0-否 1-是
     */
    public Integer getGdPlusIsToday() {
        return (Integer) get(54);
    }

    /**
     * Setter for <code>lau_unit.gd_plus_today_hour</code>. 今日开始小时
     */
    public void setGdPlusTodayHour(Integer value) {
        set(55, value);
    }

    /**
     * Getter for <code>lau_unit.gd_plus_today_hour</code>. 今日开始小时
     */
    public Integer getGdPlusTodayHour() {
        return (Integer) get(55);
    }

    /**
     * Setter for <code>lau_unit.business_domain</code>. 业务范畴: 0-未知; 1-必选;
     * 2-商业起飞
     */
    public void setBusinessDomain(Integer value) {
        set(56, value);
    }

    /**
     * Getter for <code>lau_unit.business_domain</code>. 业务范畴: 0-未知; 1-必选;
     * 2-商业起飞
     */
    public Integer getBusinessDomain() {
        return (Integer) get(56);
    }

    /**
     * Setter for <code>lau_unit.is_middle_ad</code>. 是新中台广告：0-否 1-是
     */
    public void setIsMiddleAd(Integer value) {
        set(57, value);
    }

    /**
     * Getter for <code>lau_unit.is_middle_ad</code>. 是新中台广告：0-否 1-是
     */
    public Integer getIsMiddleAd() {
        return (Integer) get(57);
    }

    /**
     * Setter for <code>lau_unit.is_high_priority</code>. 是否高优单元：0-否 1-是
     */
    public void setIsHighPriority(Integer value) {
        set(58, value);
    }

    /**
     * Getter for <code>lau_unit.is_high_priority</code>. 是否高优单元：0-否 1-是
     */
    public Integer getIsHighPriority() {
        return (Integer) get(58);
    }

    /**
     * Setter for <code>lau_unit.is_no_bid</code>. 是否nobid 1-是 0-否
     */
    public void setIsNoBid(Integer value) {
        set(59, value);
    }

    /**
     * Getter for <code>lau_unit.is_no_bid</code>. 是否nobid 1-是 0-否
     */
    public Integer getIsNoBid() {
        return (Integer) get(59);
    }

    /**
     * Setter for <code>lau_unit.has_no_bid_max_bid</code>. 是否有nobid最高出价
     */
    public void setHasNoBidMaxBid(Integer value) {
        set(60, value);
    }

    /**
     * Getter for <code>lau_unit.has_no_bid_max_bid</code>. 是否有nobid最高出价
     */
    public Integer getHasNoBidMaxBid() {
        return (Integer) get(60);
    }

    /**
     * Setter for <code>lau_unit.target_package_id</code>. 单元绑定新版定向包id
     */
    public void setTargetPackageId(Integer value) {
        set(61, value);
    }

    /**
     * Getter for <code>lau_unit.target_package_id</code>. 单元绑定新版定向包id
     */
    public Integer getTargetPackageId() {
        return (Integer) get(61);
    }

    /**
     * Setter for <code>lau_unit.no_bid_max</code>. nobid 上限(单位:分)
     */
    public void setNoBidMax(Integer value) {
        set(62, value);
    }

    /**
     * Getter for <code>lau_unit.no_bid_max</code>. nobid 上限(单位:分)
     */
    public Integer getNoBidMax() {
        return (Integer) get(62);
    }

    /**
     * Setter for <code>lau_unit.sub_business_domain</code>.
     * 子业务范畴(原字段已被算法绑架无法扩展): 0-无; 1-带货投流
     */
    public void setSubBusinessDomain(Integer value) {
        set(63, value);
    }

    /**
     * Getter for <code>lau_unit.sub_business_domain</code>.
     * 子业务范畴(原字段已被算法绑架无法扩展): 0-无; 1-带货投流
     */
    public Integer getSubBusinessDomain() {
        return (Integer) get(63);
    }

    /**
     * Setter for <code>lau_unit.is_smart_material</code>. 智能创意: 0-不启用, 1-启用
     */
    public void setIsSmartMaterial(Integer value) {
        set(64, value);
    }

    /**
     * Getter for <code>lau_unit.is_smart_material</code>. 智能创意: 0-不启用, 1-启用
     */
    public Integer getIsSmartMaterial() {
        return (Integer) get(64);
    }

    /**
     * Setter for <code>lau_unit.assist_target</code>. 辅助目标
     */
    public void setAssistTarget(Integer value) {
        set(65, value);
    }

    /**
     * Getter for <code>lau_unit.assist_target</code>. 辅助目标
     */
    public Integer getAssistTarget() {
        return (Integer) get(65);
    }

    /**
     * Setter for <code>lau_unit.assist_price</code>. 辅助出价（单位：分）
     */
    public void setAssistPrice(Integer value) {
        set(66, value);
    }

    /**
     * Getter for <code>lau_unit.assist_price</code>. 辅助出价（单位：分）
     */
    public Integer getAssistPrice() {
        return (Integer) get(66);
    }

    /**
     * Setter for <code>lau_unit.dual_bid_two_stage_optimization</code>.
     * 是否开启二阶段优化：0-不启用 1-启用
     */
    public void setDualBidTwoStageOptimization(Integer value) {
        set(67, value);
    }

    /**
     * Getter for <code>lau_unit.dual_bid_two_stage_optimization</code>.
     * 是否开启二阶段优化：0-不启用 1-启用
     */
    public Integer getDualBidTwoStageOptimization() {
        return (Integer) get(67);
    }

    /**
     * Setter for <code>lau_unit.target_expand</code>. 定向拓展开关
     */
    public void setTargetExpand(Integer value) {
        set(68, value);
    }

    /**
     * Getter for <code>lau_unit.target_expand</code>. 定向拓展开关
     */
    public Integer getTargetExpand() {
        return (Integer) get(68);
    }

    /**
     * Setter for <code>lau_unit.search_price_coefficient</code>. 搜索出价系数已乘100
     */
    public void setSearchPriceCoefficient(Integer value) {
        set(69, value);
    }

    /**
     * Getter for <code>lau_unit.search_price_coefficient</code>. 搜索出价系数已乘100
     */
    public Integer getSearchPriceCoefficient() {
        return (Integer) get(69);
    }

    /**
     * Setter for <code>lau_unit.is_all_ad_search_unit</code>. 是否所有广告搜索词0 否 1是
     */
    public void setIsAllAdSearchUnit(Integer value) {
        set(70, value);
    }

    /**
     * Getter for <code>lau_unit.is_all_ad_search_unit</code>. 是否所有广告搜索词0 否 1是
     */
    public Integer getIsAllAdSearchUnit() {
        return (Integer) get(70);
    }

    /**
     * Setter for <code>lau_unit.parent_unit_id</code>. 母单元id
     */
    public void setParentUnitId(Integer value) {
        set(71, value);
    }

    /**
     * Getter for <code>lau_unit.parent_unit_id</code>. 母单元id
     */
    public Integer getParentUnitId() {
        return (Integer) get(71);
    }

    /**
     * Setter for <code>lau_unit.bid_type</code>. 出价类型 0-历史数据UNKNOWN 1-CPM 2-CPC
     * 3-OCPM 4-NOBID
     */
    public void setBidType(Integer value) {
        set(72, value);
    }

    /**
     * Getter for <code>lau_unit.bid_type</code>. 出价类型 0-历史数据UNKNOWN 1-CPM 2-CPC
     * 3-OCPM 4-NOBID
     */
    public Integer getBidType() {
        return (Integer) get(72);
    }

    /**
     * Setter for <code>lau_unit.creative_explore_status</code>. 创意探索状态
     * 0-UNKNOWN 1-开启 2-暂停 3-已删除
     */
    public void setCreativeExploreStatus(Integer value) {
        set(73, value);
    }

    /**
     * Getter for <code>lau_unit.creative_explore_status</code>. 创意探索状态
     * 0-UNKNOWN 1-开启 2-暂停 3-已删除
     */
    public Integer getCreativeExploreStatus() {
        return (Integer) get(73);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauUnitRecord
     */
    public LauUnitRecord() {
        super(TLauUnit.LAU_UNIT);
    }

    /**
     * Create a detached, initialised LauUnitRecord
     */
    public LauUnitRecord(Integer unitId, Integer accountId, Integer campaignId, String unitName, Integer promotionPurposeType, Integer channelId, Integer slotGroup, String launchBeginDate, String launchEndDate, String launchTime, Integer costPrice, Integer budgetType, Long budget, Integer frequencyUnit, Integer frequencyLimit, Integer creativeDisplayMode, Integer status, Integer isDeleted, Timestamp ctime, Timestamp mtime, Integer salesType, Integer slotId, Integer unitStatus, Timestamp unitStatusMtime, String repeatFlag, String tags, Integer appPackageId, Integer dailyBudgetType, Integer isHistory, Integer ocpcTarget, Integer twoStageBid, Integer subjectId, Integer smartIncrease, Integer flowType, Integer adpVersion, Integer speedMode, Integer isNewFly, Integer isStoreDirectLaunch, Integer productGroupId, Integer isProgrammatic, Integer brandInfoId, Integer flag, Long enterpriseMid, Integer isLongTermLaunch, Integer ocpxTargetTwo, Integer ocpxTargetTwoBid, Integer ocpxMode, Integer isManaged, Integer isGdPlus, Integer gdPlusOptimizeModel, Integer gdPlusOptimizeTarget, Integer gdPlusFinishFlag, String gdPlusFailMsg, Integer gdPlusAreaGroupId, Integer gdPlusIsToday, Integer gdPlusTodayHour, Integer businessDomain, Integer isMiddleAd, Integer isHighPriority, Integer isNoBid, Integer hasNoBidMaxBid, Integer targetPackageId, Integer noBidMax, Integer subBusinessDomain, Integer isSmartMaterial, Integer assistTarget, Integer assistPrice, Integer dualBidTwoStageOptimization, Integer targetExpand, Integer searchPriceCoefficient, Integer isAllAdSearchUnit, Integer parentUnitId, Integer bidType, Integer creativeExploreStatus) {
        super(TLauUnit.LAU_UNIT);

        setUnitId(unitId);
        setAccountId(accountId);
        setCampaignId(campaignId);
        setUnitName(unitName);
        setPromotionPurposeType(promotionPurposeType);
        setChannelId(channelId);
        setSlotGroup(slotGroup);
        setLaunchBeginDate(launchBeginDate);
        setLaunchEndDate(launchEndDate);
        setLaunchTime(launchTime);
        setCostPrice(costPrice);
        setBudgetType(budgetType);
        setBudget(budget);
        setFrequencyUnit(frequencyUnit);
        setFrequencyLimit(frequencyLimit);
        setCreativeDisplayMode(creativeDisplayMode);
        setStatus(status);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setSalesType(salesType);
        setSlotId(slotId);
        setUnitStatus(unitStatus);
        setUnitStatusMtime(unitStatusMtime);
        setRepeatFlag(repeatFlag);
        setTags(tags);
        setAppPackageId(appPackageId);
        setDailyBudgetType(dailyBudgetType);
        setIsHistory(isHistory);
        setOcpcTarget(ocpcTarget);
        setTwoStageBid(twoStageBid);
        setSubjectId(subjectId);
        setSmartIncrease(smartIncrease);
        setFlowType(flowType);
        setAdpVersion(adpVersion);
        setSpeedMode(speedMode);
        setIsNewFly(isNewFly);
        setIsStoreDirectLaunch(isStoreDirectLaunch);
        setProductGroupId(productGroupId);
        setIsProgrammatic(isProgrammatic);
        setBrandInfoId(brandInfoId);
        setFlag(flag);
        setEnterpriseMid(enterpriseMid);
        setIsLongTermLaunch(isLongTermLaunch);
        setOcpxTargetTwo(ocpxTargetTwo);
        setOcpxTargetTwoBid(ocpxTargetTwoBid);
        setOcpxMode(ocpxMode);
        setIsManaged(isManaged);
        setIsGdPlus(isGdPlus);
        setGdPlusOptimizeModel(gdPlusOptimizeModel);
        setGdPlusOptimizeTarget(gdPlusOptimizeTarget);
        setGdPlusFinishFlag(gdPlusFinishFlag);
        setGdPlusFailMsg(gdPlusFailMsg);
        setGdPlusAreaGroupId(gdPlusAreaGroupId);
        setGdPlusIsToday(gdPlusIsToday);
        setGdPlusTodayHour(gdPlusTodayHour);
        setBusinessDomain(businessDomain);
        setIsMiddleAd(isMiddleAd);
        setIsHighPriority(isHighPriority);
        setIsNoBid(isNoBid);
        setHasNoBidMaxBid(hasNoBidMaxBid);
        setTargetPackageId(targetPackageId);
        setNoBidMax(noBidMax);
        setSubBusinessDomain(subBusinessDomain);
        setIsSmartMaterial(isSmartMaterial);
        setAssistTarget(assistTarget);
        setAssistPrice(assistPrice);
        setDualBidTwoStageOptimization(dualBidTwoStageOptimization);
        setTargetExpand(targetExpand);
        setSearchPriceCoefficient(searchPriceCoefficient);
        setIsAllAdSearchUnit(isAllAdSearchUnit);
        setParentUnitId(parentUnitId);
        setBidType(bidType);
        setCreativeExploreStatus(creativeExploreStatus);
    }

    /**
     * Create a detached, initialised LauUnitRecord
     */
    public LauUnitRecord(LauUnitPo value) {
        super(TLauUnit.LAU_UNIT);

        if (value != null) {
            setUnitId(value.getUnitId());
            setAccountId(value.getAccountId());
            setCampaignId(value.getCampaignId());
            setUnitName(value.getUnitName());
            setPromotionPurposeType(value.getPromotionPurposeType());
            setChannelId(value.getChannelId());
            setSlotGroup(value.getSlotGroup());
            setLaunchBeginDate(value.getLaunchBeginDate());
            setLaunchEndDate(value.getLaunchEndDate());
            setLaunchTime(value.getLaunchTime());
            setCostPrice(value.getCostPrice());
            setBudgetType(value.getBudgetType());
            setBudget(value.getBudget());
            setFrequencyUnit(value.getFrequencyUnit());
            setFrequencyLimit(value.getFrequencyLimit());
            setCreativeDisplayMode(value.getCreativeDisplayMode());
            setStatus(value.getStatus());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setSalesType(value.getSalesType());
            setSlotId(value.getSlotId());
            setUnitStatus(value.getUnitStatus());
            setUnitStatusMtime(value.getUnitStatusMtime());
            setRepeatFlag(value.getRepeatFlag());
            setTags(value.getTags());
            setAppPackageId(value.getAppPackageId());
            setDailyBudgetType(value.getDailyBudgetType());
            setIsHistory(value.getIsHistory());
            setOcpcTarget(value.getOcpcTarget());
            setTwoStageBid(value.getTwoStageBid());
            setSubjectId(value.getSubjectId());
            setSmartIncrease(value.getSmartIncrease());
            setFlowType(value.getFlowType());
            setAdpVersion(value.getAdpVersion());
            setSpeedMode(value.getSpeedMode());
            setIsNewFly(value.getIsNewFly());
            setIsStoreDirectLaunch(value.getIsStoreDirectLaunch());
            setProductGroupId(value.getProductGroupId());
            setIsProgrammatic(value.getIsProgrammatic());
            setBrandInfoId(value.getBrandInfoId());
            setFlag(value.getFlag());
            setEnterpriseMid(value.getEnterpriseMid());
            setIsLongTermLaunch(value.getIsLongTermLaunch());
            setOcpxTargetTwo(value.getOcpxTargetTwo());
            setOcpxTargetTwoBid(value.getOcpxTargetTwoBid());
            setOcpxMode(value.getOcpxMode());
            setIsManaged(value.getIsManaged());
            setIsGdPlus(value.getIsGdPlus());
            setGdPlusOptimizeModel(value.getGdPlusOptimizeModel());
            setGdPlusOptimizeTarget(value.getGdPlusOptimizeTarget());
            setGdPlusFinishFlag(value.getGdPlusFinishFlag());
            setGdPlusFailMsg(value.getGdPlusFailMsg());
            setGdPlusAreaGroupId(value.getGdPlusAreaGroupId());
            setGdPlusIsToday(value.getGdPlusIsToday());
            setGdPlusTodayHour(value.getGdPlusTodayHour());
            setBusinessDomain(value.getBusinessDomain());
            setIsMiddleAd(value.getIsMiddleAd());
            setIsHighPriority(value.getIsHighPriority());
            setIsNoBid(value.getIsNoBid());
            setHasNoBidMaxBid(value.getHasNoBidMaxBid());
            setTargetPackageId(value.getTargetPackageId());
            setNoBidMax(value.getNoBidMax());
            setSubBusinessDomain(value.getSubBusinessDomain());
            setIsSmartMaterial(value.getIsSmartMaterial());
            setAssistTarget(value.getAssistTarget());
            setAssistPrice(value.getAssistPrice());
            setDualBidTwoStageOptimization(value.getDualBidTwoStageOptimization());
            setTargetExpand(value.getTargetExpand());
            setSearchPriceCoefficient(value.getSearchPriceCoefficient());
            setIsAllAdSearchUnit(value.getIsAllAdSearchUnit());
            setParentUnitId(value.getParentUnitId());
            setBidType(value.getBidType());
            setCreativeExploreStatus(value.getCreativeExploreStatus());
        }
    }
}
