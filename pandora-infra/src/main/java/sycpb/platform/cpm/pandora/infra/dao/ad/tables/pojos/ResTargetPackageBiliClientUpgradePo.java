/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 新版定向包分端bili客户端定向表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResTargetPackageBiliClientUpgradePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   targetPackageId;
    private Integer   os;
    private Integer   relation;
    private Integer   smallerVersion;
    private Integer   largerVersion;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;

    public ResTargetPackageBiliClientUpgradePo() {}

    public ResTargetPackageBiliClientUpgradePo(ResTargetPackageBiliClientUpgradePo value) {
        this.id = value.id;
        this.targetPackageId = value.targetPackageId;
        this.os = value.os;
        this.relation = value.relation;
        this.smallerVersion = value.smallerVersion;
        this.largerVersion = value.largerVersion;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
    }

    public ResTargetPackageBiliClientUpgradePo(
        Long      id,
        Integer   targetPackageId,
        Integer   os,
        Integer   relation,
        Integer   smallerVersion,
        Integer   largerVersion,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime
    ) {
        this.id = id;
        this.targetPackageId = targetPackageId;
        this.os = os;
        this.relation = relation;
        this.smallerVersion = smallerVersion;
        this.largerVersion = largerVersion;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
    }

    /**
     * Getter for <code>res_target_package_bili_client_upgrade.id</code>. 自增id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>res_target_package_bili_client_upgrade.id</code>. 自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for
     * <code>res_target_package_bili_client_upgrade.target_package_id</code>.
     * 定向包id
     */
    public Integer getTargetPackageId() {
        return this.targetPackageId;
    }

    /**
     * Setter for
     * <code>res_target_package_bili_client_upgrade.target_package_id</code>.
     * 定向包id
     */
    public void setTargetPackageId(Integer targetPackageId) {
        this.targetPackageId = targetPackageId;
    }

    /**
     * Getter for <code>res_target_package_bili_client_upgrade.os</code>. 设备平台
     * android-399,iphone-398,ipad-421
     */
    public Integer getOs() {
        return this.os;
    }

    /**
     * Setter for <code>res_target_package_bili_client_upgrade.os</code>. 设备平台
     * android-399,iphone-398,ipad-421
     */
    public void setOs(Integer os) {
        this.os = os;
    }

    /**
     * Getter for <code>res_target_package_bili_client_upgrade.relation</code>.
     * 定向关系 0-不限1-介于2-小于等于3-大于等于4-小于5大于
     */
    public Integer getRelation() {
        return this.relation;
    }

    /**
     * Setter for <code>res_target_package_bili_client_upgrade.relation</code>.
     * 定向关系 0-不限1-介于2-小于等于3-大于等于4-小于5大于
     */
    public void setRelation(Integer relation) {
        this.relation = relation;
    }

    /**
     * Getter for
     * <code>res_target_package_bili_client_upgrade.smaller_version</code>.
     * 较小版本号 build值
     */
    public Integer getSmallerVersion() {
        return this.smallerVersion;
    }

    /**
     * Setter for
     * <code>res_target_package_bili_client_upgrade.smaller_version</code>.
     * 较小版本号 build值
     */
    public void setSmallerVersion(Integer smallerVersion) {
        this.smallerVersion = smallerVersion;
    }

    /**
     * Getter for
     * <code>res_target_package_bili_client_upgrade.larger_version</code>. 较大版本号
     * build值
     */
    public Integer getLargerVersion() {
        return this.largerVersion;
    }

    /**
     * Setter for
     * <code>res_target_package_bili_client_upgrade.larger_version</code>. 较大版本号
     * build值
     */
    public void setLargerVersion(Integer largerVersion) {
        this.largerVersion = largerVersion;
    }

    /**
     * Getter for
     * <code>res_target_package_bili_client_upgrade.is_deleted</code>. 软删除 0 否
     * 1是
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for
     * <code>res_target_package_bili_client_upgrade.is_deleted</code>. 软删除 0 否
     * 1是
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>res_target_package_bili_client_upgrade.ctime</code>.
     * 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>res_target_package_bili_client_upgrade.ctime</code>.
     * 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>res_target_package_bili_client_upgrade.mtime</code>.
     * 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>res_target_package_bili_client_upgrade.mtime</code>.
     * 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ResTargetPackageBiliClientUpgradePo (");

        sb.append(id);
        sb.append(", ").append(targetPackageId);
        sb.append(", ").append(os);
        sb.append(", ").append(relation);
        sb.append(", ").append(smallerVersion);
        sb.append(", ").append(largerVersion);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);

        sb.append(")");
        return sb.toString();
    }
}
