/*
 * This file is generated by j<PERSON><PERSON><PERSON>.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record17;
import org.jooq.Row17;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauStoryCouponComponent;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauStoryCouponComponentPo;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauStoryCouponComponentRecord extends UpdatableRecordImpl<LauStoryCouponComponentRecord> implements Record17<Long, Timestamp, Timestamp, Integer, Integer, Long, String, Integer, String, String, Timestamp, Timestamp, Timestamp, Timestamp, Integer, Integer, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_story_coupon_component.id</code>. id
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.id</code>. id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_story_coupon_component.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(1);
    }

    /**
     * Setter for <code>lau_story_coupon_component.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(2);
    }

    /**
     * Setter for <code>lau_story_coupon_component.is_deleted</code>. 软删除:
     * 0-未删除,1-已删除
     */
    public void setIsDeleted(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.is_deleted</code>. 软删除:
     * 0-未删除,1-已删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_story_coupon_component.account_id</code>. 账号id
     */
    public void setAccountId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.account_id</code>. 账号id
     */
    public Integer getAccountId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_story_coupon_component.component_id</code>.
     * story组件id
     */
    public void setComponentId(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.component_id</code>.
     * story组件id
     */
    public Long getComponentId() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>lau_story_coupon_component.component_name</code>. 组件名称
     */
    public void setComponentName(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.component_name</code>. 组件名称
     */
    public String getComponentName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>lau_story_coupon_component.cost_fen</code>. 优惠金额(单位分)
     */
    public void setCostFen(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.cost_fen</code>. 优惠金额(单位分)
     */
    public Integer getCostFen() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>lau_story_coupon_component.description</code>. 优惠券名称
     */
    public void setDescription(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.description</code>. 优惠券名称
     */
    public String getDescription() {
        return (String) get(8);
    }

    /**
     * Setter for <code>lau_story_coupon_component.comment</code>. 备注信息
     */
    public void setComment(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.comment</code>. 备注信息
     */
    public String getComment() {
        return (String) get(9);
    }

    /**
     * Setter for <code>lau_story_coupon_component.use_period_start</code>.
     * 优惠券有效期开始时间
     */
    public void setUsePeriodStart(Timestamp value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.use_period_start</code>.
     * 优惠券有效期开始时间
     */
    public Timestamp getUsePeriodStart() {
        return (Timestamp) get(10);
    }

    /**
     * Setter for <code>lau_story_coupon_component.use_period_end</code>.
     * 优惠券有效期结束时间
     */
    public void setUsePeriodEnd(Timestamp value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.use_period_end</code>.
     * 优惠券有效期结束时间
     */
    public Timestamp getUsePeriodEnd() {
        return (Timestamp) get(11);
    }

    /**
     * Setter for <code>lau_story_coupon_component.obtain_period_start</code>.
     * 优惠券获取开始时间
     */
    public void setObtainPeriodStart(Timestamp value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.obtain_period_start</code>.
     * 优惠券获取开始时间
     */
    public Timestamp getObtainPeriodStart() {
        return (Timestamp) get(12);
    }

    /**
     * Setter for <code>lau_story_coupon_component.obtain_period_end</code>.
     * 优惠券获取结束时间
     */
    public void setObtainPeriodEnd(Timestamp value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.obtain_period_end</code>.
     * 优惠券获取结束时间
     */
    public Timestamp getObtainPeriodEnd() {
        return (Timestamp) get(13);
    }

    /**
     * Setter for <code>lau_story_coupon_component.button_id</code>. 按钮id
     */
    public void setButtonId(Integer value) {
        set(14, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.button_id</code>. 按钮id
     */
    public Integer getButtonId() {
        return (Integer) get(14);
    }

    /**
     * Setter for <code>lau_story_coupon_component.button_type</code>. 按钮类型
     */
    public void setButtonType(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.button_type</code>. 按钮类型
     */
    public Integer getButtonType() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>lau_story_coupon_component.button_text</code>. 按钮文案
     */
    public void setButtonText(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>lau_story_coupon_component.button_text</code>. 按钮文案
     */
    public String getButtonText() {
        return (String) get(16);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record17 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row17<Long, Timestamp, Timestamp, Integer, Integer, Long, String, Integer, String, String, Timestamp, Timestamp, Timestamp, Timestamp, Integer, Integer, String> fieldsRow() {
        return (Row17) super.fieldsRow();
    }

    @Override
    public Row17<Long, Timestamp, Timestamp, Integer, Integer, Long, String, Integer, String, String, Timestamp, Timestamp, Timestamp, Timestamp, Integer, Integer, String> valuesRow() {
        return (Row17) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.ID;
    }

    @Override
    public Field<Timestamp> field2() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.CTIME;
    }

    @Override
    public Field<Timestamp> field3() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.MTIME;
    }

    @Override
    public Field<Integer> field4() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.IS_DELETED;
    }

    @Override
    public Field<Integer> field5() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.ACCOUNT_ID;
    }

    @Override
    public Field<Long> field6() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.COMPONENT_ID;
    }

    @Override
    public Field<String> field7() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.COMPONENT_NAME;
    }

    @Override
    public Field<Integer> field8() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.COST_FEN;
    }

    @Override
    public Field<String> field9() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.DESCRIPTION;
    }

    @Override
    public Field<String> field10() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.COMMENT;
    }

    @Override
    public Field<Timestamp> field11() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.USE_PERIOD_START;
    }

    @Override
    public Field<Timestamp> field12() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.USE_PERIOD_END;
    }

    @Override
    public Field<Timestamp> field13() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.OBTAIN_PERIOD_START;
    }

    @Override
    public Field<Timestamp> field14() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.OBTAIN_PERIOD_END;
    }

    @Override
    public Field<Integer> field15() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.BUTTON_ID;
    }

    @Override
    public Field<Integer> field16() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.BUTTON_TYPE;
    }

    @Override
    public Field<String> field17() {
        return TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.BUTTON_TEXT;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Timestamp component2() {
        return getCtime();
    }

    @Override
    public Timestamp component3() {
        return getMtime();
    }

    @Override
    public Integer component4() {
        return getIsDeleted();
    }

    @Override
    public Integer component5() {
        return getAccountId();
    }

    @Override
    public Long component6() {
        return getComponentId();
    }

    @Override
    public String component7() {
        return getComponentName();
    }

    @Override
    public Integer component8() {
        return getCostFen();
    }

    @Override
    public String component9() {
        return getDescription();
    }

    @Override
    public String component10() {
        return getComment();
    }

    @Override
    public Timestamp component11() {
        return getUsePeriodStart();
    }

    @Override
    public Timestamp component12() {
        return getUsePeriodEnd();
    }

    @Override
    public Timestamp component13() {
        return getObtainPeriodStart();
    }

    @Override
    public Timestamp component14() {
        return getObtainPeriodEnd();
    }

    @Override
    public Integer component15() {
        return getButtonId();
    }

    @Override
    public Integer component16() {
        return getButtonType();
    }

    @Override
    public String component17() {
        return getButtonText();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Timestamp value2() {
        return getCtime();
    }

    @Override
    public Timestamp value3() {
        return getMtime();
    }

    @Override
    public Integer value4() {
        return getIsDeleted();
    }

    @Override
    public Integer value5() {
        return getAccountId();
    }

    @Override
    public Long value6() {
        return getComponentId();
    }

    @Override
    public String value7() {
        return getComponentName();
    }

    @Override
    public Integer value8() {
        return getCostFen();
    }

    @Override
    public String value9() {
        return getDescription();
    }

    @Override
    public String value10() {
        return getComment();
    }

    @Override
    public Timestamp value11() {
        return getUsePeriodStart();
    }

    @Override
    public Timestamp value12() {
        return getUsePeriodEnd();
    }

    @Override
    public Timestamp value13() {
        return getObtainPeriodStart();
    }

    @Override
    public Timestamp value14() {
        return getObtainPeriodEnd();
    }

    @Override
    public Integer value15() {
        return getButtonId();
    }

    @Override
    public Integer value16() {
        return getButtonType();
    }

    @Override
    public String value17() {
        return getButtonText();
    }

    @Override
    public LauStoryCouponComponentRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value2(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value3(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value4(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value5(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value6(Long value) {
        setComponentId(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value7(String value) {
        setComponentName(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value8(Integer value) {
        setCostFen(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value9(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value10(String value) {
        setComment(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value11(Timestamp value) {
        setUsePeriodStart(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value12(Timestamp value) {
        setUsePeriodEnd(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value13(Timestamp value) {
        setObtainPeriodStart(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value14(Timestamp value) {
        setObtainPeriodEnd(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value15(Integer value) {
        setButtonId(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value16(Integer value) {
        setButtonType(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord value17(String value) {
        setButtonText(value);
        return this;
    }

    @Override
    public LauStoryCouponComponentRecord values(Long value1, Timestamp value2, Timestamp value3, Integer value4, Integer value5, Long value6, String value7, Integer value8, String value9, String value10, Timestamp value11, Timestamp value12, Timestamp value13, Timestamp value14, Integer value15, Integer value16, String value17) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauStoryCouponComponentRecord
     */
    public LauStoryCouponComponentRecord() {
        super(TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT);
    }

    /**
     * Create a detached, initialised LauStoryCouponComponentRecord
     */
    public LauStoryCouponComponentRecord(Long id, Timestamp ctime, Timestamp mtime, Integer isDeleted, Integer accountId, Long componentId, String componentName, Integer costFen, String description, String comment, Timestamp usePeriodStart, Timestamp usePeriodEnd, Timestamp obtainPeriodStart, Timestamp obtainPeriodEnd, Integer buttonId, Integer buttonType, String buttonText) {
        super(TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT);

        setId(id);
        setCtime(ctime);
        setMtime(mtime);
        setIsDeleted(isDeleted);
        setAccountId(accountId);
        setComponentId(componentId);
        setComponentName(componentName);
        setCostFen(costFen);
        setDescription(description);
        setComment(comment);
        setUsePeriodStart(usePeriodStart);
        setUsePeriodEnd(usePeriodEnd);
        setObtainPeriodStart(obtainPeriodStart);
        setObtainPeriodEnd(obtainPeriodEnd);
        setButtonId(buttonId);
        setButtonType(buttonType);
        setButtonText(buttonText);
    }

    /**
     * Create a detached, initialised LauStoryCouponComponentRecord
     */
    public LauStoryCouponComponentRecord(LauStoryCouponComponentPo value) {
        super(TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT);

        if (value != null) {
            setId(value.getId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setIsDeleted(value.getIsDeleted());
            setAccountId(value.getAccountId());
            setComponentId(value.getComponentId());
            setComponentName(value.getComponentName());
            setCostFen(value.getCostFen());
            setDescription(value.getDescription());
            setComment(value.getComment());
            setUsePeriodStart(value.getUsePeriodStart());
            setUsePeriodEnd(value.getUsePeriodEnd());
            setObtainPeriodStart(value.getObtainPeriodStart());
            setObtainPeriodEnd(value.getObtainPeriodEnd());
            setButtonId(value.getButtonId());
            setButtonType(value.getButtonType());
            setButtonText(value.getButtonText());
        }
    }
}
