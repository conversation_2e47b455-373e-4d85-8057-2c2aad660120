/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record13;
import org.jooq.Row13;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauBatchOperation;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauBatchOperationPo;


/**
 * 批量操作
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauBatchOperationRecord extends UpdatableRecordImpl<LauBatchOperationRecord> implements Record13<Long, Timestamp, Timestamp, Integer, Integer, Integer, String, Long, Integer, Integer, Integer, String, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_batch_operation.id</code>. ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_batch_operation.id</code>. ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_batch_operation.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_batch_operation.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(1);
    }

    /**
     * Setter for <code>lau_batch_operation.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_batch_operation.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(2);
    }

    /**
     * Setter for <code>lau_batch_operation.account_id</code>. 账号id
     */
    public void setAccountId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_batch_operation.account_id</code>. 账号id
     */
    public Integer getAccountId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_batch_operation.target_type</code>. 对象类型: 0-未知,
     * 1-计划, 2-单元, 3-创意
     */
    public void setTargetType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_batch_operation.target_type</code>. 对象类型: 0-未知,
     * 1-计划, 2-单元, 3-创意
     */
    public Integer getTargetType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_batch_operation.operator_type</code>. 操作员类型: 0-未知
     */
    public void setOperatorType(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_batch_operation.operator_type</code>. 操作员类型: 0-未知
     */
    public Integer getOperatorType() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_batch_operation.operator_name</code>. 操作员用户名
     */
    public void setOperatorName(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_batch_operation.operator_name</code>. 操作员用户名
     */
    public String getOperatorName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>lau_batch_operation.operation_id</code>. 操作id
     */
    public void setOperationId(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_batch_operation.operation_id</code>. 操作id
     */
    public Long getOperationId() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>lau_batch_operation.operation_type</code>. 操作类型: 0-未知
     */
    public void setOperationType(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_batch_operation.operation_type</code>. 操作类型: 0-未知
     */
    public Integer getOperationType() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>lau_batch_operation.operation_status</code>. 操作状态: 0-未知,
     * 1-未开始, 2-进行中, 3-已结束, 4-已失败, 5-已挂起
     */
    public void setOperationStatus(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_batch_operation.operation_status</code>. 操作状态: 0-未知,
     * 1-未开始, 2-进行中, 3-已结束, 4-已失败, 5-已挂起
     */
    public Integer getOperationStatus() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>lau_batch_operation.operation_precedence</code>. 操作优先级:
     * 0-禁止
     */
    public void setOperationPrecedence(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_batch_operation.operation_precedence</code>. 操作优先级:
     * 0-禁止
     */
    public Integer getOperationPrecedence() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>lau_batch_operation.operation_ext</code>. 操作额外信息,
     * 包括操作内容, 开始时间, 结束时间
     */
    public void setOperationExt(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_batch_operation.operation_ext</code>. 操作额外信息,
     * 包括操作内容, 开始时间, 结束时间
     */
    public String getOperationExt() {
        return (String) get(11);
    }

    /**
     * Setter for <code>lau_batch_operation.operation_env</code>. 执行器环境: 0-未知,
     * 1-prod, 2-pre, 3-uat, 4-dev
     */
    public void setOperationEnv(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_batch_operation.operation_env</code>. 执行器环境: 0-未知,
     * 1-prod, 2-pre, 3-uat, 4-dev
     */
    public Integer getOperationEnv() {
        return (Integer) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record13 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row13<Long, Timestamp, Timestamp, Integer, Integer, Integer, String, Long, Integer, Integer, Integer, String, Integer> fieldsRow() {
        return (Row13) super.fieldsRow();
    }

    @Override
    public Row13<Long, Timestamp, Timestamp, Integer, Integer, Integer, String, Long, Integer, Integer, Integer, String, Integer> valuesRow() {
        return (Row13) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.ID;
    }

    @Override
    public Field<Timestamp> field2() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.CTIME;
    }

    @Override
    public Field<Timestamp> field3() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.MTIME;
    }

    @Override
    public Field<Integer> field4() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.ACCOUNT_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.TARGET_TYPE;
    }

    @Override
    public Field<Integer> field6() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.OPERATOR_TYPE;
    }

    @Override
    public Field<String> field7() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.OPERATOR_NAME;
    }

    @Override
    public Field<Long> field8() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.OPERATION_ID;
    }

    @Override
    public Field<Integer> field9() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.OPERATION_TYPE;
    }

    @Override
    public Field<Integer> field10() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.OPERATION_STATUS;
    }

    @Override
    public Field<Integer> field11() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.OPERATION_PRECEDENCE;
    }

    @Override
    public Field<String> field12() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.OPERATION_EXT;
    }

    @Override
    public Field<Integer> field13() {
        return TLauBatchOperation.LAU_BATCH_OPERATION.OPERATION_ENV;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Timestamp component2() {
        return getCtime();
    }

    @Override
    public Timestamp component3() {
        return getMtime();
    }

    @Override
    public Integer component4() {
        return getAccountId();
    }

    @Override
    public Integer component5() {
        return getTargetType();
    }

    @Override
    public Integer component6() {
        return getOperatorType();
    }

    @Override
    public String component7() {
        return getOperatorName();
    }

    @Override
    public Long component8() {
        return getOperationId();
    }

    @Override
    public Integer component9() {
        return getOperationType();
    }

    @Override
    public Integer component10() {
        return getOperationStatus();
    }

    @Override
    public Integer component11() {
        return getOperationPrecedence();
    }

    @Override
    public String component12() {
        return getOperationExt();
    }

    @Override
    public Integer component13() {
        return getOperationEnv();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Timestamp value2() {
        return getCtime();
    }

    @Override
    public Timestamp value3() {
        return getMtime();
    }

    @Override
    public Integer value4() {
        return getAccountId();
    }

    @Override
    public Integer value5() {
        return getTargetType();
    }

    @Override
    public Integer value6() {
        return getOperatorType();
    }

    @Override
    public String value7() {
        return getOperatorName();
    }

    @Override
    public Long value8() {
        return getOperationId();
    }

    @Override
    public Integer value9() {
        return getOperationType();
    }

    @Override
    public Integer value10() {
        return getOperationStatus();
    }

    @Override
    public Integer value11() {
        return getOperationPrecedence();
    }

    @Override
    public String value12() {
        return getOperationExt();
    }

    @Override
    public Integer value13() {
        return getOperationEnv();
    }

    @Override
    public LauBatchOperationRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value2(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value3(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value4(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value5(Integer value) {
        setTargetType(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value6(Integer value) {
        setOperatorType(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value7(String value) {
        setOperatorName(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value8(Long value) {
        setOperationId(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value9(Integer value) {
        setOperationType(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value10(Integer value) {
        setOperationStatus(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value11(Integer value) {
        setOperationPrecedence(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value12(String value) {
        setOperationExt(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord value13(Integer value) {
        setOperationEnv(value);
        return this;
    }

    @Override
    public LauBatchOperationRecord values(Long value1, Timestamp value2, Timestamp value3, Integer value4, Integer value5, Integer value6, String value7, Long value8, Integer value9, Integer value10, Integer value11, String value12, Integer value13) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauBatchOperationRecord
     */
    public LauBatchOperationRecord() {
        super(TLauBatchOperation.LAU_BATCH_OPERATION);
    }

    /**
     * Create a detached, initialised LauBatchOperationRecord
     */
    public LauBatchOperationRecord(Long id, Timestamp ctime, Timestamp mtime, Integer accountId, Integer targetType, Integer operatorType, String operatorName, Long operationId, Integer operationType, Integer operationStatus, Integer operationPrecedence, String operationExt, Integer operationEnv) {
        super(TLauBatchOperation.LAU_BATCH_OPERATION);

        setId(id);
        setCtime(ctime);
        setMtime(mtime);
        setAccountId(accountId);
        setTargetType(targetType);
        setOperatorType(operatorType);
        setOperatorName(operatorName);
        setOperationId(operationId);
        setOperationType(operationType);
        setOperationStatus(operationStatus);
        setOperationPrecedence(operationPrecedence);
        setOperationExt(operationExt);
        setOperationEnv(operationEnv);
    }

    /**
     * Create a detached, initialised LauBatchOperationRecord
     */
    public LauBatchOperationRecord(LauBatchOperationPo value) {
        super(TLauBatchOperation.LAU_BATCH_OPERATION);

        if (value != null) {
            setId(value.getId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setAccountId(value.getAccountId());
            setTargetType(value.getTargetType());
            setOperatorType(value.getOperatorType());
            setOperatorName(value.getOperatorName());
            setOperationId(value.getOperationId());
            setOperationType(value.getOperationType());
            setOperationStatus(value.getOperationStatus());
            setOperationPrecedence(value.getOperationPrecedence());
            setOperationExt(value.getOperationExt());
            setOperationEnv(value.getOperationEnv());
        }
    }
}
