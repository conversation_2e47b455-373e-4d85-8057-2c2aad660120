/*
 * This file is generated by j<PERSON><PERSON><PERSON>.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauCreativeExploreCandidateMapping;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauCreativeExploreCandidateMappingPo;


/**
 * 创意探索投放端侧候选集
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeExploreCandidateMappingRecord extends UpdatableRecordImpl<LauCreativeExploreCandidateMappingRecord> implements Record11<<PERSON>, <PERSON>te<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Timestamp, Timestamp> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_explore_candidate_mapping.id</code>. 自增主键ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_explore_candidate_mapping.id</code>. 自增主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.account_id</code>. 账户id
     */
    public void setAccountId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return (Integer) get(1);
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.campaign_id</code>. 计划id
     */
    public void setCampaignId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.campaign_id</code>. 计划id
     */
    public Integer getCampaignId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_creative_explore_candidate_mapping.unit_id</code>.
     * 单元id
     */
    public void setUnitId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_creative_explore_candidate_mapping.unit_id</code>.
     * 单元id
     */
    public Integer getUnitId() {
        return (Integer) get(3);
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.creative_id</code>.
     * 创意探索母创意id
     */
    public void setCreativeId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.creative_id</code>.
     * 创意探索母创意id
     */
    public Integer getCreativeId() {
        return (Integer) get(4);
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.candidate_campaign_id</code>.
     * 创意探索候选计划id
     */
    public void setCandidateCampaignId(Integer value) {
        set(5, value);
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.candidate_campaign_id</code>.
     * 创意探索候选计划id
     */
    public Integer getCandidateCampaignId() {
        return (Integer) get(5);
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.candidate_unit_id</code>.
     * 创意探索候选单元id
     */
    public void setCandidateUnitId(Integer value) {
        set(6, value);
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.candidate_unit_id</code>.
     * 创意探索候选单元id
     */
    public Integer getCandidateUnitId() {
        return (Integer) get(6);
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.candidate_creative_id</code>.
     * 创意探索候选创意id
     */
    public void setCandidateCreativeId(Integer value) {
        set(7, value);
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.candidate_creative_id</code>.
     * 创意探索候选创意id
     */
    public Integer getCandidateCreativeId() {
        return (Integer) get(7);
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.is_deleted</code>. 是否被删除
     * 0-正常 1-被删除
     */
    public void setIsDeleted(Integer value) {
        set(8, value);
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.is_deleted</code>. 是否被删除
     * 0-正常 1-被删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>lau_creative_explore_candidate_mapping.ctime</code>.
     * 创建时间
     */
    public void setCtime(Timestamp value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_creative_explore_candidate_mapping.ctime</code>.
     * 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(9);
    }

    /**
     * Setter for <code>lau_creative_explore_candidate_mapping.mtime</code>.
     * 更新时间
     */
    public void setMtime(Timestamp value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_creative_explore_candidate_mapping.mtime</code>.
     * 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<Long, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<Long, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Timestamp, Timestamp> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.ACCOUNT_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CAMPAIGN_ID;
    }

    @Override
    public Field<Integer> field4() {
        return TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.UNIT_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CREATIVE_ID;
    }

    @Override
    public Field<Integer> field6() {
        return TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CANDIDATE_CAMPAIGN_ID;
    }

    @Override
    public Field<Integer> field7() {
        return TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CANDIDATE_UNIT_ID;
    }

    @Override
    public Field<Integer> field8() {
        return TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CANDIDATE_CREATIVE_ID;
    }

    @Override
    public Field<Integer> field9() {
        return TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field10() {
        return TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.CTIME;
    }

    @Override
    public Field<Timestamp> field11() {
        return TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING.MTIME;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getAccountId();
    }

    @Override
    public Integer component3() {
        return getCampaignId();
    }

    @Override
    public Integer component4() {
        return getUnitId();
    }

    @Override
    public Integer component5() {
        return getCreativeId();
    }

    @Override
    public Integer component6() {
        return getCandidateCampaignId();
    }

    @Override
    public Integer component7() {
        return getCandidateUnitId();
    }

    @Override
    public Integer component8() {
        return getCandidateCreativeId();
    }

    @Override
    public Integer component9() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component10() {
        return getCtime();
    }

    @Override
    public Timestamp component11() {
        return getMtime();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getAccountId();
    }

    @Override
    public Integer value3() {
        return getCampaignId();
    }

    @Override
    public Integer value4() {
        return getUnitId();
    }

    @Override
    public Integer value5() {
        return getCreativeId();
    }

    @Override
    public Integer value6() {
        return getCandidateCampaignId();
    }

    @Override
    public Integer value7() {
        return getCandidateUnitId();
    }

    @Override
    public Integer value8() {
        return getCandidateCreativeId();
    }

    @Override
    public Integer value9() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value10() {
        return getCtime();
    }

    @Override
    public Timestamp value11() {
        return getMtime();
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord value2(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord value3(Integer value) {
        setCampaignId(value);
        return this;
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord value4(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord value5(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord value6(Integer value) {
        setCandidateCampaignId(value);
        return this;
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord value7(Integer value) {
        setCandidateUnitId(value);
        return this;
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord value8(Integer value) {
        setCandidateCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord value9(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord value10(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord value11(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativeExploreCandidateMappingRecord values(Long value1, Integer value2, Integer value3, Integer value4, Integer value5, Integer value6, Integer value7, Integer value8, Integer value9, Timestamp value10, Timestamp value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativeExploreCandidateMappingRecord
     */
    public LauCreativeExploreCandidateMappingRecord() {
        super(TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING);
    }

    /**
     * Create a detached, initialised LauCreativeExploreCandidateMappingRecord
     */
    public LauCreativeExploreCandidateMappingRecord(Long id, Integer accountId, Integer campaignId, Integer unitId, Integer creativeId, Integer candidateCampaignId, Integer candidateUnitId, Integer candidateCreativeId, Integer isDeleted, Timestamp ctime, Timestamp mtime) {
        super(TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING);

        setId(id);
        setAccountId(accountId);
        setCampaignId(campaignId);
        setUnitId(unitId);
        setCreativeId(creativeId);
        setCandidateCampaignId(candidateCampaignId);
        setCandidateUnitId(candidateUnitId);
        setCandidateCreativeId(candidateCreativeId);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
    }

    /**
     * Create a detached, initialised LauCreativeExploreCandidateMappingRecord
     */
    public LauCreativeExploreCandidateMappingRecord(LauCreativeExploreCandidateMappingPo value) {
        super(TLauCreativeExploreCandidateMapping.LAU_CREATIVE_EXPLORE_CANDIDATE_MAPPING);

        if (value != null) {
            setId(value.getId());
            setAccountId(value.getAccountId());
            setCampaignId(value.getCampaignId());
            setUnitId(value.getUnitId());
            setCreativeId(value.getCreativeId());
            setCandidateCampaignId(value.getCandidateCampaignId());
            setCandidateUnitId(value.getCandidateUnitId());
            setCandidateCreativeId(value.getCandidateCreativeId());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
        }
    }
}
