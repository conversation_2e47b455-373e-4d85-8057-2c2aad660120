/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row12;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauQualificationRecord;


/**
 * 投放资质表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauQualification extends TableImpl<LauQualificationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_qualification</code>
     */
    public static final TLauQualification LAU_QUALIFICATION = new TLauQualification();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauQualificationRecord> getRecordType() {
        return LauQualificationRecord.class;
    }

    /**
     * The column <code>lau_qualification.id</code>. 自增id
     */
    public final TableField<LauQualificationRecord, Integer> ID = createField(DSL.name("id"), SQLDataType.INTEGER.nullable(false).identity(true), this, "自增id");

    /**
     * The column <code>lau_qualification.ctime</code>. 添加时间
     */
    public final TableField<LauQualificationRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column <code>lau_qualification.mtime</code>. 修改时间
     */
    public final TableField<LauQualificationRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>lau_qualification.is_deleted</code>. 软删除: 0 - 未删除, 1 -
     * 已删除
     */
    public final TableField<LauQualificationRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除: 0 - 未删除, 1 - 已删除");

    /**
     * The column <code>lau_qualification.account_id</code>. 账号ID
     */
    public final TableField<LauQualificationRecord, Integer> ACCOUNT_ID = createField(DSL.name("account_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "账号ID");

    /**
     * The column <code>lau_qualification.qua_url</code>. 资质链接
     */
    public final TableField<LauQualificationRecord, String> QUA_URL = createField(DSL.name("qua_url"), SQLDataType.VARCHAR(256).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "资质链接");

    /**
     * The column <code>lau_qualification.qua_md5</code>. 资质md5
     */
    public final TableField<LauQualificationRecord, String> QUA_MD5 = createField(DSL.name("qua_md5"), SQLDataType.VARCHAR(32).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "资质md5");

    /**
     * The column <code>lau_qualification.qua_type</code>. 资质类型: 0 - 其他, 1 -
     * 商标证, 2 - 品牌合作授权, 3 - 肖像使用权
     */
    public final TableField<LauQualificationRecord, Integer> QUA_TYPE = createField(DSL.name("qua_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "资质类型: 0 - 其他, 1 - 商标证, 2 - 品牌合作授权, 3 - 肖像使用权");

    /**
     * The column <code>lau_qualification.qua_valid_from</code>. 资质开始时间
     */
    public final TableField<LauQualificationRecord, Timestamp> QUA_VALID_FROM = createField(DSL.name("qua_valid_from"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "资质开始时间");

    /**
     * The column <code>lau_qualification.qua_valid_to</code>. 资质结束时间
     */
    public final TableField<LauQualificationRecord, Timestamp> QUA_VALID_TO = createField(DSL.name("qua_valid_to"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "资质结束时间");

    /**
     * The column <code>lau_qualification.audit_status</code>. 审核状态: 0 - 审核通过, 1
     * - 待审核, 2 - 审核拒绝
     */
    public final TableField<LauQualificationRecord, Integer> AUDIT_STATUS = createField(DSL.name("audit_status"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "审核状态: 0 - 审核通过, 1 - 待审核, 2 - 审核拒绝");

    /**
     * The column <code>lau_qualification.qua_name</code>. 资质名称
     */
    public final TableField<LauQualificationRecord, String> QUA_NAME = createField(DSL.name("qua_name"), SQLDataType.VARCHAR(32).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "资质名称");

    private TLauQualification(Name alias, Table<LauQualificationRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauQualification(Name alias, Table<LauQualificationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("投放资质表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_qualification</code> table reference
     */
    public TLauQualification(String alias) {
        this(DSL.name(alias), LAU_QUALIFICATION);
    }

    /**
     * Create an aliased <code>lau_qualification</code> table reference
     */
    public TLauQualification(Name alias) {
        this(alias, LAU_QUALIFICATION);
    }

    /**
     * Create a <code>lau_qualification</code> table reference
     */
    public TLauQualification() {
        this(DSL.name("lau_qualification"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauQualificationRecord, Integer> getIdentity() {
        return (Identity<LauQualificationRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauQualificationRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauQualification.LAU_QUALIFICATION, DSL.name("KEY_lau_qualification_PRIMARY"), new TableField[] { TLauQualification.LAU_QUALIFICATION.ID }, true);
    }

    @Override
    public TLauQualification as(String alias) {
        return new TLauQualification(DSL.name(alias), this);
    }

    @Override
    public TLauQualification as(Name alias) {
        return new TLauQualification(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauQualification rename(String name) {
        return new TLauQualification(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauQualification rename(Name name) {
        return new TLauQualification(name, null);
    }

    // -------------------------------------------------------------------------
    // Row12 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row12<Integer, Timestamp, Timestamp, Integer, Integer, String, String, Integer, Timestamp, Timestamp, Integer, String> fieldsRow() {
        return (Row12) super.fieldsRow();
    }
}
