/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauQualificationPackage;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauQualificationPackagePo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauQualificationPackageRecord;


/**
 * 投放资质包表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauQualificationPackageDao extends DAOImpl<LauQualificationPackageRecord, LauQualificationPackagePo, Integer> {

    /**
     * Create a new LauQualificationPackageDao without any configuration
     */
    public LauQualificationPackageDao() {
        super(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE, LauQualificationPackagePo.class);
    }

    /**
     * Create a new LauQualificationPackageDao with an attached configuration
     */
    @Autowired
    public LauQualificationPackageDao(Configuration configuration) {
        super(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE, LauQualificationPackagePo.class, configuration);
    }

    @Override
    public Integer getId(LauQualificationPackagePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauQualificationPackagePo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauQualificationPackagePo> fetchById(Integer... values) {
        return fetch(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauQualificationPackagePo fetchOneById(Integer value) {
        return fetchOne(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.ID, value);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauQualificationPackagePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauQualificationPackagePo> fetchByCtime(Timestamp... values) {
        return fetch(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauQualificationPackagePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauQualificationPackagePo> fetchByMtime(Timestamp... values) {
        return fetch(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.MTIME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauQualificationPackagePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauQualificationPackagePo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauQualificationPackagePo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauQualificationPackagePo> fetchByAccountId(Integer... values) {
        return fetch(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauQualificationPackagePo> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<LauQualificationPackagePo> fetchByName(String... values) {
        return fetch(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.NAME, values);
    }
}
