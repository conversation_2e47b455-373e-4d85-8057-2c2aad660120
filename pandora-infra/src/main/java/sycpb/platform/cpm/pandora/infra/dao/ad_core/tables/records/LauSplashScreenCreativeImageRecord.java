/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauSplashScreenCreativeImage;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauSplashScreenCreativeImagePo;


/**
 * 效果闪屏图片表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauSplashScreenCreativeImageRecord extends UpdatableRecordImpl<LauSplashScreenCreativeImageRecord> implements Record10<Long, Integer, Integer, Integer, String, String, Integer, Timestamp, Timestamp, Long> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_splash_screen_creative_image.id</code>. 自增id
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative_image.id</code>. 自增id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_splash_screen_creative_image.creative_id</code>.
     * 创意id
     */
    public void setCreativeId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative_image.creative_id</code>.
     * 创意id
     */
    public Integer getCreativeId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_splash_screen_creative_image.width</code>. 图片宽度
     */
    public void setWidth(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative_image.width</code>. 图片宽度
     */
    public Integer getWidth() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_splash_screen_creative_image.height</code>. 图片高度
     */
    public void setHeight(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative_image.height</code>. 图片高度
     */
    public Integer getHeight() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_splash_screen_creative_image.url</code>. 图片URL
     */
    public void setUrl(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative_image.url</code>. 图片URL
     */
    public String getUrl() {
        return (String) get(4);
    }

    /**
     * Setter for <code>lau_splash_screen_creative_image.md5</code>. 图片MD5
     */
    public void setMd5(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative_image.md5</code>. 图片MD5
     */
    public String getMd5() {
        return (String) get(5);
    }

    /**
     * Setter for <code>lau_splash_screen_creative_image.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative_image.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lau_splash_screen_creative_image.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative_image.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(7);
    }

    /**
     * Setter for <code>lau_splash_screen_creative_image.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative_image.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(8);
    }

    /**
     * Setter for <code>lau_splash_screen_creative_image.material_id</code>.
     * 物料id
     */
    public void setMaterialId(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative_image.material_id</code>.
     * 物料id
     */
    public Long getMaterialId() {
        return (Long) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row10<Long, Integer, Integer, Integer, String, String, Integer, Timestamp, Timestamp, Long> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    @Override
    public Row10<Long, Integer, Integer, Integer, String, String, Integer, Timestamp, Timestamp, Long> valuesRow() {
        return (Row10) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.CREATIVE_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.WIDTH;
    }

    @Override
    public Field<Integer> field4() {
        return TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.HEIGHT;
    }

    @Override
    public Field<String> field5() {
        return TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.URL;
    }

    @Override
    public Field<String> field6() {
        return TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.MD5;
    }

    @Override
    public Field<Integer> field7() {
        return TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field8() {
        return TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.CTIME;
    }

    @Override
    public Field<Timestamp> field9() {
        return TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.MTIME;
    }

    @Override
    public Field<Long> field10() {
        return TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.MATERIAL_ID;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getCreativeId();
    }

    @Override
    public Integer component3() {
        return getWidth();
    }

    @Override
    public Integer component4() {
        return getHeight();
    }

    @Override
    public String component5() {
        return getUrl();
    }

    @Override
    public String component6() {
        return getMd5();
    }

    @Override
    public Integer component7() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component8() {
        return getCtime();
    }

    @Override
    public Timestamp component9() {
        return getMtime();
    }

    @Override
    public Long component10() {
        return getMaterialId();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getCreativeId();
    }

    @Override
    public Integer value3() {
        return getWidth();
    }

    @Override
    public Integer value4() {
        return getHeight();
    }

    @Override
    public String value5() {
        return getUrl();
    }

    @Override
    public String value6() {
        return getMd5();
    }

    @Override
    public Integer value7() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value8() {
        return getCtime();
    }

    @Override
    public Timestamp value9() {
        return getMtime();
    }

    @Override
    public Long value10() {
        return getMaterialId();
    }

    @Override
    public LauSplashScreenCreativeImageRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeImageRecord value2(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeImageRecord value3(Integer value) {
        setWidth(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeImageRecord value4(Integer value) {
        setHeight(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeImageRecord value5(String value) {
        setUrl(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeImageRecord value6(String value) {
        setMd5(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeImageRecord value7(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeImageRecord value8(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeImageRecord value9(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeImageRecord value10(Long value) {
        setMaterialId(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeImageRecord values(Long value1, Integer value2, Integer value3, Integer value4, String value5, String value6, Integer value7, Timestamp value8, Timestamp value9, Long value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauSplashScreenCreativeImageRecord
     */
    public LauSplashScreenCreativeImageRecord() {
        super(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE);
    }

    /**
     * Create a detached, initialised LauSplashScreenCreativeImageRecord
     */
    public LauSplashScreenCreativeImageRecord(Long id, Integer creativeId, Integer width, Integer height, String url, String md5, Integer isDeleted, Timestamp ctime, Timestamp mtime, Long materialId) {
        super(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE);

        setId(id);
        setCreativeId(creativeId);
        setWidth(width);
        setHeight(height);
        setUrl(url);
        setMd5(md5);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setMaterialId(materialId);
    }

    /**
     * Create a detached, initialised LauSplashScreenCreativeImageRecord
     */
    public LauSplashScreenCreativeImageRecord(LauSplashScreenCreativeImagePo value) {
        super(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE);

        if (value != null) {
            setId(value.getId());
            setCreativeId(value.getCreativeId());
            setWidth(value.getWidth());
            setHeight(value.getHeight());
            setUrl(value.getUrl());
            setMd5(value.getMd5());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setMaterialId(value.getMaterialId());
        }
    }
}
