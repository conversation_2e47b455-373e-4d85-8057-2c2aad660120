/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauDynamicRecord;


/**
 * 投放端动态表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauDynamic extends TableImpl<LauDynamicRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_dynamic</code>
     */
    public static final TLauDynamic LAU_DYNAMIC = new TLauDynamic();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauDynamicRecord> getRecordType() {
        return LauDynamicRecord.class;
    }

    /**
     * The column <code>lau_dynamic.id</code>. 自增ID
     */
    public final TableField<LauDynamicRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增ID");

    /**
     * The column <code>lau_dynamic.dynamic_id</code>. 动态id
     */
    public final TableField<LauDynamicRecord, Long> DYNAMIC_ID = createField(DSL.name("dynamic_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "动态id");

    /**
     * The column <code>lau_dynamic.sid</code>. 动态关联的直播预约id
     */
    public final TableField<LauDynamicRecord, Long> SID = createField(DSL.name("sid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "动态关联的直播预约id");

    /**
     * The column <code>lau_dynamic.dynamic_up_mid</code>. 动态up主mid
     */
    public final TableField<LauDynamicRecord, Long> DYNAMIC_UP_MID = createField(DSL.name("dynamic_up_mid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "动态up主mid");

    /**
     * The column <code>lau_dynamic.cover</code>. 动态原始封面，多个取第一个
     */
    public final TableField<LauDynamicRecord, String> COVER = createField(DSL.name("cover"), SQLDataType.VARCHAR(512).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "动态原始封面，多个取第一个");

    /**
     * The column <code>lau_dynamic.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public final TableField<LauDynamicRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除，0是有效，1是删除");

    /**
     * The column <code>lau_dynamic.ctime</code>. 添加时间
     */
    public final TableField<LauDynamicRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column <code>lau_dynamic.mtime</code>. 更新时间
     */
    public final TableField<LauDynamicRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    private TLauDynamic(Name alias, Table<LauDynamicRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauDynamic(Name alias, Table<LauDynamicRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("投放端动态表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_dynamic</code> table reference
     */
    public TLauDynamic(String alias) {
        this(DSL.name(alias), LAU_DYNAMIC);
    }

    /**
     * Create an aliased <code>lau_dynamic</code> table reference
     */
    public TLauDynamic(Name alias) {
        this(alias, LAU_DYNAMIC);
    }

    /**
     * Create a <code>lau_dynamic</code> table reference
     */
    public TLauDynamic() {
        this(DSL.name("lau_dynamic"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauDynamicRecord, Long> getIdentity() {
        return (Identity<LauDynamicRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauDynamicRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauDynamic.LAU_DYNAMIC, DSL.name("KEY_lau_dynamic_PRIMARY"), new TableField[] { TLauDynamic.LAU_DYNAMIC.ID }, true);
    }

    @Override
    public List<UniqueKey<LauDynamicRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TLauDynamic.LAU_DYNAMIC, DSL.name("KEY_lau_dynamic_ix_dynamic_id"), new TableField[] { TLauDynamic.LAU_DYNAMIC.DYNAMIC_ID }, true)
        );
    }

    @Override
    public TLauDynamic as(String alias) {
        return new TLauDynamic(DSL.name(alias), this);
    }

    @Override
    public TLauDynamic as(Name alias) {
        return new TLauDynamic(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauDynamic rename(String name) {
        return new TLauDynamic(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauDynamic rename(Name name) {
        return new TLauDynamic(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<Long, Long, Long, Long, String, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
