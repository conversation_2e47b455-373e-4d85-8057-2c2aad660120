/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 新版定向包额外定向
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResTargetPackageExtraUpgradePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   targetPackageId;
    private Integer   targetPackageTargetType;
    private String    targetPackageTargetValue;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   recommendType;

    public ResTargetPackageExtraUpgradePo() {}

    public ResTargetPackageExtraUpgradePo(ResTargetPackageExtraUpgradePo value) {
        this.id = value.id;
        this.targetPackageId = value.targetPackageId;
        this.targetPackageTargetType = value.targetPackageTargetType;
        this.targetPackageTargetValue = value.targetPackageTargetValue;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.recommendType = value.recommendType;
    }

    public ResTargetPackageExtraUpgradePo(
        Long      id,
        Integer   targetPackageId,
        Integer   targetPackageTargetType,
        String    targetPackageTargetValue,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime,
        Integer   recommendType
    ) {
        this.id = id;
        this.targetPackageId = targetPackageId;
        this.targetPackageTargetType = targetPackageTargetType;
        this.targetPackageTargetValue = targetPackageTargetValue;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
        this.recommendType = recommendType;
    }

    /**
     * Getter for <code>res_target_package_extra_upgrade.id</code>. 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>res_target_package_extra_upgrade.id</code>. 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for
     * <code>res_target_package_extra_upgrade.target_package_id</code>. 定向包id
     */
    public Integer getTargetPackageId() {
        return this.targetPackageId;
    }

    /**
     * Setter for
     * <code>res_target_package_extra_upgrade.target_package_id</code>. 定向包id
     */
    public void setTargetPackageId(Integer targetPackageId) {
        this.targetPackageId = targetPackageId;
    }

    /**
     * Getter for
     * <code>res_target_package_extra_upgrade.target_package_target_type</code>.
     * 定向包定向类型:1-粉丝关系,2-互动行为,3-浏览行为,4-视频二级分区,5-投放粉丝,6-排除粉丝,7-刷次定向
     */
    public Integer getTargetPackageTargetType() {
        return this.targetPackageTargetType;
    }

    /**
     * Setter for
     * <code>res_target_package_extra_upgrade.target_package_target_type</code>.
     * 定向包定向类型:1-粉丝关系,2-互动行为,3-浏览行为,4-视频二级分区,5-投放粉丝,6-排除粉丝,7-刷次定向
     */
    public void setTargetPackageTargetType(Integer targetPackageTargetType) {
        this.targetPackageTargetType = targetPackageTargetType;
    }

    /**
     * Getter for
     * <code>res_target_package_extra_upgrade.target_package_target_value</code>.
     * 定向包定向值
     */
    public String getTargetPackageTargetValue() {
        return this.targetPackageTargetValue;
    }

    /**
     * Setter for
     * <code>res_target_package_extra_upgrade.target_package_target_value</code>.
     * 定向包定向值
     */
    public void setTargetPackageTargetValue(String targetPackageTargetValue) {
        this.targetPackageTargetValue = targetPackageTargetValue;
    }

    /**
     * Getter for <code>res_target_package_extra_upgrade.is_deleted</code>.
     * 软删除:0-有效,1-删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>res_target_package_extra_upgrade.is_deleted</code>.
     * 软删除:0-有效,1-删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>res_target_package_extra_upgrade.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>res_target_package_extra_upgrade.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>res_target_package_extra_upgrade.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>res_target_package_extra_upgrade.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>res_target_package_extra_upgrade.recommend_type</code>.
     * 投放粉丝来源推荐包（0-无 1-核心up主 2-优选up主 3-高潜up主）
     */
    public Integer getRecommendType() {
        return this.recommendType;
    }

    /**
     * Setter for <code>res_target_package_extra_upgrade.recommend_type</code>.
     * 投放粉丝来源推荐包（0-无 1-核心up主 2-优选up主 3-高潜up主）
     */
    public void setRecommendType(Integer recommendType) {
        this.recommendType = recommendType;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ResTargetPackageExtraUpgradePo (");

        sb.append(id);
        sb.append(", ").append(targetPackageId);
        sb.append(", ").append(targetPackageTargetType);
        sb.append(", ").append(targetPackageTargetValue);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(recommendType);

        sb.append(")");
        return sb.toString();
    }
}
