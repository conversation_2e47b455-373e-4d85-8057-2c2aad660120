/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCampaignPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer   campaignId;
    private Integer   accountId;
    private String    campaignName;
    private Integer   costType;
    private Integer   budgetType;
    private Long      budget;
    private Integer   speedMode;
    private Integer   status;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   salesType;
    private Integer   orderId;
    private Integer   campaignStatus;
    private Timestamp campaignStatusMtime;
    private Integer   promotionPurposeType;
    private Integer   originTag;
    private Integer   lauAccountId;
    private Integer   needWakeApp;
    private Integer   adpVersion;
    private Integer   isNewFly;
    private Integer   budgetLimitType;
    private Integer   flag;
    private Integer   deductionSign;
    private Integer   isManaged;
    private String    managedBeginTime;
    private String    managedEndTime;
    private Integer   isGdPlus;
    private Integer   actionId;
    private Integer   crmOrderId;
    private Integer   isMiddleAd;
    private Integer   adType;
    private Integer   supportAuto;

    public LauCampaignPo() {}

    public LauCampaignPo(LauCampaignPo value) {
        this.campaignId = value.campaignId;
        this.accountId = value.accountId;
        this.campaignName = value.campaignName;
        this.costType = value.costType;
        this.budgetType = value.budgetType;
        this.budget = value.budget;
        this.speedMode = value.speedMode;
        this.status = value.status;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.salesType = value.salesType;
        this.orderId = value.orderId;
        this.campaignStatus = value.campaignStatus;
        this.campaignStatusMtime = value.campaignStatusMtime;
        this.promotionPurposeType = value.promotionPurposeType;
        this.originTag = value.originTag;
        this.lauAccountId = value.lauAccountId;
        this.needWakeApp = value.needWakeApp;
        this.adpVersion = value.adpVersion;
        this.isNewFly = value.isNewFly;
        this.budgetLimitType = value.budgetLimitType;
        this.flag = value.flag;
        this.deductionSign = value.deductionSign;
        this.isManaged = value.isManaged;
        this.managedBeginTime = value.managedBeginTime;
        this.managedEndTime = value.managedEndTime;
        this.isGdPlus = value.isGdPlus;
        this.actionId = value.actionId;
        this.crmOrderId = value.crmOrderId;
        this.isMiddleAd = value.isMiddleAd;
        this.adType = value.adType;
        this.supportAuto = value.supportAuto;
    }

    public LauCampaignPo(
        Integer   campaignId,
        Integer   accountId,
        String    campaignName,
        Integer   costType,
        Integer   budgetType,
        Long      budget,
        Integer   speedMode,
        Integer   status,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime,
        Integer   salesType,
        Integer   orderId,
        Integer   campaignStatus,
        Timestamp campaignStatusMtime,
        Integer   promotionPurposeType,
        Integer   originTag,
        Integer   lauAccountId,
        Integer   needWakeApp,
        Integer   adpVersion,
        Integer   isNewFly,
        Integer   budgetLimitType,
        Integer   flag,
        Integer   deductionSign,
        Integer   isManaged,
        String    managedBeginTime,
        String    managedEndTime,
        Integer   isGdPlus,
        Integer   actionId,
        Integer   crmOrderId,
        Integer   isMiddleAd,
        Integer   adType,
        Integer   supportAuto
    ) {
        this.campaignId = campaignId;
        this.accountId = accountId;
        this.campaignName = campaignName;
        this.costType = costType;
        this.budgetType = budgetType;
        this.budget = budget;
        this.speedMode = speedMode;
        this.status = status;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
        this.salesType = salesType;
        this.orderId = orderId;
        this.campaignStatus = campaignStatus;
        this.campaignStatusMtime = campaignStatusMtime;
        this.promotionPurposeType = promotionPurposeType;
        this.originTag = originTag;
        this.lauAccountId = lauAccountId;
        this.needWakeApp = needWakeApp;
        this.adpVersion = adpVersion;
        this.isNewFly = isNewFly;
        this.budgetLimitType = budgetLimitType;
        this.flag = flag;
        this.deductionSign = deductionSign;
        this.isManaged = isManaged;
        this.managedBeginTime = managedBeginTime;
        this.managedEndTime = managedEndTime;
        this.isGdPlus = isGdPlus;
        this.actionId = actionId;
        this.crmOrderId = crmOrderId;
        this.isMiddleAd = isMiddleAd;
        this.adType = adType;
        this.supportAuto = supportAuto;
    }

    /**
     * Getter for <code>lau_campaign.campaign_id</code>. 推广计划ID
     */
    public Integer getCampaignId() {
        return this.campaignId;
    }

    /**
     * Setter for <code>lau_campaign.campaign_id</code>. 推广计划ID
     */
    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    /**
     * Getter for <code>lau_campaign.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>lau_campaign.account_id</code>. 账号ID
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>lau_campaign.campaign_name</code>. 推广计划名称
     */
    public String getCampaignName() {
        return this.campaignName;
    }

    /**
     * Setter for <code>lau_campaign.campaign_name</code>. 推广计划名称
     */
    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    /**
     * Getter for <code>lau_campaign.cost_type</code>. 扣费类型：1-cpm、2-cpc
     */
    public Integer getCostType() {
        return this.costType;
    }

    /**
     * Setter for <code>lau_campaign.cost_type</code>. 扣费类型：1-cpm、2-cpc
     */
    public void setCostType(Integer costType) {
        this.costType = costType;
    }

    /**
     * Getter for <code>lau_campaign.budget_type</code>. 预算类型：1-日预算、2-总预算
     */
    public Integer getBudgetType() {
        return this.budgetType;
    }

    /**
     * Setter for <code>lau_campaign.budget_type</code>. 预算类型：1-日预算、2-总预算
     */
    public void setBudgetType(Integer budgetType) {
        this.budgetType = budgetType;
    }

    /**
     * Getter for <code>lau_campaign.budget</code>. 推广预算
     */
    public Long getBudget() {
        return this.budget;
    }

    /**
     * Setter for <code>lau_campaign.budget</code>. 推广预算
     */
    public void setBudget(Long budget) {
        this.budget = budget;
    }

    /**
     * Getter for <code>lau_campaign.speed_mode</code>. 投放模式（1-匀速投放，2-加速投放）
     */
    public Integer getSpeedMode() {
        return this.speedMode;
    }

    /**
     * Setter for <code>lau_campaign.speed_mode</code>. 投放模式（1-匀速投放，2-加速投放）
     */
    public void setSpeedMode(Integer speedMode) {
        this.speedMode = speedMode;
    }

    /**
     * Getter for <code>lau_campaign.status</code>. 状态（1-有效，2-暂停, 3-删除）
     */
    public Integer getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>lau_campaign.status</code>. 状态（1-有效，2-暂停, 3-删除）
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * Getter for <code>lau_campaign.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_campaign.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_campaign.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_campaign.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_campaign.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_campaign.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_campaign.sales_type</code>. 售卖类型 11-CPM, 12-CPC,
     * 21-GD
     */
    public Integer getSalesType() {
        return this.salesType;
    }

    /**
     * Setter for <code>lau_campaign.sales_type</code>. 售卖类型 11-CPM, 12-CPC,
     * 21-GD
     */
    public void setSalesType(Integer salesType) {
        this.salesType = salesType;
    }

    /**
     * Getter for <code>lau_campaign.order_id</code>. fc_order.id 订单ID,
     * CPM广告订单ID为0
     */
    public Integer getOrderId() {
        return this.orderId;
    }

    /**
     * Setter for <code>lau_campaign.order_id</code>. fc_order.id 订单ID,
     * CPM广告订单ID为0
     */
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /**
     * Getter for <code>lau_campaign.campaign_status</code>.
     * 计划状态:1-有效,2-已暂停,3-已结束,4-已删除,5-预算超限
     */
    public Integer getCampaignStatus() {
        return this.campaignStatus;
    }

    /**
     * Setter for <code>lau_campaign.campaign_status</code>.
     * 计划状态:1-有效,2-已暂停,3-已结束,4-已删除,5-预算超限
     */
    public void setCampaignStatus(Integer campaignStatus) {
        this.campaignStatus = campaignStatus;
    }

    /**
     * Getter for <code>lau_campaign.campaign_status_mtime</code>. 单元状态更新时间
     */
    public Timestamp getCampaignStatusMtime() {
        return this.campaignStatusMtime;
    }

    /**
     * Setter for <code>lau_campaign.campaign_status_mtime</code>. 单元状态更新时间
     */
    public void setCampaignStatusMtime(Timestamp campaignStatusMtime) {
        this.campaignStatusMtime = campaignStatusMtime;
    }

    /**
     * Getter for <code>lau_campaign.promotion_purpose_type</code>. 推广目的类型（2-落地页
     * 4-应用下载 5-会员购 6-上架游戏 7-投稿内容 8-直播间 9-电商
     */
    public Integer getPromotionPurposeType() {
        return this.promotionPurposeType;
    }

    /**
     * Setter for <code>lau_campaign.promotion_purpose_type</code>. 推广目的类型（2-落地页
     * 4-应用下载 5-会员购 6-上架游戏 7-投稿内容 8-直播间 9-电商
     */
    public void setPromotionPurposeType(Integer promotionPurposeType) {
        this.promotionPurposeType = promotionPurposeType;
    }

    /**
     * Getter for <code>lau_campaign.origin_tag</code>. 来源标签 0 默认 1adx 2dpa 3ssa
     */
    public Integer getOriginTag() {
        return this.originTag;
    }

    /**
     * Setter for <code>lau_campaign.origin_tag</code>. 来源标签 0 默认 1adx 2dpa 3ssa
     */
    public void setOriginTag(Integer originTag) {
        this.originTag = originTag;
    }

    /**
     * Getter for <code>lau_campaign.lau_account_id</code>. 投放端账号信息
     */
    public Integer getLauAccountId() {
        return this.lauAccountId;
    }

    /**
     * Setter for <code>lau_campaign.lau_account_id</code>. 投放端账号信息
     */
    public void setLauAccountId(Integer lauAccountId) {
        this.lauAccountId = lauAccountId;
    }

    /**
     * Getter for <code>lau_campaign.need_wake_app</code>. 唤起外部APP：0-无须唤起 1-需要唤起
     */
    public Integer getNeedWakeApp() {
        return this.needWakeApp;
    }

    /**
     * Setter for <code>lau_campaign.need_wake_app</code>. 唤起外部APP：0-无须唤起 1-需要唤起
     */
    public void setNeedWakeApp(Integer needWakeApp) {
        this.needWakeApp = needWakeApp;
    }

    /**
     * Getter for <code>lau_campaign.adp_version</code>. 广告平台版本号 0-老版 1-新版
     */
    public Integer getAdpVersion() {
        return this.adpVersion;
    }

    /**
     * Setter for <code>lau_campaign.adp_version</code>. 广告平台版本号 0-老版 1-新版
     */
    public void setAdpVersion(Integer adpVersion) {
        this.adpVersion = adpVersion;
    }

    /**
     * Getter for <code>lau_campaign.is_new_fly</code>. 新起飞：0-否 1-是
     */
    public Integer getIsNewFly() {
        return this.isNewFly;
    }

    /**
     * Setter for <code>lau_campaign.is_new_fly</code>. 新起飞：0-否 1-是
     */
    public void setIsNewFly(Integer isNewFly) {
        this.isNewFly = isNewFly;
    }

    /**
     * Getter for <code>lau_campaign.budget_limit_type</code>.
     * 预算限制类型，1：指定预算，2：不限预算
     */
    public Integer getBudgetLimitType() {
        return this.budgetLimitType;
    }

    /**
     * Setter for <code>lau_campaign.budget_limit_type</code>.
     * 预算限制类型，1：指定预算，2：不限预算
     */
    public void setBudgetLimitType(Integer budgetLimitType) {
        this.budgetLimitType = budgetLimitType;
    }

    /**
     * Getter for <code>lau_campaign.flag</code>. 多种类型、枚举等通用字段。 0~100 来源
     */
    public Integer getFlag() {
        return this.flag;
    }

    /**
     * Setter for <code>lau_campaign.flag</code>. 多种类型、枚举等通用字段。 0~100 来源
     */
    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    /**
     * Getter for <code>lau_campaign.deduction_sign</code>. 扣费标识位（0-默认 21-起飞签约托管
     * 22-起飞签约订单 23-起飞-现金-托管 24-起飞-激励金-托管 25-起飞-起飞币-托管）
     */
    public Integer getDeductionSign() {
        return this.deductionSign;
    }

    /**
     * Setter for <code>lau_campaign.deduction_sign</code>. 扣费标识位（0-默认 21-起飞签约托管
     * 22-起飞签约订单 23-起飞-现金-托管 24-起飞-激励金-托管 25-起飞-起飞币-托管）
     */
    public void setDeductionSign(Integer deductionSign) {
        this.deductionSign = deductionSign;
    }

    /**
     * Getter for <code>lau_campaign.is_managed</code>. 是否是托管计划（0-非托管 1-托管）
     */
    public Integer getIsManaged() {
        return this.isManaged;
    }

    /**
     * Setter for <code>lau_campaign.is_managed</code>. 是否是托管计划（0-非托管 1-托管）
     */
    public void setIsManaged(Integer isManaged) {
        this.isManaged = isManaged;
    }

    /**
     * Getter for <code>lau_campaign.managed_begin_time</code>. 托管计划投放开始日期
     */
    public String getManagedBeginTime() {
        return this.managedBeginTime;
    }

    /**
     * Setter for <code>lau_campaign.managed_begin_time</code>. 托管计划投放开始日期
     */
    public void setManagedBeginTime(String managedBeginTime) {
        this.managedBeginTime = managedBeginTime;
    }

    /**
     * Getter for <code>lau_campaign.managed_end_time</code>. 托管计划投放结束日期
     */
    public String getManagedEndTime() {
        return this.managedEndTime;
    }

    /**
     * Setter for <code>lau_campaign.managed_end_time</code>. 托管计划投放结束日期
     */
    public void setManagedEndTime(String managedEndTime) {
        this.managedEndTime = managedEndTime;
    }

    /**
     * Getter for <code>lau_campaign.is_gd_plus</code>. 是否gd+：0- 否 1-是
     */
    public Integer getIsGdPlus() {
        return this.isGdPlus;
    }

    /**
     * Setter for <code>lau_campaign.is_gd_plus</code>. 是否gd+：0- 否 1-是
     */
    public void setIsGdPlus(Integer isGdPlus) {
        this.isGdPlus = isGdPlus;
    }

    /**
     * Getter for <code>lau_campaign.action_id</code>. 活动id
     */
    public Integer getActionId() {
        return this.actionId;
    }

    /**
     * Setter for <code>lau_campaign.action_id</code>. 活动id
     */
    public void setActionId(Integer actionId) {
        this.actionId = actionId;
    }

    /**
     * Getter for <code>lau_campaign.crm_order_id</code>. 绑定的crm订单号（起飞业务）
     */
    public Integer getCrmOrderId() {
        return this.crmOrderId;
    }

    /**
     * Setter for <code>lau_campaign.crm_order_id</code>. 绑定的crm订单号（起飞业务）
     */
    public void setCrmOrderId(Integer crmOrderId) {
        this.crmOrderId = crmOrderId;
    }

    /**
     * Getter for <code>lau_campaign.is_middle_ad</code>. 是新中台广告：0-否 1-是
     */
    public Integer getIsMiddleAd() {
        return this.isMiddleAd;
    }

    /**
     * Setter for <code>lau_campaign.is_middle_ad</code>. 是新中台广告：0-否 1-是
     */
    public void setIsMiddleAd(Integer isMiddleAd) {
        this.isMiddleAd = isMiddleAd;
    }

    /**
     * Getter for <code>lau_campaign.ad_type</code>. 广告类型：0-所有广告 1-搜索广告
     */
    public Integer getAdType() {
        return this.adType;
    }

    /**
     * Setter for <code>lau_campaign.ad_type</code>. 广告类型：0-所有广告 1-搜索广告
     */
    public void setAdType(Integer adType) {
        this.adType = adType;
    }

    /**
     * Getter for <code>lau_campaign.support_auto</code>. 是否支持自动投放: 0-否, 1-是
     */
    public Integer getSupportAuto() {
        return this.supportAuto;
    }

    /**
     * Setter for <code>lau_campaign.support_auto</code>. 是否支持自动投放: 0-否, 1-是
     */
    public void setSupportAuto(Integer supportAuto) {
        this.supportAuto = supportAuto;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauCampaignPo (");

        sb.append(campaignId);
        sb.append(", ").append(accountId);
        sb.append(", ").append(campaignName);
        sb.append(", ").append(costType);
        sb.append(", ").append(budgetType);
        sb.append(", ").append(budget);
        sb.append(", ").append(speedMode);
        sb.append(", ").append(status);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(salesType);
        sb.append(", ").append(orderId);
        sb.append(", ").append(campaignStatus);
        sb.append(", ").append(campaignStatusMtime);
        sb.append(", ").append(promotionPurposeType);
        sb.append(", ").append(originTag);
        sb.append(", ").append(lauAccountId);
        sb.append(", ").append(needWakeApp);
        sb.append(", ").append(adpVersion);
        sb.append(", ").append(isNewFly);
        sb.append(", ").append(budgetLimitType);
        sb.append(", ").append(flag);
        sb.append(", ").append(deductionSign);
        sb.append(", ").append(isManaged);
        sb.append(", ").append(managedBeginTime);
        sb.append(", ").append(managedEndTime);
        sb.append(", ").append(isGdPlus);
        sb.append(", ").append(actionId);
        sb.append(", ").append(crmOrderId);
        sb.append(", ").append(isMiddleAd);
        sb.append(", ").append(adType);
        sb.append(", ").append(supportAuto);

        sb.append(")");
        return sb.toString();
    }
}
