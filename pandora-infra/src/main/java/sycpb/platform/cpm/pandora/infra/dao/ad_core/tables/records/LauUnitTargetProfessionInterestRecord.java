/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitTargetProfessionInterest;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitTargetProfessionInterestPo;


/**
 * 单元-行业兴趣人群定向表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitTargetProfessionInterestRecord extends UpdatableRecordImpl<LauUnitTargetProfessionInterestRecord> implements Record6<Inte<PERSON>, Inte<PERSON>, Inte<PERSON>, Timestamp, Timestamp, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_unit_target_profession_interest.id</code>.
     */
    public void setId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_unit_target_profession_interest.id</code>.
     */
    public Integer getId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_unit_target_profession_interest.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_unit_target_profession_interest.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_unit_target_profession_interest.crowd_id</code>.
     * 人群包ID
     */
    public void setCrowdId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_unit_target_profession_interest.crowd_id</code>.
     * 人群包ID
     */
    public Integer getCrowdId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_unit_target_profession_interest.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_unit_target_profession_interest.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(3);
    }

    /**
     * Setter for <code>lau_unit_target_profession_interest.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_unit_target_profession_interest.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(4);
    }

    /**
     * Setter for <code>lau_unit_target_profession_interest.is_deleted</code>.
     * 软删除 0-有效，1-删除
     */
    public void setIsDeleted(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_unit_target_profession_interest.is_deleted</code>.
     * 软删除 0-有效，1-删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<Integer, Integer, Integer, Timestamp, Timestamp, Integer> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<Integer, Integer, Integer, Timestamp, Timestamp, Integer> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<Integer> field1() {
        return TLauUnitTargetProfessionInterest.LAU_UNIT_TARGET_PROFESSION_INTEREST.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauUnitTargetProfessionInterest.LAU_UNIT_TARGET_PROFESSION_INTEREST.UNIT_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TLauUnitTargetProfessionInterest.LAU_UNIT_TARGET_PROFESSION_INTEREST.CROWD_ID;
    }

    @Override
    public Field<Timestamp> field4() {
        return TLauUnitTargetProfessionInterest.LAU_UNIT_TARGET_PROFESSION_INTEREST.CTIME;
    }

    @Override
    public Field<Timestamp> field5() {
        return TLauUnitTargetProfessionInterest.LAU_UNIT_TARGET_PROFESSION_INTEREST.MTIME;
    }

    @Override
    public Field<Integer> field6() {
        return TLauUnitTargetProfessionInterest.LAU_UNIT_TARGET_PROFESSION_INTEREST.IS_DELETED;
    }

    @Override
    public Integer component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getUnitId();
    }

    @Override
    public Integer component3() {
        return getCrowdId();
    }

    @Override
    public Timestamp component4() {
        return getCtime();
    }

    @Override
    public Timestamp component5() {
        return getMtime();
    }

    @Override
    public Integer component6() {
        return getIsDeleted();
    }

    @Override
    public Integer value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getUnitId();
    }

    @Override
    public Integer value3() {
        return getCrowdId();
    }

    @Override
    public Timestamp value4() {
        return getCtime();
    }

    @Override
    public Timestamp value5() {
        return getMtime();
    }

    @Override
    public Integer value6() {
        return getIsDeleted();
    }

    @Override
    public LauUnitTargetProfessionInterestRecord value1(Integer value) {
        setId(value);
        return this;
    }

    @Override
    public LauUnitTargetProfessionInterestRecord value2(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauUnitTargetProfessionInterestRecord value3(Integer value) {
        setCrowdId(value);
        return this;
    }

    @Override
    public LauUnitTargetProfessionInterestRecord value4(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauUnitTargetProfessionInterestRecord value5(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauUnitTargetProfessionInterestRecord value6(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauUnitTargetProfessionInterestRecord values(Integer value1, Integer value2, Integer value3, Timestamp value4, Timestamp value5, Integer value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauUnitTargetProfessionInterestRecord
     */
    public LauUnitTargetProfessionInterestRecord() {
        super(TLauUnitTargetProfessionInterest.LAU_UNIT_TARGET_PROFESSION_INTEREST);
    }

    /**
     * Create a detached, initialised LauUnitTargetProfessionInterestRecord
     */
    public LauUnitTargetProfessionInterestRecord(Integer id, Integer unitId, Integer crowdId, Timestamp ctime, Timestamp mtime, Integer isDeleted) {
        super(TLauUnitTargetProfessionInterest.LAU_UNIT_TARGET_PROFESSION_INTEREST);

        setId(id);
        setUnitId(unitId);
        setCrowdId(crowdId);
        setCtime(ctime);
        setMtime(mtime);
        setIsDeleted(isDeleted);
    }

    /**
     * Create a detached, initialised LauUnitTargetProfessionInterestRecord
     */
    public LauUnitTargetProfessionInterestRecord(LauUnitTargetProfessionInterestPo value) {
        super(TLauUnitTargetProfessionInterest.LAU_UNIT_TARGET_PROFESSION_INTEREST);

        if (value != null) {
            setId(value.getId());
            setUnitId(value.getUnitId());
            setCrowdId(value.getCrowdId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setIsDeleted(value.getIsDeleted());
        }
    }
}
