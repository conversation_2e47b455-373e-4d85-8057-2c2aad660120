/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauStoryCommonComponent;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauStoryCommonComponentPo;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauStoryCommonComponentRecord extends UpdatableRecordImpl<LauStoryCommonComponentRecord> implements Record14<Long, Timestamp, Timestamp, Integer, Integer, Long, String, String, String, String, String, Integer, Integer, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_story_common_component.id</code>. id
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_story_common_component.id</code>. id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_story_common_component.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_story_common_component.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(1);
    }

    /**
     * Setter for <code>lau_story_common_component.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_story_common_component.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(2);
    }

    /**
     * Setter for <code>lau_story_common_component.is_deleted</code>. 软删除:
     * 0-未删除,1-已删除
     */
    public void setIsDeleted(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_story_common_component.is_deleted</code>. 软删除:
     * 0-未删除,1-已删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_story_common_component.account_id</code>. 账号id
     */
    public void setAccountId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_story_common_component.account_id</code>. 账号id
     */
    public Integer getAccountId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_story_common_component.component_id</code>.
     * story组件id
     */
    public void setComponentId(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_story_common_component.component_id</code>.
     * story组件id
     */
    public Long getComponentId() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>lau_story_common_component.component_name</code>. 组件名称
     */
    public void setComponentName(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_story_common_component.component_name</code>. 组件名称
     */
    public String getComponentName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>lau_story_common_component.title</code>. 标题
     */
    public void setTitle(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_story_common_component.title</code>. 标题
     */
    public String getTitle() {
        return (String) get(7);
    }

    /**
     * Setter for <code>lau_story_common_component.description</code>. 描述
     */
    public void setDescription(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_story_common_component.description</code>. 描述
     */
    public String getDescription() {
        return (String) get(8);
    }

    /**
     * Setter for <code>lau_story_common_component.image_url</code>. 图片url
     */
    public void setImageUrl(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_story_common_component.image_url</code>. 图片url
     */
    public String getImageUrl() {
        return (String) get(9);
    }

    /**
     * Setter for <code>lau_story_common_component.image_md5</code>. 图片md5
     */
    public void setImageMd5(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_story_common_component.image_md5</code>. 图片md5
     */
    public String getImageMd5() {
        return (String) get(10);
    }

    /**
     * Setter for <code>lau_story_common_component.button_id</code>. 按钮id
     */
    public void setButtonId(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_story_common_component.button_id</code>. 按钮id
     */
    public Integer getButtonId() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>lau_story_common_component.button_type</code>. 按钮类型
     */
    public void setButtonType(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_story_common_component.button_type</code>. 按钮类型
     */
    public Integer getButtonType() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>lau_story_common_component.button_text</code>. 按钮文案
     */
    public void setButtonText(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_story_common_component.button_text</code>. 按钮文案
     */
    public String getButtonText() {
        return (String) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<Long, Timestamp, Timestamp, Integer, Integer, Long, String, String, String, String, String, Integer, Integer, String> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<Long, Timestamp, Timestamp, Integer, Integer, Long, String, String, String, String, String, Integer, Integer, String> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.ID;
    }

    @Override
    public Field<Timestamp> field2() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.CTIME;
    }

    @Override
    public Field<Timestamp> field3() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.MTIME;
    }

    @Override
    public Field<Integer> field4() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.IS_DELETED;
    }

    @Override
    public Field<Integer> field5() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.ACCOUNT_ID;
    }

    @Override
    public Field<Long> field6() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.COMPONENT_ID;
    }

    @Override
    public Field<String> field7() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.COMPONENT_NAME;
    }

    @Override
    public Field<String> field8() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.TITLE;
    }

    @Override
    public Field<String> field9() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.DESCRIPTION;
    }

    @Override
    public Field<String> field10() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.IMAGE_URL;
    }

    @Override
    public Field<String> field11() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.IMAGE_MD5;
    }

    @Override
    public Field<Integer> field12() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.BUTTON_ID;
    }

    @Override
    public Field<Integer> field13() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.BUTTON_TYPE;
    }

    @Override
    public Field<String> field14() {
        return TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT.BUTTON_TEXT;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Timestamp component2() {
        return getCtime();
    }

    @Override
    public Timestamp component3() {
        return getMtime();
    }

    @Override
    public Integer component4() {
        return getIsDeleted();
    }

    @Override
    public Integer component5() {
        return getAccountId();
    }

    @Override
    public Long component6() {
        return getComponentId();
    }

    @Override
    public String component7() {
        return getComponentName();
    }

    @Override
    public String component8() {
        return getTitle();
    }

    @Override
    public String component9() {
        return getDescription();
    }

    @Override
    public String component10() {
        return getImageUrl();
    }

    @Override
    public String component11() {
        return getImageMd5();
    }

    @Override
    public Integer component12() {
        return getButtonId();
    }

    @Override
    public Integer component13() {
        return getButtonType();
    }

    @Override
    public String component14() {
        return getButtonText();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Timestamp value2() {
        return getCtime();
    }

    @Override
    public Timestamp value3() {
        return getMtime();
    }

    @Override
    public Integer value4() {
        return getIsDeleted();
    }

    @Override
    public Integer value5() {
        return getAccountId();
    }

    @Override
    public Long value6() {
        return getComponentId();
    }

    @Override
    public String value7() {
        return getComponentName();
    }

    @Override
    public String value8() {
        return getTitle();
    }

    @Override
    public String value9() {
        return getDescription();
    }

    @Override
    public String value10() {
        return getImageUrl();
    }

    @Override
    public String value11() {
        return getImageMd5();
    }

    @Override
    public Integer value12() {
        return getButtonId();
    }

    @Override
    public Integer value13() {
        return getButtonType();
    }

    @Override
    public String value14() {
        return getButtonText();
    }

    @Override
    public LauStoryCommonComponentRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value2(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value3(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value4(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value5(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value6(Long value) {
        setComponentId(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value7(String value) {
        setComponentName(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value8(String value) {
        setTitle(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value9(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value10(String value) {
        setImageUrl(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value11(String value) {
        setImageMd5(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value12(Integer value) {
        setButtonId(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value13(Integer value) {
        setButtonType(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord value14(String value) {
        setButtonText(value);
        return this;
    }

    @Override
    public LauStoryCommonComponentRecord values(Long value1, Timestamp value2, Timestamp value3, Integer value4, Integer value5, Long value6, String value7, String value8, String value9, String value10, String value11, Integer value12, Integer value13, String value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauStoryCommonComponentRecord
     */
    public LauStoryCommonComponentRecord() {
        super(TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT);
    }

    /**
     * Create a detached, initialised LauStoryCommonComponentRecord
     */
    public LauStoryCommonComponentRecord(Long id, Timestamp ctime, Timestamp mtime, Integer isDeleted, Integer accountId, Long componentId, String componentName, String title, String description, String imageUrl, String imageMd5, Integer buttonId, Integer buttonType, String buttonText) {
        super(TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT);

        setId(id);
        setCtime(ctime);
        setMtime(mtime);
        setIsDeleted(isDeleted);
        setAccountId(accountId);
        setComponentId(componentId);
        setComponentName(componentName);
        setTitle(title);
        setDescription(description);
        setImageUrl(imageUrl);
        setImageMd5(imageMd5);
        setButtonId(buttonId);
        setButtonType(buttonType);
        setButtonText(buttonText);
    }

    /**
     * Create a detached, initialised LauStoryCommonComponentRecord
     */
    public LauStoryCommonComponentRecord(LauStoryCommonComponentPo value) {
        super(TLauStoryCommonComponent.LAU_STORY_COMMON_COMPONENT);

        if (value != null) {
            setId(value.getId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setIsDeleted(value.getIsDeleted());
            setAccountId(value.getAccountId());
            setComponentId(value.getComponentId());
            setComponentName(value.getComponentName());
            setTitle(value.getTitle());
            setDescription(value.getDescription());
            setImageUrl(value.getImageUrl());
            setImageMd5(value.getImageMd5());
            setButtonId(value.getButtonId());
            setButtonType(value.getButtonType());
            setButtonText(value.getButtonText());
        }
    }
}
