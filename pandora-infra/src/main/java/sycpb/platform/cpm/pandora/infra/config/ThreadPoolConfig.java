package sycpb.platform.cpm.pandora.infra.config;

import io.vavr.Tuple;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Configuration
@PropertySource(value = "paladin://thread_pool.yaml")
public class ThreadPoolConfig {
    public static final String TP_MAP = "threadPoolMap";

    @Data
    public static class Properties {
        private Integer corePoolSize;
        private Integer maximumPoolSize;
        private Integer queueCapacity;
        private Integer keepAliveTimeSec;
    }

    @Bean
    @ConfigurationProperties("thread-pool")
    public Map<String, Properties> executorPropertyMap() {
        return new HashMap<>();
    }

    @Bean(TP_MAP)
    public Map<String, ThreadPoolExecutor> executorMap(Map<String, Properties> propertiesMap) {
        return propertiesMap.entrySet()
                .stream()
                .map(x -> {
                    final var properties = x.getValue();
                    if (!NumberUtils.isPositive(properties.getMaximumPoolSize())) return null;

                    final var executor = new ThreadPoolExecutor(
                            Optional.ofNullable(properties.getCorePoolSize()).orElse(1),
                            properties.getMaximumPoolSize(),
                            Optional.ofNullable(properties.getKeepAliveTimeSec()).orElse(0),
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(Optional.ofNullable(properties.getQueueCapacity()).orElse(Integer.MAX_VALUE)));
                    return Tuple.of(x.getKey(), executor);
                }).filter(Objects::nonNull)
                .collect(Collectors.toMap(x -> x._1, x -> x._2));
    }
}
