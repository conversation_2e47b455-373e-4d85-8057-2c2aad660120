package sycpb.platform.cpm.pandora.infra.compare;

import java.util.Objects;

public class OperationType {
    public static final int INSERT = 0;
    public static final int DELETE = 1;
    public static final int UPDATE = 2;

    public static boolean isDelete(Integer x) {
        return Objects.equals(x, DELETE);
    }

    public static boolean isInsert(Integer x) {
        return Objects.equals(x, INSERT);
    }

    public static boolean isUpdate(Integer x) {
        return Objects.equals(x, UPDATE);
    }
}
