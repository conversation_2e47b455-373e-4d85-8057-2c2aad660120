/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitGoodsRecord;


/**
 * 单元-商品表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauUnitGoods extends TableImpl<LauUnitGoodsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_unit_goods</code>
     */
    public static final TLauUnitGoods LAU_UNIT_GOODS = new TLauUnitGoods();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauUnitGoodsRecord> getRecordType() {
        return LauUnitGoodsRecord.class;
    }

    /**
     * The column <code>lau_unit_goods.id</code>. id
     */
    public final TableField<LauUnitGoodsRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "id");

    /**
     * The column <code>lau_unit_goods.unit_id</code>. 单元id
     */
    public final TableField<LauUnitGoodsRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元id");

    /**
     * The column <code>lau_unit_goods.item_id</code>. 商品id
     */
    public final TableField<LauUnitGoodsRecord, Long> ITEM_ID = createField(DSL.name("item_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "商品id");

    /**
     * The column <code>lau_unit_goods.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public final TableField<LauUnitGoodsRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除，0是有效，1是删除");

    /**
     * The column <code>lau_unit_goods.ctime</code>. 创建时间
     */
    public final TableField<LauUnitGoodsRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_unit_goods.mtime</code>. 更新时间
     */
    public final TableField<LauUnitGoodsRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    private TLauUnitGoods(Name alias, Table<LauUnitGoodsRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauUnitGoods(Name alias, Table<LauUnitGoodsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("单元-商品表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_unit_goods</code> table reference
     */
    public TLauUnitGoods(String alias) {
        this(DSL.name(alias), LAU_UNIT_GOODS);
    }

    /**
     * Create an aliased <code>lau_unit_goods</code> table reference
     */
    public TLauUnitGoods(Name alias) {
        this(alias, LAU_UNIT_GOODS);
    }

    /**
     * Create a <code>lau_unit_goods</code> table reference
     */
    public TLauUnitGoods() {
        this(DSL.name("lau_unit_goods"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauUnitGoodsRecord, Long> getIdentity() {
        return (Identity<LauUnitGoodsRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauUnitGoodsRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauUnitGoods.LAU_UNIT_GOODS, DSL.name("KEY_lau_unit_goods_PRIMARY"), new TableField[] { TLauUnitGoods.LAU_UNIT_GOODS.ID }, true);
    }

    @Override
    public TLauUnitGoods as(String alias) {
        return new TLauUnitGoods(DSL.name(alias), this);
    }

    @Override
    public TLauUnitGoods as(Name alias) {
        return new TLauUnitGoods(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitGoods rename(String name) {
        return new TLauUnitGoods(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitGoods rename(Name name) {
        return new TLauUnitGoods(name, null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Integer, Long, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row6) super.fieldsRow();
    }
}
