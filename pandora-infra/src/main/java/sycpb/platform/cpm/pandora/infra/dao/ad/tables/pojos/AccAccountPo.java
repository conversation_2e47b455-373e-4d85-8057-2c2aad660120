/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;


/**
 * s_support_pickup
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AccAccountPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer   accountId;
    private String    username;
    private String    mobile;
    private Integer   passwordStrength;
    private String    salt;
    private String    saltPassword;
    private Integer   status;
    private Integer   crmCustomerId;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   isDeleted;
    private Integer   orderType;
    private Long      mid;
    private Integer   accountType;
    private Timestamp activeTime;
    private String    name;
    private String    icpRecordNumber;
    private String    icpInfoImage;
    private String    brandDomain;
    private Integer   userType;
    private Integer   adStatus;
    private Integer   version;
    private Integer   categoryFirstId;
    private Integer   categorySecondId;
    private String    remark;
    private Integer   isAgent;
    private Integer   agentType;
    private Integer   businessRoleId;
    private String    companyName;
    private Integer   areaId;
    private Integer   dependencyAgentId;
    private String    websiteName;
    private String    weibo;
    private String    internalLinkman;
    private String    linkmanAddress;
    private String    bank;
    private Integer   qualificationId;
    private String    businessLicenceCode;
    private Date      businessLicenceExpireDate;
    private Integer   isBusinessLicenceIndefinite;
    private String    legalPersonName;
    private Date      legalPersonIdcardExpireDate;
    private Integer   isLegalPersonIdcardIndefinite;
    private String    auditRemark;
    private Integer   accountStatus;
    private String    linkmanEmail;
    private Integer   gdStatus;
    private Date      agentAuthExpireDate;
    private Integer   isInner;
    private Integer   departmentId;
    private Integer   isSupportSeller;
    private Integer   isSupportGame;
    private Integer   isSupportDpa;
    private Integer   isSupportContent;
    private String    productLine;
    private String    phoneNumber;
    private Integer   idcardType;
    private String    idcardNumber;
    private Timestamp idcardExpireDate;
    private String    personalAddress;
    private Integer   isIdcardIndefinite;
    private String    personalName;
    private Integer   groupId;
    private Integer   productLineId;
    private Integer   productId;
    private Integer   isSupportPickup;
    private Integer   isSupportMas;
    private Integer   autoUpdateLabel;
    private Integer   isSupportFly;
    private Integer   allowCashPay;
    private Integer   allowIncentiveBonusPay;
    private Integer   customerId;
    private String    creator;
    private Integer   paymentPeriod;
    private Integer   isSupportLocalAd;
    private Integer   firstIndustryTagId;
    private Integer   secondIndustryTagId;
    private Integer   thirdIndustryTagId;
    private Integer   commerceCategoryFirstId;
    private Integer   commerceCategorySecondId;
    private Integer   allowSigningBonusPay;
    private Integer   allowFlyCoinPay;
    private Integer   allowFlowTicketPay;
    private Integer   financeType;
    private Integer   hasMgkForm;
    private Integer   mgkFormPrivacyPolicy;
    private Integer   showToCustomer;
    private Integer   isSupportCluePass;
    private Integer   promotionType;
    private Integer   unitedFirstIndustryId;
    private Integer   unitedSecondIndustryId;
    private Integer   unitedThirdIndustryId;
    private Integer   oldAgentId;

    public AccAccountPo() {}

    public AccAccountPo(AccAccountPo value) {
        this.accountId = value.accountId;
        this.username = value.username;
        this.mobile = value.mobile;
        this.passwordStrength = value.passwordStrength;
        this.salt = value.salt;
        this.saltPassword = value.saltPassword;
        this.status = value.status;
        this.crmCustomerId = value.crmCustomerId;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.isDeleted = value.isDeleted;
        this.orderType = value.orderType;
        this.mid = value.mid;
        this.accountType = value.accountType;
        this.activeTime = value.activeTime;
        this.name = value.name;
        this.icpRecordNumber = value.icpRecordNumber;
        this.icpInfoImage = value.icpInfoImage;
        this.brandDomain = value.brandDomain;
        this.userType = value.userType;
        this.adStatus = value.adStatus;
        this.version = value.version;
        this.categoryFirstId = value.categoryFirstId;
        this.categorySecondId = value.categorySecondId;
        this.remark = value.remark;
        this.isAgent = value.isAgent;
        this.agentType = value.agentType;
        this.businessRoleId = value.businessRoleId;
        this.companyName = value.companyName;
        this.areaId = value.areaId;
        this.dependencyAgentId = value.dependencyAgentId;
        this.websiteName = value.websiteName;
        this.weibo = value.weibo;
        this.internalLinkman = value.internalLinkman;
        this.linkmanAddress = value.linkmanAddress;
        this.bank = value.bank;
        this.qualificationId = value.qualificationId;
        this.businessLicenceCode = value.businessLicenceCode;
        this.businessLicenceExpireDate = value.businessLicenceExpireDate;
        this.isBusinessLicenceIndefinite = value.isBusinessLicenceIndefinite;
        this.legalPersonName = value.legalPersonName;
        this.legalPersonIdcardExpireDate = value.legalPersonIdcardExpireDate;
        this.isLegalPersonIdcardIndefinite = value.isLegalPersonIdcardIndefinite;
        this.auditRemark = value.auditRemark;
        this.accountStatus = value.accountStatus;
        this.linkmanEmail = value.linkmanEmail;
        this.gdStatus = value.gdStatus;
        this.agentAuthExpireDate = value.agentAuthExpireDate;
        this.isInner = value.isInner;
        this.departmentId = value.departmentId;
        this.isSupportSeller = value.isSupportSeller;
        this.isSupportGame = value.isSupportGame;
        this.isSupportDpa = value.isSupportDpa;
        this.isSupportContent = value.isSupportContent;
        this.productLine = value.productLine;
        this.phoneNumber = value.phoneNumber;
        this.idcardType = value.idcardType;
        this.idcardNumber = value.idcardNumber;
        this.idcardExpireDate = value.idcardExpireDate;
        this.personalAddress = value.personalAddress;
        this.isIdcardIndefinite = value.isIdcardIndefinite;
        this.personalName = value.personalName;
        this.groupId = value.groupId;
        this.productLineId = value.productLineId;
        this.productId = value.productId;
        this.isSupportPickup = value.isSupportPickup;
        this.isSupportMas = value.isSupportMas;
        this.autoUpdateLabel = value.autoUpdateLabel;
        this.isSupportFly = value.isSupportFly;
        this.allowCashPay = value.allowCashPay;
        this.allowIncentiveBonusPay = value.allowIncentiveBonusPay;
        this.customerId = value.customerId;
        this.creator = value.creator;
        this.paymentPeriod = value.paymentPeriod;
        this.isSupportLocalAd = value.isSupportLocalAd;
        this.firstIndustryTagId = value.firstIndustryTagId;
        this.secondIndustryTagId = value.secondIndustryTagId;
        this.thirdIndustryTagId = value.thirdIndustryTagId;
        this.commerceCategoryFirstId = value.commerceCategoryFirstId;
        this.commerceCategorySecondId = value.commerceCategorySecondId;
        this.allowSigningBonusPay = value.allowSigningBonusPay;
        this.allowFlyCoinPay = value.allowFlyCoinPay;
        this.allowFlowTicketPay = value.allowFlowTicketPay;
        this.financeType = value.financeType;
        this.hasMgkForm = value.hasMgkForm;
        this.mgkFormPrivacyPolicy = value.mgkFormPrivacyPolicy;
        this.showToCustomer = value.showToCustomer;
        this.isSupportCluePass = value.isSupportCluePass;
        this.promotionType = value.promotionType;
        this.unitedFirstIndustryId = value.unitedFirstIndustryId;
        this.unitedSecondIndustryId = value.unitedSecondIndustryId;
        this.unitedThirdIndustryId = value.unitedThirdIndustryId;
        this.oldAgentId = value.oldAgentId;
    }

    public AccAccountPo(
        Integer   accountId,
        String    username,
        String    mobile,
        Integer   passwordStrength,
        String    salt,
        String    saltPassword,
        Integer   status,
        Integer   crmCustomerId,
        Timestamp ctime,
        Timestamp mtime,
        Integer   isDeleted,
        Integer   orderType,
        Long      mid,
        Integer   accountType,
        Timestamp activeTime,
        String    name,
        String    icpRecordNumber,
        String    icpInfoImage,
        String    brandDomain,
        Integer   userType,
        Integer   adStatus,
        Integer   version,
        Integer   categoryFirstId,
        Integer   categorySecondId,
        String    remark,
        Integer   isAgent,
        Integer   agentType,
        Integer   businessRoleId,
        String    companyName,
        Integer   areaId,
        Integer   dependencyAgentId,
        String    websiteName,
        String    weibo,
        String    internalLinkman,
        String    linkmanAddress,
        String    bank,
        Integer   qualificationId,
        String    businessLicenceCode,
        Date      businessLicenceExpireDate,
        Integer   isBusinessLicenceIndefinite,
        String    legalPersonName,
        Date      legalPersonIdcardExpireDate,
        Integer   isLegalPersonIdcardIndefinite,
        String    auditRemark,
        Integer   accountStatus,
        String    linkmanEmail,
        Integer   gdStatus,
        Date      agentAuthExpireDate,
        Integer   isInner,
        Integer   departmentId,
        Integer   isSupportSeller,
        Integer   isSupportGame,
        Integer   isSupportDpa,
        Integer   isSupportContent,
        String    productLine,
        String    phoneNumber,
        Integer   idcardType,
        String    idcardNumber,
        Timestamp idcardExpireDate,
        String    personalAddress,
        Integer   isIdcardIndefinite,
        String    personalName,
        Integer   groupId,
        Integer   productLineId,
        Integer   productId,
        Integer   isSupportPickup,
        Integer   isSupportMas,
        Integer   autoUpdateLabel,
        Integer   isSupportFly,
        Integer   allowCashPay,
        Integer   allowIncentiveBonusPay,
        Integer   customerId,
        String    creator,
        Integer   paymentPeriod,
        Integer   isSupportLocalAd,
        Integer   firstIndustryTagId,
        Integer   secondIndustryTagId,
        Integer   thirdIndustryTagId,
        Integer   commerceCategoryFirstId,
        Integer   commerceCategorySecondId,
        Integer   allowSigningBonusPay,
        Integer   allowFlyCoinPay,
        Integer   allowFlowTicketPay,
        Integer   financeType,
        Integer   hasMgkForm,
        Integer   mgkFormPrivacyPolicy,
        Integer   showToCustomer,
        Integer   isSupportCluePass,
        Integer   promotionType,
        Integer   unitedFirstIndustryId,
        Integer   unitedSecondIndustryId,
        Integer   unitedThirdIndustryId,
        Integer   oldAgentId
    ) {
        this.accountId = accountId;
        this.username = username;
        this.mobile = mobile;
        this.passwordStrength = passwordStrength;
        this.salt = salt;
        this.saltPassword = saltPassword;
        this.status = status;
        this.crmCustomerId = crmCustomerId;
        this.ctime = ctime;
        this.mtime = mtime;
        this.isDeleted = isDeleted;
        this.orderType = orderType;
        this.mid = mid;
        this.accountType = accountType;
        this.activeTime = activeTime;
        this.name = name;
        this.icpRecordNumber = icpRecordNumber;
        this.icpInfoImage = icpInfoImage;
        this.brandDomain = brandDomain;
        this.userType = userType;
        this.adStatus = adStatus;
        this.version = version;
        this.categoryFirstId = categoryFirstId;
        this.categorySecondId = categorySecondId;
        this.remark = remark;
        this.isAgent = isAgent;
        this.agentType = agentType;
        this.businessRoleId = businessRoleId;
        this.companyName = companyName;
        this.areaId = areaId;
        this.dependencyAgentId = dependencyAgentId;
        this.websiteName = websiteName;
        this.weibo = weibo;
        this.internalLinkman = internalLinkman;
        this.linkmanAddress = linkmanAddress;
        this.bank = bank;
        this.qualificationId = qualificationId;
        this.businessLicenceCode = businessLicenceCode;
        this.businessLicenceExpireDate = businessLicenceExpireDate;
        this.isBusinessLicenceIndefinite = isBusinessLicenceIndefinite;
        this.legalPersonName = legalPersonName;
        this.legalPersonIdcardExpireDate = legalPersonIdcardExpireDate;
        this.isLegalPersonIdcardIndefinite = isLegalPersonIdcardIndefinite;
        this.auditRemark = auditRemark;
        this.accountStatus = accountStatus;
        this.linkmanEmail = linkmanEmail;
        this.gdStatus = gdStatus;
        this.agentAuthExpireDate = agentAuthExpireDate;
        this.isInner = isInner;
        this.departmentId = departmentId;
        this.isSupportSeller = isSupportSeller;
        this.isSupportGame = isSupportGame;
        this.isSupportDpa = isSupportDpa;
        this.isSupportContent = isSupportContent;
        this.productLine = productLine;
        this.phoneNumber = phoneNumber;
        this.idcardType = idcardType;
        this.idcardNumber = idcardNumber;
        this.idcardExpireDate = idcardExpireDate;
        this.personalAddress = personalAddress;
        this.isIdcardIndefinite = isIdcardIndefinite;
        this.personalName = personalName;
        this.groupId = groupId;
        this.productLineId = productLineId;
        this.productId = productId;
        this.isSupportPickup = isSupportPickup;
        this.isSupportMas = isSupportMas;
        this.autoUpdateLabel = autoUpdateLabel;
        this.isSupportFly = isSupportFly;
        this.allowCashPay = allowCashPay;
        this.allowIncentiveBonusPay = allowIncentiveBonusPay;
        this.customerId = customerId;
        this.creator = creator;
        this.paymentPeriod = paymentPeriod;
        this.isSupportLocalAd = isSupportLocalAd;
        this.firstIndustryTagId = firstIndustryTagId;
        this.secondIndustryTagId = secondIndustryTagId;
        this.thirdIndustryTagId = thirdIndustryTagId;
        this.commerceCategoryFirstId = commerceCategoryFirstId;
        this.commerceCategorySecondId = commerceCategorySecondId;
        this.allowSigningBonusPay = allowSigningBonusPay;
        this.allowFlyCoinPay = allowFlyCoinPay;
        this.allowFlowTicketPay = allowFlowTicketPay;
        this.financeType = financeType;
        this.hasMgkForm = hasMgkForm;
        this.mgkFormPrivacyPolicy = mgkFormPrivacyPolicy;
        this.showToCustomer = showToCustomer;
        this.isSupportCluePass = isSupportCluePass;
        this.promotionType = promotionType;
        this.unitedFirstIndustryId = unitedFirstIndustryId;
        this.unitedSecondIndustryId = unitedSecondIndustryId;
        this.unitedThirdIndustryId = unitedThirdIndustryId;
        this.oldAgentId = oldAgentId;
    }

    /**
     * Getter for <code>acc_account.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>acc_account.account_id</code>. 账号ID
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>acc_account.username</code>. 用户名
     */
    public String getUsername() {
        return this.username;
    }

    /**
     * Setter for <code>acc_account.username</code>. 用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * Getter for <code>acc_account.mobile</code>. 手机号码
     */
    public String getMobile() {
        return this.mobile;
    }

    /**
     * Setter for <code>acc_account.mobile</code>. 手机号码
     */
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * Getter for <code>acc_account.password_strength</code>. 密码强度-已废弃
     */
    public Integer getPasswordStrength() {
        return this.passwordStrength;
    }

    /**
     * Setter for <code>acc_account.password_strength</code>. 密码强度-已废弃
     */
    public void setPasswordStrength(Integer passwordStrength) {
        this.passwordStrength = passwordStrength;
    }

    /**
     * Getter for <code>acc_account.salt</code>. 盐-已废弃
     */
    public String getSalt() {
        return this.salt;
    }

    /**
     * Setter for <code>acc_account.salt</code>. 盐-已废弃
     */
    public void setSalt(String salt) {
        this.salt = salt;
    }

    /**
     * Getter for <code>acc_account.salt_password</code>. 加盐密码-已废弃
     */
    public String getSaltPassword() {
        return this.saltPassword;
    }

    /**
     * Setter for <code>acc_account.salt_password</code>. 加盐密码-已废弃
     */
    public void setSaltPassword(String saltPassword) {
        this.saltPassword = saltPassword;
    }

    /**
     * Getter for <code>acc_account.status</code>. 状态:   -1-未开通 0-启用 1-冻结 2-余额不足
     */
    public Integer getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>acc_account.status</code>. 状态:   -1-未开通 0-启用 1-冻结 2-余额不足
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * Getter for <code>acc_account.crm_customer_id</code>. crm customer主键-已废弃
     */
    public Integer getCrmCustomerId() {
        return this.crmCustomerId;
    }

    /**
     * Setter for <code>acc_account.crm_customer_id</code>. crm customer主键-已废弃
     */
    public void setCrmCustomerId(Integer crmCustomerId) {
        this.crmCustomerId = crmCustomerId;
    }

    /**
     * Getter for <code>acc_account.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>acc_account.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>acc_account.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>acc_account.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>acc_account.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>acc_account.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>acc_account.order_type</code>. 账户订单类型(0预付款 1后付款
     * -1未开通)-已废弃
     */
    public Integer getOrderType() {
        return this.orderType;
    }

    /**
     * Setter for <code>acc_account.order_type</code>. 账户订单类型(0预付款 1后付款
     * -1未开通)-已废弃
     */
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    /**
     * Getter for <code>acc_account.mid</code>. 主站账号id
     */
    public Long getMid() {
        return this.mid;
    }

    /**
     * Setter for <code>acc_account.mid</code>. 主站账号id
     */
    public void setMid(Long mid) {
        this.mid = mid;
    }

    /**
     * Getter for <code>acc_account.account_type</code>. 0客户（空绑定账号） 1主站账号
     */
    public Integer getAccountType() {
        return this.accountType;
    }

    /**
     * Setter for <code>acc_account.account_type</code>. 0客户（空绑定账号） 1主站账号
     */
    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    /**
     * Getter for <code>acc_account.active_time</code>. 激活时间（默认为空）
     */
    public Timestamp getActiveTime() {
        return this.activeTime;
    }

    /**
     * Setter for <code>acc_account.active_time</code>. 激活时间（默认为空）
     */
    public void setActiveTime(Timestamp activeTime) {
        this.activeTime = activeTime;
    }

    /**
     * Getter for <code>acc_account.name</code>. 真实姓名（企业用户为公司名）
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>acc_account.name</code>. 真实姓名（企业用户为公司名）
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Getter for <code>acc_account.icp_record_number</code>. icp备案号
     */
    public String getIcpRecordNumber() {
        return this.icpRecordNumber;
    }

    /**
     * Setter for <code>acc_account.icp_record_number</code>. icp备案号
     */
    public void setIcpRecordNumber(String icpRecordNumber) {
        this.icpRecordNumber = icpRecordNumber;
    }

    /**
     * Getter for <code>acc_account.icp_info_image</code>. icp截图url(废弃)
     */
    public String getIcpInfoImage() {
        return this.icpInfoImage;
    }

    /**
     * Setter for <code>acc_account.icp_info_image</code>. icp截图url(废弃)
     */
    public void setIcpInfoImage(String icpInfoImage) {
        this.icpInfoImage = icpInfoImage;
    }

    /**
     * Getter for <code>acc_account.brand_domain</code>. 推广域名
     */
    public String getBrandDomain() {
        return this.brandDomain;
    }

    /**
     * Setter for <code>acc_account.brand_domain</code>. 推广域名
     */
    public void setBrandDomain(String brandDomain) {
        this.brandDomain = brandDomain;
    }

    /**
     * Getter for <code>acc_account.user_type</code>. 用户属性 0个人用户 1机构用户 2个人起飞用户
     */
    public Integer getUserType() {
        return this.userType;
    }

    /**
     * Setter for <code>acc_account.user_type</code>. 用户属性 0个人用户 1机构用户 2个人起飞用户
     */
    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    /**
     * Getter for <code>acc_account.ad_status</code>. 是否允许投放效果广告，广告系统状态（-1未激活
     * 0允许 1禁止）
     */
    public Integer getAdStatus() {
        return this.adStatus;
    }

    /**
     * Setter for <code>acc_account.ad_status</code>. 是否允许投放效果广告，广告系统状态（-1未激活
     * 0允许 1禁止）
     */
    public void setAdStatus(Integer adStatus) {
        this.adStatus = adStatus;
    }

    /**
     * Getter for <code>acc_account.version</code>. 版本号(mvcc)
     */
    public Integer getVersion() {
        return this.version;
    }

    /**
     * Setter for <code>acc_account.version</code>. 版本号(mvcc)
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     * Getter for <code>acc_account.category_first_id</code>. 行业一级分类id
     */
    public Integer getCategoryFirstId() {
        return this.categoryFirstId;
    }

    /**
     * Setter for <code>acc_account.category_first_id</code>. 行业一级分类id
     */
    public void setCategoryFirstId(Integer categoryFirstId) {
        this.categoryFirstId = categoryFirstId;
    }

    /**
     * Getter for <code>acc_account.category_second_id</code>. 行业二级分类id
     */
    public Integer getCategorySecondId() {
        return this.categorySecondId;
    }

    /**
     * Setter for <code>acc_account.category_second_id</code>. 行业二级分类id
     */
    public void setCategorySecondId(Integer categorySecondId) {
        this.categorySecondId = categorySecondId;
    }

    /**
     * Getter for <code>acc_account.remark</code>. 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * Setter for <code>acc_account.remark</code>. 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * Getter for <code>acc_account.is_agent</code>. 是否是代理商 0否 1是
     */
    public Integer getIsAgent() {
        return this.isAgent;
    }

    /**
     * Setter for <code>acc_account.is_agent</code>. 是否是代理商 0否 1是
     */
    public void setIsAgent(Integer isAgent) {
        this.isAgent = isAgent;
    }

    /**
     * Getter for <code>acc_account.agent_type</code>. 代理商类型：0-无 1-品牌代理商 
     * 2-MCN代理商  3-效果代理商
     */
    public Integer getAgentType() {
        return this.agentType;
    }

    /**
     * Setter for <code>acc_account.agent_type</code>. 代理商类型：0-无 1-品牌代理商 
     * 2-MCN代理商  3-效果代理商
     */
    public void setAgentType(Integer agentType) {
        this.agentType = agentType;
    }

    /**
     * Getter for <code>acc_account.business_role_id</code>. 业务角色ID
     */
    public Integer getBusinessRoleId() {
        return this.businessRoleId;
    }

    /**
     * Setter for <code>acc_account.business_role_id</code>. 业务角色ID
     */
    public void setBusinessRoleId(Integer businessRoleId) {
        this.businessRoleId = businessRoleId;
    }

    /**
     * Getter for <code>acc_account.company_name</code>. 公司名称
     */
    public String getCompanyName() {
        return this.companyName;
    }

    /**
     * Setter for <code>acc_account.company_name</code>. 公司名称
     */
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    /**
     * Getter for <code>acc_account.area_id</code>. 区域id
     */
    public Integer getAreaId() {
        return this.areaId;
    }

    /**
     * Setter for <code>acc_account.area_id</code>. 区域id
     */
    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    /**
     * Getter for <code>acc_account.dependency_agent_id</code>. 所属代理商id
     */
    public Integer getDependencyAgentId() {
        return this.dependencyAgentId;
    }

    /**
     * Setter for <code>acc_account.dependency_agent_id</code>. 所属代理商id
     */
    public void setDependencyAgentId(Integer dependencyAgentId) {
        this.dependencyAgentId = dependencyAgentId;
    }

    /**
     * Getter for <code>acc_account.website_name</code>. 网站名称
     */
    public String getWebsiteName() {
        return this.websiteName;
    }

    /**
     * Setter for <code>acc_account.website_name</code>. 网站名称
     */
    public void setWebsiteName(String websiteName) {
        this.websiteName = websiteName;
    }

    /**
     * Getter for <code>acc_account.weibo</code>. 微博账号
     */
    public String getWeibo() {
        return this.weibo;
    }

    /**
     * Setter for <code>acc_account.weibo</code>. 微博账号
     */
    public void setWeibo(String weibo) {
        this.weibo = weibo;
    }

    /**
     * Getter for <code>acc_account.internal_linkman</code>. 内部联系人（内网账号）
     */
    public String getInternalLinkman() {
        return this.internalLinkman;
    }

    /**
     * Setter for <code>acc_account.internal_linkman</code>. 内部联系人（内网账号）
     */
    public void setInternalLinkman(String internalLinkman) {
        this.internalLinkman = internalLinkman;
    }

    /**
     * Getter for <code>acc_account.linkman_address</code>. 联系人地址
     */
    public String getLinkmanAddress() {
        return this.linkmanAddress;
    }

    /**
     * Setter for <code>acc_account.linkman_address</code>. 联系人地址
     */
    public void setLinkmanAddress(String linkmanAddress) {
        this.linkmanAddress = linkmanAddress;
    }

    /**
     * Getter for <code>acc_account.bank</code>. 开户行
     */
    public String getBank() {
        return this.bank;
    }

    /**
     * Setter for <code>acc_account.bank</code>. 开户行
     */
    public void setBank(String bank) {
        this.bank = bank;
    }

    /**
     * Getter for <code>acc_account.qualification_id</code>. 主体资质分类id
     */
    public Integer getQualificationId() {
        return this.qualificationId;
    }

    /**
     * Setter for <code>acc_account.qualification_id</code>. 主体资质分类id
     */
    public void setQualificationId(Integer qualificationId) {
        this.qualificationId = qualificationId;
    }

    /**
     * Getter for <code>acc_account.business_licence_code</code>. 营业执照编码
     */
    public String getBusinessLicenceCode() {
        return this.businessLicenceCode;
    }

    /**
     * Setter for <code>acc_account.business_licence_code</code>. 营业执照编码
     */
    public void setBusinessLicenceCode(String businessLicenceCode) {
        this.businessLicenceCode = businessLicenceCode;
    }

    /**
     * Getter for <code>acc_account.business_licence_expire_date</code>.
     * 营业执照到期时间
     */
    public Date getBusinessLicenceExpireDate() {
        return this.businessLicenceExpireDate;
    }

    /**
     * Setter for <code>acc_account.business_licence_expire_date</code>.
     * 营业执照到期时间
     */
    public void setBusinessLicenceExpireDate(Date businessLicenceExpireDate) {
        this.businessLicenceExpireDate = businessLicenceExpireDate;
    }

    /**
     * Getter for <code>acc_account.is_business_licence_indefinite</code>.
     * 营业执照是否长期有效 1是 0 否
     */
    public Integer getIsBusinessLicenceIndefinite() {
        return this.isBusinessLicenceIndefinite;
    }

    /**
     * Setter for <code>acc_account.is_business_licence_indefinite</code>.
     * 营业执照是否长期有效 1是 0 否
     */
    public void setIsBusinessLicenceIndefinite(Integer isBusinessLicenceIndefinite) {
        this.isBusinessLicenceIndefinite = isBusinessLicenceIndefinite;
    }

    /**
     * Getter for <code>acc_account.legal_person_name</code>. 法人姓名
     */
    public String getLegalPersonName() {
        return this.legalPersonName;
    }

    /**
     * Setter for <code>acc_account.legal_person_name</code>. 法人姓名
     */
    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    /**
     * Getter for <code>acc_account.legal_person_idcard_expire_date</code>.
     * 企业法人身份证过期时间
     */
    public Date getLegalPersonIdcardExpireDate() {
        return this.legalPersonIdcardExpireDate;
    }

    /**
     * Setter for <code>acc_account.legal_person_idcard_expire_date</code>.
     * 企业法人身份证过期时间
     */
    public void setLegalPersonIdcardExpireDate(Date legalPersonIdcardExpireDate) {
        this.legalPersonIdcardExpireDate = legalPersonIdcardExpireDate;
    }

    /**
     * Getter for <code>acc_account.is_legal_person_idcard_indefinite</code>.
     * 法人身份证是否长期有效 1是 0 否
     */
    public Integer getIsLegalPersonIdcardIndefinite() {
        return this.isLegalPersonIdcardIndefinite;
    }

    /**
     * Setter for <code>acc_account.is_legal_person_idcard_indefinite</code>.
     * 法人身份证是否长期有效 1是 0 否
     */
    public void setIsLegalPersonIdcardIndefinite(Integer isLegalPersonIdcardIndefinite) {
        this.isLegalPersonIdcardIndefinite = isLegalPersonIdcardIndefinite;
    }

    /**
     * Getter for <code>acc_account.audit_remark</code>. 审核备注

     */
    public String getAuditRemark() {
        return this.auditRemark;
    }

    /**
     * Setter for <code>acc_account.audit_remark</code>. 审核备注

     */
    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    /**
     * Getter for <code>acc_account.account_status</code>. 0-启用 1-冻结 3编辑中 4待审核
     * 5驳回
     * 
     * 
     */
    public Integer getAccountStatus() {
        return this.accountStatus;
    }

    /**
     * Setter for <code>acc_account.account_status</code>. 0-启用 1-冻结 3编辑中 4待审核
     * 5驳回
     * 
     * 
     */
    public void setAccountStatus(Integer accountStatus) {
        this.accountStatus = accountStatus;
    }

    /**
     * Getter for <code>acc_account.linkman_email</code>. 联系人邮箱
     */
    public String getLinkmanEmail() {
        return this.linkmanEmail;
    }

    /**
     * Setter for <code>acc_account.linkman_email</code>. 联系人邮箱
     */
    public void setLinkmanEmail(String linkmanEmail) {
        this.linkmanEmail = linkmanEmail;
    }

    /**
     * Getter for <code>acc_account.gd_status</code>. GD广告系统状态（0允许 1禁止）
     */
    public Integer getGdStatus() {
        return this.gdStatus;
    }

    /**
     * Setter for <code>acc_account.gd_status</code>. GD广告系统状态（0允许 1禁止）
     */
    public void setGdStatus(Integer gdStatus) {
        this.gdStatus = gdStatus;
    }

    /**
     * Getter for <code>acc_account.agent_auth_expire_date</code>. 代理商授权有效期

     */
    public Date getAgentAuthExpireDate() {
        return this.agentAuthExpireDate;
    }

    /**
     * Setter for <code>acc_account.agent_auth_expire_date</code>. 代理商授权有效期

     */
    public void setAgentAuthExpireDate(Date agentAuthExpireDate) {
        this.agentAuthExpireDate = agentAuthExpireDate;
    }

    /**
     * Getter for <code>acc_account.is_inner</code>. 是否是内部账号 0否 1是
     */
    public Integer getIsInner() {
        return this.isInner;
    }

    /**
     * Setter for <code>acc_account.is_inner</code>. 是否是内部账号 0否 1是
     */
    public void setIsInner(Integer isInner) {
        this.isInner = isInner;
    }

    /**
     * Getter for <code>acc_account.department_id</code>. 部门 id 
     */
    public Integer getDepartmentId() {
        return this.departmentId;
    }

    /**
     * Setter for <code>acc_account.department_id</code>. 部门 id 
     */
    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    /**
     * Getter for <code>acc_account.is_support_seller</code>. 是否支持商家中心投放 0-否 1-是
     */
    public Integer getIsSupportSeller() {
        return this.isSupportSeller;
    }

    /**
     * Setter for <code>acc_account.is_support_seller</code>. 是否支持商家中心投放 0-否 1-是
     */
    public void setIsSupportSeller(Integer isSupportSeller) {
        this.isSupportSeller = isSupportSeller;
    }

    /**
     * Getter for <code>acc_account.is_support_game</code>. 是否支持游戏中心投放 0-否 1-是
     */
    public Integer getIsSupportGame() {
        return this.isSupportGame;
    }

    /**
     * Setter for <code>acc_account.is_support_game</code>. 是否支持游戏中心投放 0-否 1-是
     */
    public void setIsSupportGame(Integer isSupportGame) {
        this.isSupportGame = isSupportGame;
    }

    /**
     * Getter for <code>acc_account.is_support_dpa</code>. 是否支持DPA投放 0-否 1-是
     */
    public Integer getIsSupportDpa() {
        return this.isSupportDpa;
    }

    /**
     * Setter for <code>acc_account.is_support_dpa</code>. 是否支持DPA投放 0-否 1-是
     */
    public void setIsSupportDpa(Integer isSupportDpa) {
        this.isSupportDpa = isSupportDpa;
    }

    /**
     * Getter for <code>acc_account.is_support_content</code>. 是否支持内容推广 0-否 1-是
     */
    public Integer getIsSupportContent() {
        return this.isSupportContent;
    }

    /**
     * Setter for <code>acc_account.is_support_content</code>. 是否支持内容推广 0-否 1-是
     */
    public void setIsSupportContent(Integer isSupportContent) {
        this.isSupportContent = isSupportContent;
    }

    /**
     * Getter for <code>acc_account.product_line</code>. 产品线名
     */
    public String getProductLine() {
        return this.productLine;
    }

    /**
     * Setter for <code>acc_account.product_line</code>. 产品线名
     */
    public void setProductLine(String productLine) {
        this.productLine = productLine;
    }

    /**
     * Getter for <code>acc_account.phone_number</code>. 联系人手机号
     */
    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    /**
     * Setter for <code>acc_account.phone_number</code>. 联系人手机号
     */
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    /**
     * Getter for <code>acc_account.idcard_type</code>. 证件类型 0-未知 1-身份证（大陆地区）
     * 2-护照（港澳台及海外）
     */
    public Integer getIdcardType() {
        return this.idcardType;
    }

    /**
     * Setter for <code>acc_account.idcard_type</code>. 证件类型 0-未知 1-身份证（大陆地区）
     * 2-护照（港澳台及海外）
     */
    public void setIdcardType(Integer idcardType) {
        this.idcardType = idcardType;
    }

    /**
     * Getter for <code>acc_account.idcard_number</code>. 证件号码
     */
    public String getIdcardNumber() {
        return this.idcardNumber;
    }

    /**
     * Setter for <code>acc_account.idcard_number</code>. 证件号码
     */
    public void setIdcardNumber(String idcardNumber) {
        this.idcardNumber = idcardNumber;
    }

    /**
     * Getter for <code>acc_account.idcard_expire_date</code>. 证件到期时间
     */
    public Timestamp getIdcardExpireDate() {
        return this.idcardExpireDate;
    }

    /**
     * Setter for <code>acc_account.idcard_expire_date</code>. 证件到期时间
     */
    public void setIdcardExpireDate(Timestamp idcardExpireDate) {
        this.idcardExpireDate = idcardExpireDate;
    }

    /**
     * Getter for <code>acc_account.personal_address</code>. 联系人地址（个人账户）
     */
    public String getPersonalAddress() {
        return this.personalAddress;
    }

    /**
     * Setter for <code>acc_account.personal_address</code>. 联系人地址（个人账户）
     */
    public void setPersonalAddress(String personalAddress) {
        this.personalAddress = personalAddress;
    }

    /**
     * Getter for <code>acc_account.is_idcard_indefinite</code>. 证件有效期是否长期有效 1是
     * 0 否
     */
    public Integer getIsIdcardIndefinite() {
        return this.isIdcardIndefinite;
    }

    /**
     * Setter for <code>acc_account.is_idcard_indefinite</code>. 证件有效期是否长期有效 1是
     * 0 否
     */
    public void setIsIdcardIndefinite(Integer isIdcardIndefinite) {
        this.isIdcardIndefinite = isIdcardIndefinite;
    }

    /**
     * Getter for <code>acc_account.personal_name</code>. 个人姓名
     */
    public String getPersonalName() {
        return this.personalName;
    }

    /**
     * Setter for <code>acc_account.personal_name</code>. 个人姓名
     */
    public void setPersonalName(String personalName) {
        this.personalName = personalName;
    }

    /**
     * Getter for <code>acc_account.group_id</code>.
     * 集团id（对应acc_company_group的ID）
     */
    public Integer getGroupId() {
        return this.groupId;
    }

    /**
     * Setter for <code>acc_account.group_id</code>.
     * 集团id（对应acc_company_group的ID）
     */
    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    /**
     * Getter for <code>acc_account.product_line_id</code>. 产品线id
     */
    public Integer getProductLineId() {
        return this.productLineId;
    }

    /**
     * Setter for <code>acc_account.product_line_id</code>. 产品线id
     */
    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    /**
     * Getter for <code>acc_account.product_id</code>. 产品id
     */
    public Integer getProductId() {
        return this.productId;
    }

    /**
     * Setter for <code>acc_account.product_id</code>. 产品id
     */
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    /**
     * Getter for <code>acc_account.is_support_pickup</code>. 是否支持UP主商单投放 0-否
     * 1-是
     */
    public Integer getIsSupportPickup() {
        return this.isSupportPickup;
    }

    /**
     * Setter for <code>acc_account.is_support_pickup</code>. 是否支持UP主商单投放 0-否
     * 1-是
     */
    public void setIsSupportPickup(Integer isSupportPickup) {
        this.isSupportPickup = isSupportPickup;
    }

    /**
     * Getter for <code>acc_account.is_support_mas</code>. 是否支持互选广告投放 0-否 1-是
     */
    public Integer getIsSupportMas() {
        return this.isSupportMas;
    }

    /**
     * Setter for <code>acc_account.is_support_mas</code>. 是否支持互选广告投放 0-否 1-是
     */
    public void setIsSupportMas(Integer isSupportMas) {
        this.isSupportMas = isSupportMas;
    }

    /**
     * Getter for <code>acc_account.auto_update_label</code>. 是否自动更新帐号标签 0-否 1-是
     */
    public Integer getAutoUpdateLabel() {
        return this.autoUpdateLabel;
    }

    /**
     * Setter for <code>acc_account.auto_update_label</code>. 是否自动更新帐号标签 0-否 1-是
     */
    public void setAutoUpdateLabel(Integer autoUpdateLabel) {
        this.autoUpdateLabel = autoUpdateLabel;
    }

    /**
     * Getter for <code>acc_account.is_support_fly</code>. 是否支持商业起飞投放 0-否 1-是
     */
    public Integer getIsSupportFly() {
        return this.isSupportFly;
    }

    /**
     * Setter for <code>acc_account.is_support_fly</code>. 是否支持商业起飞投放 0-否 1-是
     */
    public void setIsSupportFly(Integer isSupportFly) {
        this.isSupportFly = isSupportFly;
    }

    /**
     * Getter for <code>acc_account.allow_cash_pay</code>. 是否允许现金支付（个人起飞专用）
     * 0-不允许 1-允许
     */
    public Integer getAllowCashPay() {
        return this.allowCashPay;
    }

    /**
     * Setter for <code>acc_account.allow_cash_pay</code>. 是否允许现金支付（个人起飞专用）
     * 0-不允许 1-允许
     */
    public void setAllowCashPay(Integer allowCashPay) {
        this.allowCashPay = allowCashPay;
    }

    /**
     * Getter for <code>acc_account.allow_incentive_bonus_pay</code>.
     * 是否允许激励金支付（个人起飞专用） 0-不允许 1-允许
     */
    public Integer getAllowIncentiveBonusPay() {
        return this.allowIncentiveBonusPay;
    }

    /**
     * Setter for <code>acc_account.allow_incentive_bonus_pay</code>.
     * 是否允许激励金支付（个人起飞专用） 0-不允许 1-允许
     */
    public void setAllowIncentiveBonusPay(Integer allowIncentiveBonusPay) {
        this.allowIncentiveBonusPay = allowIncentiveBonusPay;
    }

    /**
     * Getter for <code>acc_account.customer_id</code>. 所属客户id
     */
    public Integer getCustomerId() {
        return this.customerId;
    }

    /**
     * Setter for <code>acc_account.customer_id</code>. 所属客户id
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * Getter for <code>acc_account.creator</code>. 创建人
     */
    public String getCreator() {
        return this.creator;
    }

    /**
     * Setter for <code>acc_account.creator</code>. 创建人
     */
    public void setCreator(String creator) {
        this.creator = creator;
    }

    /**
     * Getter for <code>acc_account.payment_period</code>. 账期
     */
    public Integer getPaymentPeriod() {
        return this.paymentPeriod;
    }

    /**
     * Setter for <code>acc_account.payment_period</code>. 账期
     */
    public void setPaymentPeriod(Integer paymentPeriod) {
        this.paymentPeriod = paymentPeriod;
    }

    /**
     * Getter for <code>acc_account.is_support_local_ad</code>. 是否支持本地广告产品 0-否
     * 1-是
     */
    public Integer getIsSupportLocalAd() {
        return this.isSupportLocalAd;
    }

    /**
     * Setter for <code>acc_account.is_support_local_ad</code>. 是否支持本地广告产品 0-否
     * 1-是
     */
    public void setIsSupportLocalAd(Integer isSupportLocalAd) {
        this.isSupportLocalAd = isSupportLocalAd;
    }

    /**
     * Getter for <code>acc_account.first_industry_tag_id</code>. 一级行业标签
     */
    public Integer getFirstIndustryTagId() {
        return this.firstIndustryTagId;
    }

    /**
     * Setter for <code>acc_account.first_industry_tag_id</code>. 一级行业标签
     */
    public void setFirstIndustryTagId(Integer firstIndustryTagId) {
        this.firstIndustryTagId = firstIndustryTagId;
    }

    /**
     * Getter for <code>acc_account.second_industry_tag_id</code>. 二级行业标签
     */
    public Integer getSecondIndustryTagId() {
        return this.secondIndustryTagId;
    }

    /**
     * Setter for <code>acc_account.second_industry_tag_id</code>. 二级行业标签
     */
    public void setSecondIndustryTagId(Integer secondIndustryTagId) {
        this.secondIndustryTagId = secondIndustryTagId;
    }

    /**
     * Getter for <code>acc_account.third_industry_tag_id</code>. 三级行业标签
     */
    public Integer getThirdIndustryTagId() {
        return this.thirdIndustryTagId;
    }

    /**
     * Setter for <code>acc_account.third_industry_tag_id</code>. 三级行业标签
     */
    public void setThirdIndustryTagId(Integer thirdIndustryTagId) {
        this.thirdIndustryTagId = thirdIndustryTagId;
    }

    /**
     * Getter for <code>acc_account.commerce_category_first_id</code>.
     * 商业行业一级分类id(新版)
     */
    public Integer getCommerceCategoryFirstId() {
        return this.commerceCategoryFirstId;
    }

    /**
     * Setter for <code>acc_account.commerce_category_first_id</code>.
     * 商业行业一级分类id(新版)
     */
    public void setCommerceCategoryFirstId(Integer commerceCategoryFirstId) {
        this.commerceCategoryFirstId = commerceCategoryFirstId;
    }

    /**
     * Getter for <code>acc_account.commerce_category_second_id</code>.
     * 商业行业二级分类id（新版）
     */
    public Integer getCommerceCategorySecondId() {
        return this.commerceCategorySecondId;
    }

    /**
     * Setter for <code>acc_account.commerce_category_second_id</code>.
     * 商业行业二级分类id（新版）
     */
    public void setCommerceCategorySecondId(Integer commerceCategorySecondId) {
        this.commerceCategorySecondId = commerceCategorySecondId;
    }

    /**
     * Getter for <code>acc_account.allow_signing_bonus_pay</code>.
     * 是否允许签约金支付（个人起飞专用） 0-不允许 1-允许
     */
    public Integer getAllowSigningBonusPay() {
        return this.allowSigningBonusPay;
    }

    /**
     * Setter for <code>acc_account.allow_signing_bonus_pay</code>.
     * 是否允许签约金支付（个人起飞专用） 0-不允许 1-允许
     */
    public void setAllowSigningBonusPay(Integer allowSigningBonusPay) {
        this.allowSigningBonusPay = allowSigningBonusPay;
    }

    /**
     * Getter for <code>acc_account.allow_fly_coin_pay</code>. 是否支持起飞币支付 0-否 1-是
     */
    public Integer getAllowFlyCoinPay() {
        return this.allowFlyCoinPay;
    }

    /**
     * Setter for <code>acc_account.allow_fly_coin_pay</code>. 是否支持起飞币支付 0-否 1-是
     */
    public void setAllowFlyCoinPay(Integer allowFlyCoinPay) {
        this.allowFlyCoinPay = allowFlyCoinPay;
    }

    /**
     * Getter for <code>acc_account.allow_flow_ticket_pay</code>. 是否支持流量券支付 0-否
     * 1-是
     */
    public Integer getAllowFlowTicketPay() {
        return this.allowFlowTicketPay;
    }

    /**
     * Setter for <code>acc_account.allow_flow_ticket_pay</code>. 是否支持流量券支付 0-否
     * 1-是
     */
    public void setAllowFlowTicketPay(Integer allowFlowTicketPay) {
        this.allowFlowTicketPay = allowFlowTicketPay;
    }

    /**
     * Getter for <code>acc_account.finance_type</code>. 财务类型：0-无 1-现金账户 2-虚拟金账户
     */
    public Integer getFinanceType() {
        return this.financeType;
    }

    /**
     * Setter for <code>acc_account.finance_type</code>. 财务类型：0-无 1-现金账户 2-虚拟金账户
     */
    public void setFinanceType(Integer financeType) {
        this.financeType = financeType;
    }

    /**
     * Getter for <code>acc_account.has_mgk_form</code>. 是否含有落地页表单 0-不包含 1-包含
     */
    public Integer getHasMgkForm() {
        return this.hasMgkForm;
    }

    /**
     * Setter for <code>acc_account.has_mgk_form</code>. 是否含有落地页表单 0-不包含 1-包含
     */
    public void setHasMgkForm(Integer hasMgkForm) {
        this.hasMgkForm = hasMgkForm;
    }

    /**
     * Getter for <code>acc_account.mgk_form_privacy_policy</code>. 建站隐私政策是否同意
     * 0-未设置 1-同意 2 不同意
     */
    public Integer getMgkFormPrivacyPolicy() {
        return this.mgkFormPrivacyPolicy;
    }

    /**
     * Setter for <code>acc_account.mgk_form_privacy_policy</code>. 建站隐私政策是否同意
     * 0-未设置 1-同意 2 不同意
     */
    public void setMgkFormPrivacyPolicy(Integer mgkFormPrivacyPolicy) {
        this.mgkFormPrivacyPolicy = mgkFormPrivacyPolicy;
    }

    /**
     * Getter for <code>acc_account.show_to_customer</code>. 0:对客户可见 1:对客户不可见
     */
    public Integer getShowToCustomer() {
        return this.showToCustomer;
    }

    /**
     * Setter for <code>acc_account.show_to_customer</code>. 0:对客户可见 1:对客户不可见
     */
    public void setShowToCustomer(Integer showToCustomer) {
        this.showToCustomer = showToCustomer;
    }

    /**
     * Getter for <code>acc_account.is_support_clue_pass</code>. 是否支持线索通 0-否 1-是
     */
    public Integer getIsSupportCluePass() {
        return this.isSupportCluePass;
    }

    /**
     * Setter for <code>acc_account.is_support_clue_pass</code>. 是否支持线索通 0-否 1-是
     */
    public void setIsSupportCluePass(Integer isSupportCluePass) {
        this.isSupportCluePass = isSupportCluePass;
    }

    /**
     * Getter for <code>acc_account.promotion_type</code>. 花火开户推广类型 0-无 1-网站
     * 2-网上店铺 3-线下实体店
     */
    public Integer getPromotionType() {
        return this.promotionType;
    }

    /**
     * Setter for <code>acc_account.promotion_type</code>. 花火开户推广类型 0-无 1-网站
     * 2-网上店铺 3-线下实体店
     */
    public void setPromotionType(Integer promotionType) {
        this.promotionType = promotionType;
    }

    /**
     * Getter for <code>acc_account.united_first_industry_id</code>. 统一一级行业标签
     */
    public Integer getUnitedFirstIndustryId() {
        return this.unitedFirstIndustryId;
    }

    /**
     * Setter for <code>acc_account.united_first_industry_id</code>. 统一一级行业标签
     */
    public void setUnitedFirstIndustryId(Integer unitedFirstIndustryId) {
        this.unitedFirstIndustryId = unitedFirstIndustryId;
    }

    /**
     * Getter for <code>acc_account.united_second_industry_id</code>. 统一一级行业标签
     */
    public Integer getUnitedSecondIndustryId() {
        return this.unitedSecondIndustryId;
    }

    /**
     * Setter for <code>acc_account.united_second_industry_id</code>. 统一一级行业标签
     */
    public void setUnitedSecondIndustryId(Integer unitedSecondIndustryId) {
        this.unitedSecondIndustryId = unitedSecondIndustryId;
    }

    /**
     * Getter for <code>acc_account.united_third_industry_id</code>. 统一一级行业标签
     */
    public Integer getUnitedThirdIndustryId() {
        return this.unitedThirdIndustryId;
    }

    /**
     * Setter for <code>acc_account.united_third_industry_id</code>. 统一一级行业标签
     */
    public void setUnitedThirdIndustryId(Integer unitedThirdIndustryId) {
        this.unitedThirdIndustryId = unitedThirdIndustryId;
    }

    /**
     * Getter for <code>acc_account.old_agent_id</code>. 历史代理商id
     */
    public Integer getOldAgentId() {
        return this.oldAgentId;
    }

    /**
     * Setter for <code>acc_account.old_agent_id</code>. 历史代理商id
     */
    public void setOldAgentId(Integer oldAgentId) {
        this.oldAgentId = oldAgentId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("AccAccountPo (");

        sb.append(accountId);
        sb.append(", ").append(username);
        sb.append(", ").append(mobile);
        sb.append(", ").append(passwordStrength);
        sb.append(", ").append(salt);
        sb.append(", ").append(saltPassword);
        sb.append(", ").append(status);
        sb.append(", ").append(crmCustomerId);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(orderType);
        sb.append(", ").append(mid);
        sb.append(", ").append(accountType);
        sb.append(", ").append(activeTime);
        sb.append(", ").append(name);
        sb.append(", ").append(icpRecordNumber);
        sb.append(", ").append(icpInfoImage);
        sb.append(", ").append(brandDomain);
        sb.append(", ").append(userType);
        sb.append(", ").append(adStatus);
        sb.append(", ").append(version);
        sb.append(", ").append(categoryFirstId);
        sb.append(", ").append(categorySecondId);
        sb.append(", ").append(remark);
        sb.append(", ").append(isAgent);
        sb.append(", ").append(agentType);
        sb.append(", ").append(businessRoleId);
        sb.append(", ").append(companyName);
        sb.append(", ").append(areaId);
        sb.append(", ").append(dependencyAgentId);
        sb.append(", ").append(websiteName);
        sb.append(", ").append(weibo);
        sb.append(", ").append(internalLinkman);
        sb.append(", ").append(linkmanAddress);
        sb.append(", ").append(bank);
        sb.append(", ").append(qualificationId);
        sb.append(", ").append(businessLicenceCode);
        sb.append(", ").append(businessLicenceExpireDate);
        sb.append(", ").append(isBusinessLicenceIndefinite);
        sb.append(", ").append(legalPersonName);
        sb.append(", ").append(legalPersonIdcardExpireDate);
        sb.append(", ").append(isLegalPersonIdcardIndefinite);
        sb.append(", ").append(auditRemark);
        sb.append(", ").append(accountStatus);
        sb.append(", ").append(linkmanEmail);
        sb.append(", ").append(gdStatus);
        sb.append(", ").append(agentAuthExpireDate);
        sb.append(", ").append(isInner);
        sb.append(", ").append(departmentId);
        sb.append(", ").append(isSupportSeller);
        sb.append(", ").append(isSupportGame);
        sb.append(", ").append(isSupportDpa);
        sb.append(", ").append(isSupportContent);
        sb.append(", ").append(productLine);
        sb.append(", ").append(phoneNumber);
        sb.append(", ").append(idcardType);
        sb.append(", ").append(idcardNumber);
        sb.append(", ").append(idcardExpireDate);
        sb.append(", ").append(personalAddress);
        sb.append(", ").append(isIdcardIndefinite);
        sb.append(", ").append(personalName);
        sb.append(", ").append(groupId);
        sb.append(", ").append(productLineId);
        sb.append(", ").append(productId);
        sb.append(", ").append(isSupportPickup);
        sb.append(", ").append(isSupportMas);
        sb.append(", ").append(autoUpdateLabel);
        sb.append(", ").append(isSupportFly);
        sb.append(", ").append(allowCashPay);
        sb.append(", ").append(allowIncentiveBonusPay);
        sb.append(", ").append(customerId);
        sb.append(", ").append(creator);
        sb.append(", ").append(paymentPeriod);
        sb.append(", ").append(isSupportLocalAd);
        sb.append(", ").append(firstIndustryTagId);
        sb.append(", ").append(secondIndustryTagId);
        sb.append(", ").append(thirdIndustryTagId);
        sb.append(", ").append(commerceCategoryFirstId);
        sb.append(", ").append(commerceCategorySecondId);
        sb.append(", ").append(allowSigningBonusPay);
        sb.append(", ").append(allowFlyCoinPay);
        sb.append(", ").append(allowFlowTicketPay);
        sb.append(", ").append(financeType);
        sb.append(", ").append(hasMgkForm);
        sb.append(", ").append(mgkFormPrivacyPolicy);
        sb.append(", ").append(showToCustomer);
        sb.append(", ").append(isSupportCluePass);
        sb.append(", ").append(promotionType);
        sb.append(", ").append(unitedFirstIndustryId);
        sb.append(", ").append(unitedSecondIndustryId);
        sb.append(", ").append(unitedThirdIndustryId);
        sb.append(", ").append(oldAgentId);

        sb.append(")");
        return sb.toString();
    }
}
