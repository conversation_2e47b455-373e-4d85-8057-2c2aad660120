/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeMiniGameMapping;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeMiniGameMappingPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeMiniGameMappingRecord;


/**
 * 效果创意微信小游戏绑定关系映射表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauCreativeMiniGameMappingDao extends DAOImpl<LauCreativeMiniGameMappingRecord, LauCreativeMiniGameMappingPo, Long> {

    /**
     * Create a new LauCreativeMiniGameMappingDao without any configuration
     */
    public LauCreativeMiniGameMappingDao() {
        super(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING, LauCreativeMiniGameMappingPo.class);
    }

    /**
     * Create a new LauCreativeMiniGameMappingDao with an attached configuration
     */
    @Autowired
    public LauCreativeMiniGameMappingDao(Configuration configuration) {
        super(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING, LauCreativeMiniGameMappingPo.class, configuration);
    }

    @Override
    public Long getId(LauCreativeMiniGameMappingPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchById(Long... values) {
        return fetch(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauCreativeMiniGameMappingPo fetchOneById(Long value) {
        return fetchOne(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.ID, value);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchByCreativeId(Integer... values) {
        return fetch(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.CREATIVE_ID, values);
    }

    /**
     * Fetch records that have <code>game_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchRangeOfGameUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.GAME_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>game_url IN (values)</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchByGameUrl(String... values) {
        return fetch(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.GAME_URL, values);
    }

    /**
     * Fetch records that have <code>mini_game_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchRangeOfMiniGameId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.MINI_GAME_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mini_game_id IN (values)</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchByMiniGameId(Integer... values) {
        return fetch(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.MINI_GAME_ID, values);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchByAccountId(Integer... values) {
        return fetch(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauCreativeMiniGameMappingPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauCreativeMiniGameMapping.LAU_CREATIVE_MINI_GAME_MAPPING.MTIME, values);
    }
}
