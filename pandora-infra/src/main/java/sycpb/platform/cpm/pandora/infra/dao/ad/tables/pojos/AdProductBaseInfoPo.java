/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 投放产品基本信息
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AdProductBaseInfoPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   accountId;
    private Long      libraryId;
    private Integer   libraryType;
    private String    name;
    private Integer   bizStatus;
    private Long      firstCategoryCode;
    private Long      secondCategoryCode;
    private Long      thirdCategoryCode;
    private Long      spuCode;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;
    private Long      skuCode;

    public AdProductBaseInfoPo() {}

    public AdProductBaseInfoPo(AdProductBaseInfoPo value) {
        this.id = value.id;
        this.accountId = value.accountId;
        this.libraryId = value.libraryId;
        this.libraryType = value.libraryType;
        this.name = value.name;
        this.bizStatus = value.bizStatus;
        this.firstCategoryCode = value.firstCategoryCode;
        this.secondCategoryCode = value.secondCategoryCode;
        this.thirdCategoryCode = value.thirdCategoryCode;
        this.spuCode = value.spuCode;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.skuCode = value.skuCode;
    }

    public AdProductBaseInfoPo(
        Long      id,
        Integer   accountId,
        Long      libraryId,
        Integer   libraryType,
        String    name,
        Integer   bizStatus,
        Long      firstCategoryCode,
        Long      secondCategoryCode,
        Long      thirdCategoryCode,
        Long      spuCode,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime,
        Long      skuCode
    ) {
        this.id = id;
        this.accountId = accountId;
        this.libraryId = libraryId;
        this.libraryType = libraryType;
        this.name = name;
        this.bizStatus = bizStatus;
        this.firstCategoryCode = firstCategoryCode;
        this.secondCategoryCode = secondCategoryCode;
        this.thirdCategoryCode = thirdCategoryCode;
        this.spuCode = spuCode;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
        this.skuCode = skuCode;
    }

    /**
     * Getter for <code>ad_product_base_info.id</code>. 产品ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>ad_product_base_info.id</code>. 产品ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>ad_product_base_info.account_id</code>. 账户ID
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>ad_product_base_info.account_id</code>. 账户ID
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>ad_product_base_info.library_id</code>. 产品库id
     */
    public Long getLibraryId() {
        return this.libraryId;
    }

    /**
     * Setter for <code>ad_product_base_info.library_id</code>. 产品库id
     */
    public void setLibraryId(Long libraryId) {
        this.libraryId = libraryId;
    }

    /**
     * Getter for <code>ad_product_base_info.library_type</code>. 产品库类型1-课程库,
     * 2-借贷产品库
     */
    public Integer getLibraryType() {
        return this.libraryType;
    }

    /**
     * Setter for <code>ad_product_base_info.library_type</code>. 产品库类型1-课程库,
     * 2-借贷产品库
     */
    public void setLibraryType(Integer libraryType) {
        this.libraryType = libraryType;
    }

    /**
     * Getter for <code>ad_product_base_info.name</code>. 产品名称
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>ad_product_base_info.name</code>. 产品名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Getter for <code>ad_product_base_info.biz_status</code>. 状态,1：有效，0：无效
     */
    public Integer getBizStatus() {
        return this.bizStatus;
    }

    /**
     * Setter for <code>ad_product_base_info.biz_status</code>. 状态,1：有效，0：无效
     */
    public void setBizStatus(Integer bizStatus) {
        this.bizStatus = bizStatus;
    }

    /**
     * Getter for <code>ad_product_base_info.first_category_code</code>.
     * 一级行业code
     */
    public Long getFirstCategoryCode() {
        return this.firstCategoryCode;
    }

    /**
     * Setter for <code>ad_product_base_info.first_category_code</code>.
     * 一级行业code
     */
    public void setFirstCategoryCode(Long firstCategoryCode) {
        this.firstCategoryCode = firstCategoryCode;
    }

    /**
     * Getter for <code>ad_product_base_info.second_category_code</code>.
     * 二级行业code
     */
    public Long getSecondCategoryCode() {
        return this.secondCategoryCode;
    }

    /**
     * Setter for <code>ad_product_base_info.second_category_code</code>.
     * 二级行业code
     */
    public void setSecondCategoryCode(Long secondCategoryCode) {
        this.secondCategoryCode = secondCategoryCode;
    }

    /**
     * Getter for <code>ad_product_base_info.third_category_code</code>.
     * 三级行业code
     */
    public Long getThirdCategoryCode() {
        return this.thirdCategoryCode;
    }

    /**
     * Setter for <code>ad_product_base_info.third_category_code</code>.
     * 三级行业code
     */
    public void setThirdCategoryCode(Long thirdCategoryCode) {
        this.thirdCategoryCode = thirdCategoryCode;
    }

    /**
     * Getter for <code>ad_product_base_info.spu_code</code>. spu code
     */
    public Long getSpuCode() {
        return this.spuCode;
    }

    /**
     * Setter for <code>ad_product_base_info.spu_code</code>. spu code
     */
    public void setSpuCode(Long spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * Getter for <code>ad_product_base_info.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>ad_product_base_info.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>ad_product_base_info.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>ad_product_base_info.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>ad_product_base_info.mtime</code>. 变更时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>ad_product_base_info.mtime</code>. 变更时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>ad_product_base_info.sku_code</code>. sku
     */
    public Long getSkuCode() {
        return this.skuCode;
    }

    /**
     * Setter for <code>ad_product_base_info.sku_code</code>. sku
     */
    public void setSkuCode(Long skuCode) {
        this.skuCode = skuCode;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("AdProductBaseInfoPo (");

        sb.append(id);
        sb.append(", ").append(accountId);
        sb.append(", ").append(libraryId);
        sb.append(", ").append(libraryType);
        sb.append(", ").append(name);
        sb.append(", ").append(bizStatus);
        sb.append(", ").append(firstCategoryCode);
        sb.append(", ").append(secondCategoryCode);
        sb.append(", ").append(thirdCategoryCode);
        sb.append(", ").append(spuCode);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(skuCode);

        sb.append(")");
        return sb.toString();
    }
}
