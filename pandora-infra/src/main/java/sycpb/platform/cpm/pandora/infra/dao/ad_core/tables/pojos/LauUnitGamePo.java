/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 单元-游戏映射表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitGamePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer   id;
    private Integer   accountId;
    private Long      mid;
    private Integer   unitId;
    private Integer   gameBaseId;
    private Integer   platformType;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   isDeleted;
    private Integer   subPkg;

    public LauUnitGamePo() {}

    public LauUnitGamePo(LauUnitGamePo value) {
        this.id = value.id;
        this.accountId = value.accountId;
        this.mid = value.mid;
        this.unitId = value.unitId;
        this.gameBaseId = value.gameBaseId;
        this.platformType = value.platformType;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.isDeleted = value.isDeleted;
        this.subPkg = value.subPkg;
    }

    public LauUnitGamePo(
        Integer   id,
        Integer   accountId,
        Long      mid,
        Integer   unitId,
        Integer   gameBaseId,
        Integer   platformType,
        Timestamp ctime,
        Timestamp mtime,
        Integer   isDeleted,
        Integer   subPkg
    ) {
        this.id = id;
        this.accountId = accountId;
        this.mid = mid;
        this.unitId = unitId;
        this.gameBaseId = gameBaseId;
        this.platformType = platformType;
        this.ctime = ctime;
        this.mtime = mtime;
        this.isDeleted = isDeleted;
        this.subPkg = subPkg;
    }

    /**
     * Getter for <code>lau_unit_game.id</code>.
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_unit_game.id</code>.
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_unit_game.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>lau_unit_game.account_id</code>. 账号ID
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>lau_unit_game.mid</code>. 单元所属账号的MID
     */
    public Long getMid() {
        return this.mid;
    }

    /**
     * Setter for <code>lau_unit_game.mid</code>. 单元所属账号的MID
     */
    public void setMid(Long mid) {
        this.mid = mid;
    }

    /**
     * Getter for <code>lau_unit_game.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_unit_game.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for <code>lau_unit_game.game_base_id</code>. 游戏唯一标识
     */
    public Integer getGameBaseId() {
        return this.gameBaseId;
    }

    /**
     * Setter for <code>lau_unit_game.game_base_id</code>. 游戏唯一标识
     */
    public void setGameBaseId(Integer gameBaseId) {
        this.gameBaseId = gameBaseId;
    }

    /**
     * Getter for <code>lau_unit_game.platform_type</code>. 平台类型: 1-安卓 2-IOS
     */
    public Integer getPlatformType() {
        return this.platformType;
    }

    /**
     * Setter for <code>lau_unit_game.platform_type</code>. 平台类型: 1-安卓 2-IOS
     */
    public void setPlatformType(Integer platformType) {
        this.platformType = platformType;
    }

    /**
     * Getter for <code>lau_unit_game.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_unit_game.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_unit_game.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_unit_game.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_unit_game.is_deleted</code>. 软删除 0-有效，1-删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_unit_game.is_deleted</code>. 软删除 0-有效，1-删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_unit_game.sub_pkg</code>. 渠道分包: 0-普通游戏包;1-广告包
     */
    public Integer getSubPkg() {
        return this.subPkg;
    }

    /**
     * Setter for <code>lau_unit_game.sub_pkg</code>. 渠道分包: 0-普通游戏包;1-广告包
     */
    public void setSubPkg(Integer subPkg) {
        this.subPkg = subPkg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauUnitGamePo (");

        sb.append(id);
        sb.append(", ").append(accountId);
        sb.append(", ").append(mid);
        sb.append(", ").append(unitId);
        sb.append(", ").append(gameBaseId);
        sb.append(", ").append(platformType);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(subPkg);

        sb.append(")");
        return sb.toString();
    }
}
