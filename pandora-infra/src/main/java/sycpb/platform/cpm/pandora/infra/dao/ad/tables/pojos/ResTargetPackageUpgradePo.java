/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 新版三连推广定向包配置表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResTargetPackageUpgradePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   accountId;
    private String    packageName;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;
    private String    description;
    private Integer   promotionPurposeType;
    private Integer   adpVersion;
    private Long      originTargetId;

    public ResTargetPackageUpgradePo() {}

    public ResTargetPackageUpgradePo(ResTargetPackageUpgradePo value) {
        this.id = value.id;
        this.accountId = value.accountId;
        this.packageName = value.packageName;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.description = value.description;
        this.promotionPurposeType = value.promotionPurposeType;
        this.adpVersion = value.adpVersion;
        this.originTargetId = value.originTargetId;
    }

    public ResTargetPackageUpgradePo(
        Long      id,
        Integer   accountId,
        String    packageName,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime,
        String    description,
        Integer   promotionPurposeType,
        Integer   adpVersion,
        Long      originTargetId
    ) {
        this.id = id;
        this.accountId = accountId;
        this.packageName = packageName;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
        this.description = description;
        this.promotionPurposeType = promotionPurposeType;
        this.adpVersion = adpVersion;
        this.originTargetId = originTargetId;
    }

    /**
     * Getter for <code>res_target_package_upgrade.id</code>. 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>res_target_package_upgrade.id</code>. 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>res_target_package_upgrade.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>res_target_package_upgrade.account_id</code>. 账户id
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>res_target_package_upgrade.package_name</code>. 定向包名称
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * Setter for <code>res_target_package_upgrade.package_name</code>. 定向包名称
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * Getter for <code>res_target_package_upgrade.is_deleted</code>. 软删除 0-有效
     * 1-删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>res_target_package_upgrade.is_deleted</code>. 软删除 0-有效
     * 1-删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>res_target_package_upgrade.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>res_target_package_upgrade.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>res_target_package_upgrade.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>res_target_package_upgrade.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>res_target_package_upgrade.description</code>. 描述
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>res_target_package_upgrade.description</code>. 描述
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * Getter for
     * <code>res_target_package_upgrade.promotion_purpose_type</code>.
     * 推广目的类型（2-落地页 4-应用下载 5-会员购 6-上架游戏 7-投稿内容 8-直播间 9-电商
     */
    public Integer getPromotionPurposeType() {
        return this.promotionPurposeType;
    }

    /**
     * Setter for
     * <code>res_target_package_upgrade.promotion_purpose_type</code>.
     * 推广目的类型（2-落地页 4-应用下载 5-会员购 6-上架游戏 7-投稿内容 8-直播间 9-电商
     */
    public void setPromotionPurposeType(Integer promotionPurposeType) {
        this.promotionPurposeType = promotionPurposeType;
    }

    /**
     * Getter for <code>res_target_package_upgrade.adp_version</code>. 广告版本
     */
    public Integer getAdpVersion() {
        return this.adpVersion;
    }

    /**
     * Setter for <code>res_target_package_upgrade.adp_version</code>. 广告版本
     */
    public void setAdpVersion(Integer adpVersion) {
        this.adpVersion = adpVersion;
    }

    /**
     * Getter for <code>res_target_package_upgrade.origin_target_id</code>.
     * 分享的来源定向包ID
     */
    public Long getOriginTargetId() {
        return this.originTargetId;
    }

    /**
     * Setter for <code>res_target_package_upgrade.origin_target_id</code>.
     * 分享的来源定向包ID
     */
    public void setOriginTargetId(Long originTargetId) {
        this.originTargetId = originTargetId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ResTargetPackageUpgradePo (");

        sb.append(id);
        sb.append(", ").append(accountId);
        sb.append(", ").append(packageName);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(description);
        sb.append(", ").append(promotionPurposeType);
        sb.append(", ").append(adpVersion);
        sb.append(", ").append(originTargetId);

        sb.append(")");
        return sb.toString();
    }
}
