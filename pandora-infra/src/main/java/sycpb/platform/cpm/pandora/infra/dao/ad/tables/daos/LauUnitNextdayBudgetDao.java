/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauUnitNextdayBudget;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauUnitNextdayBudgetPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauUnitNextdayBudgetRecord;


/**
 * 单元次日预算表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauUnitNextdayBudgetDao extends DAOImpl<LauUnitNextdayBudgetRecord, LauUnitNextdayBudgetPo, Integer> {

    /**
     * Create a new LauUnitNextdayBudgetDao without any configuration
     */
    public LauUnitNextdayBudgetDao() {
        super(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET, LauUnitNextdayBudgetPo.class);
    }

    /**
     * Create a new LauUnitNextdayBudgetDao with an attached configuration
     */
    @Autowired
    public LauUnitNextdayBudgetDao(Configuration configuration) {
        super(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET, LauUnitNextdayBudgetPo.class, configuration);
    }

    @Override
    public Integer getId(LauUnitNextdayBudgetPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchById(Integer... values) {
        return fetch(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauUnitNextdayBudgetPo fetchOneById(Integer value) {
        return fetchOne(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.ID, value);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchByUnitId(Integer... values) {
        return fetch(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>budget BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchRangeOfBudget(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.BUDGET, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>budget IN (values)</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchByBudget(Long... values) {
        return fetch(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.BUDGET, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.MTIME, values);
    }

    /**
     * Fetch records that have <code>budget_effective_time BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchRangeOfBudgetEffectiveTime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.BUDGET_EFFECTIVE_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>budget_effective_time IN (values)</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchByBudgetEffectiveTime(Timestamp... values) {
        return fetch(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.BUDGET_EFFECTIVE_TIME, values);
    }

    /**
     * Fetch records that have <code>is_repeat BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchRangeOfIsRepeat(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.IS_REPEAT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_repeat IN (values)</code>
     */
    public List<LauUnitNextdayBudgetPo> fetchByIsRepeat(Integer... values) {
        return fetch(TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET.IS_REPEAT, values);
    }
}
