/*
 * This file is generated by j<PERSON><PERSON><PERSON>.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record16;
import org.jooq.Row16;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauSplashScreenCreative;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauSplashScreenCreativePo;


/**
 * 效果闪屏创意表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauSplashScreenCreativeRecord extends UpdatableRecordImpl<LauSplashScreenCreativeRecord> implements Record16<Long, Inte<PERSON>, Inte<PERSON>, Integer, Integer, <PERSON>, Inte<PERSON>, <PERSON>te<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>te<PERSON>, <PERSON>, <PERSON>te<PERSON>, <PERSON>te<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>tamp, Timestamp> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_splash_screen_creative.id</code>. 自增id
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.id</code>. 自增id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.account_id</code>. 账户id
     */
    public void setAccountId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.campaign_id</code>. 计划id
     */
    public void setCampaignId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.campaign_id</code>. 计划id
     */
    public Integer getCampaignId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.unit_id</code>. 单元id
     */
    public void setUnitId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.unit_id</code>. 单元id
     */
    public Integer getUnitId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.creative_id</code>. 创意id
     */
    public void setCreativeId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.creative_id</code>. 创意id
     */
    public Integer getCreativeId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.app_package_name</code>. 应用包名
     */
    public void setAppPackageName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.app_package_name</code>. 应用包名
     */
    public String getAppPackageName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.duration</code>. 展示时长
     */
    public void setDuration(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.duration</code>. 展示时长
     */
    public Integer getDuration() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.is_skip</code>. 是否可跳过: 0-否
     * 1-是
     */
    public void setIsSkip(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.is_skip</code>. 是否可跳过: 0-否
     * 1-是
     */
    public Integer getIsSkip() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.navigation_type</code>. 跳转类型
     * 1-跳转活动页  2-唤起第三方应用
     */
    public void setNavigationType(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.navigation_type</code>. 跳转类型
     * 1-跳转活动页  2-唤起第三方应用
     */
    public Integer getNavigationType() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.interact_type</code>. 0-点击交互
     * 1-滑动交互
     */
    public void setInteractType(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.interact_type</code>. 0-点击交互
     * 1-滑动交互
     */
    public Integer getInteractType() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.skip_button_height</code>.
     * 跳过按钮占屏幕高度
     */
    public void setSkipButtonHeight(Double value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.skip_button_height</code>.
     * 跳过按钮占屏幕高度
     */
    public Double getSkipButtonHeight() {
        return (Double) get(10);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.mark_with_skip_style</code>.
     * 广告标和跳过按钮位置样式:0-默认样式,广告标右上,跳过按钮右下
     * 1-实验样式,广告标右上,跳过按钮右下(虽然和0样式相同,但是0表示维持以前的默认配置,位置和按钮大小都不做变更) 2-广告标左上，跳过按钮右上
     */
    public void setMarkWithSkipStyle(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.mark_with_skip_style</code>.
     * 广告标和跳过按钮位置样式:0-默认样式,广告标右上,跳过按钮右下
     * 1-实验样式,广告标右上,跳过按钮右下(虽然和0样式相同,但是0表示维持以前的默认配置,位置和按钮大小都不做变更) 2-广告标左上，跳过按钮右上
     */
    public Integer getMarkWithSkipStyle() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.click_area</code>. 素材点击区域
     * 0-常规区域 1-全素材区域
     */
    public void setClickArea(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.click_area</code>. 素材点击区域
     * 0-常规区域 1-全素材区域
     */
    public Integer getClickArea() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(14, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(14);
    }

    /**
     * Setter for <code>lau_splash_screen_creative.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(15, value);
    }

    /**
     * Getter for <code>lau_splash_screen_creative.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(15);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record16 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row16<Long, Integer, Integer, Integer, Integer, String, Integer, Integer, Integer, Integer, Double, Integer, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row16) super.fieldsRow();
    }

    @Override
    public Row16<Long, Integer, Integer, Integer, Integer, String, Integer, Integer, Integer, Integer, Double, Integer, Integer, Integer, Timestamp, Timestamp> valuesRow() {
        return (Row16) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.ACCOUNT_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CAMPAIGN_ID;
    }

    @Override
    public Field<Integer> field4() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.UNIT_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CREATIVE_ID;
    }

    @Override
    public Field<String> field6() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.APP_PACKAGE_NAME;
    }

    @Override
    public Field<Integer> field7() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.DURATION;
    }

    @Override
    public Field<Integer> field8() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.IS_SKIP;
    }

    @Override
    public Field<Integer> field9() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.NAVIGATION_TYPE;
    }

    @Override
    public Field<Integer> field10() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.INTERACT_TYPE;
    }

    @Override
    public Field<Double> field11() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.SKIP_BUTTON_HEIGHT;
    }

    @Override
    public Field<Integer> field12() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.MARK_WITH_SKIP_STYLE;
    }

    @Override
    public Field<Integer> field13() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CLICK_AREA;
    }

    @Override
    public Field<Integer> field14() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field15() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CTIME;
    }

    @Override
    public Field<Timestamp> field16() {
        return TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.MTIME;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getAccountId();
    }

    @Override
    public Integer component3() {
        return getCampaignId();
    }

    @Override
    public Integer component4() {
        return getUnitId();
    }

    @Override
    public Integer component5() {
        return getCreativeId();
    }

    @Override
    public String component6() {
        return getAppPackageName();
    }

    @Override
    public Integer component7() {
        return getDuration();
    }

    @Override
    public Integer component8() {
        return getIsSkip();
    }

    @Override
    public Integer component9() {
        return getNavigationType();
    }

    @Override
    public Integer component10() {
        return getInteractType();
    }

    @Override
    public Double component11() {
        return getSkipButtonHeight();
    }

    @Override
    public Integer component12() {
        return getMarkWithSkipStyle();
    }

    @Override
    public Integer component13() {
        return getClickArea();
    }

    @Override
    public Integer component14() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component15() {
        return getCtime();
    }

    @Override
    public Timestamp component16() {
        return getMtime();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getAccountId();
    }

    @Override
    public Integer value3() {
        return getCampaignId();
    }

    @Override
    public Integer value4() {
        return getUnitId();
    }

    @Override
    public Integer value5() {
        return getCreativeId();
    }

    @Override
    public String value6() {
        return getAppPackageName();
    }

    @Override
    public Integer value7() {
        return getDuration();
    }

    @Override
    public Integer value8() {
        return getIsSkip();
    }

    @Override
    public Integer value9() {
        return getNavigationType();
    }

    @Override
    public Integer value10() {
        return getInteractType();
    }

    @Override
    public Double value11() {
        return getSkipButtonHeight();
    }

    @Override
    public Integer value12() {
        return getMarkWithSkipStyle();
    }

    @Override
    public Integer value13() {
        return getClickArea();
    }

    @Override
    public Integer value14() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value15() {
        return getCtime();
    }

    @Override
    public Timestamp value16() {
        return getMtime();
    }

    @Override
    public LauSplashScreenCreativeRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value2(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value3(Integer value) {
        setCampaignId(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value4(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value5(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value6(String value) {
        setAppPackageName(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value7(Integer value) {
        setDuration(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value8(Integer value) {
        setIsSkip(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value9(Integer value) {
        setNavigationType(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value10(Integer value) {
        setInteractType(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value11(Double value) {
        setSkipButtonHeight(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value12(Integer value) {
        setMarkWithSkipStyle(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value13(Integer value) {
        setClickArea(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value14(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value15(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord value16(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauSplashScreenCreativeRecord values(Long value1, Integer value2, Integer value3, Integer value4, Integer value5, String value6, Integer value7, Integer value8, Integer value9, Integer value10, Double value11, Integer value12, Integer value13, Integer value14, Timestamp value15, Timestamp value16) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauSplashScreenCreativeRecord
     */
    public LauSplashScreenCreativeRecord() {
        super(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE);
    }

    /**
     * Create a detached, initialised LauSplashScreenCreativeRecord
     */
    public LauSplashScreenCreativeRecord(Long id, Integer accountId, Integer campaignId, Integer unitId, Integer creativeId, String appPackageName, Integer duration, Integer isSkip, Integer navigationType, Integer interactType, Double skipButtonHeight, Integer markWithSkipStyle, Integer clickArea, Integer isDeleted, Timestamp ctime, Timestamp mtime) {
        super(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE);

        setId(id);
        setAccountId(accountId);
        setCampaignId(campaignId);
        setUnitId(unitId);
        setCreativeId(creativeId);
        setAppPackageName(appPackageName);
        setDuration(duration);
        setIsSkip(isSkip);
        setNavigationType(navigationType);
        setInteractType(interactType);
        setSkipButtonHeight(skipButtonHeight);
        setMarkWithSkipStyle(markWithSkipStyle);
        setClickArea(clickArea);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
    }

    /**
     * Create a detached, initialised LauSplashScreenCreativeRecord
     */
    public LauSplashScreenCreativeRecord(LauSplashScreenCreativePo value) {
        super(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE);

        if (value != null) {
            setId(value.getId());
            setAccountId(value.getAccountId());
            setCampaignId(value.getCampaignId());
            setUnitId(value.getUnitId());
            setCreativeId(value.getCreativeId());
            setAppPackageName(value.getAppPackageName());
            setDuration(value.getDuration());
            setIsSkip(value.getIsSkip());
            setNavigationType(value.getNavigationType());
            setInteractType(value.getInteractType());
            setSkipButtonHeight(value.getSkipButtonHeight());
            setMarkWithSkipStyle(value.getMarkWithSkipStyle());
            setClickArea(value.getClickArea());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
        }
    }
}
