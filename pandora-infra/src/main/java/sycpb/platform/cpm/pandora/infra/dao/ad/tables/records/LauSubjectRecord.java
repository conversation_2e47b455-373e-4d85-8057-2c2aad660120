/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauSubject;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauSubjectPo;


/**
 * 单元-创意投放标的物表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauSubjectRecord extends UpdatableRecordImpl<LauSubjectRecord> implements Record9<Integer, String, Integer, Integer, Integer, Timestamp, Timestamp, Integer, Long> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_subject.id</code>. 主键ID
     */
    public void setId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_subject.id</code>. 主键ID
     */
    public Integer getId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_subject.material_id</code>. 标的物ID
     */
    public void setMaterialId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_subject.material_id</code>. 标的物ID
     */
    public String getMaterialId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>lau_subject.type</code>. 标的物类型 1-直播间
     */
    public void setType(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_subject.type</code>. 标的物类型 1-直播间
     */
    public Integer getType() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_subject.launchable</code>. 可投放状态 0-不可投放 1-可投放
     */
    public void setLaunchable(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_subject.launchable</code>. 可投放状态 0-不可投放 1-可投放
     */
    public Integer getLaunchable() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_subject.is_deleted</code>. 是否删除0否 1是
     */
    public void setIsDeleted(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_subject.is_deleted</code>. 是否删除0否 1是
     */
    public Integer getIsDeleted() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_subject.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_subject.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(5);
    }

    /**
     * Setter for <code>lau_subject.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_subject.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(6);
    }

    /**
     * Setter for <code>lau_subject.area_id</code>. 直播间的分区id
     */
    public void setAreaId(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_subject.area_id</code>. 直播间的分区id
     */
    public Integer getAreaId() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>lau_subject.mid</code>. 直播间绑定的mid
     */
    public void setMid(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_subject.mid</code>. 直播间绑定的mid
     */
    public Long getMid() {
        return (Long) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row9<Integer, String, Integer, Integer, Integer, Timestamp, Timestamp, Integer, Long> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    @Override
    public Row9<Integer, String, Integer, Integer, Integer, Timestamp, Timestamp, Integer, Long> valuesRow() {
        return (Row9) super.valuesRow();
    }

    @Override
    public Field<Integer> field1() {
        return TLauSubject.LAU_SUBJECT.ID;
    }

    @Override
    public Field<String> field2() {
        return TLauSubject.LAU_SUBJECT.MATERIAL_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TLauSubject.LAU_SUBJECT.TYPE;
    }

    @Override
    public Field<Integer> field4() {
        return TLauSubject.LAU_SUBJECT.LAUNCHABLE;
    }

    @Override
    public Field<Integer> field5() {
        return TLauSubject.LAU_SUBJECT.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field6() {
        return TLauSubject.LAU_SUBJECT.CTIME;
    }

    @Override
    public Field<Timestamp> field7() {
        return TLauSubject.LAU_SUBJECT.MTIME;
    }

    @Override
    public Field<Integer> field8() {
        return TLauSubject.LAU_SUBJECT.AREA_ID;
    }

    @Override
    public Field<Long> field9() {
        return TLauSubject.LAU_SUBJECT.MID;
    }

    @Override
    public Integer component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getMaterialId();
    }

    @Override
    public Integer component3() {
        return getType();
    }

    @Override
    public Integer component4() {
        return getLaunchable();
    }

    @Override
    public Integer component5() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component6() {
        return getCtime();
    }

    @Override
    public Timestamp component7() {
        return getMtime();
    }

    @Override
    public Integer component8() {
        return getAreaId();
    }

    @Override
    public Long component9() {
        return getMid();
    }

    @Override
    public Integer value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getMaterialId();
    }

    @Override
    public Integer value3() {
        return getType();
    }

    @Override
    public Integer value4() {
        return getLaunchable();
    }

    @Override
    public Integer value5() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value6() {
        return getCtime();
    }

    @Override
    public Timestamp value7() {
        return getMtime();
    }

    @Override
    public Integer value8() {
        return getAreaId();
    }

    @Override
    public Long value9() {
        return getMid();
    }

    @Override
    public LauSubjectRecord value1(Integer value) {
        setId(value);
        return this;
    }

    @Override
    public LauSubjectRecord value2(String value) {
        setMaterialId(value);
        return this;
    }

    @Override
    public LauSubjectRecord value3(Integer value) {
        setType(value);
        return this;
    }

    @Override
    public LauSubjectRecord value4(Integer value) {
        setLaunchable(value);
        return this;
    }

    @Override
    public LauSubjectRecord value5(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauSubjectRecord value6(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauSubjectRecord value7(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauSubjectRecord value8(Integer value) {
        setAreaId(value);
        return this;
    }

    @Override
    public LauSubjectRecord value9(Long value) {
        setMid(value);
        return this;
    }

    @Override
    public LauSubjectRecord values(Integer value1, String value2, Integer value3, Integer value4, Integer value5, Timestamp value6, Timestamp value7, Integer value8, Long value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauSubjectRecord
     */
    public LauSubjectRecord() {
        super(TLauSubject.LAU_SUBJECT);
    }

    /**
     * Create a detached, initialised LauSubjectRecord
     */
    public LauSubjectRecord(Integer id, String materialId, Integer type, Integer launchable, Integer isDeleted, Timestamp ctime, Timestamp mtime, Integer areaId, Long mid) {
        super(TLauSubject.LAU_SUBJECT);

        setId(id);
        setMaterialId(materialId);
        setType(type);
        setLaunchable(launchable);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setAreaId(areaId);
        setMid(mid);
    }

    /**
     * Create a detached, initialised LauSubjectRecord
     */
    public LauSubjectRecord(LauSubjectPo value) {
        super(TLauSubject.LAU_SUBJECT);

        if (value != null) {
            setId(value.getId());
            setMaterialId(value.getMaterialId());
            setType(value.getType());
            setLaunchable(value.getLaunchable());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setAreaId(value.getAreaId());
            setMid(value.getMid());
        }
    }
}
