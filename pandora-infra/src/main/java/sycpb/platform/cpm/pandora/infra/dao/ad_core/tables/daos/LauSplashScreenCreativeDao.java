/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauSplashScreenCreative;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauSplashScreenCreativePo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauSplashScreenCreativeRecord;


/**
 * 效果闪屏创意表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauSplashScreenCreativeDao extends DAOImpl<LauSplashScreenCreativeRecord, LauSplashScreenCreativePo, Long> {

    /**
     * Create a new LauSplashScreenCreativeDao without any configuration
     */
    public LauSplashScreenCreativeDao() {
        super(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE, LauSplashScreenCreativePo.class);
    }

    /**
     * Create a new LauSplashScreenCreativeDao with an attached configuration
     */
    @Autowired
    public LauSplashScreenCreativeDao(Configuration configuration) {
        super(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE, LauSplashScreenCreativePo.class, configuration);
    }

    @Override
    public Long getId(LauSplashScreenCreativePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchById(Long... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauSplashScreenCreativePo fetchOneById(Long value) {
        return fetchOne(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.ID, value);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByAccountId(Integer... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>campaign_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfCampaignId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CAMPAIGN_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>campaign_id IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByCampaignId(Integer... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CAMPAIGN_ID, values);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByUnitId(Integer... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByCreativeId(Integer... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CREATIVE_ID, values);
    }

    /**
     * Fetch records that have <code>app_package_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfAppPackageName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.APP_PACKAGE_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>app_package_name IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByAppPackageName(String... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.APP_PACKAGE_NAME, values);
    }

    /**
     * Fetch records that have <code>duration BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfDuration(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.DURATION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>duration IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByDuration(Integer... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.DURATION, values);
    }

    /**
     * Fetch records that have <code>is_skip BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfIsSkip(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.IS_SKIP, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_skip IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByIsSkip(Integer... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.IS_SKIP, values);
    }

    /**
     * Fetch records that have <code>navigation_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfNavigationType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.NAVIGATION_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>navigation_type IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByNavigationType(Integer... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.NAVIGATION_TYPE, values);
    }

    /**
     * Fetch records that have <code>interact_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfInteractType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.INTERACT_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>interact_type IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByInteractType(Integer... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.INTERACT_TYPE, values);
    }

    /**
     * Fetch records that have <code>skip_button_height BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfSkipButtonHeight(Double lowerInclusive, Double upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.SKIP_BUTTON_HEIGHT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>skip_button_height IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchBySkipButtonHeight(Double... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.SKIP_BUTTON_HEIGHT, values);
    }

    /**
     * Fetch records that have <code>mark_with_skip_style BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfMarkWithSkipStyle(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.MARK_WITH_SKIP_STYLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mark_with_skip_style IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByMarkWithSkipStyle(Integer... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.MARK_WITH_SKIP_STYLE, values);
    }

    /**
     * Fetch records that have <code>click_area BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfClickArea(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CLICK_AREA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>click_area IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByClickArea(Integer... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CLICK_AREA, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByCtime(Timestamp... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauSplashScreenCreativePo> fetchByMtime(Timestamp... values) {
        return fetch(TLauSplashScreenCreative.LAU_SPLASH_SCREEN_CREATIVE.MTIME, values);
    }
}
