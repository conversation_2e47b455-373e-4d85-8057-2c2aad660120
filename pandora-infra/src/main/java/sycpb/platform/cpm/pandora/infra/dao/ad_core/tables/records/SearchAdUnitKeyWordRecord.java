/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TSearchAdUnitKeyWord;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.SearchAdUnitKeyWordPo;


/**
 * 单元关键词表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SearchAdUnitKeyWordRecord extends UpdatableRecordImpl<SearchAdUnitKeyWordRecord> implements Record8<Long, Integer, String, Timestamp, Timestamp, String, Integer, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>search_ad_unit_key_word.id</code>. 自增ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>search_ad_unit_key_word.id</code>. 自增ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>search_ad_unit_key_word.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>search_ad_unit_key_word.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>search_ad_unit_key_word.key_word</code>. 关键词
     */
    public void setKeyWord(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>search_ad_unit_key_word.key_word</code>. 关键词
     */
    public String getKeyWord() {
        return (String) get(2);
    }

    /**
     * Setter for <code>search_ad_unit_key_word.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(3, value);
    }

    /**
     * Getter for <code>search_ad_unit_key_word.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(3);
    }

    /**
     * Setter for <code>search_ad_unit_key_word.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(4, value);
    }

    /**
     * Getter for <code>search_ad_unit_key_word.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(4);
    }

    /**
     * Setter for <code>search_ad_unit_key_word.keyword_md5</code>. 关键词md5
     */
    public void setKeywordMd5(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>search_ad_unit_key_word.keyword_md5</code>. 关键词md5
     */
    public String getKeywordMd5() {
        return (String) get(5);
    }

    /**
     * Setter for <code>search_ad_unit_key_word.source</code>. 0-商业召回 1-主站sug
     */
    public void setSource(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>search_ad_unit_key_word.source</code>. 0-商业召回 1-主站sug
     */
    public Integer getSource() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>search_ad_unit_key_word.search_word</code>. 搜索词
     */
    public void setSearchWord(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>search_ad_unit_key_word.search_word</code>. 搜索词
     */
    public String getSearchWord() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<Long, Integer, String, Timestamp, Timestamp, String, Integer, String> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<Long, Integer, String, Timestamp, Timestamp, String, Integer, String> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD.UNIT_ID;
    }

    @Override
    public Field<String> field3() {
        return TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD.KEY_WORD;
    }

    @Override
    public Field<Timestamp> field4() {
        return TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD.CTIME;
    }

    @Override
    public Field<Timestamp> field5() {
        return TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD.MTIME;
    }

    @Override
    public Field<String> field6() {
        return TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD.KEYWORD_MD5;
    }

    @Override
    public Field<Integer> field7() {
        return TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD.SOURCE;
    }

    @Override
    public Field<String> field8() {
        return TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD.SEARCH_WORD;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getUnitId();
    }

    @Override
    public String component3() {
        return getKeyWord();
    }

    @Override
    public Timestamp component4() {
        return getCtime();
    }

    @Override
    public Timestamp component5() {
        return getMtime();
    }

    @Override
    public String component6() {
        return getKeywordMd5();
    }

    @Override
    public Integer component7() {
        return getSource();
    }

    @Override
    public String component8() {
        return getSearchWord();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getUnitId();
    }

    @Override
    public String value3() {
        return getKeyWord();
    }

    @Override
    public Timestamp value4() {
        return getCtime();
    }

    @Override
    public Timestamp value5() {
        return getMtime();
    }

    @Override
    public String value6() {
        return getKeywordMd5();
    }

    @Override
    public Integer value7() {
        return getSource();
    }

    @Override
    public String value8() {
        return getSearchWord();
    }

    @Override
    public SearchAdUnitKeyWordRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public SearchAdUnitKeyWordRecord value2(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public SearchAdUnitKeyWordRecord value3(String value) {
        setKeyWord(value);
        return this;
    }

    @Override
    public SearchAdUnitKeyWordRecord value4(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public SearchAdUnitKeyWordRecord value5(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public SearchAdUnitKeyWordRecord value6(String value) {
        setKeywordMd5(value);
        return this;
    }

    @Override
    public SearchAdUnitKeyWordRecord value7(Integer value) {
        setSource(value);
        return this;
    }

    @Override
    public SearchAdUnitKeyWordRecord value8(String value) {
        setSearchWord(value);
        return this;
    }

    @Override
    public SearchAdUnitKeyWordRecord values(Long value1, Integer value2, String value3, Timestamp value4, Timestamp value5, String value6, Integer value7, String value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SearchAdUnitKeyWordRecord
     */
    public SearchAdUnitKeyWordRecord() {
        super(TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD);
    }

    /**
     * Create a detached, initialised SearchAdUnitKeyWordRecord
     */
    public SearchAdUnitKeyWordRecord(Long id, Integer unitId, String keyWord, Timestamp ctime, Timestamp mtime, String keywordMd5, Integer source, String searchWord) {
        super(TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD);

        setId(id);
        setUnitId(unitId);
        setKeyWord(keyWord);
        setCtime(ctime);
        setMtime(mtime);
        setKeywordMd5(keywordMd5);
        setSource(source);
        setSearchWord(searchWord);
    }

    /**
     * Create a detached, initialised SearchAdUnitKeyWordRecord
     */
    public SearchAdUnitKeyWordRecord(SearchAdUnitKeyWordPo value) {
        super(TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD);

        if (value != null) {
            setId(value.getId());
            setUnitId(value.getUnitId());
            setKeyWord(value.getKeyWord());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setKeywordMd5(value.getKeywordMd5());
            setSource(value.getSource());
            setSearchWord(value.getSearchWord());
        }
    }
}
