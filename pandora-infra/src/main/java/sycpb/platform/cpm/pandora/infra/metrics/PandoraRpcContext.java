package sycpb.platform.cpm.pandora.infra.metrics;


import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.common.AttributesBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PandoraRpcContext {
    // ThreadLocal 变量，用于存储与线程关联的Attribute
    private static final ThreadLocal<AttributesBuilder> counterAttributesBuilder = ThreadLocal.withInitial(Attributes::builder);
    // 获取当前线程的 Map
    public static AttributesBuilder getCounterAttributes() {
        return counterAttributesBuilder.get();
    }

    public static void setAttribute(String key, Object value) {
        if (value == null || key == null){
            log.error("[setAttribute] key={}, value={}", key, value);
            return;
        }
        var builder = counterAttributesBuilder.get();
        builder.put(key, value.toString());
    }

    // 清理当前线程的Attributes
    public static void clear() {
        counterAttributesBuilder.remove();
    }
}
