/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 新版定向包扩展种子人群包表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResTargetPackageExtraCrowdPackageUpgradePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   targetPackageId;
    private Integer   crowdPackId;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   isDeleted;

    public ResTargetPackageExtraCrowdPackageUpgradePo() {}

    public ResTargetPackageExtraCrowdPackageUpgradePo(ResTargetPackageExtraCrowdPackageUpgradePo value) {
        this.id = value.id;
        this.targetPackageId = value.targetPackageId;
        this.crowdPackId = value.crowdPackId;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.isDeleted = value.isDeleted;
    }

    public ResTargetPackageExtraCrowdPackageUpgradePo(
        Long      id,
        Integer   targetPackageId,
        Integer   crowdPackId,
        Timestamp ctime,
        Timestamp mtime,
        Integer   isDeleted
    ) {
        this.id = id;
        this.targetPackageId = targetPackageId;
        this.crowdPackId = crowdPackId;
        this.ctime = ctime;
        this.mtime = mtime;
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for
     * <code>res_target_package_extra_crowd_package_upgrade.id</code>.
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for
     * <code>res_target_package_extra_crowd_package_upgrade.id</code>.
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for
     * <code>res_target_package_extra_crowd_package_upgrade.target_package_id</code>.
     * 定向包ID
     */
    public Integer getTargetPackageId() {
        return this.targetPackageId;
    }

    /**
     * Setter for
     * <code>res_target_package_extra_crowd_package_upgrade.target_package_id</code>.
     * 定向包ID
     */
    public void setTargetPackageId(Integer targetPackageId) {
        this.targetPackageId = targetPackageId;
    }

    /**
     * Getter for
     * <code>res_target_package_extra_crowd_package_upgrade.crowd_pack_id</code>.
     * 人群包ID(来自DMP)
     */
    public Integer getCrowdPackId() {
        return this.crowdPackId;
    }

    /**
     * Setter for
     * <code>res_target_package_extra_crowd_package_upgrade.crowd_pack_id</code>.
     * 人群包ID(来自DMP)
     */
    public void setCrowdPackId(Integer crowdPackId) {
        this.crowdPackId = crowdPackId;
    }

    /**
     * Getter for
     * <code>res_target_package_extra_crowd_package_upgrade.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for
     * <code>res_target_package_extra_crowd_package_upgrade.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for
     * <code>res_target_package_extra_crowd_package_upgrade.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for
     * <code>res_target_package_extra_crowd_package_upgrade.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for
     * <code>res_target_package_extra_crowd_package_upgrade.is_deleted</code>.
     * 软删除 0-有效，1-删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for
     * <code>res_target_package_extra_crowd_package_upgrade.is_deleted</code>.
     * 软删除 0-有效，1-删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ResTargetPackageExtraCrowdPackageUpgradePo (");

        sb.append(id);
        sb.append(", ").append(targetPackageId);
        sb.append(", ").append(crowdPackId);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(isDeleted);

        sb.append(")");
        return sb.toString();
    }
}
