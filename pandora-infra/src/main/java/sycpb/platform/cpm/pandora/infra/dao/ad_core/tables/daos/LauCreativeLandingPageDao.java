/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeLandingPage;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeLandingPagePo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeLandingPageRecord;


/**
 * 稿件视频信息表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauCreativeLandingPageDao extends DAOImpl<LauCreativeLandingPageRecord, LauCreativeLandingPagePo, Long> {

    /**
     * Create a new LauCreativeLandingPageDao without any configuration
     */
    public LauCreativeLandingPageDao() {
        super(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE, LauCreativeLandingPagePo.class);
    }

    /**
     * Create a new LauCreativeLandingPageDao with an attached configuration
     */
    @Autowired
    public LauCreativeLandingPageDao(Configuration configuration) {
        super(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE, LauCreativeLandingPagePo.class, configuration);
    }

    @Override
    public Long getId(LauCreativeLandingPagePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeLandingPagePo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauCreativeLandingPagePo> fetchById(Long... values) {
        return fetch(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauCreativeLandingPagePo fetchOneById(Long value) {
        return fetchOne(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.ID, value);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeLandingPagePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauCreativeLandingPagePo> fetchByCtime(Timestamp... values) {
        return fetch(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeLandingPagePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauCreativeLandingPagePo> fetchByMtime(Timestamp... values) {
        return fetch(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.MTIME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeLandingPagePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauCreativeLandingPagePo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeLandingPagePo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauCreativeLandingPagePo> fetchByAccountId(Integer... values) {
        return fetch(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>campaign_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeLandingPagePo> fetchRangeOfCampaignId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CAMPAIGN_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>campaign_id IN (values)</code>
     */
    public List<LauCreativeLandingPagePo> fetchByCampaignId(Integer... values) {
        return fetch(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CAMPAIGN_ID, values);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeLandingPagePo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauCreativeLandingPagePo> fetchByUnitId(Integer... values) {
        return fetch(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeLandingPagePo> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauCreativeLandingPagePo> fetchByCreativeId(Integer... values) {
        return fetch(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CREATIVE_ID, values);
    }

    /**
     * Fetch a unique record that has <code>creative_id = value</code>
     */
    public LauCreativeLandingPagePo fetchOneByCreativeId(Integer value) {
        return fetchOne(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CREATIVE_ID, value);
    }

    /**
     * Fetch records that have <code>container_page_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauCreativeLandingPagePo> fetchRangeOfContainerPageId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CONTAINER_PAGE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>container_page_id IN (values)</code>
     */
    public List<LauCreativeLandingPagePo> fetchByContainerPageId(Long... values) {
        return fetch(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CONTAINER_PAGE_ID, values);
    }

    /**
     * Fetch records that have <code>container_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeLandingPagePo> fetchRangeOfContainerUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CONTAINER_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>container_url IN (values)</code>
     */
    public List<LauCreativeLandingPagePo> fetchByContainerUrl(String... values) {
        return fetch(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CONTAINER_URL, values);
    }

    /**
     * Fetch records that have <code>container_secondary_url BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<LauCreativeLandingPagePo> fetchRangeOfContainerSecondaryUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CONTAINER_SECONDARY_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>container_secondary_url IN (values)</code>
     */
    public List<LauCreativeLandingPagePo> fetchByContainerSecondaryUrl(String... values) {
        return fetch(TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE.CONTAINER_SECONDARY_URL, values);
    }
}
