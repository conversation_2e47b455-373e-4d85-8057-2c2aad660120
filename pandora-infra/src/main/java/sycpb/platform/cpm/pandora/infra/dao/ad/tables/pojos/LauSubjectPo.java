/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 单元-创意投放标的物表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauSubjectPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer   id;
    private String    materialId;
    private Integer   type;
    private Integer   launchable;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   areaId;
    private Long      mid;

    public LauSubjectPo() {}

    public LauSubjectPo(LauSubjectPo value) {
        this.id = value.id;
        this.materialId = value.materialId;
        this.type = value.type;
        this.launchable = value.launchable;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.areaId = value.areaId;
        this.mid = value.mid;
    }

    public LauSubjectPo(
        Integer   id,
        String    materialId,
        Integer   type,
        Integer   launchable,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime,
        Integer   areaId,
        Long      mid
    ) {
        this.id = id;
        this.materialId = materialId;
        this.type = type;
        this.launchable = launchable;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
        this.areaId = areaId;
        this.mid = mid;
    }

    /**
     * Getter for <code>lau_subject.id</code>. 主键ID
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_subject.id</code>. 主键ID
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_subject.material_id</code>. 标的物ID
     */
    public String getMaterialId() {
        return this.materialId;
    }

    /**
     * Setter for <code>lau_subject.material_id</code>. 标的物ID
     */
    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    /**
     * Getter for <code>lau_subject.type</code>. 标的物类型 1-直播间
     */
    public Integer getType() {
        return this.type;
    }

    /**
     * Setter for <code>lau_subject.type</code>. 标的物类型 1-直播间
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * Getter for <code>lau_subject.launchable</code>. 可投放状态 0-不可投放 1-可投放
     */
    public Integer getLaunchable() {
        return this.launchable;
    }

    /**
     * Setter for <code>lau_subject.launchable</code>. 可投放状态 0-不可投放 1-可投放
     */
    public void setLaunchable(Integer launchable) {
        this.launchable = launchable;
    }

    /**
     * Getter for <code>lau_subject.is_deleted</code>. 是否删除0否 1是
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_subject.is_deleted</code>. 是否删除0否 1是
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_subject.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_subject.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_subject.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_subject.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_subject.area_id</code>. 直播间的分区id
     */
    public Integer getAreaId() {
        return this.areaId;
    }

    /**
     * Setter for <code>lau_subject.area_id</code>. 直播间的分区id
     */
    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    /**
     * Getter for <code>lau_subject.mid</code>. 直播间绑定的mid
     */
    public Long getMid() {
        return this.mid;
    }

    /**
     * Setter for <code>lau_subject.mid</code>. 直播间绑定的mid
     */
    public void setMid(Long mid) {
        this.mid = mid;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauSubjectPo (");

        sb.append(id);
        sb.append(", ").append(materialId);
        sb.append(", ").append(type);
        sb.append(", ").append(launchable);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(areaId);
        sb.append(", ").append(mid);

        sb.append(")");
        return sb.toString();
    }
}
