/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TAdProductLibraryShare;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.AdProductLibrarySharePo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.AdProductLibraryShareRecord;


/**
 * 投放产品库分享表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class AdProductLibraryShareDao extends DAOImpl<AdProductLibraryShareRecord, AdProductLibrarySharePo, Long> {

    /**
     * Create a new AdProductLibraryShareDao without any configuration
     */
    public AdProductLibraryShareDao() {
        super(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE, AdProductLibrarySharePo.class);
    }

    /**
     * Create a new AdProductLibraryShareDao with an attached configuration
     */
    @Autowired
    public AdProductLibraryShareDao(Configuration configuration) {
        super(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE, AdProductLibrarySharePo.class, configuration);
    }

    @Override
    public Long getId(AdProductLibrarySharePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductLibrarySharePo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<AdProductLibrarySharePo> fetchById(Long... values) {
        return fetch(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public AdProductLibrarySharePo fetchOneById(Long value) {
        return fetchOne(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.ID, value);
    }

    /**
     * Fetch records that have <code>library_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductLibrarySharePo> fetchRangeOfLibraryId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.LIBRARY_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>library_id IN (values)</code>
     */
    public List<AdProductLibrarySharePo> fetchByLibraryId(Long... values) {
        return fetch(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.LIBRARY_ID, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductLibrarySharePo> fetchRangeOfType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<AdProductLibrarySharePo> fetchByType(Integer... values) {
        return fetch(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.TYPE, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductLibrarySharePo> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<AdProductLibrarySharePo> fetchByName(String... values) {
        return fetch(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.NAME, values);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductLibrarySharePo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<AdProductLibrarySharePo> fetchByAccountId(Integer... values) {
        return fetch(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>biz_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductLibrarySharePo> fetchRangeOfBizStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.BIZ_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_status IN (values)</code>
     */
    public List<AdProductLibrarySharePo> fetchByBizStatus(Integer... values) {
        return fetch(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.BIZ_STATUS, values);
    }

    /**
     * Fetch records that have <code>belong_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductLibrarySharePo> fetchRangeOfBelongType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.BELONG_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>belong_type IN (values)</code>
     */
    public List<AdProductLibrarySharePo> fetchByBelongType(Integer... values) {
        return fetch(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.BELONG_TYPE, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductLibrarySharePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<AdProductLibrarySharePo> fetchByIsDeleted(Integer... values) {
        return fetch(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductLibrarySharePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<AdProductLibrarySharePo> fetchByCtime(Timestamp... values) {
        return fetch(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductLibrarySharePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<AdProductLibrarySharePo> fetchByMtime(Timestamp... values) {
        return fetch(TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE.MTIME, values);
    }
}
