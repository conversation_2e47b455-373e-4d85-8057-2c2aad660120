/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TResTargetPackageUpgrade;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.ResTargetPackageUpgradePo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.ResTargetPackageUpgradeRecord;


/**
 * 新版三连推广定向包配置表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ResTargetPackageUpgradeDao extends DAOImpl<ResTargetPackageUpgradeRecord, ResTargetPackageUpgradePo, Long> {

    /**
     * Create a new ResTargetPackageUpgradeDao without any configuration
     */
    public ResTargetPackageUpgradeDao() {
        super(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE, ResTargetPackageUpgradePo.class);
    }

    /**
     * Create a new ResTargetPackageUpgradeDao with an attached configuration
     */
    @Autowired
    public ResTargetPackageUpgradeDao(Configuration configuration) {
        super(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE, ResTargetPackageUpgradePo.class, configuration);
    }

    @Override
    public Long getId(ResTargetPackageUpgradePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageUpgradePo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<ResTargetPackageUpgradePo> fetchById(Long... values) {
        return fetch(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public ResTargetPackageUpgradePo fetchOneById(Long value) {
        return fetchOne(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.ID, value);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageUpgradePo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<ResTargetPackageUpgradePo> fetchByAccountId(Integer... values) {
        return fetch(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>package_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageUpgradePo> fetchRangeOfPackageName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.PACKAGE_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>package_name IN (values)</code>
     */
    public List<ResTargetPackageUpgradePo> fetchByPackageName(String... values) {
        return fetch(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.PACKAGE_NAME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageUpgradePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<ResTargetPackageUpgradePo> fetchByIsDeleted(Integer... values) {
        return fetch(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageUpgradePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<ResTargetPackageUpgradePo> fetchByCtime(Timestamp... values) {
        return fetch(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageUpgradePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<ResTargetPackageUpgradePo> fetchByMtime(Timestamp... values) {
        return fetch(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.MTIME, values);
    }

    /**
     * Fetch records that have <code>description BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageUpgradePo> fetchRangeOfDescription(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.DESCRIPTION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>description IN (values)</code>
     */
    public List<ResTargetPackageUpgradePo> fetchByDescription(String... values) {
        return fetch(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.DESCRIPTION, values);
    }

    /**
     * Fetch records that have <code>promotion_purpose_type BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<ResTargetPackageUpgradePo> fetchRangeOfPromotionPurposeType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.PROMOTION_PURPOSE_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>promotion_purpose_type IN (values)</code>
     */
    public List<ResTargetPackageUpgradePo> fetchByPromotionPurposeType(Integer... values) {
        return fetch(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.PROMOTION_PURPOSE_TYPE, values);
    }

    /**
     * Fetch records that have <code>adp_version BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageUpgradePo> fetchRangeOfAdpVersion(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.ADP_VERSION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>adp_version IN (values)</code>
     */
    public List<ResTargetPackageUpgradePo> fetchByAdpVersion(Integer... values) {
        return fetch(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.ADP_VERSION, values);
    }

    /**
     * Fetch records that have <code>origin_target_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageUpgradePo> fetchRangeOfOriginTargetId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.ORIGIN_TARGET_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>origin_target_id IN (values)</code>
     */
    public List<ResTargetPackageUpgradePo> fetchByOriginTargetId(Long... values) {
        return fetch(TResTargetPackageUpgrade.RES_TARGET_PACKAGE_UPGRADE.ORIGIN_TARGET_ID, values);
    }
}
