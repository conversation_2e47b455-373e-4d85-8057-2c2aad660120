/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * app应用包信息表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResAppPackagePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer   id;
    private Integer   accountId;
    private String    name;
    private String    url;
    private String    packageName;
    private String    appName;
    private Integer   platform;
    private String    version;
    private Integer   size;
    private String    md5;
    private String    iconUrl;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   isDeleted;
    private String    internalUrl;
    private Integer   status;
    private Integer   platformStatus;
    private String    developerName;
    private String    authorityUrl;
    private String    authCodeList;
    private Timestamp apkUpdateTime;
    private String    privacyPolicy;
    private Integer   dmpAppId;
    private Integer   isNewFly;
    private String    description;
    private String    subTitle;
    private Integer   isIconValid;
    private String    deviceAppStore;
    private Integer   copySourceAccountId;
    private String    recordNumber;
    private Integer   addType;

    public ResAppPackagePo() {}

    public ResAppPackagePo(ResAppPackagePo value) {
        this.id = value.id;
        this.accountId = value.accountId;
        this.name = value.name;
        this.url = value.url;
        this.packageName = value.packageName;
        this.appName = value.appName;
        this.platform = value.platform;
        this.version = value.version;
        this.size = value.size;
        this.md5 = value.md5;
        this.iconUrl = value.iconUrl;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.isDeleted = value.isDeleted;
        this.internalUrl = value.internalUrl;
        this.status = value.status;
        this.platformStatus = value.platformStatus;
        this.developerName = value.developerName;
        this.authorityUrl = value.authorityUrl;
        this.authCodeList = value.authCodeList;
        this.apkUpdateTime = value.apkUpdateTime;
        this.privacyPolicy = value.privacyPolicy;
        this.dmpAppId = value.dmpAppId;
        this.isNewFly = value.isNewFly;
        this.description = value.description;
        this.subTitle = value.subTitle;
        this.isIconValid = value.isIconValid;
        this.deviceAppStore = value.deviceAppStore;
        this.copySourceAccountId = value.copySourceAccountId;
        this.recordNumber = value.recordNumber;
        this.addType = value.addType;
    }

    public ResAppPackagePo(
        Integer   id,
        Integer   accountId,
        String    name,
        String    url,
        String    packageName,
        String    appName,
        Integer   platform,
        String    version,
        Integer   size,
        String    md5,
        String    iconUrl,
        Timestamp ctime,
        Timestamp mtime,
        Integer   isDeleted,
        String    internalUrl,
        Integer   status,
        Integer   platformStatus,
        String    developerName,
        String    authorityUrl,
        String    authCodeList,
        Timestamp apkUpdateTime,
        String    privacyPolicy,
        Integer   dmpAppId,
        Integer   isNewFly,
        String    description,
        String    subTitle,
        Integer   isIconValid,
        String    deviceAppStore,
        Integer   copySourceAccountId,
        String    recordNumber,
        Integer   addType
    ) {
        this.id = id;
        this.accountId = accountId;
        this.name = name;
        this.url = url;
        this.packageName = packageName;
        this.appName = appName;
        this.platform = platform;
        this.version = version;
        this.size = size;
        this.md5 = md5;
        this.iconUrl = iconUrl;
        this.ctime = ctime;
        this.mtime = mtime;
        this.isDeleted = isDeleted;
        this.internalUrl = internalUrl;
        this.status = status;
        this.platformStatus = platformStatus;
        this.developerName = developerName;
        this.authorityUrl = authorityUrl;
        this.authCodeList = authCodeList;
        this.apkUpdateTime = apkUpdateTime;
        this.privacyPolicy = privacyPolicy;
        this.dmpAppId = dmpAppId;
        this.isNewFly = isNewFly;
        this.description = description;
        this.subTitle = subTitle;
        this.isIconValid = isIconValid;
        this.deviceAppStore = deviceAppStore;
        this.copySourceAccountId = copySourceAccountId;
        this.recordNumber = recordNumber;
        this.addType = addType;
    }

    /**
     * Getter for <code>res_app_package.id</code>. 主键ID
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * Setter for <code>res_app_package.id</code>. 主键ID
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Getter for <code>res_app_package.account_id</code>. 账号id
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>res_app_package.account_id</code>. 账号id
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>res_app_package.name</code>. 应用包名称
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>res_app_package.name</code>. 应用包名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Getter for <code>res_app_package.url</code>. 应用包原始下载链接
     */
    public String getUrl() {
        return this.url;
    }

    /**
     * Setter for <code>res_app_package.url</code>. 应用包原始下载链接
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * Getter for <code>res_app_package.package_name</code>. 应用包的包名称
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * Setter for <code>res_app_package.package_name</code>. 应用包的包名称
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * Getter for <code>res_app_package.app_name</code>. 应用名称
     */
    public String getAppName() {
        return this.appName;
    }

    /**
     * Setter for <code>res_app_package.app_name</code>. 应用名称
     */
    public void setAppName(String appName) {
        this.appName = appName;
    }

    /**
     * Getter for <code>res_app_package.platform</code>. 适应系统 1-IOS,
     * 2-Android,3-iphone, 4-ipad
     */
    public Integer getPlatform() {
        return this.platform;
    }

    /**
     * Setter for <code>res_app_package.platform</code>. 适应系统 1-IOS,
     * 2-Android,3-iphone, 4-ipad
     */
    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    /**
     * Getter for <code>res_app_package.version</code>. 版本号
     */
    public String getVersion() {
        return this.version;
    }

    /**
     * Setter for <code>res_app_package.version</code>. 版本号
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * Getter for <code>res_app_package.size</code>. 应用包大小（单位字节）
     */
    public Integer getSize() {
        return this.size;
    }

    /**
     * Setter for <code>res_app_package.size</code>. 应用包大小（单位字节）
     */
    public void setSize(Integer size) {
        this.size = size;
    }

    /**
     * Getter for <code>res_app_package.md5</code>. 应用包的MD5
     */
    public String getMd5() {
        return this.md5;
    }

    /**
     * Setter for <code>res_app_package.md5</code>. 应用包的MD5
     */
    public void setMd5(String md5) {
        this.md5 = md5;
    }

    /**
     * Getter for <code>res_app_package.icon_url</code>. 图标url
     */
    public String getIconUrl() {
        return this.iconUrl;
    }

    /**
     * Setter for <code>res_app_package.icon_url</code>. 图标url
     */
    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    /**
     * Getter for <code>res_app_package.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>res_app_package.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>res_app_package.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>res_app_package.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>res_app_package.is_deleted</code>. 软删除 0-有效, 1-删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>res_app_package.is_deleted</code>. 软删除 0-有效, 1-删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>res_app_package.internal_url</code>. 应用包内部下载链接
     */
    public String getInternalUrl() {
        return this.internalUrl;
    }

    /**
     * Setter for <code>res_app_package.internal_url</code>. 应用包内部下载链接
     */
    public void setInternalUrl(String internalUrl) {
        this.internalUrl = internalUrl;
    }

    /**
     * Getter for <code>res_app_package.status</code>. 应用包状态 0-有效，1-无效
     */
    public Integer getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>res_app_package.status</code>. 应用包状态 0-有效，1-无效
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * Getter for <code>res_app_package.platform_status</code>. 平台状态 0-有效, 1-禁用
     */
    public Integer getPlatformStatus() {
        return this.platformStatus;
    }

    /**
     * Setter for <code>res_app_package.platform_status</code>. 平台状态 0-有效, 1-禁用
     */
    public void setPlatformStatus(Integer platformStatus) {
        this.platformStatus = platformStatus;
    }

    /**
     * Getter for <code>res_app_package.developer_name</code>. 开发商名称
     */
    public String getDeveloperName() {
        return this.developerName;
    }

    /**
     * Setter for <code>res_app_package.developer_name</code>. 开发商名称
     */
    public void setDeveloperName(String developerName) {
        this.developerName = developerName;
    }

    /**
     * Getter for <code>res_app_package.authority_url</code>. 权限地址
     */
    public String getAuthorityUrl() {
        return this.authorityUrl;
    }

    /**
     * Setter for <code>res_app_package.authority_url</code>. 权限地址
     */
    public void setAuthorityUrl(String authorityUrl) {
        this.authorityUrl = authorityUrl;
    }

    /**
     * Getter for <code>res_app_package.auth_code_list</code>. 权限code
     */
    public String getAuthCodeList() {
        return this.authCodeList;
    }

    /**
     * Setter for <code>res_app_package.auth_code_list</code>. 权限code
     */
    public void setAuthCodeList(String authCodeList) {
        this.authCodeList = authCodeList;
    }

    /**
     * Getter for <code>res_app_package.apk_update_time</code>. 安装包更新时间
     */
    public Timestamp getApkUpdateTime() {
        return this.apkUpdateTime;
    }

    /**
     * Setter for <code>res_app_package.apk_update_time</code>. 安装包更新时间
     */
    public void setApkUpdateTime(Timestamp apkUpdateTime) {
        this.apkUpdateTime = apkUpdateTime;
    }

    /**
     * Getter for <code>res_app_package.privacy_policy</code>. 隐私政策地址
     */
    public String getPrivacyPolicy() {
        return this.privacyPolicy;
    }

    /**
     * Setter for <code>res_app_package.privacy_policy</code>. 隐私政策地址
     */
    public void setPrivacyPolicy(String privacyPolicy) {
        this.privacyPolicy = privacyPolicy;
    }

    /**
     * Getter for <code>res_app_package.dmp_app_id</code>. dmp_app_id
     */
    public Integer getDmpAppId() {
        return this.dmpAppId;
    }

    /**
     * Setter for <code>res_app_package.dmp_app_id</code>. dmp_app_id
     */
    public void setDmpAppId(Integer dmpAppId) {
        this.dmpAppId = dmpAppId;
    }

    /**
     * Getter for <code>res_app_package.is_new_fly</code>. 是否新起飞：0-否 1-是
     */
    public Integer getIsNewFly() {
        return this.isNewFly;
    }

    /**
     * Setter for <code>res_app_package.is_new_fly</code>. 是否新起飞：0-否 1-是
     */
    public void setIsNewFly(Integer isNewFly) {
        this.isNewFly = isNewFly;
    }

    /**
     * Getter for <code>res_app_package.description</code>. 描述信息
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>res_app_package.description</code>. 描述信息
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * Getter for <code>res_app_package.sub_title</code>. 简介
     */
    public String getSubTitle() {
        return this.subTitle;
    }

    /**
     * Setter for <code>res_app_package.sub_title</code>. 简介
     */
    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    /**
     * Getter for <code>res_app_package.is_icon_valid</code>. 安卓应用包头像是否可用: 0-未知;
     * 1-可用; 2-不可用
     */
    public Integer getIsIconValid() {
        return this.isIconValid;
    }

    /**
     * Setter for <code>res_app_package.is_icon_valid</code>. 安卓应用包头像是否可用: 0-未知;
     * 1-可用; 2-不可用
     */
    public void setIsIconValid(Integer isIconValid) {
        this.isIconValid = isIconValid;
    }

    /**
     * Getter for <code>res_app_package.device_app_store</code>. 已上架的安卓App
     * Store。存储格式  1,2,4  表示 小米、华为、VIVO 已上架
     */
    public String getDeviceAppStore() {
        return this.deviceAppStore;
    }

    /**
     * Setter for <code>res_app_package.device_app_store</code>. 已上架的安卓App
     * Store。存储格式  1,2,4  表示 小米、华为、VIVO 已上架
     */
    public void setDeviceAppStore(String deviceAppStore) {
        this.deviceAppStore = deviceAppStore;
    }

    /**
     * Getter for <code>res_app_package.copy_source_account_id</code>. 复制来源账户id
     */
    public Integer getCopySourceAccountId() {
        return this.copySourceAccountId;
    }

    /**
     * Setter for <code>res_app_package.copy_source_account_id</code>. 复制来源账户id
     */
    public void setCopySourceAccountId(Integer copySourceAccountId) {
        this.copySourceAccountId = copySourceAccountId;
    }

    /**
     * Getter for <code>res_app_package.record_number</code>. app备案号
     */
    public String getRecordNumber() {
        return this.recordNumber;
    }

    /**
     * Setter for <code>res_app_package.record_number</code>. app备案号
     */
    public void setRecordNumber(String recordNumber) {
        this.recordNumber = recordNumber;
    }

    /**
     * Getter for <code>res_app_package.add_type</code>. 添加方式：链接添加(0)or手动上传(1)
     */
    public Integer getAddType() {
        return this.addType;
    }

    /**
     * Setter for <code>res_app_package.add_type</code>. 添加方式：链接添加(0)or手动上传(1)
     */
    public void setAddType(Integer addType) {
        this.addType = addType;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ResAppPackagePo (");

        sb.append(id);
        sb.append(", ").append(accountId);
        sb.append(", ").append(name);
        sb.append(", ").append(url);
        sb.append(", ").append(packageName);
        sb.append(", ").append(appName);
        sb.append(", ").append(platform);
        sb.append(", ").append(version);
        sb.append(", ").append(size);
        sb.append(", ").append(md5);
        sb.append(", ").append(iconUrl);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(internalUrl);
        sb.append(", ").append(status);
        sb.append(", ").append(platformStatus);
        sb.append(", ").append(developerName);
        sb.append(", ").append(authorityUrl);
        sb.append(", ").append(authCodeList);
        sb.append(", ").append(apkUpdateTime);
        sb.append(", ").append(privacyPolicy);
        sb.append(", ").append(dmpAppId);
        sb.append(", ").append(isNewFly);
        sb.append(", ").append(description);
        sb.append(", ").append(subTitle);
        sb.append(", ").append(isIconValid);
        sb.append(", ").append(deviceAppStore);
        sb.append(", ").append(copySourceAccountId);
        sb.append(", ").append(recordNumber);
        sb.append(", ").append(addType);

        sb.append(")");
        return sb.toString();
    }
}
