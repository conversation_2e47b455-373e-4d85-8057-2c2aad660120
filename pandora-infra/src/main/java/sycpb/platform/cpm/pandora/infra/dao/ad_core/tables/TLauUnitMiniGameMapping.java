/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitMiniGameMappingRecord;


/**
 * 效果单元微信小游戏绑定关系映射表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauUnitMiniGameMapping extends TableImpl<LauUnitMiniGameMappingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_unit_mini_game_mapping</code>
     */
    public static final TLauUnitMiniGameMapping LAU_UNIT_MINI_GAME_MAPPING = new TLauUnitMiniGameMapping();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauUnitMiniGameMappingRecord> getRecordType() {
        return LauUnitMiniGameMappingRecord.class;
    }

    /**
     * The column <code>lau_unit_mini_game_mapping.id</code>. 主键id
     */
    public final TableField<LauUnitMiniGameMappingRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键id");

    /**
     * The column <code>lau_unit_mini_game_mapping.unit_id</code>. 单元id
     */
    public final TableField<LauUnitMiniGameMappingRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元id");

    /**
     * The column <code>lau_unit_mini_game_mapping.game_url</code>. 微信小游戏链接
     */
    public final TableField<LauUnitMiniGameMappingRecord, String> GAME_URL = createField(DSL.name("game_url"), SQLDataType.VARCHAR(2048).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "微信小游戏链接");

    /**
     * The column <code>lau_unit_mini_game_mapping.mini_game_id</code>. 微信小游戏id
     */
    public final TableField<LauUnitMiniGameMappingRecord, Integer> MINI_GAME_ID = createField(DSL.name("mini_game_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "微信小游戏id");

    /**
     * The column <code>lau_unit_mini_game_mapping.account_id</code>. 账户id
     */
    public final TableField<LauUnitMiniGameMappingRecord, Integer> ACCOUNT_ID = createField(DSL.name("account_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "账户id");

    /**
     * The column <code>lau_unit_mini_game_mapping.is_deleted</code>. 是否被删除 0-正常
     * 1-被删除
     */
    public final TableField<LauUnitMiniGameMappingRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否被删除 0-正常 1-被删除");

    /**
     * The column <code>lau_unit_mini_game_mapping.ctime</code>. 创建时间
     */
    public final TableField<LauUnitMiniGameMappingRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_unit_mini_game_mapping.mtime</code>. 更新时间
     */
    public final TableField<LauUnitMiniGameMappingRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    private TLauUnitMiniGameMapping(Name alias, Table<LauUnitMiniGameMappingRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauUnitMiniGameMapping(Name alias, Table<LauUnitMiniGameMappingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("效果单元微信小游戏绑定关系映射表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_unit_mini_game_mapping</code> table reference
     */
    public TLauUnitMiniGameMapping(String alias) {
        this(DSL.name(alias), LAU_UNIT_MINI_GAME_MAPPING);
    }

    /**
     * Create an aliased <code>lau_unit_mini_game_mapping</code> table reference
     */
    public TLauUnitMiniGameMapping(Name alias) {
        this(alias, LAU_UNIT_MINI_GAME_MAPPING);
    }

    /**
     * Create a <code>lau_unit_mini_game_mapping</code> table reference
     */
    public TLauUnitMiniGameMapping() {
        this(DSL.name("lau_unit_mini_game_mapping"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauUnitMiniGameMappingRecord, Long> getIdentity() {
        return (Identity<LauUnitMiniGameMappingRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauUnitMiniGameMappingRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauUnitMiniGameMapping.LAU_UNIT_MINI_GAME_MAPPING, DSL.name("KEY_lau_unit_mini_game_mapping_PRIMARY"), new TableField[] { TLauUnitMiniGameMapping.LAU_UNIT_MINI_GAME_MAPPING.ID }, true);
    }

    @Override
    public TLauUnitMiniGameMapping as(String alias) {
        return new TLauUnitMiniGameMapping(DSL.name(alias), this);
    }

    @Override
    public TLauUnitMiniGameMapping as(Name alias) {
        return new TLauUnitMiniGameMapping(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitMiniGameMapping rename(String name) {
        return new TLauUnitMiniGameMapping(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitMiniGameMapping rename(Name name) {
        return new TLauUnitMiniGameMapping(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<Long, Integer, String, Integer, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
