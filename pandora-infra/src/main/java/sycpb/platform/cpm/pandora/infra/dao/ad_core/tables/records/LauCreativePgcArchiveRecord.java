/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativePgcArchive;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativePgcArchivePo;


/**
 * 创意-PGC稿件视频关联表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativePgcArchiveRecord extends UpdatableRecordImpl<LauCreativePgcArchiveRecord> implements Record11<Integer, Integer, Integer, <PERSON>, <PERSON>, Integer, Integer, Integer, Timestamp, Timestamp, Long> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_pgc_archive.id</code>. 主键
     */
    public void setId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_pgc_archive.id</code>. 主键
     */
    public Integer getId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_creative_pgc_archive.unit_id</code>. 单元id
     */
    public void setUnitId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_creative_pgc_archive.unit_id</code>. 单元id
     */
    public Integer getUnitId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_creative_pgc_archive.creative_id</code>. 创意id
     */
    public void setCreativeId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_creative_pgc_archive.creative_id</code>. 创意id
     */
    public Integer getCreativeId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_creative_pgc_archive.mid</code>. 主站mid
     */
    public void setMid(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_creative_pgc_archive.mid</code>. 主站mid
     */
    public Long getMid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>lau_creative_pgc_archive.aid</code>. 视频id
     */
    public void setAid(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_creative_pgc_archive.aid</code>. 视频id
     */
    public Long getAid() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>lau_creative_pgc_archive.episode_id</code>. epid
     */
    public void setEpisodeId(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_creative_pgc_archive.episode_id</code>. epid
     */
    public Integer getEpisodeId() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_creative_pgc_archive.season_id</code>. seasonId
     */
    public void setSeasonId(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_creative_pgc_archive.season_id</code>. seasonId
     */
    public Integer getSeasonId() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lau_creative_pgc_archive.is_deleted</code>.
     * 软删除：0-有效，1-删除
     */
    public void setIsDeleted(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_creative_pgc_archive.is_deleted</code>.
     * 软删除：0-有效，1-删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>lau_creative_pgc_archive.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_creative_pgc_archive.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(8);
    }

    /**
     * Setter for <code>lau_creative_pgc_archive.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_creative_pgc_archive.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(9);
    }

    /**
     * Setter for <code>lau_creative_pgc_archive.material_no</code>. 高能起播片段id
     */
    public void setMaterialNo(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_creative_pgc_archive.material_no</code>. 高能起播片段id
     */
    public Long getMaterialNo() {
        return (Long) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<Integer, Integer, Integer, Long, Long, Integer, Integer, Integer, Timestamp, Timestamp, Long> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<Integer, Integer, Integer, Long, Long, Integer, Integer, Integer, Timestamp, Timestamp, Long> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<Integer> field1() {
        return TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE.UNIT_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE.CREATIVE_ID;
    }

    @Override
    public Field<Long> field4() {
        return TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE.MID;
    }

    @Override
    public Field<Long> field5() {
        return TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE.AID;
    }

    @Override
    public Field<Integer> field6() {
        return TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE.EPISODE_ID;
    }

    @Override
    public Field<Integer> field7() {
        return TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE.SEASON_ID;
    }

    @Override
    public Field<Integer> field8() {
        return TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field9() {
        return TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE.CTIME;
    }

    @Override
    public Field<Timestamp> field10() {
        return TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE.MTIME;
    }

    @Override
    public Field<Long> field11() {
        return TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE.MATERIAL_NO;
    }

    @Override
    public Integer component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getUnitId();
    }

    @Override
    public Integer component3() {
        return getCreativeId();
    }

    @Override
    public Long component4() {
        return getMid();
    }

    @Override
    public Long component5() {
        return getAid();
    }

    @Override
    public Integer component6() {
        return getEpisodeId();
    }

    @Override
    public Integer component7() {
        return getSeasonId();
    }

    @Override
    public Integer component8() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component9() {
        return getCtime();
    }

    @Override
    public Timestamp component10() {
        return getMtime();
    }

    @Override
    public Long component11() {
        return getMaterialNo();
    }

    @Override
    public Integer value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getUnitId();
    }

    @Override
    public Integer value3() {
        return getCreativeId();
    }

    @Override
    public Long value4() {
        return getMid();
    }

    @Override
    public Long value5() {
        return getAid();
    }

    @Override
    public Integer value6() {
        return getEpisodeId();
    }

    @Override
    public Integer value7() {
        return getSeasonId();
    }

    @Override
    public Integer value8() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value9() {
        return getCtime();
    }

    @Override
    public Timestamp value10() {
        return getMtime();
    }

    @Override
    public Long value11() {
        return getMaterialNo();
    }

    @Override
    public LauCreativePgcArchiveRecord value1(Integer value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativePgcArchiveRecord value2(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauCreativePgcArchiveRecord value3(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativePgcArchiveRecord value4(Long value) {
        setMid(value);
        return this;
    }

    @Override
    public LauCreativePgcArchiveRecord value5(Long value) {
        setAid(value);
        return this;
    }

    @Override
    public LauCreativePgcArchiveRecord value6(Integer value) {
        setEpisodeId(value);
        return this;
    }

    @Override
    public LauCreativePgcArchiveRecord value7(Integer value) {
        setSeasonId(value);
        return this;
    }

    @Override
    public LauCreativePgcArchiveRecord value8(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauCreativePgcArchiveRecord value9(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativePgcArchiveRecord value10(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativePgcArchiveRecord value11(Long value) {
        setMaterialNo(value);
        return this;
    }

    @Override
    public LauCreativePgcArchiveRecord values(Integer value1, Integer value2, Integer value3, Long value4, Long value5, Integer value6, Integer value7, Integer value8, Timestamp value9, Timestamp value10, Long value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativePgcArchiveRecord
     */
    public LauCreativePgcArchiveRecord() {
        super(TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE);
    }

    /**
     * Create a detached, initialised LauCreativePgcArchiveRecord
     */
    public LauCreativePgcArchiveRecord(Integer id, Integer unitId, Integer creativeId, Long mid, Long aid, Integer episodeId, Integer seasonId, Integer isDeleted, Timestamp ctime, Timestamp mtime, Long materialNo) {
        super(TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE);

        setId(id);
        setUnitId(unitId);
        setCreativeId(creativeId);
        setMid(mid);
        setAid(aid);
        setEpisodeId(episodeId);
        setSeasonId(seasonId);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setMaterialNo(materialNo);
    }

    /**
     * Create a detached, initialised LauCreativePgcArchiveRecord
     */
    public LauCreativePgcArchiveRecord(LauCreativePgcArchivePo value) {
        super(TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE);

        if (value != null) {
            setId(value.getId());
            setUnitId(value.getUnitId());
            setCreativeId(value.getCreativeId());
            setMid(value.getMid());
            setAid(value.getAid());
            setEpisodeId(value.getEpisodeId());
            setSeasonId(value.getSeasonId());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setMaterialNo(value.getMaterialNo());
        }
    }
}
