/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitBusinessCategory;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitBusinessCategoryPo;


/**
 * 单元商业分类表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitBusinessCategoryRecord extends UpdatableRecordImpl<LauUnitBusinessCategoryRecord> implements Record8<Integer, Timestamp, Timestamp, Integer, Integer, Integer, Integer, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_unit_business_category.id</code>. 主键id
     */
    public void setId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_unit_business_category.id</code>. 主键id
     */
    public Integer getId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_unit_business_category.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_unit_business_category.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(1);
    }

    /**
     * Setter for <code>lau_unit_business_category.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_unit_business_category.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(2);
    }

    /**
     * Setter for <code>lau_unit_business_category.unit_id</code>. 单元id
     */
    public void setUnitId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_unit_business_category.unit_id</code>. 单元id
     */
    public Integer getUnitId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_unit_business_category.first_category_id</code>.
     * 一级分类
     */
    public void setFirstCategoryId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_unit_business_category.first_category_id</code>.
     * 一级分类
     */
    public Integer getFirstCategoryId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_unit_business_category.second_category_id</code>.
     * 二级分类
     */
    public void setSecondCategoryId(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_unit_business_category.second_category_id</code>.
     * 二级分类
     */
    public Integer getSecondCategoryId() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_unit_business_category.third_category_id</code>.
     * 三级分类
     */
    public void setThirdCategoryId(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_unit_business_category.third_category_id</code>.
     * 三级分类
     */
    public Integer getThirdCategoryId() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lau_unit_business_category.biz_status</code>.
     * 业务状态：0-正常，1-删除
     */
    public void setBizStatus(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_unit_business_category.biz_status</code>.
     * 业务状态：0-正常，1-删除
     */
    public Integer getBizStatus() {
        return (Integer) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<Integer, Timestamp, Timestamp, Integer, Integer, Integer, Integer, Integer> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<Integer, Timestamp, Timestamp, Integer, Integer, Integer, Integer, Integer> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<Integer> field1() {
        return TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.ID;
    }

    @Override
    public Field<Timestamp> field2() {
        return TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.CTIME;
    }

    @Override
    public Field<Timestamp> field3() {
        return TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.MTIME;
    }

    @Override
    public Field<Integer> field4() {
        return TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.UNIT_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.FIRST_CATEGORY_ID;
    }

    @Override
    public Field<Integer> field6() {
        return TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.SECOND_CATEGORY_ID;
    }

    @Override
    public Field<Integer> field7() {
        return TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.THIRD_CATEGORY_ID;
    }

    @Override
    public Field<Integer> field8() {
        return TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.BIZ_STATUS;
    }

    @Override
    public Integer component1() {
        return getId();
    }

    @Override
    public Timestamp component2() {
        return getCtime();
    }

    @Override
    public Timestamp component3() {
        return getMtime();
    }

    @Override
    public Integer component4() {
        return getUnitId();
    }

    @Override
    public Integer component5() {
        return getFirstCategoryId();
    }

    @Override
    public Integer component6() {
        return getSecondCategoryId();
    }

    @Override
    public Integer component7() {
        return getThirdCategoryId();
    }

    @Override
    public Integer component8() {
        return getBizStatus();
    }

    @Override
    public Integer value1() {
        return getId();
    }

    @Override
    public Timestamp value2() {
        return getCtime();
    }

    @Override
    public Timestamp value3() {
        return getMtime();
    }

    @Override
    public Integer value4() {
        return getUnitId();
    }

    @Override
    public Integer value5() {
        return getFirstCategoryId();
    }

    @Override
    public Integer value6() {
        return getSecondCategoryId();
    }

    @Override
    public Integer value7() {
        return getThirdCategoryId();
    }

    @Override
    public Integer value8() {
        return getBizStatus();
    }

    @Override
    public LauUnitBusinessCategoryRecord value1(Integer value) {
        setId(value);
        return this;
    }

    @Override
    public LauUnitBusinessCategoryRecord value2(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauUnitBusinessCategoryRecord value3(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauUnitBusinessCategoryRecord value4(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauUnitBusinessCategoryRecord value5(Integer value) {
        setFirstCategoryId(value);
        return this;
    }

    @Override
    public LauUnitBusinessCategoryRecord value6(Integer value) {
        setSecondCategoryId(value);
        return this;
    }

    @Override
    public LauUnitBusinessCategoryRecord value7(Integer value) {
        setThirdCategoryId(value);
        return this;
    }

    @Override
    public LauUnitBusinessCategoryRecord value8(Integer value) {
        setBizStatus(value);
        return this;
    }

    @Override
    public LauUnitBusinessCategoryRecord values(Integer value1, Timestamp value2, Timestamp value3, Integer value4, Integer value5, Integer value6, Integer value7, Integer value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauUnitBusinessCategoryRecord
     */
    public LauUnitBusinessCategoryRecord() {
        super(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY);
    }

    /**
     * Create a detached, initialised LauUnitBusinessCategoryRecord
     */
    public LauUnitBusinessCategoryRecord(Integer id, Timestamp ctime, Timestamp mtime, Integer unitId, Integer firstCategoryId, Integer secondCategoryId, Integer thirdCategoryId, Integer bizStatus) {
        super(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY);

        setId(id);
        setCtime(ctime);
        setMtime(mtime);
        setUnitId(unitId);
        setFirstCategoryId(firstCategoryId);
        setSecondCategoryId(secondCategoryId);
        setThirdCategoryId(thirdCategoryId);
        setBizStatus(bizStatus);
    }

    /**
     * Create a detached, initialised LauUnitBusinessCategoryRecord
     */
    public LauUnitBusinessCategoryRecord(LauUnitBusinessCategoryPo value) {
        super(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY);

        if (value != null) {
            setId(value.getId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setUnitId(value.getUnitId());
            setFirstCategoryId(value.getFirstCategoryId());
            setSecondCategoryId(value.getSecondCategoryId());
            setThirdCategoryId(value.getThirdCategoryId());
            setBizStatus(value.getBizStatus());
        }
    }
}
