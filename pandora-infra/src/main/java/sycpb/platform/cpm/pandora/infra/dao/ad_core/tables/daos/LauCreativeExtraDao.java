/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeExtra;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeExtraPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeExtraRecord;


/**
 * 创意层级额外信息
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauCreativeExtraDao extends DAOImpl<LauCreativeExtraRecord, LauCreativeExtraPo, Long> {

    /**
     * Create a new LauCreativeExtraDao without any configuration
     */
    public LauCreativeExtraDao() {
        super(TLauCreativeExtra.LAU_CREATIVE_EXTRA, LauCreativeExtraPo.class);
    }

    /**
     * Create a new LauCreativeExtraDao with an attached configuration
     */
    @Autowired
    public LauCreativeExtraDao(Configuration configuration) {
        super(TLauCreativeExtra.LAU_CREATIVE_EXTRA, LauCreativeExtraPo.class, configuration);
    }

    @Override
    public Long getId(LauCreativeExtraPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchById(Long... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauCreativeExtraPo fetchOneById(Long value) {
        return fetchOne(TLauCreativeExtra.LAU_CREATIVE_EXTRA.ID, value);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByCreativeId(Integer... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.CREATIVE_ID, values);
    }

    /**
     * Fetch a unique record that has <code>creative_id = value</code>
     */
    public LauCreativeExtraPo fetchOneByCreativeId(Integer value) {
        return fetchOne(TLauCreativeExtra.LAU_CREATIVE_EXTRA.CREATIVE_ID, value);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByAccountId(Integer... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>campaign_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfCampaignId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.CAMPAIGN_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>campaign_id IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByCampaignId(Integer... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.CAMPAIGN_ID, values);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByUnitId(Integer... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>image_macro_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfImageMacroType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.IMAGE_MACRO_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_macro_type IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByImageMacroType(Integer... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.IMAGE_MACRO_TYPE, values);
    }

    /**
     * Fetch records that have <code>image_macro BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfImageMacro(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.IMAGE_MACRO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_macro IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByImageMacro(String... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.IMAGE_MACRO, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.MTIME, values);
    }

    /**
     * Fetch records that have <code>qualification_package_id BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfQualificationPackageId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.QUALIFICATION_PACKAGE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>qualification_package_id IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByQualificationPackageId(Integer... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.QUALIFICATION_PACKAGE_ID, values);
    }

    /**
     * Fetch records that have <code>ad_space_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfAdSpaceId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.AD_SPACE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ad_space_id IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByAdSpaceId(Integer... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.AD_SPACE_ID, values);
    }

    /**
     * Fetch records that have <code>bili_space_mid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfBiliSpaceMid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.BILI_SPACE_MID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>bili_space_mid IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByBiliSpaceMid(Long... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.BILI_SPACE_MID, values);
    }

    /**
     * Fetch records that have <code>raw_jump_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfRawJumpUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.RAW_JUMP_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>raw_jump_url IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByRawJumpUrl(String... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.RAW_JUMP_URL, values);
    }

    /**
     * Fetch records that have <code>mgk_page_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfMgkPageId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.MGK_PAGE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mgk_page_id IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByMgkPageId(Long... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.MGK_PAGE_ID, values);
    }

    /**
     * Fetch records that have <code>season_avid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfSeasonAvid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.SEASON_AVID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>season_avid IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchBySeasonAvid(Long... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.SEASON_AVID, values);
    }

    /**
     * Fetch records that have <code>season_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfSeasonId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.SEASON_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>season_id IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchBySeasonId(Long... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.SEASON_ID, values);
    }

    /**
     * Fetch records that have <code>parent_creative_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfParentCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.PARENT_CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>parent_creative_id IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByParentCreativeId(Integer... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.PARENT_CREATIVE_ID, values);
    }

    /**
     * Fetch records that have <code>ppc_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfPpcType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.PPC_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ppc_type IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByPpcType(Integer... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.PPC_TYPE, values);
    }

    /**
     * Fetch records that have <code>is_smart_derivative BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauCreativeExtraPo> fetchRangeOfIsSmartDerivative(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeExtra.LAU_CREATIVE_EXTRA.IS_SMART_DERIVATIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_smart_derivative IN (values)</code>
     */
    public List<LauCreativeExtraPo> fetchByIsSmartDerivative(Integer... values) {
        return fetch(TLauCreativeExtra.LAU_CREATIVE_EXTRA.IS_SMART_DERIVATIVE, values);
    }
}
