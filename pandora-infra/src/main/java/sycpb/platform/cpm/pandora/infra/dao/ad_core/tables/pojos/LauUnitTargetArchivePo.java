/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitTargetArchivePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer   id;
    private Integer   unitId;
    private Integer   platformType;
    private Long      avid;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;

    public LauUnitTargetArchivePo() {}

    public LauUnitTargetArchivePo(LauUnitTargetArchivePo value) {
        this.id = value.id;
        this.unitId = value.unitId;
        this.platformType = value.platformType;
        this.avid = value.avid;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
    }

    public LauUnitTargetArchivePo(
        Integer   id,
        Integer   unitId,
        Integer   platformType,
        Long      avid,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime
    ) {
        this.id = id;
        this.unitId = unitId;
        this.platformType = platformType;
        this.avid = avid;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_unit_target_archive.id</code>. id
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_unit_target_archive.id</code>. id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_unit_target_archive.unit_id</code>. 单元id
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_unit_target_archive.unit_id</code>. 单元id
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for <code>lau_unit_target_archive.platform_type</code>. 平台类型: 1-安卓
     * 2-IOS
     */
    public Integer getPlatformType() {
        return this.platformType;
    }

    /**
     * Setter for <code>lau_unit_target_archive.platform_type</code>. 平台类型: 1-安卓
     * 2-IOS
     */
    public void setPlatformType(Integer platformType) {
        this.platformType = platformType;
    }

    /**
     * Getter for <code>lau_unit_target_archive.avid</code>. 标签id
     */
    public Long getAvid() {
        return this.avid;
    }

    /**
     * Setter for <code>lau_unit_target_archive.avid</code>. 标签id
     */
    public void setAvid(Long avid) {
        this.avid = avid;
    }

    /**
     * Getter for <code>lau_unit_target_archive.is_deleted</code>. 软删除: 0-有效
     * 1-删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_unit_target_archive.is_deleted</code>. 软删除: 0-有效
     * 1-删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_unit_target_archive.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_unit_target_archive.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_unit_target_archive.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_unit_target_archive.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauUnitTargetArchivePo (");

        sb.append(id);
        sb.append(", ").append(unitId);
        sb.append(", ").append(platformType);
        sb.append(", ").append(avid);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);

        sb.append(")");
        return sb.toString();
    }
}
