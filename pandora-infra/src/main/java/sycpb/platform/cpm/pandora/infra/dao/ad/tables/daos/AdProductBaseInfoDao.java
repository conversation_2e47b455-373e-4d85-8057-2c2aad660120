/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TAdProductBaseInfo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.AdProductBaseInfoPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.AdProductBaseInfoRecord;


/**
 * 投放产品基本信息
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class AdProductBaseInfoDao extends DAOImpl<AdProductBaseInfoRecord, AdProductBaseInfoPo, Long> {

    /**
     * Create a new AdProductBaseInfoDao without any configuration
     */
    public AdProductBaseInfoDao() {
        super(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO, AdProductBaseInfoPo.class);
    }

    /**
     * Create a new AdProductBaseInfoDao with an attached configuration
     */
    @Autowired
    public AdProductBaseInfoDao(Configuration configuration) {
        super(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO, AdProductBaseInfoPo.class, configuration);
    }

    @Override
    public Long getId(AdProductBaseInfoPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchById(Long... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public AdProductBaseInfoPo fetchOneById(Long value) {
        return fetchOne(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.ID, value);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchByAccountId(Integer... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>library_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfLibraryId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.LIBRARY_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>library_id IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchByLibraryId(Long... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.LIBRARY_ID, values);
    }

    /**
     * Fetch records that have <code>library_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfLibraryType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.LIBRARY_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>library_type IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchByLibraryType(Integer... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.LIBRARY_TYPE, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchByName(String... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.NAME, values);
    }

    /**
     * Fetch records that have <code>biz_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfBizStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.BIZ_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_status IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchByBizStatus(Integer... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.BIZ_STATUS, values);
    }

    /**
     * Fetch records that have <code>first_category_code BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfFirstCategoryCode(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.FIRST_CATEGORY_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>first_category_code IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchByFirstCategoryCode(Long... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.FIRST_CATEGORY_CODE, values);
    }

    /**
     * Fetch records that have <code>second_category_code BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfSecondCategoryCode(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.SECOND_CATEGORY_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>second_category_code IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchBySecondCategoryCode(Long... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.SECOND_CATEGORY_CODE, values);
    }

    /**
     * Fetch records that have <code>third_category_code BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfThirdCategoryCode(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.THIRD_CATEGORY_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>third_category_code IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchByThirdCategoryCode(Long... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.THIRD_CATEGORY_CODE, values);
    }

    /**
     * Fetch records that have <code>spu_code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfSpuCode(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.SPU_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>spu_code IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchBySpuCode(Long... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.SPU_CODE, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchByIsDeleted(Integer... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchByCtime(Timestamp... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchByMtime(Timestamp... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.MTIME, values);
    }

    /**
     * Fetch records that have <code>sku_code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AdProductBaseInfoPo> fetchRangeOfSkuCode(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.SKU_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sku_code IN (values)</code>
     */
    public List<AdProductBaseInfoPo> fetchBySkuCode(Long... values) {
        return fetch(TAdProductBaseInfo.AD_PRODUCT_BASE_INFO.SKU_CODE, values);
    }
}
