/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauSplashScreenCreativeImage;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauSplashScreenCreativeImagePo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauSplashScreenCreativeImageRecord;


/**
 * 效果闪屏图片表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauSplashScreenCreativeImageDao extends DAOImpl<LauSplashScreenCreativeImageRecord, LauSplashScreenCreativeImagePo, Long> {

    /**
     * Create a new LauSplashScreenCreativeImageDao without any configuration
     */
    public LauSplashScreenCreativeImageDao() {
        super(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE, LauSplashScreenCreativeImagePo.class);
    }

    /**
     * Create a new LauSplashScreenCreativeImageDao with an attached
     * configuration
     */
    @Autowired
    public LauSplashScreenCreativeImageDao(Configuration configuration) {
        super(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE, LauSplashScreenCreativeImagePo.class, configuration);
    }

    @Override
    public Long getId(LauSplashScreenCreativeImagePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchById(Long... values) {
        return fetch(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauSplashScreenCreativeImagePo fetchOneById(Long value) {
        return fetchOne(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.ID, value);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchByCreativeId(Integer... values) {
        return fetch(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.CREATIVE_ID, values);
    }

    /**
     * Fetch records that have <code>width BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchRangeOfWidth(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.WIDTH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>width IN (values)</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchByWidth(Integer... values) {
        return fetch(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.WIDTH, values);
    }

    /**
     * Fetch records that have <code>height BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchRangeOfHeight(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.HEIGHT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>height IN (values)</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchByHeight(Integer... values) {
        return fetch(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.HEIGHT, values);
    }

    /**
     * Fetch records that have <code>url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchRangeOfUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>url IN (values)</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchByUrl(String... values) {
        return fetch(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.URL, values);
    }

    /**
     * Fetch records that have <code>md5 BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchRangeOfMd5(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.MD5, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>md5 IN (values)</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchByMd5(String... values) {
        return fetch(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.MD5, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchByCtime(Timestamp... values) {
        return fetch(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchByMtime(Timestamp... values) {
        return fetch(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.MTIME, values);
    }

    /**
     * Fetch records that have <code>material_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchRangeOfMaterialId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.MATERIAL_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>material_id IN (values)</code>
     */
    public List<LauSplashScreenCreativeImagePo> fetchByMaterialId(Long... values) {
        return fetch(TLauSplashScreenCreativeImage.LAU_SPLASH_SCREEN_CREATIVE_IMAGE.MATERIAL_ID, values);
    }
}
