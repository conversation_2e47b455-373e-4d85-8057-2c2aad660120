/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeAigcMaterialReplaceHistory;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeAigcMaterialReplaceHistoryPo;


/**
 * 新三连创意素材aigc替换历史记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeAigcMaterialReplaceHistoryRecord extends UpdatableRecordImpl<LauCreativeAigcMaterialReplaceHistoryRecord> implements Record8<<PERSON>, <PERSON>te<PERSON>, <PERSON>te<PERSON>, Integer, Integer, String, Timestamp, Timestamp> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_aigc_material_replace_history.id</code>.
     * 自增主键ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_aigc_material_replace_history.id</code>.
     * 自增主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for
     * <code>lau_creative_aigc_material_replace_history.account_id</code>. 账户id
     */
    public void setAccountId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for
     * <code>lau_creative_aigc_material_replace_history.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return (Integer) get(1);
    }

    /**
     * Setter for
     * <code>lau_creative_aigc_material_replace_history.campaign_id</code>. 计划id
     */
    public void setCampaignId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for
     * <code>lau_creative_aigc_material_replace_history.campaign_id</code>. 计划id
     */
    public Integer getCampaignId() {
        return (Integer) get(2);
    }

    /**
     * Setter for
     * <code>lau_creative_aigc_material_replace_history.unit_id</code>. 单元id
     */
    public void setUnitId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for
     * <code>lau_creative_aigc_material_replace_history.unit_id</code>. 单元id
     */
    public Integer getUnitId() {
        return (Integer) get(3);
    }

    /**
     * Setter for
     * <code>lau_creative_aigc_material_replace_history.creative_id</code>. 创意id
     */
    public void setCreativeId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for
     * <code>lau_creative_aigc_material_replace_history.creative_id</code>. 创意id
     */
    public Integer getCreativeId() {
        return (Integer) get(4);
    }

    /**
     * Setter for
     * <code>lau_creative_aigc_material_replace_history.history</code>. aigc变更记录
     */
    public void setHistory(String value) {
        set(5, value);
    }

    /**
     * Getter for
     * <code>lau_creative_aigc_material_replace_history.history</code>. aigc变更记录
     */
    public String getHistory() {
        return (String) get(5);
    }

    /**
     * Setter for <code>lau_creative_aigc_material_replace_history.ctime</code>.
     * 创建时间
     */
    public void setCtime(Timestamp value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_creative_aigc_material_replace_history.ctime</code>.
     * 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(6);
    }

    /**
     * Setter for <code>lau_creative_aigc_material_replace_history.mtime</code>.
     * 更新时间
     */
    public void setMtime(Timestamp value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_creative_aigc_material_replace_history.mtime</code>.
     * 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<Long, Integer, Integer, Integer, Integer, String, Timestamp, Timestamp> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<Long, Integer, Integer, Integer, Integer, String, Timestamp, Timestamp> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauCreativeAigcMaterialReplaceHistory.LAU_CREATIVE_AIGC_MATERIAL_REPLACE_HISTORY.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauCreativeAigcMaterialReplaceHistory.LAU_CREATIVE_AIGC_MATERIAL_REPLACE_HISTORY.ACCOUNT_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TLauCreativeAigcMaterialReplaceHistory.LAU_CREATIVE_AIGC_MATERIAL_REPLACE_HISTORY.CAMPAIGN_ID;
    }

    @Override
    public Field<Integer> field4() {
        return TLauCreativeAigcMaterialReplaceHistory.LAU_CREATIVE_AIGC_MATERIAL_REPLACE_HISTORY.UNIT_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauCreativeAigcMaterialReplaceHistory.LAU_CREATIVE_AIGC_MATERIAL_REPLACE_HISTORY.CREATIVE_ID;
    }

    @Override
    public Field<String> field6() {
        return TLauCreativeAigcMaterialReplaceHistory.LAU_CREATIVE_AIGC_MATERIAL_REPLACE_HISTORY.HISTORY;
    }

    @Override
    public Field<Timestamp> field7() {
        return TLauCreativeAigcMaterialReplaceHistory.LAU_CREATIVE_AIGC_MATERIAL_REPLACE_HISTORY.CTIME;
    }

    @Override
    public Field<Timestamp> field8() {
        return TLauCreativeAigcMaterialReplaceHistory.LAU_CREATIVE_AIGC_MATERIAL_REPLACE_HISTORY.MTIME;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getAccountId();
    }

    @Override
    public Integer component3() {
        return getCampaignId();
    }

    @Override
    public Integer component4() {
        return getUnitId();
    }

    @Override
    public Integer component5() {
        return getCreativeId();
    }

    @Override
    public String component6() {
        return getHistory();
    }

    @Override
    public Timestamp component7() {
        return getCtime();
    }

    @Override
    public Timestamp component8() {
        return getMtime();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getAccountId();
    }

    @Override
    public Integer value3() {
        return getCampaignId();
    }

    @Override
    public Integer value4() {
        return getUnitId();
    }

    @Override
    public Integer value5() {
        return getCreativeId();
    }

    @Override
    public String value6() {
        return getHistory();
    }

    @Override
    public Timestamp value7() {
        return getCtime();
    }

    @Override
    public Timestamp value8() {
        return getMtime();
    }

    @Override
    public LauCreativeAigcMaterialReplaceHistoryRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativeAigcMaterialReplaceHistoryRecord value2(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauCreativeAigcMaterialReplaceHistoryRecord value3(Integer value) {
        setCampaignId(value);
        return this;
    }

    @Override
    public LauCreativeAigcMaterialReplaceHistoryRecord value4(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauCreativeAigcMaterialReplaceHistoryRecord value5(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeAigcMaterialReplaceHistoryRecord value6(String value) {
        setHistory(value);
        return this;
    }

    @Override
    public LauCreativeAigcMaterialReplaceHistoryRecord value7(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativeAigcMaterialReplaceHistoryRecord value8(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativeAigcMaterialReplaceHistoryRecord values(Long value1, Integer value2, Integer value3, Integer value4, Integer value5, String value6, Timestamp value7, Timestamp value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativeAigcMaterialReplaceHistoryRecord
     */
    public LauCreativeAigcMaterialReplaceHistoryRecord() {
        super(TLauCreativeAigcMaterialReplaceHistory.LAU_CREATIVE_AIGC_MATERIAL_REPLACE_HISTORY);
    }

    /**
     * Create a detached, initialised
     * LauCreativeAigcMaterialReplaceHistoryRecord
     */
    public LauCreativeAigcMaterialReplaceHistoryRecord(Long id, Integer accountId, Integer campaignId, Integer unitId, Integer creativeId, String history, Timestamp ctime, Timestamp mtime) {
        super(TLauCreativeAigcMaterialReplaceHistory.LAU_CREATIVE_AIGC_MATERIAL_REPLACE_HISTORY);

        setId(id);
        setAccountId(accountId);
        setCampaignId(campaignId);
        setUnitId(unitId);
        setCreativeId(creativeId);
        setHistory(history);
        setCtime(ctime);
        setMtime(mtime);
    }

    /**
     * Create a detached, initialised
     * LauCreativeAigcMaterialReplaceHistoryRecord
     */
    public LauCreativeAigcMaterialReplaceHistoryRecord(LauCreativeAigcMaterialReplaceHistoryPo value) {
        super(TLauCreativeAigcMaterialReplaceHistory.LAU_CREATIVE_AIGC_MATERIAL_REPLACE_HISTORY);

        if (value != null) {
            setId(value.getId());
            setAccountId(value.getAccountId());
            setCampaignId(value.getCampaignId());
            setUnitId(value.getUnitId());
            setCreativeId(value.getCreativeId());
            setHistory(value.getHistory());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
        }
    }
}
