/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 效果闪屏按钮表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauSplashScreenCreativeButtonPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      buttonId;
    private Integer   creativeId;
    private Integer   x;
    private Integer   y;
    private String    bgColorDay;
    private String    bgColorNight;
    private String    textColorDay;
    private String    textColorNight;
    private Integer   width;
    private Integer   height;
    private Integer   buttonType;
    private Integer   interactStyle;
    private String    jumpGuideContent;
    private String    jumpImageUrl;
    private String    jumpImageMd5;
    private String    schemaGuideContent;
    private String    schemaImageUrl;
    private String    schemaImageMd5;
    private Integer   clickExpandRatio;
    private Integer   fontRatio;
    private Integer   degradeType;
    private Integer   seq;
    private String    relatedIds;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;

    public LauSplashScreenCreativeButtonPo() {}

    public LauSplashScreenCreativeButtonPo(LauSplashScreenCreativeButtonPo value) {
        this.buttonId = value.buttonId;
        this.creativeId = value.creativeId;
        this.x = value.x;
        this.y = value.y;
        this.bgColorDay = value.bgColorDay;
        this.bgColorNight = value.bgColorNight;
        this.textColorDay = value.textColorDay;
        this.textColorNight = value.textColorNight;
        this.width = value.width;
        this.height = value.height;
        this.buttonType = value.buttonType;
        this.interactStyle = value.interactStyle;
        this.jumpGuideContent = value.jumpGuideContent;
        this.jumpImageUrl = value.jumpImageUrl;
        this.jumpImageMd5 = value.jumpImageMd5;
        this.schemaGuideContent = value.schemaGuideContent;
        this.schemaImageUrl = value.schemaImageUrl;
        this.schemaImageMd5 = value.schemaImageMd5;
        this.clickExpandRatio = value.clickExpandRatio;
        this.fontRatio = value.fontRatio;
        this.degradeType = value.degradeType;
        this.seq = value.seq;
        this.relatedIds = value.relatedIds;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
    }

    public LauSplashScreenCreativeButtonPo(
        Long      buttonId,
        Integer   creativeId,
        Integer   x,
        Integer   y,
        String    bgColorDay,
        String    bgColorNight,
        String    textColorDay,
        String    textColorNight,
        Integer   width,
        Integer   height,
        Integer   buttonType,
        Integer   interactStyle,
        String    jumpGuideContent,
        String    jumpImageUrl,
        String    jumpImageMd5,
        String    schemaGuideContent,
        String    schemaImageUrl,
        String    schemaImageMd5,
        Integer   clickExpandRatio,
        Integer   fontRatio,
        Integer   degradeType,
        Integer   seq,
        String    relatedIds,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime
    ) {
        this.buttonId = buttonId;
        this.creativeId = creativeId;
        this.x = x;
        this.y = y;
        this.bgColorDay = bgColorDay;
        this.bgColorNight = bgColorNight;
        this.textColorDay = textColorDay;
        this.textColorNight = textColorNight;
        this.width = width;
        this.height = height;
        this.buttonType = buttonType;
        this.interactStyle = interactStyle;
        this.jumpGuideContent = jumpGuideContent;
        this.jumpImageUrl = jumpImageUrl;
        this.jumpImageMd5 = jumpImageMd5;
        this.schemaGuideContent = schemaGuideContent;
        this.schemaImageUrl = schemaImageUrl;
        this.schemaImageMd5 = schemaImageMd5;
        this.clickExpandRatio = clickExpandRatio;
        this.fontRatio = fontRatio;
        this.degradeType = degradeType;
        this.seq = seq;
        this.relatedIds = relatedIds;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.button_id</code>. 按钮id
     */
    public Long getButtonId() {
        return this.buttonId;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.button_id</code>. 按钮id
     */
    public void setButtonId(Long buttonId) {
        this.buttonId = buttonId;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.creative_id</code>.
     * 创意id
     */
    public Integer getCreativeId() {
        return this.creativeId;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.creative_id</code>.
     * 创意id
     */
    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.x</code>. x轴相对上边位置比例
     */
    public Integer getX() {
        return this.x;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.x</code>. x轴相对上边位置比例
     */
    public void setX(Integer x) {
        this.x = x;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.y</code>. y轴相对左边位置比例
     */
    public Integer getY() {
        return this.y;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.y</code>. y轴相对左边位置比例
     */
    public void setY(Integer y) {
        this.y = y;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.bg_color_day</code>.
     * 背景日间色值
     */
    public String getBgColorDay() {
        return this.bgColorDay;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.bg_color_day</code>.
     * 背景日间色值
     */
    public void setBgColorDay(String bgColorDay) {
        this.bgColorDay = bgColorDay;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.bg_color_night</code>.
     * 背景夜间色值
     */
    public String getBgColorNight() {
        return this.bgColorNight;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.bg_color_night</code>.
     * 背景夜间色值
     */
    public void setBgColorNight(String bgColorNight) {
        this.bgColorNight = bgColorNight;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.text_color_day</code>.
     * 文案日间色值
     */
    public String getTextColorDay() {
        return this.textColorDay;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.text_color_day</code>.
     * 文案日间色值
     */
    public void setTextColorDay(String textColorDay) {
        this.textColorDay = textColorDay;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button.text_color_night</code>. 文案夜间色值
     */
    public String getTextColorNight() {
        return this.textColorNight;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button.text_color_night</code>. 文案夜间色值
     */
    public void setTextColorNight(String textColorNight) {
        this.textColorNight = textColorNight;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.width</code>. 宽度百分比
     */
    public Integer getWidth() {
        return this.width;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.width</code>. 宽度百分比
     */
    public void setWidth(Integer width) {
        this.width = width;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.height</code>. 高度百分比
     */
    public Integer getHeight() {
        return this.height;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.height</code>. 高度百分比
     */
    public void setHeight(Integer height) {
        this.height = height;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.button_type</code>.
     * 按钮类型 1-动效按钮 2-固定文案按钮 3-自定义文案按钮
     */
    public Integer getButtonType() {
        return this.buttonType;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.button_type</code>.
     * 按钮类型 1-动效按钮 2-固定文案按钮 3-自定义文案按钮
     */
    public void setButtonType(Integer buttonType) {
        this.buttonType = buttonType;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.interact_style</code>.
     * 交互样式：3-文字 6-全屏滑动 7-扭一扭
     */
    public Integer getInteractStyle() {
        return this.interactStyle;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.interact_style</code>.
     * 交互样式：3-文字 6-全屏滑动 7-扭一扭
     */
    public void setInteractStyle(Integer interactStyle) {
        this.interactStyle = interactStyle;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button.jump_guide_content</code>. 跳转引导文案
     */
    public String getJumpGuideContent() {
        return this.jumpGuideContent;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button.jump_guide_content</code>. 跳转引导文案
     */
    public void setJumpGuideContent(String jumpGuideContent) {
        this.jumpGuideContent = jumpGuideContent;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.jump_image_url</code>.
     * 跳转图片url（目前指lottie对应的json）
     */
    public String getJumpImageUrl() {
        return this.jumpImageUrl;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.jump_image_url</code>.
     * 跳转图片url（目前指lottie对应的json）
     */
    public void setJumpImageUrl(String jumpImageUrl) {
        this.jumpImageUrl = jumpImageUrl;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.jump_image_md5</code>.
     * 跳转图片md5
     */
    public String getJumpImageMd5() {
        return this.jumpImageMd5;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.jump_image_md5</code>.
     * 跳转图片md5
     */
    public void setJumpImageMd5(String jumpImageMd5) {
        this.jumpImageMd5 = jumpImageMd5;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button.schema_guide_content</code>.
     * 唤起引导文案
     */
    public String getSchemaGuideContent() {
        return this.schemaGuideContent;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button.schema_guide_content</code>.
     * 唤起引导文案
     */
    public void setSchemaGuideContent(String schemaGuideContent) {
        this.schemaGuideContent = schemaGuideContent;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button.schema_image_url</code>.
     * 唤起图片url（目前指lottie对应的json）
     */
    public String getSchemaImageUrl() {
        return this.schemaImageUrl;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button.schema_image_url</code>.
     * 唤起图片url（目前指lottie对应的json）
     */
    public void setSchemaImageUrl(String schemaImageUrl) {
        this.schemaImageUrl = schemaImageUrl;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button.schema_image_md5</code>. 唤起图片md5
     */
    public String getSchemaImageMd5() {
        return this.schemaImageMd5;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button.schema_image_md5</code>. 唤起图片md5
     */
    public void setSchemaImageMd5(String schemaImageMd5) {
        this.schemaImageMd5 = schemaImageMd5;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button.click_expand_ratio</code>.
     * 点击区域扩张比例
     */
    public Integer getClickExpandRatio() {
        return this.clickExpandRatio;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button.click_expand_ratio</code>.
     * 点击区域扩张比例
     */
    public void setClickExpandRatio(Integer clickExpandRatio) {
        this.clickExpandRatio = clickExpandRatio;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.font_ratio</code>.
     * 字体百分比
     */
    public Integer getFontRatio() {
        return this.fontRatio;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.font_ratio</code>.
     * 字体百分比
     */
    public void setFontRatio(Integer fontRatio) {
        this.fontRatio = fontRatio;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.degrade_type</code>.
     * 降级策略 0-默认 1-外层素材去除动效
     */
    public Integer getDegradeType() {
        return this.degradeType;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.degrade_type</code>.
     * 降级策略 0-默认 1-外层素材去除动效
     */
    public void setDegradeType(Integer degradeType) {
        this.degradeType = degradeType;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.seq</code>. 序号
     */
    public Integer getSeq() {
        return this.seq;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.seq</code>. 序号
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.related_ids</code>.
     * 关联按钮id，用于选择式和摇一摇进行按钮关联
     */
    public String getRelatedIds() {
        return this.relatedIds;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.related_ids</code>.
     * 关联按钮id，用于选择式和摇一摇进行按钮关联
     */
    public void setRelatedIds(String relatedIds) {
        this.relatedIds = relatedIds;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauSplashScreenCreativeButtonPo (");

        sb.append(buttonId);
        sb.append(", ").append(creativeId);
        sb.append(", ").append(x);
        sb.append(", ").append(y);
        sb.append(", ").append(bgColorDay);
        sb.append(", ").append(bgColorNight);
        sb.append(", ").append(textColorDay);
        sb.append(", ").append(textColorNight);
        sb.append(", ").append(width);
        sb.append(", ").append(height);
        sb.append(", ").append(buttonType);
        sb.append(", ").append(interactStyle);
        sb.append(", ").append(jumpGuideContent);
        sb.append(", ").append(jumpImageUrl);
        sb.append(", ").append(jumpImageMd5);
        sb.append(", ").append(schemaGuideContent);
        sb.append(", ").append(schemaImageUrl);
        sb.append(", ").append(schemaImageMd5);
        sb.append(", ").append(clickExpandRatio);
        sb.append(", ").append(fontRatio);
        sb.append(", ").append(degradeType);
        sb.append(", ").append(seq);
        sb.append(", ").append(relatedIds);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);

        sb.append(")");
        return sb.toString();
    }
}
