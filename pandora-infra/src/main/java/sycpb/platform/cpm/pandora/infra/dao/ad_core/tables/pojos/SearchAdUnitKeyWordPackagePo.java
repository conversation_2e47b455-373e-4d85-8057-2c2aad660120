/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 单元关键词表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SearchAdUnitKeyWordPackagePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private String    packageMd5;
    private String    keyWord;
    private Timestamp ctime;
    private Timestamp mtime;
    private String    keywordMd5;
    private Integer   source;
    private String    searchWord;

    public SearchAdUnitKeyWordPackagePo() {}

    public SearchAdUnitKeyWordPackagePo(SearchAdUnitKeyWordPackagePo value) {
        this.id = value.id;
        this.packageMd5 = value.packageMd5;
        this.keyWord = value.keyWord;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.keywordMd5 = value.keywordMd5;
        this.source = value.source;
        this.searchWord = value.searchWord;
    }

    public SearchAdUnitKeyWordPackagePo(
        Long      id,
        String    packageMd5,
        String    keyWord,
        Timestamp ctime,
        Timestamp mtime,
        String    keywordMd5,
        Integer   source,
        String    searchWord
    ) {
        this.id = id;
        this.packageMd5 = packageMd5;
        this.keyWord = keyWord;
        this.ctime = ctime;
        this.mtime = mtime;
        this.keywordMd5 = keywordMd5;
        this.source = source;
        this.searchWord = searchWord;
    }

    /**
     * Getter for <code>search_ad_unit_key_word_package.id</code>. 自增ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>search_ad_unit_key_word_package.id</code>. 自增ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>search_ad_unit_key_word_package.package_md5</code>.
     * 词包md5
     */
    public String getPackageMd5() {
        return this.packageMd5;
    }

    /**
     * Setter for <code>search_ad_unit_key_word_package.package_md5</code>.
     * 词包md5
     */
    public void setPackageMd5(String packageMd5) {
        this.packageMd5 = packageMd5;
    }

    /**
     * Getter for <code>search_ad_unit_key_word_package.key_word</code>. 关键词
     */
    public String getKeyWord() {
        return this.keyWord;
    }

    /**
     * Setter for <code>search_ad_unit_key_word_package.key_word</code>. 关键词
     */
    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    /**
     * Getter for <code>search_ad_unit_key_word_package.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>search_ad_unit_key_word_package.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>search_ad_unit_key_word_package.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>search_ad_unit_key_word_package.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>search_ad_unit_key_word_package.keyword_md5</code>.
     * 关键词md5
     */
    public String getKeywordMd5() {
        return this.keywordMd5;
    }

    /**
     * Setter for <code>search_ad_unit_key_word_package.keyword_md5</code>.
     * 关键词md5
     */
    public void setKeywordMd5(String keywordMd5) {
        this.keywordMd5 = keywordMd5;
    }

    /**
     * Getter for <code>search_ad_unit_key_word_package.source</code>. 0-商业召回
     * 1-主站sug
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * Setter for <code>search_ad_unit_key_word_package.source</code>. 0-商业召回
     * 1-主站sug
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * Getter for <code>search_ad_unit_key_word_package.search_word</code>. 搜索词
     */
    public String getSearchWord() {
        return this.searchWord;
    }

    /**
     * Setter for <code>search_ad_unit_key_word_package.search_word</code>. 搜索词
     */
    public void setSearchWord(String searchWord) {
        this.searchWord = searchWord;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("SearchAdUnitKeyWordPackagePo (");

        sb.append(id);
        sb.append(", ").append(packageMd5);
        sb.append(", ").append(keyWord);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(keywordMd5);
        sb.append(", ").append(source);
        sb.append(", ").append(searchWord);

        sb.append(")");
        return sb.toString();
    }
}
