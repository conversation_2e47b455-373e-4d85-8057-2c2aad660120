/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 创意探索投放端侧候选集
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeExploreCandidateMappingPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   accountId;
    private Integer   campaignId;
    private Integer   unitId;
    private Integer   creativeId;
    private Integer   candidateCampaignId;
    private Integer   candidateUnitId;
    private Integer   candidateCreativeId;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;

    public LauCreativeExploreCandidateMappingPo() {}

    public LauCreativeExploreCandidateMappingPo(LauCreativeExploreCandidateMappingPo value) {
        this.id = value.id;
        this.accountId = value.accountId;
        this.campaignId = value.campaignId;
        this.unitId = value.unitId;
        this.creativeId = value.creativeId;
        this.candidateCampaignId = value.candidateCampaignId;
        this.candidateUnitId = value.candidateUnitId;
        this.candidateCreativeId = value.candidateCreativeId;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
    }

    public LauCreativeExploreCandidateMappingPo(
        Long      id,
        Integer   accountId,
        Integer   campaignId,
        Integer   unitId,
        Integer   creativeId,
        Integer   candidateCampaignId,
        Integer   candidateUnitId,
        Integer   candidateCreativeId,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime
    ) {
        this.id = id;
        this.accountId = accountId;
        this.campaignId = campaignId;
        this.unitId = unitId;
        this.creativeId = creativeId;
        this.candidateCampaignId = candidateCampaignId;
        this.candidateUnitId = candidateUnitId;
        this.candidateCreativeId = candidateCreativeId;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_creative_explore_candidate_mapping.id</code>. 自增主键ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_creative_explore_candidate_mapping.id</code>. 自增主键ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.account_id</code>. 账户id
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.campaign_id</code>. 计划id
     */
    public Integer getCampaignId() {
        return this.campaignId;
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.campaign_id</code>. 计划id
     */
    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    /**
     * Getter for <code>lau_creative_explore_candidate_mapping.unit_id</code>.
     * 单元id
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_creative_explore_candidate_mapping.unit_id</code>.
     * 单元id
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.creative_id</code>.
     * 创意探索母创意id
     */
    public Integer getCreativeId() {
        return this.creativeId;
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.creative_id</code>.
     * 创意探索母创意id
     */
    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.candidate_campaign_id</code>.
     * 创意探索候选计划id
     */
    public Integer getCandidateCampaignId() {
        return this.candidateCampaignId;
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.candidate_campaign_id</code>.
     * 创意探索候选计划id
     */
    public void setCandidateCampaignId(Integer candidateCampaignId) {
        this.candidateCampaignId = candidateCampaignId;
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.candidate_unit_id</code>.
     * 创意探索候选单元id
     */
    public Integer getCandidateUnitId() {
        return this.candidateUnitId;
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.candidate_unit_id</code>.
     * 创意探索候选单元id
     */
    public void setCandidateUnitId(Integer candidateUnitId) {
        this.candidateUnitId = candidateUnitId;
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.candidate_creative_id</code>.
     * 创意探索候选创意id
     */
    public Integer getCandidateCreativeId() {
        return this.candidateCreativeId;
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.candidate_creative_id</code>.
     * 创意探索候选创意id
     */
    public void setCandidateCreativeId(Integer candidateCreativeId) {
        this.candidateCreativeId = candidateCreativeId;
    }

    /**
     * Getter for
     * <code>lau_creative_explore_candidate_mapping.is_deleted</code>. 是否被删除
     * 0-正常 1-被删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for
     * <code>lau_creative_explore_candidate_mapping.is_deleted</code>. 是否被删除
     * 0-正常 1-被删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_creative_explore_candidate_mapping.ctime</code>.
     * 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_creative_explore_candidate_mapping.ctime</code>.
     * 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_creative_explore_candidate_mapping.mtime</code>.
     * 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_creative_explore_candidate_mapping.mtime</code>.
     * 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauCreativeExploreCandidateMappingPo (");

        sb.append(id);
        sb.append(", ").append(accountId);
        sb.append(", ").append(campaignId);
        sb.append(", ").append(unitId);
        sb.append(", ").append(creativeId);
        sb.append(", ").append(candidateCampaignId);
        sb.append(", ").append(candidateUnitId);
        sb.append(", ").append(candidateCreativeId);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);

        sb.append(")");
        return sb.toString();
    }
}
