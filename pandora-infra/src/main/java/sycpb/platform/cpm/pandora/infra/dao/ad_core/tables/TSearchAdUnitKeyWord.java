/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.SearchAdUnitKeyWordRecord;


/**
 * 单元关键词表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TSearchAdUnitKeyWord extends TableImpl<SearchAdUnitKeyWordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>search_ad_unit_key_word</code>
     */
    public static final TSearchAdUnitKeyWord SEARCH_AD_UNIT_KEY_WORD = new TSearchAdUnitKeyWord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SearchAdUnitKeyWordRecord> getRecordType() {
        return SearchAdUnitKeyWordRecord.class;
    }

    /**
     * The column <code>search_ad_unit_key_word.id</code>. 自增ID
     */
    public final TableField<SearchAdUnitKeyWordRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增ID");

    /**
     * The column <code>search_ad_unit_key_word.unit_id</code>. 单元ID
     */
    public final TableField<SearchAdUnitKeyWordRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元ID");

    /**
     * The column <code>search_ad_unit_key_word.key_word</code>. 关键词
     */
    public final TableField<SearchAdUnitKeyWordRecord, String> KEY_WORD = createField(DSL.name("key_word"), SQLDataType.VARCHAR(256).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "关键词");

    /**
     * The column <code>search_ad_unit_key_word.ctime</code>. 添加时间
     */
    public final TableField<SearchAdUnitKeyWordRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column <code>search_ad_unit_key_word.mtime</code>. 更新时间
     */
    public final TableField<SearchAdUnitKeyWordRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>search_ad_unit_key_word.keyword_md5</code>. 关键词md5
     */
    public final TableField<SearchAdUnitKeyWordRecord, String> KEYWORD_MD5 = createField(DSL.name("keyword_md5"), SQLDataType.VARCHAR(40).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "关键词md5");

    /**
     * The column <code>search_ad_unit_key_word.source</code>. 0-商业召回 1-主站sug
     */
    public final TableField<SearchAdUnitKeyWordRecord, Integer> SOURCE = createField(DSL.name("source"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "0-商业召回 1-主站sug");

    /**
     * The column <code>search_ad_unit_key_word.search_word</code>. 搜索词
     */
    public final TableField<SearchAdUnitKeyWordRecord, String> SEARCH_WORD = createField(DSL.name("search_word"), SQLDataType.VARCHAR(256).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "搜索词");

    private TSearchAdUnitKeyWord(Name alias, Table<SearchAdUnitKeyWordRecord> aliased) {
        this(alias, aliased, null);
    }

    private TSearchAdUnitKeyWord(Name alias, Table<SearchAdUnitKeyWordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("单元关键词表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>search_ad_unit_key_word</code> table reference
     */
    public TSearchAdUnitKeyWord(String alias) {
        this(DSL.name(alias), SEARCH_AD_UNIT_KEY_WORD);
    }

    /**
     * Create an aliased <code>search_ad_unit_key_word</code> table reference
     */
    public TSearchAdUnitKeyWord(Name alias) {
        this(alias, SEARCH_AD_UNIT_KEY_WORD);
    }

    /**
     * Create a <code>search_ad_unit_key_word</code> table reference
     */
    public TSearchAdUnitKeyWord() {
        this(DSL.name("search_ad_unit_key_word"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<SearchAdUnitKeyWordRecord, Long> getIdentity() {
        return (Identity<SearchAdUnitKeyWordRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<SearchAdUnitKeyWordRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD, DSL.name("KEY_search_ad_unit_key_word_PRIMARY"), new TableField[] { TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD.ID }, true);
    }

    @Override
    public TSearchAdUnitKeyWord as(String alias) {
        return new TSearchAdUnitKeyWord(DSL.name(alias), this);
    }

    @Override
    public TSearchAdUnitKeyWord as(Name alias) {
        return new TSearchAdUnitKeyWord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TSearchAdUnitKeyWord rename(String name) {
        return new TSearchAdUnitKeyWord(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TSearchAdUnitKeyWord rename(Name name) {
        return new TSearchAdUnitKeyWord(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<Long, Integer, String, Timestamp, Timestamp, String, Integer, String> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
