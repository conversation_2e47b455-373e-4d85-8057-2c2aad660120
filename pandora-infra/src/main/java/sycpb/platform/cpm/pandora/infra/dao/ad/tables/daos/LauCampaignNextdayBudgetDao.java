/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauCampaignNextdayBudget;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauCampaignNextdayBudgetPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauCampaignNextdayBudgetRecord;


/**
 * 计划次日预算表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauCampaignNextdayBudgetDao extends DAOImpl<LauCampaignNextdayBudgetRecord, LauCampaignNextdayBudgetPo, Integer> {

    /**
     * Create a new LauCampaignNextdayBudgetDao without any configuration
     */
    public LauCampaignNextdayBudgetDao() {
        super(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET, LauCampaignNextdayBudgetPo.class);
    }

    /**
     * Create a new LauCampaignNextdayBudgetDao with an attached configuration
     */
    @Autowired
    public LauCampaignNextdayBudgetDao(Configuration configuration) {
        super(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET, LauCampaignNextdayBudgetPo.class, configuration);
    }

    @Override
    public Integer getId(LauCampaignNextdayBudgetPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchById(Integer... values) {
        return fetch(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauCampaignNextdayBudgetPo fetchOneById(Integer value) {
        return fetchOne(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.ID, value);
    }

    /**
     * Fetch records that have <code>campaign_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchRangeOfCampaignId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.CAMPAIGN_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>campaign_id IN (values)</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchByCampaignId(Integer... values) {
        return fetch(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.CAMPAIGN_ID, values);
    }

    /**
     * Fetch records that have <code>budget BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchRangeOfBudget(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.BUDGET, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>budget IN (values)</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchByBudget(Long... values) {
        return fetch(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.BUDGET, values);
    }

    /**
     * Fetch records that have <code>budget_limit_type BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchRangeOfBudgetLimitType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.BUDGET_LIMIT_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>budget_limit_type IN (values)</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchByBudgetLimitType(Integer... values) {
        return fetch(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.BUDGET_LIMIT_TYPE, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.MTIME, values);
    }

    /**
     * Fetch records that have <code>budget_effective_time BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchRangeOfBudgetEffectiveTime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.BUDGET_EFFECTIVE_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>budget_effective_time IN (values)</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchByBudgetEffectiveTime(Timestamp... values) {
        return fetch(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.BUDGET_EFFECTIVE_TIME, values);
    }

    /**
     * Fetch records that have <code>is_repeat BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchRangeOfIsRepeat(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.IS_REPEAT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_repeat IN (values)</code>
     */
    public List<LauCampaignNextdayBudgetPo> fetchByIsRepeat(Integer... values) {
        return fetch(TLauCampaignNextdayBudget.LAU_CAMPAIGN_NEXTDAY_BUDGET.IS_REPEAT, values);
    }
}
