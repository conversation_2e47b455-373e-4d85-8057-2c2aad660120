package sycpb.platform.cpm.pandora.infra.utils;

/**
 * avid,bvid 互转工具类，代码来自adp
 */
public class BVIDUtils {
	private final static String BVID_PREFIX = "BV1";
	private final static String BVID_NUMBER_PREFIX = "1";
	private final static String ALPHABET_STR = "FcwAPNKTMug3GV5Lj7EJnHpWsx4tb8haYeviqBz6rkCy12mUSDQX9RdoZf";
	private final static char[] ALPHABET = ALPHABET_STR.toCharArray();
	private final static long XOR_CODE = 23442827791579L;
	private final static long MASK_CODE = 2251799813685247L;
	private final static long MIN_AVID = 0L;
	private final static long MAX_AVID = 1L << 51;
	private final static int BASE = 58;

	public static String avToBv(long avID) {
		if (avID <= MIN_AVID) {
			throw new IllegalArgumentException("avid id lt zero");
		}
		if (avID >= MAX_AVID) {
			throw new IllegalArgumentException("avid is too big");
		}
		StringBuilder bvIDBuilder = new StringBuilder();
		long l = (MAX_AVID | avID) ^ XOR_CODE;
		do {
			int i = (int) (l % BASE);
			bvIDBuilder.append(ALPHABET[i]);
			l = l / BASE;
		} while (l != 0L);
		bvIDBuilder.reverse();
		swap(bvIDBuilder, 0, 6);
		swap(bvIDBuilder, 1, 4);
		return String.format("%s%s", BVID_PREFIX, bvIDBuilder.toString());
	}

	public static long bvToAv(String bvID) {
		if (bvID == null || bvID.equals("")) {
			throw new IllegalArgumentException("bvid is empty");
		}
		if (bvID.length() < 9) {
			throw new IllegalArgumentException("bvid is too small");
		}
		if (bvID.substring(0, 3).equalsIgnoreCase(BVID_PREFIX)) {
			bvID = bvID.substring(3);
		} else if (bvID.length() == 10 && bvID.startsWith(BVID_NUMBER_PREFIX)) {
			bvID = bvID.substring(1);
		}
		long l = 0;
		StringBuilder bvIDBuilder = new StringBuilder(bvID);
		swap(bvIDBuilder, 0, 6);
		swap(bvIDBuilder, 1, 4);
		for (char c : bvIDBuilder.toString().toCharArray()) {
			int i = ALPHABET_STR.indexOf(c);
			if (i == -1) {
				throw new IllegalArgumentException(String.format("bvid(%s) is illegal, invalid char:%c", bvID, c));
			}
			l = l * BASE + i;
			// invalid input overflow!!! damn it!!!
			if (l <= 0 || l >= (MAX_AVID << 1)) {
				throw new IllegalArgumentException("bvid is too big");
			}
		}
		if (l < MAX_AVID) {
			throw new IllegalArgumentException("bvid is too small");
		}
		l = (l & MASK_CODE) ^ XOR_CODE;
		if (l >= MAX_AVID || l <= MIN_AVID) {
			throw new IllegalArgumentException("bvid is too big");
		}
		return l;
	}

	private static void swap(StringBuilder bvIDBuilder, int pos0, int pos1) {
		char c0 = bvIDBuilder.charAt(pos0);
		bvIDBuilder.setCharAt(pos0, bvIDBuilder.charAt(pos1));
		bvIDBuilder.setCharAt(pos1, c0);
	}
}
