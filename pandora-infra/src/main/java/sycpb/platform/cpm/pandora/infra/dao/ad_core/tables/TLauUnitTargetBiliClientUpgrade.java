/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitTargetBiliClientUpgradeRecord;


/**
 * 单元定向分端bili客户端
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauUnitTargetBiliClientUpgrade extends TableImpl<LauUnitTargetBiliClientUpgradeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>lau_unit_target_bili_client_upgrade</code>
     */
    public static final TLauUnitTargetBiliClientUpgrade LAU_UNIT_TARGET_BILI_CLIENT_UPGRADE = new TLauUnitTargetBiliClientUpgrade();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauUnitTargetBiliClientUpgradeRecord> getRecordType() {
        return LauUnitTargetBiliClientUpgradeRecord.class;
    }

    /**
     * The column <code>lau_unit_target_bili_client_upgrade.id</code>. 自增id
     */
    public final TableField<LauUnitTargetBiliClientUpgradeRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增id");

    /**
     * The column <code>lau_unit_target_bili_client_upgrade.unit_id</code>. 单元id
     */
    public final TableField<LauUnitTargetBiliClientUpgradeRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元id");

    /**
     * The column <code>lau_unit_target_bili_client_upgrade.os</code>. 设备平台
     * android-399,iphone-398,ipad-421
     */
    public final TableField<LauUnitTargetBiliClientUpgradeRecord, Integer> OS = createField(DSL.name("os"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "设备平台 android-399,iphone-398,ipad-421");

    /**
     * The column <code>lau_unit_target_bili_client_upgrade.relation</code>.
     * 定向关系 0-不限1-介于2-小于等于3-大于等于4-小于5大于
     */
    public final TableField<LauUnitTargetBiliClientUpgradeRecord, Integer> RELATION = createField(DSL.name("relation"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "定向关系 0-不限1-介于2-小于等于3-大于等于4-小于5大于");

    /**
     * The column
     * <code>lau_unit_target_bili_client_upgrade.smaller_version</code>. 较小版本号
     * build值
     */
    public final TableField<LauUnitTargetBiliClientUpgradeRecord, Integer> SMALLER_VERSION = createField(DSL.name("smaller_version"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "较小版本号 build值");

    /**
     * The column
     * <code>lau_unit_target_bili_client_upgrade.larger_version</code>. 较大版本号
     * build值
     */
    public final TableField<LauUnitTargetBiliClientUpgradeRecord, Integer> LARGER_VERSION = createField(DSL.name("larger_version"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "较大版本号 build值");

    /**
     * The column <code>lau_unit_target_bili_client_upgrade.is_deleted</code>.
     * 软删除 0 否 1是
     */
    public final TableField<LauUnitTargetBiliClientUpgradeRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除 0 否 1是");

    /**
     * The column <code>lau_unit_target_bili_client_upgrade.ctime</code>. 创建时间
     */
    public final TableField<LauUnitTargetBiliClientUpgradeRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_unit_target_bili_client_upgrade.mtime</code>. 更新时间
     */
    public final TableField<LauUnitTargetBiliClientUpgradeRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    private TLauUnitTargetBiliClientUpgrade(Name alias, Table<LauUnitTargetBiliClientUpgradeRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauUnitTargetBiliClientUpgrade(Name alias, Table<LauUnitTargetBiliClientUpgradeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("单元定向分端bili客户端"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_unit_target_bili_client_upgrade</code> table
     * reference
     */
    public TLauUnitTargetBiliClientUpgrade(String alias) {
        this(DSL.name(alias), LAU_UNIT_TARGET_BILI_CLIENT_UPGRADE);
    }

    /**
     * Create an aliased <code>lau_unit_target_bili_client_upgrade</code> table
     * reference
     */
    public TLauUnitTargetBiliClientUpgrade(Name alias) {
        this(alias, LAU_UNIT_TARGET_BILI_CLIENT_UPGRADE);
    }

    /**
     * Create a <code>lau_unit_target_bili_client_upgrade</code> table reference
     */
    public TLauUnitTargetBiliClientUpgrade() {
        this(DSL.name("lau_unit_target_bili_client_upgrade"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauUnitTargetBiliClientUpgradeRecord, Long> getIdentity() {
        return (Identity<LauUnitTargetBiliClientUpgradeRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauUnitTargetBiliClientUpgradeRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauUnitTargetBiliClientUpgrade.LAU_UNIT_TARGET_BILI_CLIENT_UPGRADE, DSL.name("KEY_lau_unit_target_bili_client_upgrade_PRIMARY"), new TableField[] { TLauUnitTargetBiliClientUpgrade.LAU_UNIT_TARGET_BILI_CLIENT_UPGRADE.ID }, true);
    }

    @Override
    public List<UniqueKey<LauUnitTargetBiliClientUpgradeRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TLauUnitTargetBiliClientUpgrade.LAU_UNIT_TARGET_BILI_CLIENT_UPGRADE, DSL.name("KEY_lau_unit_target_bili_client_upgrade_uk_unit_id_os"), new TableField[] { TLauUnitTargetBiliClientUpgrade.LAU_UNIT_TARGET_BILI_CLIENT_UPGRADE.UNIT_ID, TLauUnitTargetBiliClientUpgrade.LAU_UNIT_TARGET_BILI_CLIENT_UPGRADE.OS }, true)
        );
    }

    @Override
    public TLauUnitTargetBiliClientUpgrade as(String alias) {
        return new TLauUnitTargetBiliClientUpgrade(DSL.name(alias), this);
    }

    @Override
    public TLauUnitTargetBiliClientUpgrade as(Name alias) {
        return new TLauUnitTargetBiliClientUpgrade(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitTargetBiliClientUpgrade rename(String name) {
        return new TLauUnitTargetBiliClientUpgrade(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitTargetBiliClientUpgrade rename(Name name) {
        return new TLauUnitTargetBiliClientUpgrade(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<Long, Integer, Integer, Integer, Integer, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
