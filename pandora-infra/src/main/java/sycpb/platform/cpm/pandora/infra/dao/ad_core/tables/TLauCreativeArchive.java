/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row14;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeArchiveRecord;


/**
 * 创意稿件表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauCreativeArchive extends TableImpl<LauCreativeArchiveRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_creative_archive</code>
     */
    public static final TLauCreativeArchive LAU_CREATIVE_ARCHIVE = new TLauCreativeArchive();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauCreativeArchiveRecord> getRecordType() {
        return LauCreativeArchiveRecord.class;
    }

    /**
     * The column <code>lau_creative_archive.id</code>. 主键id
     */
    public final TableField<LauCreativeArchiveRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键id");

    /**
     * The column <code>lau_creative_archive.ctime</code>. 创建时间
     */
    public final TableField<LauCreativeArchiveRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_creative_archive.mtime</code>. 修改时间
     */
    public final TableField<LauCreativeArchiveRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>lau_creative_archive.unit_id</code>. 单元id
     */
    public final TableField<LauCreativeArchiveRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元id");

    /**
     * The column <code>lau_creative_archive.creative_id</code>. 创意id
     */
    public final TableField<LauCreativeArchiveRecord, Integer> CREATIVE_ID = createField(DSL.name("creative_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "创意id");

    /**
     * The column <code>lau_creative_archive.avid</code>. 稿件id
     */
    public final TableField<LauCreativeArchiveRecord, Long> AVID = createField(DSL.name("avid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "稿件id");

    /**
     * The column <code>lau_creative_archive.cid</code>. 视频云id
     */
    public final TableField<LauCreativeArchiveRecord, Long> CID = createField(DSL.name("cid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "视频云id");

    /**
     * The column <code>lau_creative_archive.source</code>. 稿件来源: 0-未知, 1-我的视频,
     * 2-bilibili账号视频, 3-花火商单视频, 4-普通内容视频
     */
    public final TableField<LauCreativeArchiveRecord, Integer> SOURCE = createField(DSL.name("source"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "稿件来源: 0-未知, 1-我的视频, 2-bilibili账号视频, 3-花火商单视频, 4-普通内容视频");

    /**
     * The column <code>lau_creative_archive.cover_url</code>. 封面url
     */
    public final TableField<LauCreativeArchiveRecord, String> COVER_URL = createField(DSL.name("cover_url"), SQLDataType.VARCHAR(1024).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "封面url");

    /**
     * The column <code>lau_creative_archive.cover_md5</code>. 封面md5
     */
    public final TableField<LauCreativeArchiveRecord, String> COVER_MD5 = createField(DSL.name("cover_md5"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "封面md5");

    /**
     * The column <code>lau_creative_archive.cover_material_id</code>. 封面物料id
     */
    public final TableField<LauCreativeArchiveRecord, Long> COVER_MATERIAL_ID = createField(DSL.name("cover_material_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "封面物料id");

    /**
     * The column <code>lau_creative_archive.material_md5</code>. 物料md5,
     * 稿件+封面的唯一标识
     */
    public final TableField<LauCreativeArchiveRecord, String> MATERIAL_MD5 = createField(DSL.name("material_md5"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "物料md5, 稿件+封面的唯一标识");

    /**
     * The column <code>lau_creative_archive.material_id</code>. 稿件+封面组合物料id
     */
    public final TableField<LauCreativeArchiveRecord, Long> MATERIAL_ID = createField(DSL.name("material_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "稿件+封面组合物料id");

    /**
     * The column <code>lau_creative_archive.is_use_smart_cut</code>.
     * 是否使用智能剪裁，0是不使用,1是使用
     */
    public final TableField<LauCreativeArchiveRecord, Integer> IS_USE_SMART_CUT = createField(DSL.name("is_use_smart_cut"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否使用智能剪裁，0是不使用,1是使用");

    private TLauCreativeArchive(Name alias, Table<LauCreativeArchiveRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauCreativeArchive(Name alias, Table<LauCreativeArchiveRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("创意稿件表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_creative_archive</code> table reference
     */
    public TLauCreativeArchive(String alias) {
        this(DSL.name(alias), LAU_CREATIVE_ARCHIVE);
    }

    /**
     * Create an aliased <code>lau_creative_archive</code> table reference
     */
    public TLauCreativeArchive(Name alias) {
        this(alias, LAU_CREATIVE_ARCHIVE);
    }

    /**
     * Create a <code>lau_creative_archive</code> table reference
     */
    public TLauCreativeArchive() {
        this(DSL.name("lau_creative_archive"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauCreativeArchiveRecord, Long> getIdentity() {
        return (Identity<LauCreativeArchiveRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauCreativeArchiveRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauCreativeArchive.LAU_CREATIVE_ARCHIVE, DSL.name("KEY_lau_creative_archive_PRIMARY"), new TableField[] { TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.ID }, true);
    }

    @Override
    public TLauCreativeArchive as(String alias) {
        return new TLauCreativeArchive(DSL.name(alias), this);
    }

    @Override
    public TLauCreativeArchive as(Name alias) {
        return new TLauCreativeArchive(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeArchive rename(String name) {
        return new TLauCreativeArchive(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeArchive rename(Name name) {
        return new TLauCreativeArchive(name, null);
    }

    // -------------------------------------------------------------------------
    // Row14 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row14<Long, Timestamp, Timestamp, Integer, Integer, Long, Long, Integer, String, String, Long, String, Long, Integer> fieldsRow() {
        return (Row14) super.fieldsRow();
    }
}
