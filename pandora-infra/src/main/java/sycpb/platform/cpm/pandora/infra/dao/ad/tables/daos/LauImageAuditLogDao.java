/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauImageAuditLog;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauImageAuditLogPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauImageAuditLogRecord;


/**
 * 图片审核记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauImageAuditLogDao extends DAOImpl<LauImageAuditLogRecord, LauImageAuditLogPo, Integer> {

    /**
     * Create a new LauImageAuditLogDao without any configuration
     */
    public LauImageAuditLogDao() {
        super(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG, LauImageAuditLogPo.class);
    }

    /**
     * Create a new LauImageAuditLogDao with an attached configuration
     */
    @Autowired
    public LauImageAuditLogDao(Configuration configuration) {
        super(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG, LauImageAuditLogPo.class, configuration);
    }

    @Override
    public Integer getId(LauImageAuditLogPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauImageAuditLogPo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauImageAuditLogPo> fetchById(Integer... values) {
        return fetch(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauImageAuditLogPo fetchOneById(Integer value) {
        return fetchOne(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.ID, value);
    }

    /**
     * Fetch records that have <code>image_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauImageAuditLogPo> fetchRangeOfImageId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.IMAGE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_id IN (values)</code>
     */
    public List<LauImageAuditLogPo> fetchByImageId(Integer... values) {
        return fetch(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.IMAGE_ID, values);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauImageAuditLogPo> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauImageAuditLogPo> fetchByCreativeId(Integer... values) {
        return fetch(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.CREATIVE_ID, values);
    }

    /**
     * Fetch records that have <code>audit_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauImageAuditLogPo> fetchRangeOfAuditStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.AUDIT_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>audit_status IN (values)</code>
     */
    public List<LauImageAuditLogPo> fetchByAuditStatus(Integer... values) {
        return fetch(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.AUDIT_STATUS, values);
    }

    /**
     * Fetch records that have <code>reason BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauImageAuditLogPo> fetchRangeOfReason(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.REASON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reason IN (values)</code>
     */
    public List<LauImageAuditLogPo> fetchByReason(String... values) {
        return fetch(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.REASON, values);
    }

    /**
     * Fetch records that have <code>image_md5 BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauImageAuditLogPo> fetchRangeOfImageMd5(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.IMAGE_MD5, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_md5 IN (values)</code>
     */
    public List<LauImageAuditLogPo> fetchByImageMd5(String... values) {
        return fetch(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.IMAGE_MD5, values);
    }

    /**
     * Fetch a unique record that has <code>image_md5 = value</code>
     */
    public LauImageAuditLogPo fetchOneByImageMd5(String value) {
        return fetchOne(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.IMAGE_MD5, value);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauImageAuditLogPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauImageAuditLogPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauImageAuditLogPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauImageAuditLogPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauImageAuditLogPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauImageAuditLogPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauImageAuditLog.LAU_IMAGE_AUDIT_LOG.MTIME, values);
    }
}
