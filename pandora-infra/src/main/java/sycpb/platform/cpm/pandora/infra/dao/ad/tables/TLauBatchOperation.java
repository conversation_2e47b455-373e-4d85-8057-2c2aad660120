/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row13;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauBatchOperationRecord;


/**
 * 批量操作
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauBatchOperation extends TableImpl<LauBatchOperationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_batch_operation</code>
     */
    public static final TLauBatchOperation LAU_BATCH_OPERATION = new TLauBatchOperation();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauBatchOperationRecord> getRecordType() {
        return LauBatchOperationRecord.class;
    }

    /**
     * The column <code>lau_batch_operation.id</code>. ID
     */
    public final TableField<LauBatchOperationRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "ID");

    /**
     * The column <code>lau_batch_operation.ctime</code>. 添加时间
     */
    public final TableField<LauBatchOperationRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column <code>lau_batch_operation.mtime</code>. 更新时间
     */
    public final TableField<LauBatchOperationRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>lau_batch_operation.account_id</code>. 账号id
     */
    public final TableField<LauBatchOperationRecord, Integer> ACCOUNT_ID = createField(DSL.name("account_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "账号id");

    /**
     * The column <code>lau_batch_operation.target_type</code>. 对象类型: 0-未知,
     * 1-计划, 2-单元, 3-创意
     */
    public final TableField<LauBatchOperationRecord, Integer> TARGET_TYPE = createField(DSL.name("target_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "对象类型: 0-未知, 1-计划, 2-单元, 3-创意");

    /**
     * The column <code>lau_batch_operation.operator_type</code>. 操作员类型: 0-未知
     */
    public final TableField<LauBatchOperationRecord, Integer> OPERATOR_TYPE = createField(DSL.name("operator_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "操作员类型: 0-未知");

    /**
     * The column <code>lau_batch_operation.operator_name</code>. 操作员用户名
     */
    public final TableField<LauBatchOperationRecord, String> OPERATOR_NAME = createField(DSL.name("operator_name"), SQLDataType.VARCHAR(512).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "操作员用户名");

    /**
     * The column <code>lau_batch_operation.operation_id</code>. 操作id
     */
    public final TableField<LauBatchOperationRecord, Long> OPERATION_ID = createField(DSL.name("operation_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "操作id");

    /**
     * The column <code>lau_batch_operation.operation_type</code>. 操作类型: 0-未知
     */
    public final TableField<LauBatchOperationRecord, Integer> OPERATION_TYPE = createField(DSL.name("operation_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "操作类型: 0-未知");

    /**
     * The column <code>lau_batch_operation.operation_status</code>. 操作状态: 0-未知,
     * 1-未开始, 2-进行中, 3-已结束, 4-已失败, 5-已挂起
     */
    public final TableField<LauBatchOperationRecord, Integer> OPERATION_STATUS = createField(DSL.name("operation_status"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "操作状态: 0-未知, 1-未开始, 2-进行中, 3-已结束, 4-已失败, 5-已挂起");

    /**
     * The column <code>lau_batch_operation.operation_precedence</code>. 操作优先级:
     * 0-禁止
     */
    public final TableField<LauBatchOperationRecord, Integer> OPERATION_PRECEDENCE = createField(DSL.name("operation_precedence"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "操作优先级: 0-禁止");

    /**
     * The column <code>lau_batch_operation.operation_ext</code>. 操作额外信息,
     * 包括操作内容, 开始时间, 结束时间
     */
    public final TableField<LauBatchOperationRecord, String> OPERATION_EXT = createField(DSL.name("operation_ext"), SQLDataType.CLOB.nullable(false).defaultValue(DSL.inline("''", SQLDataType.CLOB)), this, "操作额外信息, 包括操作内容, 开始时间, 结束时间");

    /**
     * The column <code>lau_batch_operation.operation_env</code>. 执行器环境: 0-未知,
     * 1-prod, 2-pre, 3-uat, 4-dev
     */
    public final TableField<LauBatchOperationRecord, Integer> OPERATION_ENV = createField(DSL.name("operation_env"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("3", SQLDataType.INTEGER)), this, "执行器环境: 0-未知, 1-prod, 2-pre, 3-uat, 4-dev");

    private TLauBatchOperation(Name alias, Table<LauBatchOperationRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauBatchOperation(Name alias, Table<LauBatchOperationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("批量操作"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_batch_operation</code> table reference
     */
    public TLauBatchOperation(String alias) {
        this(DSL.name(alias), LAU_BATCH_OPERATION);
    }

    /**
     * Create an aliased <code>lau_batch_operation</code> table reference
     */
    public TLauBatchOperation(Name alias) {
        this(alias, LAU_BATCH_OPERATION);
    }

    /**
     * Create a <code>lau_batch_operation</code> table reference
     */
    public TLauBatchOperation() {
        this(DSL.name("lau_batch_operation"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauBatchOperationRecord, Long> getIdentity() {
        return (Identity<LauBatchOperationRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauBatchOperationRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauBatchOperation.LAU_BATCH_OPERATION, DSL.name("KEY_lau_batch_operation_PRIMARY"), new TableField[] { TLauBatchOperation.LAU_BATCH_OPERATION.ID }, true);
    }

    @Override
    public TLauBatchOperation as(String alias) {
        return new TLauBatchOperation(DSL.name(alias), this);
    }

    @Override
    public TLauBatchOperation as(Name alias) {
        return new TLauBatchOperation(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauBatchOperation rename(String name) {
        return new TLauBatchOperation(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauBatchOperation rename(Name name) {
        return new TLauBatchOperation(name, null);
    }

    // -------------------------------------------------------------------------
    // Row13 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row13<Long, Timestamp, Timestamp, Integer, Integer, Integer, String, Long, Integer, Integer, Integer, String, Integer> fieldsRow() {
        return (Row13) super.fieldsRow();
    }
}
