/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeAnchorMapping;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeAnchorMappingPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeAnchorMappingRecord;


/**
 * 创意锚点关系
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauCreativeAnchorMappingDao extends DAOImpl<LauCreativeAnchorMappingRecord, LauCreativeAnchorMappingPo, Long> {

    /**
     * Create a new LauCreativeAnchorMappingDao without any configuration
     */
    public LauCreativeAnchorMappingDao() {
        super(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING, LauCreativeAnchorMappingPo.class);
    }

    /**
     * Create a new LauCreativeAnchorMappingDao with an attached configuration
     */
    @Autowired
    public LauCreativeAnchorMappingDao(Configuration configuration) {
        super(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING, LauCreativeAnchorMappingPo.class, configuration);
    }

    @Override
    public Long getId(LauCreativeAnchorMappingPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchById(Long... values) {
        return fetch(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauCreativeAnchorMappingPo fetchOneById(Long value) {
        return fetchOne(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.ID, value);
    }

    /**
     * Fetch records that have <code>aid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchRangeOfAid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.AID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>aid IN (values)</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchByAid(Long... values) {
        return fetch(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.AID, values);
    }

    /**
     * Fetch records that have <code>anchor_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchRangeOfAnchorId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.ANCHOR_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>anchor_id IN (values)</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchByAnchorId(Long... values) {
        return fetch(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.ANCHOR_ID, values);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchByCreativeId(Integer... values) {
        return fetch(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.CREATIVE_ID, values);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchByUnitId(Integer... values) {
        return fetch(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>system_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchRangeOfSystemType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.SYSTEM_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>system_type IN (values)</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchBySystemType(Integer... values) {
        return fetch(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.SYSTEM_TYPE, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauCreativeAnchorMappingPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.MTIME, values);
    }
}
