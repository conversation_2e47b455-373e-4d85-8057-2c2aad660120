/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitTargetOsVersionUpgradeRecord;


/**
 * 单元分端定向操作系统版本
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauUnitTargetOsVersionUpgrade extends TableImpl<LauUnitTargetOsVersionUpgradeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_unit_target_os_version_upgrade</code>
     */
    public static final TLauUnitTargetOsVersionUpgrade LAU_UNIT_TARGET_OS_VERSION_UPGRADE = new TLauUnitTargetOsVersionUpgrade();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauUnitTargetOsVersionUpgradeRecord> getRecordType() {
        return LauUnitTargetOsVersionUpgradeRecord.class;
    }

    /**
     * The column <code>lau_unit_target_os_version_upgrade.id</code>. 自增id
     */
    public final TableField<LauUnitTargetOsVersionUpgradeRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增id");

    /**
     * The column <code>lau_unit_target_os_version_upgrade.unit_id</code>. 单元id
     */
    public final TableField<LauUnitTargetOsVersionUpgradeRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元id");

    /**
     * The column <code>lau_unit_target_os_version_upgrade.os</code>. 设备平台
     * android-399,iphone-398,ipad-421
     */
    public final TableField<LauUnitTargetOsVersionUpgradeRecord, Integer> OS = createField(DSL.name("os"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "设备平台 android-399,iphone-398,ipad-421");

    /**
     * The column <code>lau_unit_target_os_version_upgrade.os_versions</code>.
     * 系统版本列表(,分隔)没有为[-1]
     */
    public final TableField<LauUnitTargetOsVersionUpgradeRecord, String> OS_VERSIONS = createField(DSL.name("os_versions"), SQLDataType.VARCHAR(256).nullable(false).defaultValue(DSL.inline("'[-1]'", SQLDataType.VARCHAR)), this, "系统版本列表(,分隔)没有为[-1]");

    /**
     * The column <code>lau_unit_target_os_version_upgrade.is_deleted</code>.
     * 软删除 0 否 1是
     */
    public final TableField<LauUnitTargetOsVersionUpgradeRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除 0 否 1是");

    /**
     * The column <code>lau_unit_target_os_version_upgrade.ctime</code>. 创建时间
     */
    public final TableField<LauUnitTargetOsVersionUpgradeRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_unit_target_os_version_upgrade.mtime</code>. 更新时间
     */
    public final TableField<LauUnitTargetOsVersionUpgradeRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    private TLauUnitTargetOsVersionUpgrade(Name alias, Table<LauUnitTargetOsVersionUpgradeRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauUnitTargetOsVersionUpgrade(Name alias, Table<LauUnitTargetOsVersionUpgradeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("单元分端定向操作系统版本"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_unit_target_os_version_upgrade</code> table
     * reference
     */
    public TLauUnitTargetOsVersionUpgrade(String alias) {
        this(DSL.name(alias), LAU_UNIT_TARGET_OS_VERSION_UPGRADE);
    }

    /**
     * Create an aliased <code>lau_unit_target_os_version_upgrade</code> table
     * reference
     */
    public TLauUnitTargetOsVersionUpgrade(Name alias) {
        this(alias, LAU_UNIT_TARGET_OS_VERSION_UPGRADE);
    }

    /**
     * Create a <code>lau_unit_target_os_version_upgrade</code> table reference
     */
    public TLauUnitTargetOsVersionUpgrade() {
        this(DSL.name("lau_unit_target_os_version_upgrade"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauUnitTargetOsVersionUpgradeRecord, Long> getIdentity() {
        return (Identity<LauUnitTargetOsVersionUpgradeRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauUnitTargetOsVersionUpgradeRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauUnitTargetOsVersionUpgrade.LAU_UNIT_TARGET_OS_VERSION_UPGRADE, DSL.name("KEY_lau_unit_target_os_version_upgrade_PRIMARY"), new TableField[] { TLauUnitTargetOsVersionUpgrade.LAU_UNIT_TARGET_OS_VERSION_UPGRADE.ID }, true);
    }

    @Override
    public List<UniqueKey<LauUnitTargetOsVersionUpgradeRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TLauUnitTargetOsVersionUpgrade.LAU_UNIT_TARGET_OS_VERSION_UPGRADE, DSL.name("KEY_lau_unit_target_os_version_upgrade_uk_unit_id_os"), new TableField[] { TLauUnitTargetOsVersionUpgrade.LAU_UNIT_TARGET_OS_VERSION_UPGRADE.UNIT_ID, TLauUnitTargetOsVersionUpgrade.LAU_UNIT_TARGET_OS_VERSION_UPGRADE.OS }, true)
        );
    }

    @Override
    public TLauUnitTargetOsVersionUpgrade as(String alias) {
        return new TLauUnitTargetOsVersionUpgrade(DSL.name(alias), this);
    }

    @Override
    public TLauUnitTargetOsVersionUpgrade as(Name alias) {
        return new TLauUnitTargetOsVersionUpgrade(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitTargetOsVersionUpgrade rename(String name) {
        return new TLauUnitTargetOsVersionUpgrade(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitTargetOsVersionUpgrade rename(Name name) {
        return new TLauUnitTargetOsVersionUpgrade(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<Long, Integer, Integer, String, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
