/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.FlyAssistSearchRecord;


/**
 * 起飞辅助探索表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TFlyAssistSearch extends TableImpl<FlyAssistSearchRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>fly_assist_search</code>
     */
    public static final TFlyAssistSearch FLY_ASSIST_SEARCH = new TFlyAssistSearch();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<FlyAssistSearchRecord> getRecordType() {
        return FlyAssistSearchRecord.class;
    }

    /**
     * The column <code>fly_assist_search.id</code>.
     */
    public final TableField<FlyAssistSearchRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

    /**
     * The column <code>fly_assist_search.unit_id</code>. 单元id
     */
    public final TableField<FlyAssistSearchRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元id");

    /**
     * The column <code>fly_assist_search.is_search</code>. 是否在探索期（0-不在 1-在）
     */
    public final TableField<FlyAssistSearchRecord, Integer> IS_SEARCH = createField(DSL.name("is_search"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "是否在探索期（0-不在 1-在）");

    /**
     * The column <code>fly_assist_search.search_end_date</code>. 探索期结束时间
     */
    public final TableField<FlyAssistSearchRecord, Timestamp> SEARCH_END_DATE = createField(DSL.name("search_end_date"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.inline("'1970-01-01 00:00:00'", SQLDataType.TIMESTAMP)), this, "探索期结束时间");

    /**
     * The column <code>fly_assist_search.ctime</code>. 创建时间
     */
    public final TableField<FlyAssistSearchRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>fly_assist_search.mtime</code>. 修改时间
     */
    public final TableField<FlyAssistSearchRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    private TFlyAssistSearch(Name alias, Table<FlyAssistSearchRecord> aliased) {
        this(alias, aliased, null);
    }

    private TFlyAssistSearch(Name alias, Table<FlyAssistSearchRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("起飞辅助探索表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>fly_assist_search</code> table reference
     */
    public TFlyAssistSearch(String alias) {
        this(DSL.name(alias), FLY_ASSIST_SEARCH);
    }

    /**
     * Create an aliased <code>fly_assist_search</code> table reference
     */
    public TFlyAssistSearch(Name alias) {
        this(alias, FLY_ASSIST_SEARCH);
    }

    /**
     * Create a <code>fly_assist_search</code> table reference
     */
    public TFlyAssistSearch() {
        this(DSL.name("fly_assist_search"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<FlyAssistSearchRecord, Long> getIdentity() {
        return (Identity<FlyAssistSearchRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<FlyAssistSearchRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TFlyAssistSearch.FLY_ASSIST_SEARCH, DSL.name("KEY_fly_assist_search_PRIMARY"), new TableField[] { TFlyAssistSearch.FLY_ASSIST_SEARCH.ID }, true);
    }

    @Override
    public List<UniqueKey<FlyAssistSearchRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TFlyAssistSearch.FLY_ASSIST_SEARCH, DSL.name("KEY_fly_assist_search_ix_unit"), new TableField[] { TFlyAssistSearch.FLY_ASSIST_SEARCH.UNIT_ID }, true)
        );
    }

    @Override
    public TFlyAssistSearch as(String alias) {
        return new TFlyAssistSearch(DSL.name(alias), this);
    }

    @Override
    public TFlyAssistSearch as(Name alias) {
        return new TFlyAssistSearch(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TFlyAssistSearch rename(String name) {
        return new TFlyAssistSearch(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TFlyAssistSearch rename(Name name) {
        return new TFlyAssistSearch(name, null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Integer, Integer, Timestamp, Timestamp, Timestamp> fieldsRow() {
        return (Row6) super.fieldsRow();
    }
}
