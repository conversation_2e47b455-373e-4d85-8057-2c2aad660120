/*
 * This file is generated by j<PERSON><PERSON><PERSON>.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TResTargetPackageCrowdPackageUpgrade;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.ResTargetPackageCrowdPackageUpgradePo;


/**
 * 新版定向包人群包定向表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResTargetPackageCrowdPackageUpgradeRecord extends UpdatableRecordImpl<ResTargetPackageCrowdPackageUpgradeRecord> implements Record8<Long, Inte<PERSON>, Inte<PERSON>, Integer, <PERSON>tamp, Timestamp, Integer, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>res_target_package_crowd_package_upgrade.id</code>.
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>res_target_package_crowd_package_upgrade.id</code>.
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for
     * <code>res_target_package_crowd_package_upgrade.target_package_id</code>.
     * 定向包ID
     */
    public void setTargetPackageId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for
     * <code>res_target_package_crowd_package_upgrade.target_package_id</code>.
     * 定向包ID
     */
    public Integer getTargetPackageId() {
        return (Integer) get(1);
    }

    /**
     * Setter for
     * <code>res_target_package_crowd_package_upgrade.crowd_pack_id</code>.
     * 人群包ID(来自DMP)
     */
    public void setCrowdPackId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for
     * <code>res_target_package_crowd_package_upgrade.crowd_pack_id</code>.
     * 人群包ID(来自DMP)
     */
    public Integer getCrowdPackId() {
        return (Integer) get(2);
    }

    /**
     * Setter for
     * <code>res_target_package_crowd_package_upgrade.group_id</code>.
     * 投放端对人群包分组的id
     */
    public void setGroupId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for
     * <code>res_target_package_crowd_package_upgrade.group_id</code>.
     * 投放端对人群包分组的id
     */
    public Integer getGroupId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>res_target_package_crowd_package_upgrade.ctime</code>.
     * 创建时间
     */
    public void setCtime(Timestamp value) {
        set(4, value);
    }

    /**
     * Getter for <code>res_target_package_crowd_package_upgrade.ctime</code>.
     * 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(4);
    }

    /**
     * Setter for <code>res_target_package_crowd_package_upgrade.mtime</code>.
     * 修改时间
     */
    public void setMtime(Timestamp value) {
        set(5, value);
    }

    /**
     * Getter for <code>res_target_package_crowd_package_upgrade.mtime</code>.
     * 修改时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(5);
    }

    /**
     * Setter for
     * <code>res_target_package_crowd_package_upgrade.is_deleted</code>. 软删除
     * 0-有效，1-删除
     */
    public void setIsDeleted(Integer value) {
        set(6, value);
    }

    /**
     * Getter for
     * <code>res_target_package_crowd_package_upgrade.is_deleted</code>. 软删除
     * 0-有效，1-删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>res_target_package_crowd_package_upgrade.type</code>.
     * 类型：1-包含, 2-排除
     */
    public void setType(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>res_target_package_crowd_package_upgrade.type</code>.
     * 类型：1-包含, 2-排除
     */
    public Integer getType() {
        return (Integer) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<Long, Integer, Integer, Integer, Timestamp, Timestamp, Integer, Integer> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<Long, Integer, Integer, Integer, Timestamp, Timestamp, Integer, Integer> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TResTargetPackageCrowdPackageUpgrade.RES_TARGET_PACKAGE_CROWD_PACKAGE_UPGRADE.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TResTargetPackageCrowdPackageUpgrade.RES_TARGET_PACKAGE_CROWD_PACKAGE_UPGRADE.TARGET_PACKAGE_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TResTargetPackageCrowdPackageUpgrade.RES_TARGET_PACKAGE_CROWD_PACKAGE_UPGRADE.CROWD_PACK_ID;
    }

    @Override
    public Field<Integer> field4() {
        return TResTargetPackageCrowdPackageUpgrade.RES_TARGET_PACKAGE_CROWD_PACKAGE_UPGRADE.GROUP_ID;
    }

    @Override
    public Field<Timestamp> field5() {
        return TResTargetPackageCrowdPackageUpgrade.RES_TARGET_PACKAGE_CROWD_PACKAGE_UPGRADE.CTIME;
    }

    @Override
    public Field<Timestamp> field6() {
        return TResTargetPackageCrowdPackageUpgrade.RES_TARGET_PACKAGE_CROWD_PACKAGE_UPGRADE.MTIME;
    }

    @Override
    public Field<Integer> field7() {
        return TResTargetPackageCrowdPackageUpgrade.RES_TARGET_PACKAGE_CROWD_PACKAGE_UPGRADE.IS_DELETED;
    }

    @Override
    public Field<Integer> field8() {
        return TResTargetPackageCrowdPackageUpgrade.RES_TARGET_PACKAGE_CROWD_PACKAGE_UPGRADE.TYPE;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getTargetPackageId();
    }

    @Override
    public Integer component3() {
        return getCrowdPackId();
    }

    @Override
    public Integer component4() {
        return getGroupId();
    }

    @Override
    public Timestamp component5() {
        return getCtime();
    }

    @Override
    public Timestamp component6() {
        return getMtime();
    }

    @Override
    public Integer component7() {
        return getIsDeleted();
    }

    @Override
    public Integer component8() {
        return getType();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getTargetPackageId();
    }

    @Override
    public Integer value3() {
        return getCrowdPackId();
    }

    @Override
    public Integer value4() {
        return getGroupId();
    }

    @Override
    public Timestamp value5() {
        return getCtime();
    }

    @Override
    public Timestamp value6() {
        return getMtime();
    }

    @Override
    public Integer value7() {
        return getIsDeleted();
    }

    @Override
    public Integer value8() {
        return getType();
    }

    @Override
    public ResTargetPackageCrowdPackageUpgradeRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public ResTargetPackageCrowdPackageUpgradeRecord value2(Integer value) {
        setTargetPackageId(value);
        return this;
    }

    @Override
    public ResTargetPackageCrowdPackageUpgradeRecord value3(Integer value) {
        setCrowdPackId(value);
        return this;
    }

    @Override
    public ResTargetPackageCrowdPackageUpgradeRecord value4(Integer value) {
        setGroupId(value);
        return this;
    }

    @Override
    public ResTargetPackageCrowdPackageUpgradeRecord value5(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public ResTargetPackageCrowdPackageUpgradeRecord value6(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public ResTargetPackageCrowdPackageUpgradeRecord value7(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public ResTargetPackageCrowdPackageUpgradeRecord value8(Integer value) {
        setType(value);
        return this;
    }

    @Override
    public ResTargetPackageCrowdPackageUpgradeRecord values(Long value1, Integer value2, Integer value3, Integer value4, Timestamp value5, Timestamp value6, Integer value7, Integer value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ResTargetPackageCrowdPackageUpgradeRecord
     */
    public ResTargetPackageCrowdPackageUpgradeRecord() {
        super(TResTargetPackageCrowdPackageUpgrade.RES_TARGET_PACKAGE_CROWD_PACKAGE_UPGRADE);
    }

    /**
     * Create a detached, initialised ResTargetPackageCrowdPackageUpgradeRecord
     */
    public ResTargetPackageCrowdPackageUpgradeRecord(Long id, Integer targetPackageId, Integer crowdPackId, Integer groupId, Timestamp ctime, Timestamp mtime, Integer isDeleted, Integer type) {
        super(TResTargetPackageCrowdPackageUpgrade.RES_TARGET_PACKAGE_CROWD_PACKAGE_UPGRADE);

        setId(id);
        setTargetPackageId(targetPackageId);
        setCrowdPackId(crowdPackId);
        setGroupId(groupId);
        setCtime(ctime);
        setMtime(mtime);
        setIsDeleted(isDeleted);
        setType(type);
    }

    /**
     * Create a detached, initialised ResTargetPackageCrowdPackageUpgradeRecord
     */
    public ResTargetPackageCrowdPackageUpgradeRecord(ResTargetPackageCrowdPackageUpgradePo value) {
        super(TResTargetPackageCrowdPackageUpgrade.RES_TARGET_PACKAGE_CROWD_PACKAGE_UPGRADE);

        if (value != null) {
            setId(value.getId());
            setTargetPackageId(value.getTargetPackageId());
            setCrowdPackId(value.getCrowdPackId());
            setGroupId(value.getGroupId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setIsDeleted(value.getIsDeleted());
            setType(value.getType());
        }
    }
}
