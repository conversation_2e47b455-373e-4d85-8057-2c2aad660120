/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TAccAccount;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.AccAccountPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.AccAccountRecord;


/**
 * s_support_pickup
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class AccAccountDao extends DAOImpl<AccAccountRecord, AccAccountPo, Integer> {

    /**
     * Create a new AccAccountDao without any configuration
     */
    public AccAccountDao() {
        super(TAccAccount.ACC_ACCOUNT, AccAccountPo.class);
    }

    /**
     * Create a new AccAccountDao with an attached configuration
     */
    @Autowired
    public AccAccountDao(Configuration configuration) {
        super(TAccAccount.ACC_ACCOUNT, AccAccountPo.class, configuration);
    }

    @Override
    public Integer getId(AccAccountPo object) {
        return object.getAccountId();
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByAccountId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ACCOUNT_ID, values);
    }

    /**
     * Fetch a unique record that has <code>account_id = value</code>
     */
    public AccAccountPo fetchOneByAccountId(Integer value) {
        return fetchOne(TAccAccount.ACC_ACCOUNT.ACCOUNT_ID, value);
    }

    /**
     * Fetch records that have <code>username BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfUsername(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.USERNAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>username IN (values)</code>
     */
    public List<AccAccountPo> fetchByUsername(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.USERNAME, values);
    }

    /**
     * Fetch records that have <code>mobile BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfMobile(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.MOBILE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mobile IN (values)</code>
     */
    public List<AccAccountPo> fetchByMobile(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.MOBILE, values);
    }

    /**
     * Fetch records that have <code>password_strength BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfPasswordStrength(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.PASSWORD_STRENGTH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>password_strength IN (values)</code>
     */
    public List<AccAccountPo> fetchByPasswordStrength(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.PASSWORD_STRENGTH, values);
    }

    /**
     * Fetch records that have <code>salt BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfSalt(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.SALT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>salt IN (values)</code>
     */
    public List<AccAccountPo> fetchBySalt(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.SALT, values);
    }

    /**
     * Fetch records that have <code>salt_password BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfSaltPassword(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.SALT_PASSWORD, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>salt_password IN (values)</code>
     */
    public List<AccAccountPo> fetchBySaltPassword(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.SALT_PASSWORD, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<AccAccountPo> fetchByStatus(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.STATUS, values);
    }

    /**
     * Fetch records that have <code>crm_customer_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfCrmCustomerId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.CRM_CUSTOMER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>crm_customer_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByCrmCustomerId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.CRM_CUSTOMER_ID, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<AccAccountPo> fetchByCtime(Timestamp... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<AccAccountPo> fetchByMtime(Timestamp... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.MTIME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsDeleted(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>order_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfOrderType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ORDER_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>order_type IN (values)</code>
     */
    public List<AccAccountPo> fetchByOrderType(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ORDER_TYPE, values);
    }

    /**
     * Fetch records that have <code>mid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfMid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.MID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mid IN (values)</code>
     */
    public List<AccAccountPo> fetchByMid(Long... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.MID, values);
    }

    /**
     * Fetch records that have <code>account_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAccountType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ACCOUNT_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_type IN (values)</code>
     */
    public List<AccAccountPo> fetchByAccountType(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ACCOUNT_TYPE, values);
    }

    /**
     * Fetch records that have <code>active_time BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfActiveTime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ACTIVE_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>active_time IN (values)</code>
     */
    public List<AccAccountPo> fetchByActiveTime(Timestamp... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ACTIVE_TIME, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<AccAccountPo> fetchByName(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.NAME, values);
    }

    /**
     * Fetch records that have <code>icp_record_number BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIcpRecordNumber(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ICP_RECORD_NUMBER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>icp_record_number IN (values)</code>
     */
    public List<AccAccountPo> fetchByIcpRecordNumber(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ICP_RECORD_NUMBER, values);
    }

    /**
     * Fetch records that have <code>icp_info_image BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIcpInfoImage(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ICP_INFO_IMAGE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>icp_info_image IN (values)</code>
     */
    public List<AccAccountPo> fetchByIcpInfoImage(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ICP_INFO_IMAGE, values);
    }

    /**
     * Fetch records that have <code>brand_domain BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfBrandDomain(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.BRAND_DOMAIN, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>brand_domain IN (values)</code>
     */
    public List<AccAccountPo> fetchByBrandDomain(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.BRAND_DOMAIN, values);
    }

    /**
     * Fetch records that have <code>user_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfUserType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.USER_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>user_type IN (values)</code>
     */
    public List<AccAccountPo> fetchByUserType(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.USER_TYPE, values);
    }

    /**
     * Fetch records that have <code>ad_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAdStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.AD_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ad_status IN (values)</code>
     */
    public List<AccAccountPo> fetchByAdStatus(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.AD_STATUS, values);
    }

    /**
     * Fetch records that have <code>version BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfVersion(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.VERSION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>version IN (values)</code>
     */
    public List<AccAccountPo> fetchByVersion(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.VERSION, values);
    }

    /**
     * Fetch records that have <code>category_first_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfCategoryFirstId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.CATEGORY_FIRST_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category_first_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByCategoryFirstId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.CATEGORY_FIRST_ID, values);
    }

    /**
     * Fetch records that have <code>category_second_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfCategorySecondId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.CATEGORY_SECOND_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category_second_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByCategorySecondId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.CATEGORY_SECOND_ID, values);
    }

    /**
     * Fetch records that have <code>remark BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfRemark(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.REMARK, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>remark IN (values)</code>
     */
    public List<AccAccountPo> fetchByRemark(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.REMARK, values);
    }

    /**
     * Fetch records that have <code>is_agent BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsAgent(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_AGENT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_agent IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsAgent(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_AGENT, values);
    }

    /**
     * Fetch records that have <code>agent_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAgentType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.AGENT_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>agent_type IN (values)</code>
     */
    public List<AccAccountPo> fetchByAgentType(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.AGENT_TYPE, values);
    }

    /**
     * Fetch records that have <code>business_role_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfBusinessRoleId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.BUSINESS_ROLE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>business_role_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByBusinessRoleId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.BUSINESS_ROLE_ID, values);
    }

    /**
     * Fetch records that have <code>company_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfCompanyName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.COMPANY_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>company_name IN (values)</code>
     */
    public List<AccAccountPo> fetchByCompanyName(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.COMPANY_NAME, values);
    }

    /**
     * Fetch records that have <code>area_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAreaId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.AREA_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>area_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByAreaId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.AREA_ID, values);
    }

    /**
     * Fetch records that have <code>dependency_agent_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfDependencyAgentId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.DEPENDENCY_AGENT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>dependency_agent_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByDependencyAgentId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.DEPENDENCY_AGENT_ID, values);
    }

    /**
     * Fetch records that have <code>website_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfWebsiteName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.WEBSITE_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>website_name IN (values)</code>
     */
    public List<AccAccountPo> fetchByWebsiteName(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.WEBSITE_NAME, values);
    }

    /**
     * Fetch records that have <code>weibo BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfWeibo(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.WEIBO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>weibo IN (values)</code>
     */
    public List<AccAccountPo> fetchByWeibo(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.WEIBO, values);
    }

    /**
     * Fetch records that have <code>internal_linkman BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfInternalLinkman(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.INTERNAL_LINKMAN, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>internal_linkman IN (values)</code>
     */
    public List<AccAccountPo> fetchByInternalLinkman(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.INTERNAL_LINKMAN, values);
    }

    /**
     * Fetch records that have <code>linkman_address BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfLinkmanAddress(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.LINKMAN_ADDRESS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>linkman_address IN (values)</code>
     */
    public List<AccAccountPo> fetchByLinkmanAddress(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.LINKMAN_ADDRESS, values);
    }

    /**
     * Fetch records that have <code>bank BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfBank(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.BANK, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>bank IN (values)</code>
     */
    public List<AccAccountPo> fetchByBank(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.BANK, values);
    }

    /**
     * Fetch records that have <code>qualification_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfQualificationId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.QUALIFICATION_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>qualification_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByQualificationId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.QUALIFICATION_ID, values);
    }

    /**
     * Fetch records that have <code>business_licence_code BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfBusinessLicenceCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.BUSINESS_LICENCE_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>business_licence_code IN (values)</code>
     */
    public List<AccAccountPo> fetchByBusinessLicenceCode(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.BUSINESS_LICENCE_CODE, values);
    }

    /**
     * Fetch records that have <code>business_licence_expire_date BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfBusinessLicenceExpireDate(Date lowerInclusive, Date upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.BUSINESS_LICENCE_EXPIRE_DATE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>business_licence_expire_date IN
     * (values)</code>
     */
    public List<AccAccountPo> fetchByBusinessLicenceExpireDate(Date... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.BUSINESS_LICENCE_EXPIRE_DATE, values);
    }

    /**
     * Fetch records that have <code>is_business_licence_indefinite BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsBusinessLicenceIndefinite(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_BUSINESS_LICENCE_INDEFINITE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_business_licence_indefinite IN
     * (values)</code>
     */
    public List<AccAccountPo> fetchByIsBusinessLicenceIndefinite(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_BUSINESS_LICENCE_INDEFINITE, values);
    }

    /**
     * Fetch records that have <code>legal_person_name BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfLegalPersonName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.LEGAL_PERSON_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>legal_person_name IN (values)</code>
     */
    public List<AccAccountPo> fetchByLegalPersonName(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.LEGAL_PERSON_NAME, values);
    }

    /**
     * Fetch records that have <code>legal_person_idcard_expire_date BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfLegalPersonIdcardExpireDate(Date lowerInclusive, Date upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.LEGAL_PERSON_IDCARD_EXPIRE_DATE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>legal_person_idcard_expire_date IN
     * (values)</code>
     */
    public List<AccAccountPo> fetchByLegalPersonIdcardExpireDate(Date... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.LEGAL_PERSON_IDCARD_EXPIRE_DATE, values);
    }

    /**
     * Fetch records that have <code>is_legal_person_idcard_indefinite BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsLegalPersonIdcardIndefinite(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_LEGAL_PERSON_IDCARD_INDEFINITE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_legal_person_idcard_indefinite IN
     * (values)</code>
     */
    public List<AccAccountPo> fetchByIsLegalPersonIdcardIndefinite(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_LEGAL_PERSON_IDCARD_INDEFINITE, values);
    }

    /**
     * Fetch records that have <code>audit_remark BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAuditRemark(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.AUDIT_REMARK, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>audit_remark IN (values)</code>
     */
    public List<AccAccountPo> fetchByAuditRemark(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.AUDIT_REMARK, values);
    }

    /**
     * Fetch records that have <code>account_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAccountStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ACCOUNT_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_status IN (values)</code>
     */
    public List<AccAccountPo> fetchByAccountStatus(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ACCOUNT_STATUS, values);
    }

    /**
     * Fetch records that have <code>linkman_email BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfLinkmanEmail(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.LINKMAN_EMAIL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>linkman_email IN (values)</code>
     */
    public List<AccAccountPo> fetchByLinkmanEmail(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.LINKMAN_EMAIL, values);
    }

    /**
     * Fetch records that have <code>gd_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfGdStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.GD_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>gd_status IN (values)</code>
     */
    public List<AccAccountPo> fetchByGdStatus(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.GD_STATUS, values);
    }

    /**
     * Fetch records that have <code>agent_auth_expire_date BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAgentAuthExpireDate(Date lowerInclusive, Date upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.AGENT_AUTH_EXPIRE_DATE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>agent_auth_expire_date IN (values)</code>
     */
    public List<AccAccountPo> fetchByAgentAuthExpireDate(Date... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.AGENT_AUTH_EXPIRE_DATE, values);
    }

    /**
     * Fetch records that have <code>is_inner BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsInner(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_INNER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_inner IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsInner(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_INNER, values);
    }

    /**
     * Fetch records that have <code>department_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfDepartmentId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.DEPARTMENT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>department_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByDepartmentId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.DEPARTMENT_ID, values);
    }

    /**
     * Fetch records that have <code>is_support_seller BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsSupportSeller(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_SELLER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_support_seller IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsSupportSeller(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_SELLER, values);
    }

    /**
     * Fetch records that have <code>is_support_game BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsSupportGame(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_GAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_support_game IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsSupportGame(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_GAME, values);
    }

    /**
     * Fetch records that have <code>is_support_dpa BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsSupportDpa(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_DPA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_support_dpa IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsSupportDpa(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_DPA, values);
    }

    /**
     * Fetch records that have <code>is_support_content BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsSupportContent(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_CONTENT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_support_content IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsSupportContent(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_CONTENT, values);
    }

    /**
     * Fetch records that have <code>product_line BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfProductLine(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.PRODUCT_LINE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>product_line IN (values)</code>
     */
    public List<AccAccountPo> fetchByProductLine(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.PRODUCT_LINE, values);
    }

    /**
     * Fetch records that have <code>phone_number BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfPhoneNumber(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.PHONE_NUMBER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>phone_number IN (values)</code>
     */
    public List<AccAccountPo> fetchByPhoneNumber(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.PHONE_NUMBER, values);
    }

    /**
     * Fetch records that have <code>idcard_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIdcardType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IDCARD_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>idcard_type IN (values)</code>
     */
    public List<AccAccountPo> fetchByIdcardType(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IDCARD_TYPE, values);
    }

    /**
     * Fetch records that have <code>idcard_number BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIdcardNumber(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IDCARD_NUMBER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>idcard_number IN (values)</code>
     */
    public List<AccAccountPo> fetchByIdcardNumber(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IDCARD_NUMBER, values);
    }

    /**
     * Fetch records that have <code>idcard_expire_date BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIdcardExpireDate(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IDCARD_EXPIRE_DATE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>idcard_expire_date IN (values)</code>
     */
    public List<AccAccountPo> fetchByIdcardExpireDate(Timestamp... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IDCARD_EXPIRE_DATE, values);
    }

    /**
     * Fetch records that have <code>personal_address BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfPersonalAddress(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.PERSONAL_ADDRESS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>personal_address IN (values)</code>
     */
    public List<AccAccountPo> fetchByPersonalAddress(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.PERSONAL_ADDRESS, values);
    }

    /**
     * Fetch records that have <code>is_idcard_indefinite BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsIdcardIndefinite(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_IDCARD_INDEFINITE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_idcard_indefinite IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsIdcardIndefinite(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_IDCARD_INDEFINITE, values);
    }

    /**
     * Fetch records that have <code>personal_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfPersonalName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.PERSONAL_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>personal_name IN (values)</code>
     */
    public List<AccAccountPo> fetchByPersonalName(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.PERSONAL_NAME, values);
    }

    /**
     * Fetch records that have <code>group_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfGroupId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.GROUP_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>group_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByGroupId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.GROUP_ID, values);
    }

    /**
     * Fetch records that have <code>product_line_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfProductLineId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.PRODUCT_LINE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>product_line_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByProductLineId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.PRODUCT_LINE_ID, values);
    }

    /**
     * Fetch records that have <code>product_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfProductId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.PRODUCT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>product_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByProductId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.PRODUCT_ID, values);
    }

    /**
     * Fetch records that have <code>is_support_pickup BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsSupportPickup(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_PICKUP, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_support_pickup IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsSupportPickup(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_PICKUP, values);
    }

    /**
     * Fetch records that have <code>is_support_mas BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsSupportMas(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_MAS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_support_mas IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsSupportMas(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_MAS, values);
    }

    /**
     * Fetch records that have <code>auto_update_label BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAutoUpdateLabel(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.AUTO_UPDATE_LABEL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>auto_update_label IN (values)</code>
     */
    public List<AccAccountPo> fetchByAutoUpdateLabel(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.AUTO_UPDATE_LABEL, values);
    }

    /**
     * Fetch records that have <code>is_support_fly BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsSupportFly(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_FLY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_support_fly IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsSupportFly(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_FLY, values);
    }

    /**
     * Fetch records that have <code>allow_cash_pay BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAllowCashPay(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ALLOW_CASH_PAY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>allow_cash_pay IN (values)</code>
     */
    public List<AccAccountPo> fetchByAllowCashPay(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ALLOW_CASH_PAY, values);
    }

    /**
     * Fetch records that have <code>allow_incentive_bonus_pay BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAllowIncentiveBonusPay(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ALLOW_INCENTIVE_BONUS_PAY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>allow_incentive_bonus_pay IN
     * (values)</code>
     */
    public List<AccAccountPo> fetchByAllowIncentiveBonusPay(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ALLOW_INCENTIVE_BONUS_PAY, values);
    }

    /**
     * Fetch records that have <code>customer_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfCustomerId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.CUSTOMER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>customer_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByCustomerId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.CUSTOMER_ID, values);
    }

    /**
     * Fetch records that have <code>creator BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfCreator(String lowerInclusive, String upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.CREATOR, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creator IN (values)</code>
     */
    public List<AccAccountPo> fetchByCreator(String... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.CREATOR, values);
    }

    /**
     * Fetch records that have <code>payment_period BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfPaymentPeriod(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.PAYMENT_PERIOD, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>payment_period IN (values)</code>
     */
    public List<AccAccountPo> fetchByPaymentPeriod(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.PAYMENT_PERIOD, values);
    }

    /**
     * Fetch records that have <code>is_support_local_ad BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsSupportLocalAd(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_LOCAL_AD, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_support_local_ad IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsSupportLocalAd(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_LOCAL_AD, values);
    }

    /**
     * Fetch records that have <code>first_industry_tag_id BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfFirstIndustryTagId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.FIRST_INDUSTRY_TAG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>first_industry_tag_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByFirstIndustryTagId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.FIRST_INDUSTRY_TAG_ID, values);
    }

    /**
     * Fetch records that have <code>second_industry_tag_id BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfSecondIndustryTagId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.SECOND_INDUSTRY_TAG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>second_industry_tag_id IN (values)</code>
     */
    public List<AccAccountPo> fetchBySecondIndustryTagId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.SECOND_INDUSTRY_TAG_ID, values);
    }

    /**
     * Fetch records that have <code>third_industry_tag_id BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfThirdIndustryTagId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.THIRD_INDUSTRY_TAG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>third_industry_tag_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByThirdIndustryTagId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.THIRD_INDUSTRY_TAG_ID, values);
    }

    /**
     * Fetch records that have <code>commerce_category_first_id BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfCommerceCategoryFirstId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.COMMERCE_CATEGORY_FIRST_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>commerce_category_first_id IN
     * (values)</code>
     */
    public List<AccAccountPo> fetchByCommerceCategoryFirstId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.COMMERCE_CATEGORY_FIRST_ID, values);
    }

    /**
     * Fetch records that have <code>commerce_category_second_id BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfCommerceCategorySecondId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.COMMERCE_CATEGORY_SECOND_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>commerce_category_second_id IN
     * (values)</code>
     */
    public List<AccAccountPo> fetchByCommerceCategorySecondId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.COMMERCE_CATEGORY_SECOND_ID, values);
    }

    /**
     * Fetch records that have <code>allow_signing_bonus_pay BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAllowSigningBonusPay(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ALLOW_SIGNING_BONUS_PAY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>allow_signing_bonus_pay IN (values)</code>
     */
    public List<AccAccountPo> fetchByAllowSigningBonusPay(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ALLOW_SIGNING_BONUS_PAY, values);
    }

    /**
     * Fetch records that have <code>allow_fly_coin_pay BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAllowFlyCoinPay(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ALLOW_FLY_COIN_PAY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>allow_fly_coin_pay IN (values)</code>
     */
    public List<AccAccountPo> fetchByAllowFlyCoinPay(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ALLOW_FLY_COIN_PAY, values);
    }

    /**
     * Fetch records that have <code>allow_flow_ticket_pay BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfAllowFlowTicketPay(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.ALLOW_FLOW_TICKET_PAY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>allow_flow_ticket_pay IN (values)</code>
     */
    public List<AccAccountPo> fetchByAllowFlowTicketPay(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.ALLOW_FLOW_TICKET_PAY, values);
    }

    /**
     * Fetch records that have <code>finance_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfFinanceType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.FINANCE_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>finance_type IN (values)</code>
     */
    public List<AccAccountPo> fetchByFinanceType(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.FINANCE_TYPE, values);
    }

    /**
     * Fetch records that have <code>has_mgk_form BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfHasMgkForm(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.HAS_MGK_FORM, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>has_mgk_form IN (values)</code>
     */
    public List<AccAccountPo> fetchByHasMgkForm(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.HAS_MGK_FORM, values);
    }

    /**
     * Fetch records that have <code>mgk_form_privacy_policy BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfMgkFormPrivacyPolicy(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.MGK_FORM_PRIVACY_POLICY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mgk_form_privacy_policy IN (values)</code>
     */
    public List<AccAccountPo> fetchByMgkFormPrivacyPolicy(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.MGK_FORM_PRIVACY_POLICY, values);
    }

    /**
     * Fetch records that have <code>show_to_customer BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfShowToCustomer(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.SHOW_TO_CUSTOMER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>show_to_customer IN (values)</code>
     */
    public List<AccAccountPo> fetchByShowToCustomer(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.SHOW_TO_CUSTOMER, values);
    }

    /**
     * Fetch records that have <code>is_support_clue_pass BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfIsSupportCluePass(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_CLUE_PASS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_support_clue_pass IN (values)</code>
     */
    public List<AccAccountPo> fetchByIsSupportCluePass(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.IS_SUPPORT_CLUE_PASS, values);
    }

    /**
     * Fetch records that have <code>promotion_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfPromotionType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.PROMOTION_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>promotion_type IN (values)</code>
     */
    public List<AccAccountPo> fetchByPromotionType(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.PROMOTION_TYPE, values);
    }

    /**
     * Fetch records that have <code>united_first_industry_id BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfUnitedFirstIndustryId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.UNITED_FIRST_INDUSTRY_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>united_first_industry_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByUnitedFirstIndustryId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.UNITED_FIRST_INDUSTRY_ID, values);
    }

    /**
     * Fetch records that have <code>united_second_industry_id BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfUnitedSecondIndustryId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.UNITED_SECOND_INDUSTRY_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>united_second_industry_id IN
     * (values)</code>
     */
    public List<AccAccountPo> fetchByUnitedSecondIndustryId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.UNITED_SECOND_INDUSTRY_ID, values);
    }

    /**
     * Fetch records that have <code>united_third_industry_id BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfUnitedThirdIndustryId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.UNITED_THIRD_INDUSTRY_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>united_third_industry_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByUnitedThirdIndustryId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.UNITED_THIRD_INDUSTRY_ID, values);
    }

    /**
     * Fetch records that have <code>old_agent_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<AccAccountPo> fetchRangeOfOldAgentId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TAccAccount.ACC_ACCOUNT.OLD_AGENT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>old_agent_id IN (values)</code>
     */
    public List<AccAccountPo> fetchByOldAgentId(Integer... values) {
        return fetch(TAccAccount.ACC_ACCOUNT.OLD_AGENT_ID, values);
    }
}
