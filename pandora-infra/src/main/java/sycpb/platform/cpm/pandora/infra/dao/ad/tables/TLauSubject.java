/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauSubjectRecord;


/**
 * 单元-创意投放标的物表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauSubject extends TableImpl<LauSubjectRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_subject</code>
     */
    public static final TLauSubject LAU_SUBJECT = new TLauSubject();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauSubjectRecord> getRecordType() {
        return LauSubjectRecord.class;
    }

    /**
     * The column <code>lau_subject.id</code>. 主键ID
     */
    public final TableField<LauSubjectRecord, Integer> ID = createField(DSL.name("id"), SQLDataType.INTEGER.nullable(false).identity(true), this, "主键ID");

    /**
     * The column <code>lau_subject.material_id</code>. 标的物ID
     */
    public final TableField<LauSubjectRecord, String> MATERIAL_ID = createField(DSL.name("material_id"), SQLDataType.VARCHAR(32).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "标的物ID");

    /**
     * The column <code>lau_subject.type</code>. 标的物类型 1-直播间
     */
    public final TableField<LauSubjectRecord, Integer> TYPE = createField(DSL.name("type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "标的物类型 1-直播间");

    /**
     * The column <code>lau_subject.launchable</code>. 可投放状态 0-不可投放 1-可投放
     */
    public final TableField<LauSubjectRecord, Integer> LAUNCHABLE = createField(DSL.name("launchable"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "可投放状态 0-不可投放 1-可投放");

    /**
     * The column <code>lau_subject.is_deleted</code>. 是否删除0否 1是
     */
    public final TableField<LauSubjectRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除0否 1是");

    /**
     * The column <code>lau_subject.ctime</code>. 创建时间
     */
    public final TableField<LauSubjectRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_subject.mtime</code>. 更新时间
     */
    public final TableField<LauSubjectRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>lau_subject.area_id</code>. 直播间的分区id
     */
    public final TableField<LauSubjectRecord, Integer> AREA_ID = createField(DSL.name("area_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "直播间的分区id");

    /**
     * The column <code>lau_subject.mid</code>. 直播间绑定的mid
     */
    public final TableField<LauSubjectRecord, Long> MID = createField(DSL.name("mid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "直播间绑定的mid");

    private TLauSubject(Name alias, Table<LauSubjectRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauSubject(Name alias, Table<LauSubjectRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("单元-创意投放标的物表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_subject</code> table reference
     */
    public TLauSubject(String alias) {
        this(DSL.name(alias), LAU_SUBJECT);
    }

    /**
     * Create an aliased <code>lau_subject</code> table reference
     */
    public TLauSubject(Name alias) {
        this(alias, LAU_SUBJECT);
    }

    /**
     * Create a <code>lau_subject</code> table reference
     */
    public TLauSubject() {
        this(DSL.name("lau_subject"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauSubjectRecord, Integer> getIdentity() {
        return (Identity<LauSubjectRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauSubjectRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauSubject.LAU_SUBJECT, DSL.name("KEY_lau_subject_PRIMARY"), new TableField[] { TLauSubject.LAU_SUBJECT.ID }, true);
    }

    @Override
    public List<UniqueKey<LauSubjectRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TLauSubject.LAU_SUBJECT, DSL.name("KEY_lau_subject_uk_material_id_type"), new TableField[] { TLauSubject.LAU_SUBJECT.MATERIAL_ID, TLauSubject.LAU_SUBJECT.TYPE }, true)
        );
    }

    @Override
    public TLauSubject as(String alias) {
        return new TLauSubject(DSL.name(alias), this);
    }

    @Override
    public TLauSubject as(Name alias) {
        return new TLauSubject(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauSubject rename(String name) {
        return new TLauSubject(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauSubject rename(Name name) {
        return new TLauSubject(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<Integer, String, Integer, Integer, Integer, Timestamp, Timestamp, Integer, Long> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
