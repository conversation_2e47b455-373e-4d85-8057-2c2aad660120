/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 效果闪屏扭一扭按钮表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauSplashScreenCreativeButtonTwistInfoPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   creativeId;
    private Long      buttonId;
    private Double    twistAngle;
    private Double    twistSpeed;
    private Integer   twistXType;
    private Integer   twistYType;
    private Integer   twistZType;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;

    public LauSplashScreenCreativeButtonTwistInfoPo() {}

    public LauSplashScreenCreativeButtonTwistInfoPo(LauSplashScreenCreativeButtonTwistInfoPo value) {
        this.id = value.id;
        this.creativeId = value.creativeId;
        this.buttonId = value.buttonId;
        this.twistAngle = value.twistAngle;
        this.twistSpeed = value.twistSpeed;
        this.twistXType = value.twistXType;
        this.twistYType = value.twistYType;
        this.twistZType = value.twistZType;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
    }

    public LauSplashScreenCreativeButtonTwistInfoPo(
        Long      id,
        Integer   creativeId,
        Long      buttonId,
        Double    twistAngle,
        Double    twistSpeed,
        Integer   twistXType,
        Integer   twistYType,
        Integer   twistZType,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime
    ) {
        this.id = id;
        this.creativeId = creativeId;
        this.buttonId = buttonId;
        this.twistAngle = twistAngle;
        this.twistSpeed = twistSpeed;
        this.twistXType = twistXType;
        this.twistYType = twistYType;
        this.twistZType = twistZType;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button_twist_info.id</code>.
     * 自增id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button_twist_info.id</code>.
     * 自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_twist_info.creative_id</code>.
     * 创意id
     */
    public Integer getCreativeId() {
        return this.creativeId;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_twist_info.creative_id</code>.
     * 创意id
     */
    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_twist_info.button_id</code>. 按钮id
     */
    public Long getButtonId() {
        return this.buttonId;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_twist_info.button_id</code>. 按钮id
     */
    public void setButtonId(Long buttonId) {
        this.buttonId = buttonId;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_twist_info.twist_angle</code>.
     * 扭动角度
     */
    public Double getTwistAngle() {
        return this.twistAngle;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_twist_info.twist_angle</code>.
     * 扭动角度
     */
    public void setTwistAngle(Double twistAngle) {
        this.twistAngle = twistAngle;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_twist_info.twist_speed</code>.
     * 扭动加速度
     */
    public Double getTwistSpeed() {
        return this.twistSpeed;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_twist_info.twist_speed</code>.
     * 扭动加速度
     */
    public void setTwistSpeed(Double twistSpeed) {
        this.twistSpeed = twistSpeed;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_twist_info.twist_x_type</code>.
     * 闪屏按钮扭一扭旋转轴x参数，-1：当前轴无效，0：任意，1：左扭，2：右扭
     */
    public Integer getTwistXType() {
        return this.twistXType;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_twist_info.twist_x_type</code>.
     * 闪屏按钮扭一扭旋转轴x参数，-1：当前轴无效，0：任意，1：左扭，2：右扭
     */
    public void setTwistXType(Integer twistXType) {
        this.twistXType = twistXType;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_twist_info.twist_y_type</code>.
     * 闪屏按钮扭一扭旋转轴y参数，-1：当前轴无效，0：任意，1：左扭，2：右扭
     */
    public Integer getTwistYType() {
        return this.twistYType;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_twist_info.twist_y_type</code>.
     * 闪屏按钮扭一扭旋转轴y参数，-1：当前轴无效，0：任意，1：左扭，2：右扭
     */
    public void setTwistYType(Integer twistYType) {
        this.twistYType = twistYType;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_twist_info.twist_z_type</code>.
     * 闪屏按钮扭一扭旋转轴z参数，-1：当前轴无效，0：任意，1：左扭，2：右扭
     */
    public Integer getTwistZType() {
        return this.twistZType;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_twist_info.twist_z_type</code>.
     * 闪屏按钮扭一扭旋转轴z参数，-1：当前轴无效，0：任意，1：左扭，2：右扭
     */
    public void setTwistZType(Integer twistZType) {
        this.twistZType = twistZType;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_twist_info.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_twist_info.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_twist_info.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_twist_info.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_twist_info.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_twist_info.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauSplashScreenCreativeButtonTwistInfoPo (");

        sb.append(id);
        sb.append(", ").append(creativeId);
        sb.append(", ").append(buttonId);
        sb.append(", ").append(twistAngle);
        sb.append(", ").append(twistSpeed);
        sb.append(", ").append(twistXType);
        sb.append(", ").append(twistYType);
        sb.append(", ").append(twistZType);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);

        sb.append(")");
        return sb.toString();
    }
}
