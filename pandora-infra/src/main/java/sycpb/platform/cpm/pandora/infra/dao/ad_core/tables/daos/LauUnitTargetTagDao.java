/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitTargetTag;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitTargetTagPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitTargetTagRecord;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauUnitTargetTagDao extends DAOImpl<LauUnitTargetTagRecord, LauUnitTargetTagPo, Integer> {

    /**
     * Create a new LauUnitTargetTagDao without any configuration
     */
    public LauUnitTargetTagDao() {
        super(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG, LauUnitTargetTagPo.class);
    }

    /**
     * Create a new LauUnitTargetTagDao with an attached configuration
     */
    @Autowired
    public LauUnitTargetTagDao(Configuration configuration) {
        super(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG, LauUnitTargetTagPo.class, configuration);
    }

    @Override
    public Integer getId(LauUnitTargetTagPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetTagPo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauUnitTargetTagPo> fetchById(Integer... values) {
        return fetch(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauUnitTargetTagPo fetchOneById(Integer value) {
        return fetchOne(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.ID, value);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetTagPo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauUnitTargetTagPo> fetchByUnitId(Integer... values) {
        return fetch(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>platform_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetTagPo> fetchRangeOfPlatformType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.PLATFORM_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>platform_type IN (values)</code>
     */
    public List<LauUnitTargetTagPo> fetchByPlatformType(Integer... values) {
        return fetch(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.PLATFORM_TYPE, values);
    }

    /**
     * Fetch records that have <code>tag_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetTagPo> fetchRangeOfTagId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.TAG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>tag_id IN (values)</code>
     */
    public List<LauUnitTargetTagPo> fetchByTagId(Long... values) {
        return fetch(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.TAG_ID, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetTagPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauUnitTargetTagPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetTagPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauUnitTargetTagPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetTagPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauUnitTargetTagPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauUnitTargetTag.LAU_UNIT_TARGET_TAG.MTIME, values);
    }
}
