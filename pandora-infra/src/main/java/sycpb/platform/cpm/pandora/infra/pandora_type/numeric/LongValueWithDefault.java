package sycpb.platform.cpm.pandora.infra.pandora_type.numeric;

import lombok.Getter;
import sycpb.platform.cpm.pandora.infra.pandora_type.ValueWithDefault;

import java.util.Objects;

@Getter
public abstract class LongValueWithDefault extends ValueWithDefault<Long> {
    public LongValueWithDefault(Long defaultValue) {
        super(defaultValue);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LongValueWithDefault)) return false;

        final var x = (LongValueWithDefault) o;
        return Objects.equals(x.getRawValue(), getRawValue());
    }

    @Override
    public int hashCode() {
        return Long.hashCode(getValue());
    }

}
