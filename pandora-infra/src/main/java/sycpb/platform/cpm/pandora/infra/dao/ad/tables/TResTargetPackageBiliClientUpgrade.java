/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.ResTargetPackageBiliClientUpgradeRecord;


/**
 * 新版定向包分端bili客户端定向表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TResTargetPackageBiliClientUpgrade extends TableImpl<ResTargetPackageBiliClientUpgradeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>res_target_package_bili_client_upgrade</code>
     */
    public static final TResTargetPackageBiliClientUpgrade RES_TARGET_PACKAGE_BILI_CLIENT_UPGRADE = new TResTargetPackageBiliClientUpgrade();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResTargetPackageBiliClientUpgradeRecord> getRecordType() {
        return ResTargetPackageBiliClientUpgradeRecord.class;
    }

    /**
     * The column <code>res_target_package_bili_client_upgrade.id</code>. 自增id
     */
    public final TableField<ResTargetPackageBiliClientUpgradeRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增id");

    /**
     * The column
     * <code>res_target_package_bili_client_upgrade.target_package_id</code>.
     * 定向包id
     */
    public final TableField<ResTargetPackageBiliClientUpgradeRecord, Integer> TARGET_PACKAGE_ID = createField(DSL.name("target_package_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "定向包id");

    /**
     * The column <code>res_target_package_bili_client_upgrade.os</code>. 设备平台
     * android-399,iphone-398,ipad-421
     */
    public final TableField<ResTargetPackageBiliClientUpgradeRecord, Integer> OS = createField(DSL.name("os"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "设备平台 android-399,iphone-398,ipad-421");

    /**
     * The column <code>res_target_package_bili_client_upgrade.relation</code>.
     * 定向关系 0-不限1-介于2-小于等于3-大于等于4-小于5大于
     */
    public final TableField<ResTargetPackageBiliClientUpgradeRecord, Integer> RELATION = createField(DSL.name("relation"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "定向关系 0-不限1-介于2-小于等于3-大于等于4-小于5大于");

    /**
     * The column
     * <code>res_target_package_bili_client_upgrade.smaller_version</code>.
     * 较小版本号 build值
     */
    public final TableField<ResTargetPackageBiliClientUpgradeRecord, Integer> SMALLER_VERSION = createField(DSL.name("smaller_version"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "较小版本号 build值");

    /**
     * The column
     * <code>res_target_package_bili_client_upgrade.larger_version</code>. 较大版本号
     * build值
     */
    public final TableField<ResTargetPackageBiliClientUpgradeRecord, Integer> LARGER_VERSION = createField(DSL.name("larger_version"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "较大版本号 build值");

    /**
     * The column
     * <code>res_target_package_bili_client_upgrade.is_deleted</code>. 软删除 0 否
     * 1是
     */
    public final TableField<ResTargetPackageBiliClientUpgradeRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除 0 否 1是");

    /**
     * The column <code>res_target_package_bili_client_upgrade.ctime</code>.
     * 创建时间
     */
    public final TableField<ResTargetPackageBiliClientUpgradeRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>res_target_package_bili_client_upgrade.mtime</code>.
     * 更新时间
     */
    public final TableField<ResTargetPackageBiliClientUpgradeRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    private TResTargetPackageBiliClientUpgrade(Name alias, Table<ResTargetPackageBiliClientUpgradeRecord> aliased) {
        this(alias, aliased, null);
    }

    private TResTargetPackageBiliClientUpgrade(Name alias, Table<ResTargetPackageBiliClientUpgradeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("新版定向包分端bili客户端定向表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>res_target_package_bili_client_upgrade</code>
     * table reference
     */
    public TResTargetPackageBiliClientUpgrade(String alias) {
        this(DSL.name(alias), RES_TARGET_PACKAGE_BILI_CLIENT_UPGRADE);
    }

    /**
     * Create an aliased <code>res_target_package_bili_client_upgrade</code>
     * table reference
     */
    public TResTargetPackageBiliClientUpgrade(Name alias) {
        this(alias, RES_TARGET_PACKAGE_BILI_CLIENT_UPGRADE);
    }

    /**
     * Create a <code>res_target_package_bili_client_upgrade</code> table
     * reference
     */
    public TResTargetPackageBiliClientUpgrade() {
        this(DSL.name("res_target_package_bili_client_upgrade"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<ResTargetPackageBiliClientUpgradeRecord, Long> getIdentity() {
        return (Identity<ResTargetPackageBiliClientUpgradeRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<ResTargetPackageBiliClientUpgradeRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TResTargetPackageBiliClientUpgrade.RES_TARGET_PACKAGE_BILI_CLIENT_UPGRADE, DSL.name("KEY_res_target_package_bili_client_upgrade_PRIMARY"), new TableField[] { TResTargetPackageBiliClientUpgrade.RES_TARGET_PACKAGE_BILI_CLIENT_UPGRADE.ID }, true);
    }

    @Override
    public List<UniqueKey<ResTargetPackageBiliClientUpgradeRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TResTargetPackageBiliClientUpgrade.RES_TARGET_PACKAGE_BILI_CLIENT_UPGRADE, DSL.name("KEY_res_target_package_bili_client_upgrade_uk_target_package_id_os"), new TableField[] { TResTargetPackageBiliClientUpgrade.RES_TARGET_PACKAGE_BILI_CLIENT_UPGRADE.TARGET_PACKAGE_ID, TResTargetPackageBiliClientUpgrade.RES_TARGET_PACKAGE_BILI_CLIENT_UPGRADE.OS }, true)
        );
    }

    @Override
    public TResTargetPackageBiliClientUpgrade as(String alias) {
        return new TResTargetPackageBiliClientUpgrade(DSL.name(alias), this);
    }

    @Override
    public TResTargetPackageBiliClientUpgrade as(Name alias) {
        return new TResTargetPackageBiliClientUpgrade(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TResTargetPackageBiliClientUpgrade rename(String name) {
        return new TResTargetPackageBiliClientUpgrade(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TResTargetPackageBiliClientUpgrade rename(Name name) {
        return new TResTargetPackageBiliClientUpgrade(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<Long, Integer, Integer, Integer, Integer, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
