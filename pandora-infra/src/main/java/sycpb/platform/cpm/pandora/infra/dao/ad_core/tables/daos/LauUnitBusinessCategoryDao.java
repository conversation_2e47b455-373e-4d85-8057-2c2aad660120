/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitBusinessCategory;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitBusinessCategoryPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitBusinessCategoryRecord;


/**
 * 单元商业分类表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauUnitBusinessCategoryDao extends DAOImpl<LauUnitBusinessCategoryRecord, LauUnitBusinessCategoryPo, Integer> {

    /**
     * Create a new LauUnitBusinessCategoryDao without any configuration
     */
    public LauUnitBusinessCategoryDao() {
        super(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY, LauUnitBusinessCategoryPo.class);
    }

    /**
     * Create a new LauUnitBusinessCategoryDao with an attached configuration
     */
    @Autowired
    public LauUnitBusinessCategoryDao(Configuration configuration) {
        super(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY, LauUnitBusinessCategoryPo.class, configuration);
    }

    @Override
    public Integer getId(LauUnitBusinessCategoryPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchById(Integer... values) {
        return fetch(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauUnitBusinessCategoryPo fetchOneById(Integer value) {
        return fetchOne(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.ID, value);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.MTIME, values);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchByUnitId(Integer... values) {
        return fetch(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.UNIT_ID, values);
    }

    /**
     * Fetch a unique record that has <code>unit_id = value</code>
     */
    public LauUnitBusinessCategoryPo fetchOneByUnitId(Integer value) {
        return fetchOne(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.UNIT_ID, value);
    }

    /**
     * Fetch records that have <code>first_category_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchRangeOfFirstCategoryId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.FIRST_CATEGORY_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>first_category_id IN (values)</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchByFirstCategoryId(Integer... values) {
        return fetch(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.FIRST_CATEGORY_ID, values);
    }

    /**
     * Fetch records that have <code>second_category_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchRangeOfSecondCategoryId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.SECOND_CATEGORY_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>second_category_id IN (values)</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchBySecondCategoryId(Integer... values) {
        return fetch(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.SECOND_CATEGORY_ID, values);
    }

    /**
     * Fetch records that have <code>third_category_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchRangeOfThirdCategoryId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.THIRD_CATEGORY_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>third_category_id IN (values)</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchByThirdCategoryId(Integer... values) {
        return fetch(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.THIRD_CATEGORY_ID, values);
    }

    /**
     * Fetch records that have <code>biz_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchRangeOfBizStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.BIZ_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_status IN (values)</code>
     */
    public List<LauUnitBusinessCategoryPo> fetchByBizStatus(Integer... values) {
        return fetch(TLauUnitBusinessCategory.LAU_UNIT_BUSINESS_CATEGORY.BIZ_STATUS, values);
    }
}
