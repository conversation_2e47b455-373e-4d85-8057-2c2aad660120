/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeArchive;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeArchivePo;


/**
 * 创意稿件表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeArchiveRecord extends UpdatableRecordImpl<LauCreativeArchiveRecord> implements Record14<Long, Timestamp, Timestamp, Integer, Integer, Long, Long, Integer, String, String, Long, String, Long, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_archive.id</code>. 主键id
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_archive.id</code>. 主键id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_creative_archive.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_creative_archive.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(1);
    }

    /**
     * Setter for <code>lau_creative_archive.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_creative_archive.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(2);
    }

    /**
     * Setter for <code>lau_creative_archive.unit_id</code>. 单元id
     */
    public void setUnitId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_creative_archive.unit_id</code>. 单元id
     */
    public Integer getUnitId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_creative_archive.creative_id</code>. 创意id
     */
    public void setCreativeId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_creative_archive.creative_id</code>. 创意id
     */
    public Integer getCreativeId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_creative_archive.avid</code>. 稿件id
     */
    public void setAvid(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_creative_archive.avid</code>. 稿件id
     */
    public Long getAvid() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>lau_creative_archive.cid</code>. 视频云id
     */
    public void setCid(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_creative_archive.cid</code>. 视频云id
     */
    public Long getCid() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>lau_creative_archive.source</code>. 稿件来源: 0-未知, 1-我的视频,
     * 2-bilibili账号视频, 3-花火商单视频, 4-普通内容视频
     */
    public void setSource(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_creative_archive.source</code>. 稿件来源: 0-未知, 1-我的视频,
     * 2-bilibili账号视频, 3-花火商单视频, 4-普通内容视频
     */
    public Integer getSource() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>lau_creative_archive.cover_url</code>. 封面url
     */
    public void setCoverUrl(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_creative_archive.cover_url</code>. 封面url
     */
    public String getCoverUrl() {
        return (String) get(8);
    }

    /**
     * Setter for <code>lau_creative_archive.cover_md5</code>. 封面md5
     */
    public void setCoverMd5(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_creative_archive.cover_md5</code>. 封面md5
     */
    public String getCoverMd5() {
        return (String) get(9);
    }

    /**
     * Setter for <code>lau_creative_archive.cover_material_id</code>. 封面物料id
     */
    public void setCoverMaterialId(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_creative_archive.cover_material_id</code>. 封面物料id
     */
    public Long getCoverMaterialId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>lau_creative_archive.material_md5</code>. 物料md5,
     * 稿件+封面的唯一标识
     */
    public void setMaterialMd5(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_creative_archive.material_md5</code>. 物料md5,
     * 稿件+封面的唯一标识
     */
    public String getMaterialMd5() {
        return (String) get(11);
    }

    /**
     * Setter for <code>lau_creative_archive.material_id</code>. 稿件+封面组合物料id
     */
    public void setMaterialId(Long value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_creative_archive.material_id</code>. 稿件+封面组合物料id
     */
    public Long getMaterialId() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>lau_creative_archive.is_use_smart_cut</code>.
     * 是否使用智能剪裁，0是不使用,1是使用
     */
    public void setIsUseSmartCut(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_creative_archive.is_use_smart_cut</code>.
     * 是否使用智能剪裁，0是不使用,1是使用
     */
    public Integer getIsUseSmartCut() {
        return (Integer) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<Long, Timestamp, Timestamp, Integer, Integer, Long, Long, Integer, String, String, Long, String, Long, Integer> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<Long, Timestamp, Timestamp, Integer, Integer, Long, Long, Integer, String, String, Long, String, Long, Integer> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.ID;
    }

    @Override
    public Field<Timestamp> field2() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.CTIME;
    }

    @Override
    public Field<Timestamp> field3() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.MTIME;
    }

    @Override
    public Field<Integer> field4() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.UNIT_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.CREATIVE_ID;
    }

    @Override
    public Field<Long> field6() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.AVID;
    }

    @Override
    public Field<Long> field7() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.CID;
    }

    @Override
    public Field<Integer> field8() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.SOURCE;
    }

    @Override
    public Field<String> field9() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.COVER_URL;
    }

    @Override
    public Field<String> field10() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.COVER_MD5;
    }

    @Override
    public Field<Long> field11() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.COVER_MATERIAL_ID;
    }

    @Override
    public Field<String> field12() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.MATERIAL_MD5;
    }

    @Override
    public Field<Long> field13() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.MATERIAL_ID;
    }

    @Override
    public Field<Integer> field14() {
        return TLauCreativeArchive.LAU_CREATIVE_ARCHIVE.IS_USE_SMART_CUT;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Timestamp component2() {
        return getCtime();
    }

    @Override
    public Timestamp component3() {
        return getMtime();
    }

    @Override
    public Integer component4() {
        return getUnitId();
    }

    @Override
    public Integer component5() {
        return getCreativeId();
    }

    @Override
    public Long component6() {
        return getAvid();
    }

    @Override
    public Long component7() {
        return getCid();
    }

    @Override
    public Integer component8() {
        return getSource();
    }

    @Override
    public String component9() {
        return getCoverUrl();
    }

    @Override
    public String component10() {
        return getCoverMd5();
    }

    @Override
    public Long component11() {
        return getCoverMaterialId();
    }

    @Override
    public String component12() {
        return getMaterialMd5();
    }

    @Override
    public Long component13() {
        return getMaterialId();
    }

    @Override
    public Integer component14() {
        return getIsUseSmartCut();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Timestamp value2() {
        return getCtime();
    }

    @Override
    public Timestamp value3() {
        return getMtime();
    }

    @Override
    public Integer value4() {
        return getUnitId();
    }

    @Override
    public Integer value5() {
        return getCreativeId();
    }

    @Override
    public Long value6() {
        return getAvid();
    }

    @Override
    public Long value7() {
        return getCid();
    }

    @Override
    public Integer value8() {
        return getSource();
    }

    @Override
    public String value9() {
        return getCoverUrl();
    }

    @Override
    public String value10() {
        return getCoverMd5();
    }

    @Override
    public Long value11() {
        return getCoverMaterialId();
    }

    @Override
    public String value12() {
        return getMaterialMd5();
    }

    @Override
    public Long value13() {
        return getMaterialId();
    }

    @Override
    public Integer value14() {
        return getIsUseSmartCut();
    }

    @Override
    public LauCreativeArchiveRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value2(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value3(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value4(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value5(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value6(Long value) {
        setAvid(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value7(Long value) {
        setCid(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value8(Integer value) {
        setSource(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value9(String value) {
        setCoverUrl(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value10(String value) {
        setCoverMd5(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value11(Long value) {
        setCoverMaterialId(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value12(String value) {
        setMaterialMd5(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value13(Long value) {
        setMaterialId(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord value14(Integer value) {
        setIsUseSmartCut(value);
        return this;
    }

    @Override
    public LauCreativeArchiveRecord values(Long value1, Timestamp value2, Timestamp value3, Integer value4, Integer value5, Long value6, Long value7, Integer value8, String value9, String value10, Long value11, String value12, Long value13, Integer value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativeArchiveRecord
     */
    public LauCreativeArchiveRecord() {
        super(TLauCreativeArchive.LAU_CREATIVE_ARCHIVE);
    }

    /**
     * Create a detached, initialised LauCreativeArchiveRecord
     */
    public LauCreativeArchiveRecord(Long id, Timestamp ctime, Timestamp mtime, Integer unitId, Integer creativeId, Long avid, Long cid, Integer source, String coverUrl, String coverMd5, Long coverMaterialId, String materialMd5, Long materialId, Integer isUseSmartCut) {
        super(TLauCreativeArchive.LAU_CREATIVE_ARCHIVE);

        setId(id);
        setCtime(ctime);
        setMtime(mtime);
        setUnitId(unitId);
        setCreativeId(creativeId);
        setAvid(avid);
        setCid(cid);
        setSource(source);
        setCoverUrl(coverUrl);
        setCoverMd5(coverMd5);
        setCoverMaterialId(coverMaterialId);
        setMaterialMd5(materialMd5);
        setMaterialId(materialId);
        setIsUseSmartCut(isUseSmartCut);
    }

    /**
     * Create a detached, initialised LauCreativeArchiveRecord
     */
    public LauCreativeArchiveRecord(LauCreativeArchivePo value) {
        super(TLauCreativeArchive.LAU_CREATIVE_ARCHIVE);

        if (value != null) {
            setId(value.getId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setUnitId(value.getUnitId());
            setCreativeId(value.getCreativeId());
            setAvid(value.getAvid());
            setCid(value.getCid());
            setSource(value.getSource());
            setCoverUrl(value.getCoverUrl());
            setCoverMd5(value.getCoverMd5());
            setCoverMaterialId(value.getCoverMaterialId());
            setMaterialMd5(value.getMaterialMd5());
            setMaterialId(value.getMaterialId());
            setIsUseSmartCut(value.getIsUseSmartCut());
        }
    }
}
