/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeAnchorMappingRecord;


/**
 * 创意锚点关系
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauCreativeAnchorMapping extends TableImpl<LauCreativeAnchorMappingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_creative_anchor_mapping</code>
     */
    public static final TLauCreativeAnchorMapping LAU_CREATIVE_ANCHOR_MAPPING = new TLauCreativeAnchorMapping();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauCreativeAnchorMappingRecord> getRecordType() {
        return LauCreativeAnchorMappingRecord.class;
    }

    /**
     * The column <code>lau_creative_anchor_mapping.id</code>. id
     */
    public final TableField<LauCreativeAnchorMappingRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "id");

    /**
     * The column <code>lau_creative_anchor_mapping.aid</code>. 稿件id
     */
    public final TableField<LauCreativeAnchorMappingRecord, Long> AID = createField(DSL.name("aid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "稿件id");

    /**
     * The column <code>lau_creative_anchor_mapping.anchor_id</code>. 锚点id
     */
    public final TableField<LauCreativeAnchorMappingRecord, Long> ANCHOR_ID = createField(DSL.name("anchor_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "锚点id");

    /**
     * The column <code>lau_creative_anchor_mapping.creative_id</code>. 创意id
     */
    public final TableField<LauCreativeAnchorMappingRecord, Integer> CREATIVE_ID = createField(DSL.name("creative_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "创意id");

    /**
     * The column <code>lau_creative_anchor_mapping.unit_id</code>. 单元id
     */
    public final TableField<LauCreativeAnchorMappingRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元id");

    /**
     * The column <code>lau_creative_anchor_mapping.system_type</code>.
     * 锚点系统类型：1三连 2带货 3花火 0未知
     */
    public final TableField<LauCreativeAnchorMappingRecord, Integer> SYSTEM_TYPE = createField(DSL.name("system_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "锚点系统类型：1三连 2带货 3花火 0未知");

    /**
     * The column <code>lau_creative_anchor_mapping.is_deleted</code>. 是否删除
     */
    public final TableField<LauCreativeAnchorMappingRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除");

    /**
     * The column <code>lau_creative_anchor_mapping.ctime</code>. 添加时间
     */
    public final TableField<LauCreativeAnchorMappingRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column <code>lau_creative_anchor_mapping.mtime</code>. 修改时间
     */
    public final TableField<LauCreativeAnchorMappingRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    private TLauCreativeAnchorMapping(Name alias, Table<LauCreativeAnchorMappingRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauCreativeAnchorMapping(Name alias, Table<LauCreativeAnchorMappingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("创意锚点关系"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_creative_anchor_mapping</code> table
     * reference
     */
    public TLauCreativeAnchorMapping(String alias) {
        this(DSL.name(alias), LAU_CREATIVE_ANCHOR_MAPPING);
    }

    /**
     * Create an aliased <code>lau_creative_anchor_mapping</code> table
     * reference
     */
    public TLauCreativeAnchorMapping(Name alias) {
        this(alias, LAU_CREATIVE_ANCHOR_MAPPING);
    }

    /**
     * Create a <code>lau_creative_anchor_mapping</code> table reference
     */
    public TLauCreativeAnchorMapping() {
        this(DSL.name("lau_creative_anchor_mapping"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauCreativeAnchorMappingRecord, Long> getIdentity() {
        return (Identity<LauCreativeAnchorMappingRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauCreativeAnchorMappingRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING, DSL.name("KEY_lau_creative_anchor_mapping_PRIMARY"), new TableField[] { TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.ID }, true);
    }

    @Override
    public TLauCreativeAnchorMapping as(String alias) {
        return new TLauCreativeAnchorMapping(DSL.name(alias), this);
    }

    @Override
    public TLauCreativeAnchorMapping as(Name alias) {
        return new TLauCreativeAnchorMapping(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeAnchorMapping rename(String name) {
        return new TLauCreativeAnchorMapping(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeAnchorMapping rename(Name name) {
        return new TLauCreativeAnchorMapping(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<Long, Long, Long, Integer, Integer, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
