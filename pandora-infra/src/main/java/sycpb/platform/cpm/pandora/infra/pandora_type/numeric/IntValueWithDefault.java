package sycpb.platform.cpm.pandora.infra.pandora_type.numeric;

import lombok.Getter;
import sycpb.platform.cpm.pandora.infra.pandora_type.ValueWithDefault;

import java.util.Objects;

@Getter
public abstract class IntValueWithDefault extends ValueWithDefault<Integer> {
    public IntValueWithDefault(Integer defaultValue) {
        super(defaultValue);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof IntValueWithDefault)) return false;

        final var x = (IntValueWithDefault) o;
        return Objects.equals(x.getRawValue(), getRawValue());
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(getValue());
    }
}
