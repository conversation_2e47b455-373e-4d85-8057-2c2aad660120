/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 单元额外定向
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitTargetExtraPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer   id;
    private Integer   unitId;
    private Integer   unitTargetType;
    private String    unitTargetValue;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   recommendType;

    public LauUnitTargetExtraPo() {}

    public LauUnitTargetExtraPo(LauUnitTargetExtraPo value) {
        this.id = value.id;
        this.unitId = value.unitId;
        this.unitTargetType = value.unitTargetType;
        this.unitTargetValue = value.unitTargetValue;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.recommendType = value.recommendType;
    }

    public LauUnitTargetExtraPo(
        Integer   id,
        Integer   unitId,
        Integer   unitTargetType,
        String    unitTargetValue,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime,
        Integer   recommendType
    ) {
        this.id = id;
        this.unitId = unitId;
        this.unitTargetType = unitTargetType;
        this.unitTargetValue = unitTargetValue;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
        this.recommendType = recommendType;
    }

    /**
     * Getter for <code>lau_unit_target_extra.id</code>. 主键id
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_unit_target_extra.id</code>. 主键id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_unit_target_extra.unit_id</code>. 单元id
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_unit_target_extra.unit_id</code>. 单元id
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for <code>lau_unit_target_extra.unit_target_type</code>.
     * 单元定向类型:1-粉丝关系,2-互动行为,3-浏览行为,4-视频二级分区,5-投放粉丝,6-排除粉丝,7-刷次定向
     */
    public Integer getUnitTargetType() {
        return this.unitTargetType;
    }

    /**
     * Setter for <code>lau_unit_target_extra.unit_target_type</code>.
     * 单元定向类型:1-粉丝关系,2-互动行为,3-浏览行为,4-视频二级分区,5-投放粉丝,6-排除粉丝,7-刷次定向
     */
    public void setUnitTargetType(Integer unitTargetType) {
        this.unitTargetType = unitTargetType;
    }

    /**
     * Getter for <code>lau_unit_target_extra.unit_target_value</code>. 单元定向值
     */
    public String getUnitTargetValue() {
        return this.unitTargetValue;
    }

    /**
     * Setter for <code>lau_unit_target_extra.unit_target_value</code>. 单元定向值
     */
    public void setUnitTargetValue(String unitTargetValue) {
        this.unitTargetValue = unitTargetValue;
    }

    /**
     * Getter for <code>lau_unit_target_extra.is_deleted</code>. 软删除:0-有效,1-删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_unit_target_extra.is_deleted</code>. 软删除:0-有效,1-删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_unit_target_extra.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_unit_target_extra.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_unit_target_extra.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_unit_target_extra.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_unit_target_extra.recommend_type</code>.
     * 投放粉丝来源推荐包（0-无 1-核心up主 2-优选up主 3-高潜up主）
     */
    public Integer getRecommendType() {
        return this.recommendType;
    }

    /**
     * Setter for <code>lau_unit_target_extra.recommend_type</code>.
     * 投放粉丝来源推荐包（0-无 1-核心up主 2-优选up主 3-高潜up主）
     */
    public void setRecommendType(Integer recommendType) {
        this.recommendType = recommendType;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauUnitTargetExtraPo (");

        sb.append(id);
        sb.append(", ").append(unitId);
        sb.append(", ").append(unitTargetType);
        sb.append(", ").append(unitTargetValue);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(recommendType);

        sb.append(")");
        return sb.toString();
    }
}
