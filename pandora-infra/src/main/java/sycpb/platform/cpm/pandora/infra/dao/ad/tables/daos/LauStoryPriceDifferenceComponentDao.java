/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauStoryPriceDifferenceComponent;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauStoryPriceDifferenceComponentPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauStoryPriceDifferenceComponentRecord;


/**
 * story组件-价差卡
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauStoryPriceDifferenceComponentDao extends DAOImpl<LauStoryPriceDifferenceComponentRecord, LauStoryPriceDifferenceComponentPo, Long> {

    /**
     * Create a new LauStoryPriceDifferenceComponentDao without any
     * configuration
     */
    public LauStoryPriceDifferenceComponentDao() {
        super(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT, LauStoryPriceDifferenceComponentPo.class);
    }

    /**
     * Create a new LauStoryPriceDifferenceComponentDao with an attached
     * configuration
     */
    @Autowired
    public LauStoryPriceDifferenceComponentDao(Configuration configuration) {
        super(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT, LauStoryPriceDifferenceComponentPo.class, configuration);
    }

    @Override
    public Long getId(LauStoryPriceDifferenceComponentPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchById(Long... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauStoryPriceDifferenceComponentPo fetchOneById(Long value) {
        return fetchOne(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.ID, value);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.MTIME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByAccountId(Integer... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>component_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfComponentId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.COMPONENT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>component_id IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByComponentId(Long... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.COMPONENT_ID, values);
    }

    /**
     * Fetch records that have <code>component_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfComponentName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.COMPONENT_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>component_name IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByComponentName(String... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.COMPONENT_NAME, values);
    }

    /**
     * Fetch records that have <code>image_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfImageUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.IMAGE_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_url IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByImageUrl(String... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.IMAGE_URL, values);
    }

    /**
     * Fetch records that have <code>image_md5 BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfImageMd5(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.IMAGE_MD5, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_md5 IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByImageMd5(String... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.IMAGE_MD5, values);
    }

    /**
     * Fetch records that have <code>title BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfTitle(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.TITLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>title IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByTitle(String... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.TITLE, values);
    }

    /**
     * Fetch records that have <code>original_price BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfOriginalPrice(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.ORIGINAL_PRICE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>original_price IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByOriginalPrice(Integer... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.ORIGINAL_PRICE, values);
    }

    /**
     * Fetch records that have <code>price BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfPrice(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.PRICE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>price IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByPrice(Integer... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.PRICE, values);
    }

    /**
     * Fetch records that have <code>item_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfItemId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.ITEM_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>item_id IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByItemId(Long... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.ITEM_ID, values);
    }

    /**
     * Fetch records that have <code>item_source BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfItemSource(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.ITEM_SOURCE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>item_source IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByItemSource(Integer... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.ITEM_SOURCE, values);
    }

    /**
     * Fetch records that have <code>jump_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfJumpType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.JUMP_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>jump_type IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByJumpType(Integer... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.JUMP_TYPE, values);
    }

    /**
     * Fetch records that have <code>jump_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfJumpUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.JUMP_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>jump_url IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByJumpUrl(String... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.JUMP_URL, values);
    }

    /**
     * Fetch records that have <code>schema_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfSchemaUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.SCHEMA_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>schema_url IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchBySchemaUrl(String... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.SCHEMA_URL, values);
    }

    /**
     * Fetch records that have <code>mini_program_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfMiniProgramId(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.MINI_PROGRAM_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mini_program_id IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByMiniProgramId(String... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.MINI_PROGRAM_ID, values);
    }

    /**
     * Fetch records that have <code>mini_program_name BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfMiniProgramName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.MINI_PROGRAM_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mini_program_name IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByMiniProgramName(String... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.MINI_PROGRAM_NAME, values);
    }

    /**
     * Fetch records that have <code>mini_program_path BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchRangeOfMiniProgramPath(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.MINI_PROGRAM_PATH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mini_program_path IN (values)</code>
     */
    public List<LauStoryPriceDifferenceComponentPo> fetchByMiniProgramPath(String... values) {
        return fetch(TLauStoryPriceDifferenceComponent.LAU_STORY_PRICE_DIFFERENCE_COMPONENT.MINI_PROGRAM_PATH, values);
    }
}
