/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauNativeArchive;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauNativeArchivePo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauNativeArchiveRecord;


/**
 * 原生稿件表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauNativeArchiveDao extends DAOImpl<LauNativeArchiveRecord, LauNativeArchivePo, Long> {

    /**
     * Create a new LauNativeArchiveDao without any configuration
     */
    public LauNativeArchiveDao() {
        super(TLauNativeArchive.LAU_NATIVE_ARCHIVE, LauNativeArchivePo.class);
    }

    /**
     * Create a new LauNativeArchiveDao with an attached configuration
     */
    @Autowired
    public LauNativeArchiveDao(Configuration configuration) {
        super(TLauNativeArchive.LAU_NATIVE_ARCHIVE, LauNativeArchivePo.class, configuration);
    }

    @Override
    public Long getId(LauNativeArchivePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchById(Long... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauNativeArchivePo fetchOneById(Long value) {
        return fetchOne(TLauNativeArchive.LAU_NATIVE_ARCHIVE.ID, value);
    }

    /**
     * Fetch records that have <code>avid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfAvid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.AVID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>avid IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByAvid(Long... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.AVID, values);
    }

    /**
     * Fetch records that have <code>reason BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfReason(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.REASON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reason IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByReason(String... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.REASON, values);
    }

    /**
     * Fetch records that have <code>audit_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfAuditStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.AUDIT_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>audit_status IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByAuditStatus(Integer... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.AUDIT_STATUS, values);
    }

    /**
     * Fetch records that have <code>is_recheck BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfIsRecheck(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.IS_RECHECK, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_recheck IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByIsRecheck(Integer... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.IS_RECHECK, values);
    }

    /**
     * Fetch records that have <code>version BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfVersion(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.VERSION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>version IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByVersion(Integer... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.VERSION, values);
    }

    /**
     * Fetch records that have <code>title BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfTitle(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.TITLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>title IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByTitle(String... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.TITLE, values);
    }

    /**
     * Fetch records that have <code>up_mid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfUpMid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.UP_MID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>up_mid IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByUpMid(Long... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.UP_MID, values);
    }

    /**
     * Fetch records that have <code>cover BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfCover(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.COVER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByCover(String... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.COVER, values);
    }

    /**
     * Fetch records that have <code>up_nickname BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfUpNickname(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.UP_NICKNAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>up_nickname IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByUpNickname(String... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.UP_NICKNAME, values);
    }

    /**
     * Fetch records that have <code>tag_ids BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfTagIds(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.TAG_IDS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>tag_ids IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByTagIds(String... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.TAG_IDS, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByCtime(Timestamp... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByMtime(Timestamp... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.MTIME, values);
    }

    /**
     * Fetch records that have <code>biz_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfBizType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.BIZ_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_type IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByBizType(Integer... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.BIZ_TYPE, values);
    }

    /**
     * Fetch records that have <code>shallow_audit_status BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfShallowAuditStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.SHALLOW_AUDIT_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>shallow_audit_status IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByShallowAuditStatus(Integer... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.SHALLOW_AUDIT_STATUS, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchByType(Integer... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.TYPE, values);
    }

    /**
     * Fetch records that have <code>submit_time BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauNativeArchivePo> fetchRangeOfSubmitTime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauNativeArchive.LAU_NATIVE_ARCHIVE.SUBMIT_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>submit_time IN (values)</code>
     */
    public List<LauNativeArchivePo> fetchBySubmitTime(Timestamp... values) {
        return fetch(TLauNativeArchive.LAU_NATIVE_ARCHIVE.SUBMIT_TIME, values);
    }
}
