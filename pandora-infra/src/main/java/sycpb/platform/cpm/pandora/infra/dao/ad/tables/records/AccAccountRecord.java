/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Date;
import java.sql.Timestamp;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TAccAccount;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.AccAccountPo;


/**
 * s_support_pickup
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AccAccountRecord extends UpdatableRecordImpl<AccAccountRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>acc_account.account_id</code>. 账号ID
     */
    public void setAccountId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>acc_account.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>acc_account.username</code>. 用户名
     */
    public void setUsername(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>acc_account.username</code>. 用户名
     */
    public String getUsername() {
        return (String) get(1);
    }

    /**
     * Setter for <code>acc_account.mobile</code>. 手机号码
     */
    public void setMobile(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>acc_account.mobile</code>. 手机号码
     */
    public String getMobile() {
        return (String) get(2);
    }

    /**
     * Setter for <code>acc_account.password_strength</code>. 密码强度-已废弃
     */
    public void setPasswordStrength(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>acc_account.password_strength</code>. 密码强度-已废弃
     */
    public Integer getPasswordStrength() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>acc_account.salt</code>. 盐-已废弃
     */
    public void setSalt(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>acc_account.salt</code>. 盐-已废弃
     */
    public String getSalt() {
        return (String) get(4);
    }

    /**
     * Setter for <code>acc_account.salt_password</code>. 加盐密码-已废弃
     */
    public void setSaltPassword(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>acc_account.salt_password</code>. 加盐密码-已废弃
     */
    public String getSaltPassword() {
        return (String) get(5);
    }

    /**
     * Setter for <code>acc_account.status</code>. 状态:   -1-未开通 0-启用 1-冻结 2-余额不足
     */
    public void setStatus(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>acc_account.status</code>. 状态:   -1-未开通 0-启用 1-冻结 2-余额不足
     */
    public Integer getStatus() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>acc_account.crm_customer_id</code>. crm customer主键-已废弃
     */
    public void setCrmCustomerId(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>acc_account.crm_customer_id</code>. crm customer主键-已废弃
     */
    public Integer getCrmCustomerId() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>acc_account.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>acc_account.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(8);
    }

    /**
     * Setter for <code>acc_account.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(9, value);
    }

    /**
     * Getter for <code>acc_account.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(9);
    }

    /**
     * Setter for <code>acc_account.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>acc_account.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>acc_account.order_type</code>. 账户订单类型(0预付款 1后付款
     * -1未开通)-已废弃
     */
    public void setOrderType(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>acc_account.order_type</code>. 账户订单类型(0预付款 1后付款
     * -1未开通)-已废弃
     */
    public Integer getOrderType() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>acc_account.mid</code>. 主站账号id
     */
    public void setMid(Long value) {
        set(12, value);
    }

    /**
     * Getter for <code>acc_account.mid</code>. 主站账号id
     */
    public Long getMid() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>acc_account.account_type</code>. 0客户（空绑定账号） 1主站账号
     */
    public void setAccountType(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>acc_account.account_type</code>. 0客户（空绑定账号） 1主站账号
     */
    public Integer getAccountType() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>acc_account.active_time</code>. 激活时间（默认为空）
     */
    public void setActiveTime(Timestamp value) {
        set(14, value);
    }

    /**
     * Getter for <code>acc_account.active_time</code>. 激活时间（默认为空）
     */
    public Timestamp getActiveTime() {
        return (Timestamp) get(14);
    }

    /**
     * Setter for <code>acc_account.name</code>. 真实姓名（企业用户为公司名）
     */
    public void setName(String value) {
        set(15, value);
    }

    /**
     * Getter for <code>acc_account.name</code>. 真实姓名（企业用户为公司名）
     */
    public String getName() {
        return (String) get(15);
    }

    /**
     * Setter for <code>acc_account.icp_record_number</code>. icp备案号
     */
    public void setIcpRecordNumber(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>acc_account.icp_record_number</code>. icp备案号
     */
    public String getIcpRecordNumber() {
        return (String) get(16);
    }

    /**
     * Setter for <code>acc_account.icp_info_image</code>. icp截图url(废弃)
     */
    public void setIcpInfoImage(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>acc_account.icp_info_image</code>. icp截图url(废弃)
     */
    public String getIcpInfoImage() {
        return (String) get(17);
    }

    /**
     * Setter for <code>acc_account.brand_domain</code>. 推广域名
     */
    public void setBrandDomain(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>acc_account.brand_domain</code>. 推广域名
     */
    public String getBrandDomain() {
        return (String) get(18);
    }

    /**
     * Setter for <code>acc_account.user_type</code>. 用户属性 0个人用户 1机构用户 2个人起飞用户
     */
    public void setUserType(Integer value) {
        set(19, value);
    }

    /**
     * Getter for <code>acc_account.user_type</code>. 用户属性 0个人用户 1机构用户 2个人起飞用户
     */
    public Integer getUserType() {
        return (Integer) get(19);
    }

    /**
     * Setter for <code>acc_account.ad_status</code>. 是否允许投放效果广告，广告系统状态（-1未激活
     * 0允许 1禁止）
     */
    public void setAdStatus(Integer value) {
        set(20, value);
    }

    /**
     * Getter for <code>acc_account.ad_status</code>. 是否允许投放效果广告，广告系统状态（-1未激活
     * 0允许 1禁止）
     */
    public Integer getAdStatus() {
        return (Integer) get(20);
    }

    /**
     * Setter for <code>acc_account.version</code>. 版本号(mvcc)
     */
    public void setVersion(Integer value) {
        set(21, value);
    }

    /**
     * Getter for <code>acc_account.version</code>. 版本号(mvcc)
     */
    public Integer getVersion() {
        return (Integer) get(21);
    }

    /**
     * Setter for <code>acc_account.category_first_id</code>. 行业一级分类id
     */
    public void setCategoryFirstId(Integer value) {
        set(22, value);
    }

    /**
     * Getter for <code>acc_account.category_first_id</code>. 行业一级分类id
     */
    public Integer getCategoryFirstId() {
        return (Integer) get(22);
    }

    /**
     * Setter for <code>acc_account.category_second_id</code>. 行业二级分类id
     */
    public void setCategorySecondId(Integer value) {
        set(23, value);
    }

    /**
     * Getter for <code>acc_account.category_second_id</code>. 行业二级分类id
     */
    public Integer getCategorySecondId() {
        return (Integer) get(23);
    }

    /**
     * Setter for <code>acc_account.remark</code>. 备注
     */
    public void setRemark(String value) {
        set(24, value);
    }

    /**
     * Getter for <code>acc_account.remark</code>. 备注
     */
    public String getRemark() {
        return (String) get(24);
    }

    /**
     * Setter for <code>acc_account.is_agent</code>. 是否是代理商 0否 1是
     */
    public void setIsAgent(Integer value) {
        set(25, value);
    }

    /**
     * Getter for <code>acc_account.is_agent</code>. 是否是代理商 0否 1是
     */
    public Integer getIsAgent() {
        return (Integer) get(25);
    }

    /**
     * Setter for <code>acc_account.agent_type</code>. 代理商类型：0-无 1-品牌代理商 
     * 2-MCN代理商  3-效果代理商
     */
    public void setAgentType(Integer value) {
        set(26, value);
    }

    /**
     * Getter for <code>acc_account.agent_type</code>. 代理商类型：0-无 1-品牌代理商 
     * 2-MCN代理商  3-效果代理商
     */
    public Integer getAgentType() {
        return (Integer) get(26);
    }

    /**
     * Setter for <code>acc_account.business_role_id</code>. 业务角色ID
     */
    public void setBusinessRoleId(Integer value) {
        set(27, value);
    }

    /**
     * Getter for <code>acc_account.business_role_id</code>. 业务角色ID
     */
    public Integer getBusinessRoleId() {
        return (Integer) get(27);
    }

    /**
     * Setter for <code>acc_account.company_name</code>. 公司名称
     */
    public void setCompanyName(String value) {
        set(28, value);
    }

    /**
     * Getter for <code>acc_account.company_name</code>. 公司名称
     */
    public String getCompanyName() {
        return (String) get(28);
    }

    /**
     * Setter for <code>acc_account.area_id</code>. 区域id
     */
    public void setAreaId(Integer value) {
        set(29, value);
    }

    /**
     * Getter for <code>acc_account.area_id</code>. 区域id
     */
    public Integer getAreaId() {
        return (Integer) get(29);
    }

    /**
     * Setter for <code>acc_account.dependency_agent_id</code>. 所属代理商id
     */
    public void setDependencyAgentId(Integer value) {
        set(30, value);
    }

    /**
     * Getter for <code>acc_account.dependency_agent_id</code>. 所属代理商id
     */
    public Integer getDependencyAgentId() {
        return (Integer) get(30);
    }

    /**
     * Setter for <code>acc_account.website_name</code>. 网站名称
     */
    public void setWebsiteName(String value) {
        set(31, value);
    }

    /**
     * Getter for <code>acc_account.website_name</code>. 网站名称
     */
    public String getWebsiteName() {
        return (String) get(31);
    }

    /**
     * Setter for <code>acc_account.weibo</code>. 微博账号
     */
    public void setWeibo(String value) {
        set(32, value);
    }

    /**
     * Getter for <code>acc_account.weibo</code>. 微博账号
     */
    public String getWeibo() {
        return (String) get(32);
    }

    /**
     * Setter for <code>acc_account.internal_linkman</code>. 内部联系人（内网账号）
     */
    public void setInternalLinkman(String value) {
        set(33, value);
    }

    /**
     * Getter for <code>acc_account.internal_linkman</code>. 内部联系人（内网账号）
     */
    public String getInternalLinkman() {
        return (String) get(33);
    }

    /**
     * Setter for <code>acc_account.linkman_address</code>. 联系人地址
     */
    public void setLinkmanAddress(String value) {
        set(34, value);
    }

    /**
     * Getter for <code>acc_account.linkman_address</code>. 联系人地址
     */
    public String getLinkmanAddress() {
        return (String) get(34);
    }

    /**
     * Setter for <code>acc_account.bank</code>. 开户行
     */
    public void setBank(String value) {
        set(35, value);
    }

    /**
     * Getter for <code>acc_account.bank</code>. 开户行
     */
    public String getBank() {
        return (String) get(35);
    }

    /**
     * Setter for <code>acc_account.qualification_id</code>. 主体资质分类id
     */
    public void setQualificationId(Integer value) {
        set(36, value);
    }

    /**
     * Getter for <code>acc_account.qualification_id</code>. 主体资质分类id
     */
    public Integer getQualificationId() {
        return (Integer) get(36);
    }

    /**
     * Setter for <code>acc_account.business_licence_code</code>. 营业执照编码
     */
    public void setBusinessLicenceCode(String value) {
        set(37, value);
    }

    /**
     * Getter for <code>acc_account.business_licence_code</code>. 营业执照编码
     */
    public String getBusinessLicenceCode() {
        return (String) get(37);
    }

    /**
     * Setter for <code>acc_account.business_licence_expire_date</code>.
     * 营业执照到期时间
     */
    public void setBusinessLicenceExpireDate(Date value) {
        set(38, value);
    }

    /**
     * Getter for <code>acc_account.business_licence_expire_date</code>.
     * 营业执照到期时间
     */
    public Date getBusinessLicenceExpireDate() {
        return (Date) get(38);
    }

    /**
     * Setter for <code>acc_account.is_business_licence_indefinite</code>.
     * 营业执照是否长期有效 1是 0 否
     */
    public void setIsBusinessLicenceIndefinite(Integer value) {
        set(39, value);
    }

    /**
     * Getter for <code>acc_account.is_business_licence_indefinite</code>.
     * 营业执照是否长期有效 1是 0 否
     */
    public Integer getIsBusinessLicenceIndefinite() {
        return (Integer) get(39);
    }

    /**
     * Setter for <code>acc_account.legal_person_name</code>. 法人姓名
     */
    public void setLegalPersonName(String value) {
        set(40, value);
    }

    /**
     * Getter for <code>acc_account.legal_person_name</code>. 法人姓名
     */
    public String getLegalPersonName() {
        return (String) get(40);
    }

    /**
     * Setter for <code>acc_account.legal_person_idcard_expire_date</code>.
     * 企业法人身份证过期时间
     */
    public void setLegalPersonIdcardExpireDate(Date value) {
        set(41, value);
    }

    /**
     * Getter for <code>acc_account.legal_person_idcard_expire_date</code>.
     * 企业法人身份证过期时间
     */
    public Date getLegalPersonIdcardExpireDate() {
        return (Date) get(41);
    }

    /**
     * Setter for <code>acc_account.is_legal_person_idcard_indefinite</code>.
     * 法人身份证是否长期有效 1是 0 否
     */
    public void setIsLegalPersonIdcardIndefinite(Integer value) {
        set(42, value);
    }

    /**
     * Getter for <code>acc_account.is_legal_person_idcard_indefinite</code>.
     * 法人身份证是否长期有效 1是 0 否
     */
    public Integer getIsLegalPersonIdcardIndefinite() {
        return (Integer) get(42);
    }

    /**
     * Setter for <code>acc_account.audit_remark</code>. 审核备注

     */
    public void setAuditRemark(String value) {
        set(43, value);
    }

    /**
     * Getter for <code>acc_account.audit_remark</code>. 审核备注

     */
    public String getAuditRemark() {
        return (String) get(43);
    }

    /**
     * Setter for <code>acc_account.account_status</code>. 0-启用 1-冻结 3编辑中 4待审核
     * 5驳回
     * 
     * 
     */
    public void setAccountStatus(Integer value) {
        set(44, value);
    }

    /**
     * Getter for <code>acc_account.account_status</code>. 0-启用 1-冻结 3编辑中 4待审核
     * 5驳回
     * 
     * 
     */
    public Integer getAccountStatus() {
        return (Integer) get(44);
    }

    /**
     * Setter for <code>acc_account.linkman_email</code>. 联系人邮箱
     */
    public void setLinkmanEmail(String value) {
        set(45, value);
    }

    /**
     * Getter for <code>acc_account.linkman_email</code>. 联系人邮箱
     */
    public String getLinkmanEmail() {
        return (String) get(45);
    }

    /**
     * Setter for <code>acc_account.gd_status</code>. GD广告系统状态（0允许 1禁止）
     */
    public void setGdStatus(Integer value) {
        set(46, value);
    }

    /**
     * Getter for <code>acc_account.gd_status</code>. GD广告系统状态（0允许 1禁止）
     */
    public Integer getGdStatus() {
        return (Integer) get(46);
    }

    /**
     * Setter for <code>acc_account.agent_auth_expire_date</code>. 代理商授权有效期

     */
    public void setAgentAuthExpireDate(Date value) {
        set(47, value);
    }

    /**
     * Getter for <code>acc_account.agent_auth_expire_date</code>. 代理商授权有效期

     */
    public Date getAgentAuthExpireDate() {
        return (Date) get(47);
    }

    /**
     * Setter for <code>acc_account.is_inner</code>. 是否是内部账号 0否 1是
     */
    public void setIsInner(Integer value) {
        set(48, value);
    }

    /**
     * Getter for <code>acc_account.is_inner</code>. 是否是内部账号 0否 1是
     */
    public Integer getIsInner() {
        return (Integer) get(48);
    }

    /**
     * Setter for <code>acc_account.department_id</code>. 部门 id 
     */
    public void setDepartmentId(Integer value) {
        set(49, value);
    }

    /**
     * Getter for <code>acc_account.department_id</code>. 部门 id 
     */
    public Integer getDepartmentId() {
        return (Integer) get(49);
    }

    /**
     * Setter for <code>acc_account.is_support_seller</code>. 是否支持商家中心投放 0-否 1-是
     */
    public void setIsSupportSeller(Integer value) {
        set(50, value);
    }

    /**
     * Getter for <code>acc_account.is_support_seller</code>. 是否支持商家中心投放 0-否 1-是
     */
    public Integer getIsSupportSeller() {
        return (Integer) get(50);
    }

    /**
     * Setter for <code>acc_account.is_support_game</code>. 是否支持游戏中心投放 0-否 1-是
     */
    public void setIsSupportGame(Integer value) {
        set(51, value);
    }

    /**
     * Getter for <code>acc_account.is_support_game</code>. 是否支持游戏中心投放 0-否 1-是
     */
    public Integer getIsSupportGame() {
        return (Integer) get(51);
    }

    /**
     * Setter for <code>acc_account.is_support_dpa</code>. 是否支持DPA投放 0-否 1-是
     */
    public void setIsSupportDpa(Integer value) {
        set(52, value);
    }

    /**
     * Getter for <code>acc_account.is_support_dpa</code>. 是否支持DPA投放 0-否 1-是
     */
    public Integer getIsSupportDpa() {
        return (Integer) get(52);
    }

    /**
     * Setter for <code>acc_account.is_support_content</code>. 是否支持内容推广 0-否 1-是
     */
    public void setIsSupportContent(Integer value) {
        set(53, value);
    }

    /**
     * Getter for <code>acc_account.is_support_content</code>. 是否支持内容推广 0-否 1-是
     */
    public Integer getIsSupportContent() {
        return (Integer) get(53);
    }

    /**
     * Setter for <code>acc_account.product_line</code>. 产品线名
     */
    public void setProductLine(String value) {
        set(54, value);
    }

    /**
     * Getter for <code>acc_account.product_line</code>. 产品线名
     */
    public String getProductLine() {
        return (String) get(54);
    }

    /**
     * Setter for <code>acc_account.phone_number</code>. 联系人手机号
     */
    public void setPhoneNumber(String value) {
        set(55, value);
    }

    /**
     * Getter for <code>acc_account.phone_number</code>. 联系人手机号
     */
    public String getPhoneNumber() {
        return (String) get(55);
    }

    /**
     * Setter for <code>acc_account.idcard_type</code>. 证件类型 0-未知 1-身份证（大陆地区）
     * 2-护照（港澳台及海外）
     */
    public void setIdcardType(Integer value) {
        set(56, value);
    }

    /**
     * Getter for <code>acc_account.idcard_type</code>. 证件类型 0-未知 1-身份证（大陆地区）
     * 2-护照（港澳台及海外）
     */
    public Integer getIdcardType() {
        return (Integer) get(56);
    }

    /**
     * Setter for <code>acc_account.idcard_number</code>. 证件号码
     */
    public void setIdcardNumber(String value) {
        set(57, value);
    }

    /**
     * Getter for <code>acc_account.idcard_number</code>. 证件号码
     */
    public String getIdcardNumber() {
        return (String) get(57);
    }

    /**
     * Setter for <code>acc_account.idcard_expire_date</code>. 证件到期时间
     */
    public void setIdcardExpireDate(Timestamp value) {
        set(58, value);
    }

    /**
     * Getter for <code>acc_account.idcard_expire_date</code>. 证件到期时间
     */
    public Timestamp getIdcardExpireDate() {
        return (Timestamp) get(58);
    }

    /**
     * Setter for <code>acc_account.personal_address</code>. 联系人地址（个人账户）
     */
    public void setPersonalAddress(String value) {
        set(59, value);
    }

    /**
     * Getter for <code>acc_account.personal_address</code>. 联系人地址（个人账户）
     */
    public String getPersonalAddress() {
        return (String) get(59);
    }

    /**
     * Setter for <code>acc_account.is_idcard_indefinite</code>. 证件有效期是否长期有效 1是
     * 0 否
     */
    public void setIsIdcardIndefinite(Integer value) {
        set(60, value);
    }

    /**
     * Getter for <code>acc_account.is_idcard_indefinite</code>. 证件有效期是否长期有效 1是
     * 0 否
     */
    public Integer getIsIdcardIndefinite() {
        return (Integer) get(60);
    }

    /**
     * Setter for <code>acc_account.personal_name</code>. 个人姓名
     */
    public void setPersonalName(String value) {
        set(61, value);
    }

    /**
     * Getter for <code>acc_account.personal_name</code>. 个人姓名
     */
    public String getPersonalName() {
        return (String) get(61);
    }

    /**
     * Setter for <code>acc_account.group_id</code>.
     * 集团id（对应acc_company_group的ID）
     */
    public void setGroupId(Integer value) {
        set(62, value);
    }

    /**
     * Getter for <code>acc_account.group_id</code>.
     * 集团id（对应acc_company_group的ID）
     */
    public Integer getGroupId() {
        return (Integer) get(62);
    }

    /**
     * Setter for <code>acc_account.product_line_id</code>. 产品线id
     */
    public void setProductLineId(Integer value) {
        set(63, value);
    }

    /**
     * Getter for <code>acc_account.product_line_id</code>. 产品线id
     */
    public Integer getProductLineId() {
        return (Integer) get(63);
    }

    /**
     * Setter for <code>acc_account.product_id</code>. 产品id
     */
    public void setProductId(Integer value) {
        set(64, value);
    }

    /**
     * Getter for <code>acc_account.product_id</code>. 产品id
     */
    public Integer getProductId() {
        return (Integer) get(64);
    }

    /**
     * Setter for <code>acc_account.is_support_pickup</code>. 是否支持UP主商单投放 0-否
     * 1-是
     */
    public void setIsSupportPickup(Integer value) {
        set(65, value);
    }

    /**
     * Getter for <code>acc_account.is_support_pickup</code>. 是否支持UP主商单投放 0-否
     * 1-是
     */
    public Integer getIsSupportPickup() {
        return (Integer) get(65);
    }

    /**
     * Setter for <code>acc_account.is_support_mas</code>. 是否支持互选广告投放 0-否 1-是
     */
    public void setIsSupportMas(Integer value) {
        set(66, value);
    }

    /**
     * Getter for <code>acc_account.is_support_mas</code>. 是否支持互选广告投放 0-否 1-是
     */
    public Integer getIsSupportMas() {
        return (Integer) get(66);
    }

    /**
     * Setter for <code>acc_account.auto_update_label</code>. 是否自动更新帐号标签 0-否 1-是
     */
    public void setAutoUpdateLabel(Integer value) {
        set(67, value);
    }

    /**
     * Getter for <code>acc_account.auto_update_label</code>. 是否自动更新帐号标签 0-否 1-是
     */
    public Integer getAutoUpdateLabel() {
        return (Integer) get(67);
    }

    /**
     * Setter for <code>acc_account.is_support_fly</code>. 是否支持商业起飞投放 0-否 1-是
     */
    public void setIsSupportFly(Integer value) {
        set(68, value);
    }

    /**
     * Getter for <code>acc_account.is_support_fly</code>. 是否支持商业起飞投放 0-否 1-是
     */
    public Integer getIsSupportFly() {
        return (Integer) get(68);
    }

    /**
     * Setter for <code>acc_account.allow_cash_pay</code>. 是否允许现金支付（个人起飞专用）
     * 0-不允许 1-允许
     */
    public void setAllowCashPay(Integer value) {
        set(69, value);
    }

    /**
     * Getter for <code>acc_account.allow_cash_pay</code>. 是否允许现金支付（个人起飞专用）
     * 0-不允许 1-允许
     */
    public Integer getAllowCashPay() {
        return (Integer) get(69);
    }

    /**
     * Setter for <code>acc_account.allow_incentive_bonus_pay</code>.
     * 是否允许激励金支付（个人起飞专用） 0-不允许 1-允许
     */
    public void setAllowIncentiveBonusPay(Integer value) {
        set(70, value);
    }

    /**
     * Getter for <code>acc_account.allow_incentive_bonus_pay</code>.
     * 是否允许激励金支付（个人起飞专用） 0-不允许 1-允许
     */
    public Integer getAllowIncentiveBonusPay() {
        return (Integer) get(70);
    }

    /**
     * Setter for <code>acc_account.customer_id</code>. 所属客户id
     */
    public void setCustomerId(Integer value) {
        set(71, value);
    }

    /**
     * Getter for <code>acc_account.customer_id</code>. 所属客户id
     */
    public Integer getCustomerId() {
        return (Integer) get(71);
    }

    /**
     * Setter for <code>acc_account.creator</code>. 创建人
     */
    public void setCreator(String value) {
        set(72, value);
    }

    /**
     * Getter for <code>acc_account.creator</code>. 创建人
     */
    public String getCreator() {
        return (String) get(72);
    }

    /**
     * Setter for <code>acc_account.payment_period</code>. 账期
     */
    public void setPaymentPeriod(Integer value) {
        set(73, value);
    }

    /**
     * Getter for <code>acc_account.payment_period</code>. 账期
     */
    public Integer getPaymentPeriod() {
        return (Integer) get(73);
    }

    /**
     * Setter for <code>acc_account.is_support_local_ad</code>. 是否支持本地广告产品 0-否
     * 1-是
     */
    public void setIsSupportLocalAd(Integer value) {
        set(74, value);
    }

    /**
     * Getter for <code>acc_account.is_support_local_ad</code>. 是否支持本地广告产品 0-否
     * 1-是
     */
    public Integer getIsSupportLocalAd() {
        return (Integer) get(74);
    }

    /**
     * Setter for <code>acc_account.first_industry_tag_id</code>. 一级行业标签
     */
    public void setFirstIndustryTagId(Integer value) {
        set(75, value);
    }

    /**
     * Getter for <code>acc_account.first_industry_tag_id</code>. 一级行业标签
     */
    public Integer getFirstIndustryTagId() {
        return (Integer) get(75);
    }

    /**
     * Setter for <code>acc_account.second_industry_tag_id</code>. 二级行业标签
     */
    public void setSecondIndustryTagId(Integer value) {
        set(76, value);
    }

    /**
     * Getter for <code>acc_account.second_industry_tag_id</code>. 二级行业标签
     */
    public Integer getSecondIndustryTagId() {
        return (Integer) get(76);
    }

    /**
     * Setter for <code>acc_account.third_industry_tag_id</code>. 三级行业标签
     */
    public void setThirdIndustryTagId(Integer value) {
        set(77, value);
    }

    /**
     * Getter for <code>acc_account.third_industry_tag_id</code>. 三级行业标签
     */
    public Integer getThirdIndustryTagId() {
        return (Integer) get(77);
    }

    /**
     * Setter for <code>acc_account.commerce_category_first_id</code>.
     * 商业行业一级分类id(新版)
     */
    public void setCommerceCategoryFirstId(Integer value) {
        set(78, value);
    }

    /**
     * Getter for <code>acc_account.commerce_category_first_id</code>.
     * 商业行业一级分类id(新版)
     */
    public Integer getCommerceCategoryFirstId() {
        return (Integer) get(78);
    }

    /**
     * Setter for <code>acc_account.commerce_category_second_id</code>.
     * 商业行业二级分类id（新版）
     */
    public void setCommerceCategorySecondId(Integer value) {
        set(79, value);
    }

    /**
     * Getter for <code>acc_account.commerce_category_second_id</code>.
     * 商业行业二级分类id（新版）
     */
    public Integer getCommerceCategorySecondId() {
        return (Integer) get(79);
    }

    /**
     * Setter for <code>acc_account.allow_signing_bonus_pay</code>.
     * 是否允许签约金支付（个人起飞专用） 0-不允许 1-允许
     */
    public void setAllowSigningBonusPay(Integer value) {
        set(80, value);
    }

    /**
     * Getter for <code>acc_account.allow_signing_bonus_pay</code>.
     * 是否允许签约金支付（个人起飞专用） 0-不允许 1-允许
     */
    public Integer getAllowSigningBonusPay() {
        return (Integer) get(80);
    }

    /**
     * Setter for <code>acc_account.allow_fly_coin_pay</code>. 是否支持起飞币支付 0-否 1-是
     */
    public void setAllowFlyCoinPay(Integer value) {
        set(81, value);
    }

    /**
     * Getter for <code>acc_account.allow_fly_coin_pay</code>. 是否支持起飞币支付 0-否 1-是
     */
    public Integer getAllowFlyCoinPay() {
        return (Integer) get(81);
    }

    /**
     * Setter for <code>acc_account.allow_flow_ticket_pay</code>. 是否支持流量券支付 0-否
     * 1-是
     */
    public void setAllowFlowTicketPay(Integer value) {
        set(82, value);
    }

    /**
     * Getter for <code>acc_account.allow_flow_ticket_pay</code>. 是否支持流量券支付 0-否
     * 1-是
     */
    public Integer getAllowFlowTicketPay() {
        return (Integer) get(82);
    }

    /**
     * Setter for <code>acc_account.finance_type</code>. 财务类型：0-无 1-现金账户 2-虚拟金账户
     */
    public void setFinanceType(Integer value) {
        set(83, value);
    }

    /**
     * Getter for <code>acc_account.finance_type</code>. 财务类型：0-无 1-现金账户 2-虚拟金账户
     */
    public Integer getFinanceType() {
        return (Integer) get(83);
    }

    /**
     * Setter for <code>acc_account.has_mgk_form</code>. 是否含有落地页表单 0-不包含 1-包含
     */
    public void setHasMgkForm(Integer value) {
        set(84, value);
    }

    /**
     * Getter for <code>acc_account.has_mgk_form</code>. 是否含有落地页表单 0-不包含 1-包含
     */
    public Integer getHasMgkForm() {
        return (Integer) get(84);
    }

    /**
     * Setter for <code>acc_account.mgk_form_privacy_policy</code>. 建站隐私政策是否同意
     * 0-未设置 1-同意 2 不同意
     */
    public void setMgkFormPrivacyPolicy(Integer value) {
        set(85, value);
    }

    /**
     * Getter for <code>acc_account.mgk_form_privacy_policy</code>. 建站隐私政策是否同意
     * 0-未设置 1-同意 2 不同意
     */
    public Integer getMgkFormPrivacyPolicy() {
        return (Integer) get(85);
    }

    /**
     * Setter for <code>acc_account.show_to_customer</code>. 0:对客户可见 1:对客户不可见
     */
    public void setShowToCustomer(Integer value) {
        set(86, value);
    }

    /**
     * Getter for <code>acc_account.show_to_customer</code>. 0:对客户可见 1:对客户不可见
     */
    public Integer getShowToCustomer() {
        return (Integer) get(86);
    }

    /**
     * Setter for <code>acc_account.is_support_clue_pass</code>. 是否支持线索通 0-否 1-是
     */
    public void setIsSupportCluePass(Integer value) {
        set(87, value);
    }

    /**
     * Getter for <code>acc_account.is_support_clue_pass</code>. 是否支持线索通 0-否 1-是
     */
    public Integer getIsSupportCluePass() {
        return (Integer) get(87);
    }

    /**
     * Setter for <code>acc_account.promotion_type</code>. 花火开户推广类型 0-无 1-网站
     * 2-网上店铺 3-线下实体店
     */
    public void setPromotionType(Integer value) {
        set(88, value);
    }

    /**
     * Getter for <code>acc_account.promotion_type</code>. 花火开户推广类型 0-无 1-网站
     * 2-网上店铺 3-线下实体店
     */
    public Integer getPromotionType() {
        return (Integer) get(88);
    }

    /**
     * Setter for <code>acc_account.united_first_industry_id</code>. 统一一级行业标签
     */
    public void setUnitedFirstIndustryId(Integer value) {
        set(89, value);
    }

    /**
     * Getter for <code>acc_account.united_first_industry_id</code>. 统一一级行业标签
     */
    public Integer getUnitedFirstIndustryId() {
        return (Integer) get(89);
    }

    /**
     * Setter for <code>acc_account.united_second_industry_id</code>. 统一一级行业标签
     */
    public void setUnitedSecondIndustryId(Integer value) {
        set(90, value);
    }

    /**
     * Getter for <code>acc_account.united_second_industry_id</code>. 统一一级行业标签
     */
    public Integer getUnitedSecondIndustryId() {
        return (Integer) get(90);
    }

    /**
     * Setter for <code>acc_account.united_third_industry_id</code>. 统一一级行业标签
     */
    public void setUnitedThirdIndustryId(Integer value) {
        set(91, value);
    }

    /**
     * Getter for <code>acc_account.united_third_industry_id</code>. 统一一级行业标签
     */
    public Integer getUnitedThirdIndustryId() {
        return (Integer) get(91);
    }

    /**
     * Setter for <code>acc_account.old_agent_id</code>. 历史代理商id
     */
    public void setOldAgentId(Integer value) {
        set(92, value);
    }

    /**
     * Getter for <code>acc_account.old_agent_id</code>. 历史代理商id
     */
    public Integer getOldAgentId() {
        return (Integer) get(92);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AccAccountRecord
     */
    public AccAccountRecord() {
        super(TAccAccount.ACC_ACCOUNT);
    }

    /**
     * Create a detached, initialised AccAccountRecord
     */
    public AccAccountRecord(Integer accountId, String username, String mobile, Integer passwordStrength, String salt, String saltPassword, Integer status, Integer crmCustomerId, Timestamp ctime, Timestamp mtime, Integer isDeleted, Integer orderType, Long mid, Integer accountType, Timestamp activeTime, String name, String icpRecordNumber, String icpInfoImage, String brandDomain, Integer userType, Integer adStatus, Integer version, Integer categoryFirstId, Integer categorySecondId, String remark, Integer isAgent, Integer agentType, Integer businessRoleId, String companyName, Integer areaId, Integer dependencyAgentId, String websiteName, String weibo, String internalLinkman, String linkmanAddress, String bank, Integer qualificationId, String businessLicenceCode, Date businessLicenceExpireDate, Integer isBusinessLicenceIndefinite, String legalPersonName, Date legalPersonIdcardExpireDate, Integer isLegalPersonIdcardIndefinite, String auditRemark, Integer accountStatus, String linkmanEmail, Integer gdStatus, Date agentAuthExpireDate, Integer isInner, Integer departmentId, Integer isSupportSeller, Integer isSupportGame, Integer isSupportDpa, Integer isSupportContent, String productLine, String phoneNumber, Integer idcardType, String idcardNumber, Timestamp idcardExpireDate, String personalAddress, Integer isIdcardIndefinite, String personalName, Integer groupId, Integer productLineId, Integer productId, Integer isSupportPickup, Integer isSupportMas, Integer autoUpdateLabel, Integer isSupportFly, Integer allowCashPay, Integer allowIncentiveBonusPay, Integer customerId, String creator, Integer paymentPeriod, Integer isSupportLocalAd, Integer firstIndustryTagId, Integer secondIndustryTagId, Integer thirdIndustryTagId, Integer commerceCategoryFirstId, Integer commerceCategorySecondId, Integer allowSigningBonusPay, Integer allowFlyCoinPay, Integer allowFlowTicketPay, Integer financeType, Integer hasMgkForm, Integer mgkFormPrivacyPolicy, Integer showToCustomer, Integer isSupportCluePass, Integer promotionType, Integer unitedFirstIndustryId, Integer unitedSecondIndustryId, Integer unitedThirdIndustryId, Integer oldAgentId) {
        super(TAccAccount.ACC_ACCOUNT);

        setAccountId(accountId);
        setUsername(username);
        setMobile(mobile);
        setPasswordStrength(passwordStrength);
        setSalt(salt);
        setSaltPassword(saltPassword);
        setStatus(status);
        setCrmCustomerId(crmCustomerId);
        setCtime(ctime);
        setMtime(mtime);
        setIsDeleted(isDeleted);
        setOrderType(orderType);
        setMid(mid);
        setAccountType(accountType);
        setActiveTime(activeTime);
        setName(name);
        setIcpRecordNumber(icpRecordNumber);
        setIcpInfoImage(icpInfoImage);
        setBrandDomain(brandDomain);
        setUserType(userType);
        setAdStatus(adStatus);
        setVersion(version);
        setCategoryFirstId(categoryFirstId);
        setCategorySecondId(categorySecondId);
        setRemark(remark);
        setIsAgent(isAgent);
        setAgentType(agentType);
        setBusinessRoleId(businessRoleId);
        setCompanyName(companyName);
        setAreaId(areaId);
        setDependencyAgentId(dependencyAgentId);
        setWebsiteName(websiteName);
        setWeibo(weibo);
        setInternalLinkman(internalLinkman);
        setLinkmanAddress(linkmanAddress);
        setBank(bank);
        setQualificationId(qualificationId);
        setBusinessLicenceCode(businessLicenceCode);
        setBusinessLicenceExpireDate(businessLicenceExpireDate);
        setIsBusinessLicenceIndefinite(isBusinessLicenceIndefinite);
        setLegalPersonName(legalPersonName);
        setLegalPersonIdcardExpireDate(legalPersonIdcardExpireDate);
        setIsLegalPersonIdcardIndefinite(isLegalPersonIdcardIndefinite);
        setAuditRemark(auditRemark);
        setAccountStatus(accountStatus);
        setLinkmanEmail(linkmanEmail);
        setGdStatus(gdStatus);
        setAgentAuthExpireDate(agentAuthExpireDate);
        setIsInner(isInner);
        setDepartmentId(departmentId);
        setIsSupportSeller(isSupportSeller);
        setIsSupportGame(isSupportGame);
        setIsSupportDpa(isSupportDpa);
        setIsSupportContent(isSupportContent);
        setProductLine(productLine);
        setPhoneNumber(phoneNumber);
        setIdcardType(idcardType);
        setIdcardNumber(idcardNumber);
        setIdcardExpireDate(idcardExpireDate);
        setPersonalAddress(personalAddress);
        setIsIdcardIndefinite(isIdcardIndefinite);
        setPersonalName(personalName);
        setGroupId(groupId);
        setProductLineId(productLineId);
        setProductId(productId);
        setIsSupportPickup(isSupportPickup);
        setIsSupportMas(isSupportMas);
        setAutoUpdateLabel(autoUpdateLabel);
        setIsSupportFly(isSupportFly);
        setAllowCashPay(allowCashPay);
        setAllowIncentiveBonusPay(allowIncentiveBonusPay);
        setCustomerId(customerId);
        setCreator(creator);
        setPaymentPeriod(paymentPeriod);
        setIsSupportLocalAd(isSupportLocalAd);
        setFirstIndustryTagId(firstIndustryTagId);
        setSecondIndustryTagId(secondIndustryTagId);
        setThirdIndustryTagId(thirdIndustryTagId);
        setCommerceCategoryFirstId(commerceCategoryFirstId);
        setCommerceCategorySecondId(commerceCategorySecondId);
        setAllowSigningBonusPay(allowSigningBonusPay);
        setAllowFlyCoinPay(allowFlyCoinPay);
        setAllowFlowTicketPay(allowFlowTicketPay);
        setFinanceType(financeType);
        setHasMgkForm(hasMgkForm);
        setMgkFormPrivacyPolicy(mgkFormPrivacyPolicy);
        setShowToCustomer(showToCustomer);
        setIsSupportCluePass(isSupportCluePass);
        setPromotionType(promotionType);
        setUnitedFirstIndustryId(unitedFirstIndustryId);
        setUnitedSecondIndustryId(unitedSecondIndustryId);
        setUnitedThirdIndustryId(unitedThirdIndustryId);
        setOldAgentId(oldAgentId);
    }

    /**
     * Create a detached, initialised AccAccountRecord
     */
    public AccAccountRecord(AccAccountPo value) {
        super(TAccAccount.ACC_ACCOUNT);

        if (value != null) {
            setAccountId(value.getAccountId());
            setUsername(value.getUsername());
            setMobile(value.getMobile());
            setPasswordStrength(value.getPasswordStrength());
            setSalt(value.getSalt());
            setSaltPassword(value.getSaltPassword());
            setStatus(value.getStatus());
            setCrmCustomerId(value.getCrmCustomerId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setIsDeleted(value.getIsDeleted());
            setOrderType(value.getOrderType());
            setMid(value.getMid());
            setAccountType(value.getAccountType());
            setActiveTime(value.getActiveTime());
            setName(value.getName());
            setIcpRecordNumber(value.getIcpRecordNumber());
            setIcpInfoImage(value.getIcpInfoImage());
            setBrandDomain(value.getBrandDomain());
            setUserType(value.getUserType());
            setAdStatus(value.getAdStatus());
            setVersion(value.getVersion());
            setCategoryFirstId(value.getCategoryFirstId());
            setCategorySecondId(value.getCategorySecondId());
            setRemark(value.getRemark());
            setIsAgent(value.getIsAgent());
            setAgentType(value.getAgentType());
            setBusinessRoleId(value.getBusinessRoleId());
            setCompanyName(value.getCompanyName());
            setAreaId(value.getAreaId());
            setDependencyAgentId(value.getDependencyAgentId());
            setWebsiteName(value.getWebsiteName());
            setWeibo(value.getWeibo());
            setInternalLinkman(value.getInternalLinkman());
            setLinkmanAddress(value.getLinkmanAddress());
            setBank(value.getBank());
            setQualificationId(value.getQualificationId());
            setBusinessLicenceCode(value.getBusinessLicenceCode());
            setBusinessLicenceExpireDate(value.getBusinessLicenceExpireDate());
            setIsBusinessLicenceIndefinite(value.getIsBusinessLicenceIndefinite());
            setLegalPersonName(value.getLegalPersonName());
            setLegalPersonIdcardExpireDate(value.getLegalPersonIdcardExpireDate());
            setIsLegalPersonIdcardIndefinite(value.getIsLegalPersonIdcardIndefinite());
            setAuditRemark(value.getAuditRemark());
            setAccountStatus(value.getAccountStatus());
            setLinkmanEmail(value.getLinkmanEmail());
            setGdStatus(value.getGdStatus());
            setAgentAuthExpireDate(value.getAgentAuthExpireDate());
            setIsInner(value.getIsInner());
            setDepartmentId(value.getDepartmentId());
            setIsSupportSeller(value.getIsSupportSeller());
            setIsSupportGame(value.getIsSupportGame());
            setIsSupportDpa(value.getIsSupportDpa());
            setIsSupportContent(value.getIsSupportContent());
            setProductLine(value.getProductLine());
            setPhoneNumber(value.getPhoneNumber());
            setIdcardType(value.getIdcardType());
            setIdcardNumber(value.getIdcardNumber());
            setIdcardExpireDate(value.getIdcardExpireDate());
            setPersonalAddress(value.getPersonalAddress());
            setIsIdcardIndefinite(value.getIsIdcardIndefinite());
            setPersonalName(value.getPersonalName());
            setGroupId(value.getGroupId());
            setProductLineId(value.getProductLineId());
            setProductId(value.getProductId());
            setIsSupportPickup(value.getIsSupportPickup());
            setIsSupportMas(value.getIsSupportMas());
            setAutoUpdateLabel(value.getAutoUpdateLabel());
            setIsSupportFly(value.getIsSupportFly());
            setAllowCashPay(value.getAllowCashPay());
            setAllowIncentiveBonusPay(value.getAllowIncentiveBonusPay());
            setCustomerId(value.getCustomerId());
            setCreator(value.getCreator());
            setPaymentPeriod(value.getPaymentPeriod());
            setIsSupportLocalAd(value.getIsSupportLocalAd());
            setFirstIndustryTagId(value.getFirstIndustryTagId());
            setSecondIndustryTagId(value.getSecondIndustryTagId());
            setThirdIndustryTagId(value.getThirdIndustryTagId());
            setCommerceCategoryFirstId(value.getCommerceCategoryFirstId());
            setCommerceCategorySecondId(value.getCommerceCategorySecondId());
            setAllowSigningBonusPay(value.getAllowSigningBonusPay());
            setAllowFlyCoinPay(value.getAllowFlyCoinPay());
            setAllowFlowTicketPay(value.getAllowFlowTicketPay());
            setFinanceType(value.getFinanceType());
            setHasMgkForm(value.getHasMgkForm());
            setMgkFormPrivacyPolicy(value.getMgkFormPrivacyPolicy());
            setShowToCustomer(value.getShowToCustomer());
            setIsSupportCluePass(value.getIsSupportCluePass());
            setPromotionType(value.getPromotionType());
            setUnitedFirstIndustryId(value.getUnitedFirstIndustryId());
            setUnitedSecondIndustryId(value.getUnitedSecondIndustryId());
            setUnitedThirdIndustryId(value.getUnitedThirdIndustryId());
            setOldAgentId(value.getOldAgentId());
        }
    }
}
