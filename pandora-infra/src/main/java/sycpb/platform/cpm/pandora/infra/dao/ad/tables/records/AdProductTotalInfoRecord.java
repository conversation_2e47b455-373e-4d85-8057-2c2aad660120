/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Timestamp;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TAdProductTotalInfo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.AdProductTotalInfoPo;


/**
 * 投放产品全部信息
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AdProductTotalInfoRecord extends UpdatableRecordImpl<AdProductTotalInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>ad_product_total_info.ad_product_id</code>. 产品id
     */
    public void setAdProductId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>ad_product_total_info.ad_product_id</code>. 产品id
     */
    public Long getAdProductId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>ad_product_total_info.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(1, value);
    }

    /**
     * Getter for <code>ad_product_total_info.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(1);
    }

    /**
     * Setter for <code>ad_product_total_info.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(2, value);
    }

    /**
     * Getter for <code>ad_product_total_info.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(2);
    }

    /**
     * Setter for <code>ad_product_total_info.biz_status</code>. 在线状态 1-有效 0-无效
     */
    public void setBizStatus(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>ad_product_total_info.biz_status</code>. 在线状态 1-有效 0-无效
     */
    public Integer getBizStatus() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>ad_product_total_info.delete_time</code>. 删除时间
     */
    public void setDeleteTime(Timestamp value) {
        set(4, value);
    }

    /**
     * Getter for <code>ad_product_total_info.delete_time</code>. 删除时间
     */
    public Timestamp getDeleteTime() {
        return (Timestamp) get(4);
    }

    /**
     * Setter for <code>ad_product_total_info.belong_type</code>. 数据来源
     */
    public void setBelongType(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>ad_product_total_info.belong_type</code>. 数据来源
     */
    public Integer getBelongType() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>ad_product_total_info.library_type</code>. 产品库类型
     */
    public void setLibraryType(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>ad_product_total_info.library_type</code>. 产品库类型
     */
    public Integer getLibraryType() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>ad_product_total_info.ad_product_name</code>. 产品名称
     */
    public void setAdProductName(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>ad_product_total_info.ad_product_name</code>. 产品名称
     */
    public String getAdProductName() {
        return (String) get(7);
    }

    /**
     * Setter for <code>ad_product_total_info.first_category_id</code>. 产品一级类目id
     */
    public void setFirstCategoryId(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>ad_product_total_info.first_category_id</code>. 产品一级类目id
     */
    public Long getFirstCategoryId() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>ad_product_total_info.second_category_id</code>.
     * 产品二级类目ID
     */
    public void setSecondCategoryId(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>ad_product_total_info.second_category_id</code>.
     * 产品二级类目ID
     */
    public Long getSecondCategoryId() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>ad_product_total_info.third_category_id</code>. 产品三级类目id
     */
    public void setThirdCategoryId(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>ad_product_total_info.third_category_id</code>. 产品三级类目id
     */
    public Long getThirdCategoryId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>ad_product_total_info.first_category_name</code>.
     * 产品一级类目名称
     */
    public void setFirstCategoryName(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>ad_product_total_info.first_category_name</code>.
     * 产品一级类目名称
     */
    public String getFirstCategoryName() {
        return (String) get(11);
    }

    /**
     * Setter for <code>ad_product_total_info.second_category_name</code>.
     * 产品二级类目名称
     */
    public void setSecondCategoryName(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>ad_product_total_info.second_category_name</code>.
     * 产品二级类目名称
     */
    public String getSecondCategoryName() {
        return (String) get(12);
    }

    /**
     * Setter for <code>ad_product_total_info.ad_original_price</code>. 产品原价
     */
    public void setAdOriginalPrice(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>ad_product_total_info.ad_original_price</code>. 产品原价
     */
    public String getAdOriginalPrice() {
        return (String) get(13);
    }

    /**
     * Setter for <code>ad_product_total_info.ad_main_img_url</code>. 主图URL
     */
    public void setAdMainImgUrl(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>ad_product_total_info.ad_main_img_url</code>. 主图URL
     */
    public String getAdMainImgUrl() {
        return (String) get(14);
    }

    /**
     * Setter for <code>ad_product_total_info.ad_extra_img_url</code>. 额外图片URL
     */
    public void setAdExtraImgUrl(String value) {
        set(15, value);
    }

    /**
     * Getter for <code>ad_product_total_info.ad_extra_img_url</code>. 额外图片URL
     */
    public String getAdExtraImgUrl() {
        return (String) get(15);
    }

    /**
     * Setter for <code>ad_product_total_info.h5_landing_page_url</code>.
     * H5落地页URL
     */
    public void setH5LandingPageUrl(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>ad_product_total_info.h5_landing_page_url</code>.
     * H5落地页URL
     */
    public String getH5LandingPageUrl() {
        return (String) get(16);
    }

    /**
     * Setter for <code>ad_product_total_info.pc_landing_page_url</code>.
     * PC端落地页URL
     */
    public void setPcLandingPageUrl(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>ad_product_total_info.pc_landing_page_url</code>.
     * PC端落地页URL
     */
    public String getPcLandingPageUrl() {
        return (String) get(17);
    }

    /**
     * Setter for <code>ad_product_total_info.ios_landing_page_url</code>.
     * IOS落地页URL
     */
    public void setIosLandingPageUrl(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>ad_product_total_info.ios_landing_page_url</code>.
     * IOS落地页URL
     */
    public String getIosLandingPageUrl() {
        return (String) get(18);
    }

    /**
     * Setter for <code>ad_product_total_info.android_landing_page_url</code>.
     * Android落地页URL
     */
    public void setAndroidLandingPageUrl(String value) {
        set(19, value);
    }

    /**
     * Getter for <code>ad_product_total_info.android_landing_page_url</code>.
     * Android落地页URL
     */
    public String getAndroidLandingPageUrl() {
        return (String) get(19);
    }

    /**
     * Setter for <code>ad_product_total_info.ad_attributes</code>. 产品属性
     */
    public void setAdAttributes(String value) {
        set(20, value);
    }

    /**
     * Getter for <code>ad_product_total_info.ad_attributes</code>. 产品属性
     */
    public String getAdAttributes() {
        return (String) get(20);
    }

    /**
     * Setter for <code>ad_product_total_info.library_id</code>. 产品库id
     */
    public void setLibraryId(Long value) {
        set(21, value);
    }

    /**
     * Getter for <code>ad_product_total_info.library_id</code>. 产品库id
     */
    public Long getLibraryId() {
        return (Long) get(21);
    }

    /**
     * Setter for <code>ad_product_total_info.is_deleted</code>. 是否删除 0-正常 1-已删除
     */
    public void setIsDeleted(Integer value) {
        set(22, value);
    }

    /**
     * Getter for <code>ad_product_total_info.is_deleted</code>. 是否删除 0-正常 1-已删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(22);
    }

    /**
     * Setter for <code>ad_product_total_info.third_category_name</code>.
     * 产品二级类目名称
     */
    public void setThirdCategoryName(String value) {
        set(23, value);
    }

    /**
     * Getter for <code>ad_product_total_info.third_category_name</code>.
     * 产品二级类目名称
     */
    public String getThirdCategoryName() {
        return (String) get(23);
    }

    /**
     * Setter for <code>ad_product_total_info.account_id</code>. 账户id
     */
    public void setAccountId(Integer value) {
        set(24, value);
    }

    /**
     * Getter for <code>ad_product_total_info.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return (Integer) get(24);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AdProductTotalInfoRecord
     */
    public AdProductTotalInfoRecord() {
        super(TAdProductTotalInfo.AD_PRODUCT_TOTAL_INFO);
    }

    /**
     * Create a detached, initialised AdProductTotalInfoRecord
     */
    public AdProductTotalInfoRecord(Long adProductId, Timestamp ctime, Timestamp mtime, Integer bizStatus, Timestamp deleteTime, Integer belongType, Integer libraryType, String adProductName, Long firstCategoryId, Long secondCategoryId, Long thirdCategoryId, String firstCategoryName, String secondCategoryName, String adOriginalPrice, String adMainImgUrl, String adExtraImgUrl, String h5LandingPageUrl, String pcLandingPageUrl, String iosLandingPageUrl, String androidLandingPageUrl, String adAttributes, Long libraryId, Integer isDeleted, String thirdCategoryName, Integer accountId) {
        super(TAdProductTotalInfo.AD_PRODUCT_TOTAL_INFO);

        setAdProductId(adProductId);
        setCtime(ctime);
        setMtime(mtime);
        setBizStatus(bizStatus);
        setDeleteTime(deleteTime);
        setBelongType(belongType);
        setLibraryType(libraryType);
        setAdProductName(adProductName);
        setFirstCategoryId(firstCategoryId);
        setSecondCategoryId(secondCategoryId);
        setThirdCategoryId(thirdCategoryId);
        setFirstCategoryName(firstCategoryName);
        setSecondCategoryName(secondCategoryName);
        setAdOriginalPrice(adOriginalPrice);
        setAdMainImgUrl(adMainImgUrl);
        setAdExtraImgUrl(adExtraImgUrl);
        setH5LandingPageUrl(h5LandingPageUrl);
        setPcLandingPageUrl(pcLandingPageUrl);
        setIosLandingPageUrl(iosLandingPageUrl);
        setAndroidLandingPageUrl(androidLandingPageUrl);
        setAdAttributes(adAttributes);
        setLibraryId(libraryId);
        setIsDeleted(isDeleted);
        setThirdCategoryName(thirdCategoryName);
        setAccountId(accountId);
    }

    /**
     * Create a detached, initialised AdProductTotalInfoRecord
     */
    public AdProductTotalInfoRecord(AdProductTotalInfoPo value) {
        super(TAdProductTotalInfo.AD_PRODUCT_TOTAL_INFO);

        if (value != null) {
            setAdProductId(value.getAdProductId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setBizStatus(value.getBizStatus());
            setDeleteTime(value.getDeleteTime());
            setBelongType(value.getBelongType());
            setLibraryType(value.getLibraryType());
            setAdProductName(value.getAdProductName());
            setFirstCategoryId(value.getFirstCategoryId());
            setSecondCategoryId(value.getSecondCategoryId());
            setThirdCategoryId(value.getThirdCategoryId());
            setFirstCategoryName(value.getFirstCategoryName());
            setSecondCategoryName(value.getSecondCategoryName());
            setAdOriginalPrice(value.getAdOriginalPrice());
            setAdMainImgUrl(value.getAdMainImgUrl());
            setAdExtraImgUrl(value.getAdExtraImgUrl());
            setH5LandingPageUrl(value.getH5LandingPageUrl());
            setPcLandingPageUrl(value.getPcLandingPageUrl());
            setIosLandingPageUrl(value.getIosLandingPageUrl());
            setAndroidLandingPageUrl(value.getAndroidLandingPageUrl());
            setAdAttributes(value.getAdAttributes());
            setLibraryId(value.getLibraryId());
            setIsDeleted(value.getIsDeleted());
            setThirdCategoryName(value.getThirdCategoryName());
            setAccountId(value.getAccountId());
        }
    }
}
