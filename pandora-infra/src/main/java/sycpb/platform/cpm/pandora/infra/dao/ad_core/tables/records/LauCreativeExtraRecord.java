/*
 * This file is generated by jO<PERSON>Q.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record20;
import org.jooq.Row20;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeExtra;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeExtraPo;


/**
 * 创意层级额外信息
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeExtraRecord extends UpdatableRecordImpl<LauCreativeExtraRecord> implements Record20<Long, Integer, Integer, Integer, Integer, Integer, String, Integer, Timestamp, Timestamp, Inte<PERSON>, Inte<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>te<PERSON>, <PERSON>te<PERSON>, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_extra.id</code>. 主键
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_extra.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_creative_extra.creative_id</code>. 创意id
     */
    public void setCreativeId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_creative_extra.creative_id</code>. 创意id
     */
    public Integer getCreativeId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_creative_extra.account_id</code>. 账户id
     */
    public void setAccountId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_creative_extra.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_creative_extra.campaign_id</code>. 计划id
     */
    public void setCampaignId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_creative_extra.campaign_id</code>. 计划id
     */
    public Integer getCampaignId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_creative_extra.unit_id</code>. 单位ID
     */
    public void setUnitId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_creative_extra.unit_id</code>. 单位ID
     */
    public Integer getUnitId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_creative_extra.image_macro_type</code>. 图片宏类型 1-图片
     * 2-gif
     */
    public void setImageMacroType(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_creative_extra.image_macro_type</code>. 图片宏类型 1-图片
     * 2-gif
     */
    public Integer getImageMacroType() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_creative_extra.image_macro</code>. 图片宏占位符
     */
    public void setImageMacro(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_creative_extra.image_macro</code>. 图片宏占位符
     */
    public String getImageMacro() {
        return (String) get(6);
    }

    /**
     * Setter for <code>lau_creative_extra.is_deleted</code>. 软删除 0有效 1删除
     */
    public void setIsDeleted(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_creative_extra.is_deleted</code>. 软删除 0有效 1删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>lau_creative_extra.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_creative_extra.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(8);
    }

    /**
     * Setter for <code>lau_creative_extra.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_creative_extra.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(9);
    }

    /**
     * Setter for <code>lau_creative_extra.qualification_package_id</code>.
     * 资质包id
     */
    public void setQualificationPackageId(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_creative_extra.qualification_package_id</code>.
     * 资质包id
     */
    public Integer getQualificationPackageId() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>lau_creative_extra.ad_space_id</code>. 用户自定义商业空间id
     */
    public void setAdSpaceId(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_creative_extra.ad_space_id</code>. 用户自定义商业空间id
     */
    public Integer getAdSpaceId() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>lau_creative_extra.bili_space_mid</code>. b站空间mid
     */
    public void setBiliSpaceMid(Long value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_creative_extra.bili_space_mid</code>. b站空间mid
     */
    public Long getBiliSpaceMid() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>lau_creative_extra.raw_jump_url</code>.
     * 用户填写的原始落地页(未添加任何宏参数)
     */
    public void setRawJumpUrl(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_creative_extra.raw_jump_url</code>.
     * 用户填写的原始落地页(未添加任何宏参数)
     */
    public String getRawJumpUrl() {
        return (String) get(13);
    }

    /**
     * Setter for <code>lau_creative_extra.mgk_page_id</code>.
     * 建站页id(仅adp_version&gt;=6的数据有值)
     */
    public void setMgkPageId(Long value) {
        set(14, value);
    }

    /**
     * Getter for <code>lau_creative_extra.mgk_page_id</code>.
     * 建站页id(仅adp_version&gt;=6的数据有值)
     */
    public Long getMgkPageId() {
        return (Long) get(14);
    }

    /**
     * Setter for <code>lau_creative_extra.season_avid</code>. 课程avid
     */
    public void setSeasonAvid(Long value) {
        set(15, value);
    }

    /**
     * Getter for <code>lau_creative_extra.season_avid</code>. 课程avid
     */
    public Long getSeasonAvid() {
        return (Long) get(15);
    }

    /**
     * Setter for <code>lau_creative_extra.season_id</code>. 课程id
     */
    public void setSeasonId(Long value) {
        set(16, value);
    }

    /**
     * Getter for <code>lau_creative_extra.season_id</code>. 课程id
     */
    public Long getSeasonId() {
        return (Long) get(16);
    }

    /**
     * Setter for <code>lau_creative_extra.parent_creative_id</code>. 母创意id
     */
    public void setParentCreativeId(Integer value) {
        set(17, value);
    }

    /**
     * Getter for <code>lau_creative_extra.parent_creative_id</code>. 母创意id
     */
    public Integer getParentCreativeId() {
        return (Integer) get(17);
    }

    /**
     * Setter for <code>lau_creative_extra.ppc_type</code>. 落地页类型，1-稿件播放页
     * 2-图文详情页 3-直播间 4-内容投放 5-游戏中心 6-其它
     */
    public void setPpcType(Integer value) {
        set(18, value);
    }

    /**
     * Getter for <code>lau_creative_extra.ppc_type</code>. 落地页类型，1-稿件播放页
     * 2-图文详情页 3-直播间 4-内容投放 5-游戏中心 6-其它
     */
    public Integer getPpcType() {
        return (Integer) get(18);
    }

    /**
     * Setter for <code>lau_creative_extra.is_smart_derivative</code>.
     * 是否使用智能衍生，0是不使用,1是使用
     */
    public void setIsSmartDerivative(Integer value) {
        set(19, value);
    }

    /**
     * Getter for <code>lau_creative_extra.is_smart_derivative</code>.
     * 是否使用智能衍生，0是不使用,1是使用
     */
    public Integer getIsSmartDerivative() {
        return (Integer) get(19);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record20 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row20<Long, Integer, Integer, Integer, Integer, Integer, String, Integer, Timestamp, Timestamp, Integer, Integer, Long, String, Long, Long, Long, Integer, Integer, Integer> fieldsRow() {
        return (Row20) super.fieldsRow();
    }

    @Override
    public Row20<Long, Integer, Integer, Integer, Integer, Integer, String, Integer, Timestamp, Timestamp, Integer, Integer, Long, String, Long, Long, Long, Integer, Integer, Integer> valuesRow() {
        return (Row20) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.CREATIVE_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.ACCOUNT_ID;
    }

    @Override
    public Field<Integer> field4() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.CAMPAIGN_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.UNIT_ID;
    }

    @Override
    public Field<Integer> field6() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.IMAGE_MACRO_TYPE;
    }

    @Override
    public Field<String> field7() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.IMAGE_MACRO;
    }

    @Override
    public Field<Integer> field8() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field9() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.CTIME;
    }

    @Override
    public Field<Timestamp> field10() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.MTIME;
    }

    @Override
    public Field<Integer> field11() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.QUALIFICATION_PACKAGE_ID;
    }

    @Override
    public Field<Integer> field12() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.AD_SPACE_ID;
    }

    @Override
    public Field<Long> field13() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.BILI_SPACE_MID;
    }

    @Override
    public Field<String> field14() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.RAW_JUMP_URL;
    }

    @Override
    public Field<Long> field15() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.MGK_PAGE_ID;
    }

    @Override
    public Field<Long> field16() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.SEASON_AVID;
    }

    @Override
    public Field<Long> field17() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.SEASON_ID;
    }

    @Override
    public Field<Integer> field18() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.PARENT_CREATIVE_ID;
    }

    @Override
    public Field<Integer> field19() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.PPC_TYPE;
    }

    @Override
    public Field<Integer> field20() {
        return TLauCreativeExtra.LAU_CREATIVE_EXTRA.IS_SMART_DERIVATIVE;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getCreativeId();
    }

    @Override
    public Integer component3() {
        return getAccountId();
    }

    @Override
    public Integer component4() {
        return getCampaignId();
    }

    @Override
    public Integer component5() {
        return getUnitId();
    }

    @Override
    public Integer component6() {
        return getImageMacroType();
    }

    @Override
    public String component7() {
        return getImageMacro();
    }

    @Override
    public Integer component8() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component9() {
        return getCtime();
    }

    @Override
    public Timestamp component10() {
        return getMtime();
    }

    @Override
    public Integer component11() {
        return getQualificationPackageId();
    }

    @Override
    public Integer component12() {
        return getAdSpaceId();
    }

    @Override
    public Long component13() {
        return getBiliSpaceMid();
    }

    @Override
    public String component14() {
        return getRawJumpUrl();
    }

    @Override
    public Long component15() {
        return getMgkPageId();
    }

    @Override
    public Long component16() {
        return getSeasonAvid();
    }

    @Override
    public Long component17() {
        return getSeasonId();
    }

    @Override
    public Integer component18() {
        return getParentCreativeId();
    }

    @Override
    public Integer component19() {
        return getPpcType();
    }

    @Override
    public Integer component20() {
        return getIsSmartDerivative();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getCreativeId();
    }

    @Override
    public Integer value3() {
        return getAccountId();
    }

    @Override
    public Integer value4() {
        return getCampaignId();
    }

    @Override
    public Integer value5() {
        return getUnitId();
    }

    @Override
    public Integer value6() {
        return getImageMacroType();
    }

    @Override
    public String value7() {
        return getImageMacro();
    }

    @Override
    public Integer value8() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value9() {
        return getCtime();
    }

    @Override
    public Timestamp value10() {
        return getMtime();
    }

    @Override
    public Integer value11() {
        return getQualificationPackageId();
    }

    @Override
    public Integer value12() {
        return getAdSpaceId();
    }

    @Override
    public Long value13() {
        return getBiliSpaceMid();
    }

    @Override
    public String value14() {
        return getRawJumpUrl();
    }

    @Override
    public Long value15() {
        return getMgkPageId();
    }

    @Override
    public Long value16() {
        return getSeasonAvid();
    }

    @Override
    public Long value17() {
        return getSeasonId();
    }

    @Override
    public Integer value18() {
        return getParentCreativeId();
    }

    @Override
    public Integer value19() {
        return getPpcType();
    }

    @Override
    public Integer value20() {
        return getIsSmartDerivative();
    }

    @Override
    public LauCreativeExtraRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value2(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value3(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value4(Integer value) {
        setCampaignId(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value5(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value6(Integer value) {
        setImageMacroType(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value7(String value) {
        setImageMacro(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value8(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value9(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value10(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value11(Integer value) {
        setQualificationPackageId(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value12(Integer value) {
        setAdSpaceId(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value13(Long value) {
        setBiliSpaceMid(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value14(String value) {
        setRawJumpUrl(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value15(Long value) {
        setMgkPageId(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value16(Long value) {
        setSeasonAvid(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value17(Long value) {
        setSeasonId(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value18(Integer value) {
        setParentCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value19(Integer value) {
        setPpcType(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord value20(Integer value) {
        setIsSmartDerivative(value);
        return this;
    }

    @Override
    public LauCreativeExtraRecord values(Long value1, Integer value2, Integer value3, Integer value4, Integer value5, Integer value6, String value7, Integer value8, Timestamp value9, Timestamp value10, Integer value11, Integer value12, Long value13, String value14, Long value15, Long value16, Long value17, Integer value18, Integer value19, Integer value20) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        value20(value20);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativeExtraRecord
     */
    public LauCreativeExtraRecord() {
        super(TLauCreativeExtra.LAU_CREATIVE_EXTRA);
    }

    /**
     * Create a detached, initialised LauCreativeExtraRecord
     */
    public LauCreativeExtraRecord(Long id, Integer creativeId, Integer accountId, Integer campaignId, Integer unitId, Integer imageMacroType, String imageMacro, Integer isDeleted, Timestamp ctime, Timestamp mtime, Integer qualificationPackageId, Integer adSpaceId, Long biliSpaceMid, String rawJumpUrl, Long mgkPageId, Long seasonAvid, Long seasonId, Integer parentCreativeId, Integer ppcType, Integer isSmartDerivative) {
        super(TLauCreativeExtra.LAU_CREATIVE_EXTRA);

        setId(id);
        setCreativeId(creativeId);
        setAccountId(accountId);
        setCampaignId(campaignId);
        setUnitId(unitId);
        setImageMacroType(imageMacroType);
        setImageMacro(imageMacro);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setQualificationPackageId(qualificationPackageId);
        setAdSpaceId(adSpaceId);
        setBiliSpaceMid(biliSpaceMid);
        setRawJumpUrl(rawJumpUrl);
        setMgkPageId(mgkPageId);
        setSeasonAvid(seasonAvid);
        setSeasonId(seasonId);
        setParentCreativeId(parentCreativeId);
        setPpcType(ppcType);
        setIsSmartDerivative(isSmartDerivative);
    }

    /**
     * Create a detached, initialised LauCreativeExtraRecord
     */
    public LauCreativeExtraRecord(LauCreativeExtraPo value) {
        super(TLauCreativeExtra.LAU_CREATIVE_EXTRA);

        if (value != null) {
            setId(value.getId());
            setCreativeId(value.getCreativeId());
            setAccountId(value.getAccountId());
            setCampaignId(value.getCampaignId());
            setUnitId(value.getUnitId());
            setImageMacroType(value.getImageMacroType());
            setImageMacro(value.getImageMacro());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setQualificationPackageId(value.getQualificationPackageId());
            setAdSpaceId(value.getAdSpaceId());
            setBiliSpaceMid(value.getBiliSpaceMid());
            setRawJumpUrl(value.getRawJumpUrl());
            setMgkPageId(value.getMgkPageId());
            setSeasonAvid(value.getSeasonAvid());
            setSeasonId(value.getSeasonId());
            setParentCreativeId(value.getParentCreativeId());
            setPpcType(value.getPpcType());
            setIsSmartDerivative(value.getIsSmartDerivative());
        }
    }
}
