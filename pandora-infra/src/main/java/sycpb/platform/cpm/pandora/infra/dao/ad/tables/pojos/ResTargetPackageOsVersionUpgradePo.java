/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 新版定向包分端系统版本定向表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResTargetPackageOsVersionUpgradePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   targetPackageId;
    private Integer   os;
    private String    osVersions;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;

    public ResTargetPackageOsVersionUpgradePo() {}

    public ResTargetPackageOsVersionUpgradePo(ResTargetPackageOsVersionUpgradePo value) {
        this.id = value.id;
        this.targetPackageId = value.targetPackageId;
        this.os = value.os;
        this.osVersions = value.osVersions;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
    }

    public ResTargetPackageOsVersionUpgradePo(
        Long      id,
        Integer   targetPackageId,
        Integer   os,
        String    osVersions,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime
    ) {
        this.id = id;
        this.targetPackageId = targetPackageId;
        this.os = os;
        this.osVersions = osVersions;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
    }

    /**
     * Getter for <code>res_target_package_os_version_upgrade.id</code>. 自增id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>res_target_package_os_version_upgrade.id</code>. 自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for
     * <code>res_target_package_os_version_upgrade.target_package_id</code>.
     * 定向包id
     */
    public Integer getTargetPackageId() {
        return this.targetPackageId;
    }

    /**
     * Setter for
     * <code>res_target_package_os_version_upgrade.target_package_id</code>.
     * 定向包id
     */
    public void setTargetPackageId(Integer targetPackageId) {
        this.targetPackageId = targetPackageId;
    }

    /**
     * Getter for <code>res_target_package_os_version_upgrade.os</code>. 设备平台
     * android-399,iphone-398,ipad-421
     */
    public Integer getOs() {
        return this.os;
    }

    /**
     * Setter for <code>res_target_package_os_version_upgrade.os</code>. 设备平台
     * android-399,iphone-398,ipad-421
     */
    public void setOs(Integer os) {
        this.os = os;
    }

    /**
     * Getter for
     * <code>res_target_package_os_version_upgrade.os_versions</code>.
     * 系统版本列表(,分隔)没有为[-1]
     */
    public String getOsVersions() {
        return this.osVersions;
    }

    /**
     * Setter for
     * <code>res_target_package_os_version_upgrade.os_versions</code>.
     * 系统版本列表(,分隔)没有为[-1]
     */
    public void setOsVersions(String osVersions) {
        this.osVersions = osVersions;
    }

    /**
     * Getter for <code>res_target_package_os_version_upgrade.is_deleted</code>.
     * 软删除 0 否 1是
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>res_target_package_os_version_upgrade.is_deleted</code>.
     * 软删除 0 否 1是
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>res_target_package_os_version_upgrade.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>res_target_package_os_version_upgrade.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>res_target_package_os_version_upgrade.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>res_target_package_os_version_upgrade.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ResTargetPackageOsVersionUpgradePo (");

        sb.append(id);
        sb.append(", ").append(targetPackageId);
        sb.append(", ").append(os);
        sb.append(", ").append(osVersions);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);

        sb.append(")");
        return sb.toString();
    }
}
