package sycpb.platform.cpm.pandora.infra.config.mysql;

import org.jooq.ConnectionProvider;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.jooq.impl.DataSourceConnectionProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.jdbc.datasource.TransactionAwareDataSourceProxy;
import org.springframework.transaction.PlatformTransactionManager;
import pleiades.component.mysql.datasource.BiliDataSource;

import javax.sql.DataSource;

@Configuration
@PropertySource(value = "paladin://mysql.yaml")
public class BusinessAdConfig {
    protected static final String PACKAGE_NAME = "sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos";
    protected static final String AD_DS = "adDataSource";
    protected static final String AD_READ_DS = "adReadDataSource";
    private static final String AD_TX_AWARE_DS_PROXY = "adTxAwareDsProxy";
    private static final String AD_CONNECTION_PROVIDER = "adConnectionProvider";
    protected static final String AD_JOOQ_CONFIG = "adJooqConfig";

    @Bean(AD_DS)
    @ConfigurationProperties(prefix = "mysql.ad")
    public BiliDataSource adDataSource() {
        return new BiliDataSource();
    }

    @Bean(AD_READ_DS)
    @ConfigurationProperties("mysql.ad-read")
    public BiliDataSource adReadDataSource() {
        return new BiliDataSource();
    }

    @Bean(AD_TX_AWARE_DS_PROXY)
    public TransactionAwareDataSourceProxy adTxManagerProxy(@Qualifier(AD_DS) DataSource ds) {
        return new TransactionAwareDataSourceProxy(ds);
    }

    @Bean(name = MySqlConfig.AD_TX_MGR)
    public PlatformTransactionManager adTxManager(@Qualifier(AD_DS) DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

    @Bean(name = AD_CONNECTION_PROVIDER)
    public ConnectionProvider adConnectionProvider(@Qualifier(AD_TX_AWARE_DS_PROXY) DataSource ds) {
        return new DataSourceConnectionProvider(ds);
    }

    @Bean(name = MySqlConfig.AD_DSL_CONTEXT)
    public DSLContext adDslContext(@Qualifier(AD_JOOQ_CONFIG) org.jooq.Configuration jooqConfig) {
        return DSL.using(jooqConfig);
    }

    @Bean(name = AD_JOOQ_CONFIG)
    public org.jooq.Configuration adJooqConfig(@Qualifier(AD_CONNECTION_PROVIDER) ConnectionProvider cp) {
        return JooqConfig.config(cp);
    }
}
