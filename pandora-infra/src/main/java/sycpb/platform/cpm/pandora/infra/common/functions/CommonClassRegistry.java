package sycpb.platform.cpm.pandora.infra.common.functions;

import lombok.SneakyThrows;
import org.springframework.util.Assert;
import sycpb.platform.cpm.pandora.infra.common.bos.CommonClassMetaBo;
import sycpb.platform.cpm.pandora.infra.compare.bos.CompareClassMetaBo;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.text.MessageFormat;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName CommonClassRegistry
 * <AUTHOR>
 * @Date 2024/8/15 3:30 下午
 * @Version 1.0
 **/
public class CommonClassRegistry {
    private static final Map<String, CommonClassMetaBo> registry = new ConcurrentHashMap<>();

    public static CommonClassMetaBo get(Class<?> clazz) {
        final var key = clazz.getName();
        var meta = registry.get(key);
        if (Objects.nonNull(meta)) return meta;

        meta = inspect(clazz);
        registry.put(key, meta);
        return meta;
    }

    @SneakyThrows
    private static CommonClassMetaBo inspect(Class<?> clazz) {
        CommonClassMetaBo metaBo = new CommonClassMetaBo(clazz);
        for (Field declaredField : clazz.getDeclaredFields()) {

            // 主要是为了serialVersionUID
            int modifiers = declaredField.getModifiers();
            if (Modifier.isFinal(modifiers)) {
                continue;
            }

            String name = declaredField.getName();
            PropertyDescriptor propertyDescriptor = new PropertyDescriptor(name, clazz);
            Method getterMethod = getPropertyGetterMethod(clazz, declaredField, propertyDescriptor);
            Method setterMethod = getPropertySetterMethod(clazz, declaredField, propertyDescriptor);
            if (Objects.isNull(getterMethod) || Objects.isNull(setterMethod)) {
                continue;
            }
            metaBo.getFieldGetterMap().put(name, getterMethod);
            metaBo.getFieldSetterMap().put(name, setterMethod);
            metaBo.getFieldNameList().add(name);
        }

        return metaBo;
    }

    private static Method getPropertyGetterMethod(Class<?> clazz, Field field, PropertyDescriptor propertyDescriptor) {
        Assert.notNull(propertyDescriptor, MessageFormat.format("common class inspection: can not find property descriptor, class={0}, field={1}", clazz.getName(), field.getName()));
        final var readMethod = propertyDescriptor.getReadMethod();
        Assert.notNull(readMethod, MessageFormat.format("common class inspection: can not find getter, class={0}, field={1}", clazz.getName(), field.getName()));
        return readMethod;
    }

    private static Method getPropertySetterMethod(Class<?> clazz, Field field, PropertyDescriptor propertyDescriptor) {
        Assert.notNull(propertyDescriptor, MessageFormat.format("common class inspection: can not find property descriptor, class={0}, field={1}", clazz.getName(), field.getName()));
        final var writeMethod = propertyDescriptor.getWriteMethod();
        Assert.notNull(writeMethod, MessageFormat.format("common class inspection: can not find setter, class={0}, field={1}", clazz.getName(), field.getName()));
        return writeMethod;    }
}
