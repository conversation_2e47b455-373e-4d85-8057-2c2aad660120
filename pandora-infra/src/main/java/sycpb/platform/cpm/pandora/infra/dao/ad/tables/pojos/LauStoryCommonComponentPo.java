/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauStoryCommonComponentPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   isDeleted;
    private Integer   accountId;
    private Long      componentId;
    private String    componentName;
    private String    title;
    private String    description;
    private String    imageUrl;
    private String    imageMd5;
    private Integer   buttonId;
    private Integer   buttonType;
    private String    buttonText;

    public LauStoryCommonComponentPo() {}

    public LauStoryCommonComponentPo(LauStoryCommonComponentPo value) {
        this.id = value.id;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.isDeleted = value.isDeleted;
        this.accountId = value.accountId;
        this.componentId = value.componentId;
        this.componentName = value.componentName;
        this.title = value.title;
        this.description = value.description;
        this.imageUrl = value.imageUrl;
        this.imageMd5 = value.imageMd5;
        this.buttonId = value.buttonId;
        this.buttonType = value.buttonType;
        this.buttonText = value.buttonText;
    }

    public LauStoryCommonComponentPo(
        Long      id,
        Timestamp ctime,
        Timestamp mtime,
        Integer   isDeleted,
        Integer   accountId,
        Long      componentId,
        String    componentName,
        String    title,
        String    description,
        String    imageUrl,
        String    imageMd5,
        Integer   buttonId,
        Integer   buttonType,
        String    buttonText
    ) {
        this.id = id;
        this.ctime = ctime;
        this.mtime = mtime;
        this.isDeleted = isDeleted;
        this.accountId = accountId;
        this.componentId = componentId;
        this.componentName = componentName;
        this.title = title;
        this.description = description;
        this.imageUrl = imageUrl;
        this.imageMd5 = imageMd5;
        this.buttonId = buttonId;
        this.buttonType = buttonType;
        this.buttonText = buttonText;
    }

    /**
     * Getter for <code>lau_story_common_component.id</code>. id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_story_common_component.id</code>. id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_story_common_component.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_story_common_component.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_story_common_component.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_story_common_component.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_story_common_component.is_deleted</code>. 软删除:
     * 0-未删除,1-已删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_story_common_component.is_deleted</code>. 软删除:
     * 0-未删除,1-已删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_story_common_component.account_id</code>. 账号id
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>lau_story_common_component.account_id</code>. 账号id
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>lau_story_common_component.component_id</code>.
     * story组件id
     */
    public Long getComponentId() {
        return this.componentId;
    }

    /**
     * Setter for <code>lau_story_common_component.component_id</code>.
     * story组件id
     */
    public void setComponentId(Long componentId) {
        this.componentId = componentId;
    }

    /**
     * Getter for <code>lau_story_common_component.component_name</code>. 组件名称
     */
    public String getComponentName() {
        return this.componentName;
    }

    /**
     * Setter for <code>lau_story_common_component.component_name</code>. 组件名称
     */
    public void setComponentName(String componentName) {
        this.componentName = componentName;
    }

    /**
     * Getter for <code>lau_story_common_component.title</code>. 标题
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * Setter for <code>lau_story_common_component.title</code>. 标题
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * Getter for <code>lau_story_common_component.description</code>. 描述
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>lau_story_common_component.description</code>. 描述
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * Getter for <code>lau_story_common_component.image_url</code>. 图片url
     */
    public String getImageUrl() {
        return this.imageUrl;
    }

    /**
     * Setter for <code>lau_story_common_component.image_url</code>. 图片url
     */
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    /**
     * Getter for <code>lau_story_common_component.image_md5</code>. 图片md5
     */
    public String getImageMd5() {
        return this.imageMd5;
    }

    /**
     * Setter for <code>lau_story_common_component.image_md5</code>. 图片md5
     */
    public void setImageMd5(String imageMd5) {
        this.imageMd5 = imageMd5;
    }

    /**
     * Getter for <code>lau_story_common_component.button_id</code>. 按钮id
     */
    public Integer getButtonId() {
        return this.buttonId;
    }

    /**
     * Setter for <code>lau_story_common_component.button_id</code>. 按钮id
     */
    public void setButtonId(Integer buttonId) {
        this.buttonId = buttonId;
    }

    /**
     * Getter for <code>lau_story_common_component.button_type</code>. 按钮类型
     */
    public Integer getButtonType() {
        return this.buttonType;
    }

    /**
     * Setter for <code>lau_story_common_component.button_type</code>. 按钮类型
     */
    public void setButtonType(Integer buttonType) {
        this.buttonType = buttonType;
    }

    /**
     * Getter for <code>lau_story_common_component.button_text</code>. 按钮文案
     */
    public String getButtonText() {
        return this.buttonText;
    }

    /**
     * Setter for <code>lau_story_common_component.button_text</code>. 按钮文案
     */
    public void setButtonText(String buttonText) {
        this.buttonText = buttonText;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauStoryCommonComponentPo (");

        sb.append(id);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(accountId);
        sb.append(", ").append(componentId);
        sb.append(", ").append(componentName);
        sb.append(", ").append(title);
        sb.append(", ").append(description);
        sb.append(", ").append(imageUrl);
        sb.append(", ").append(imageMd5);
        sb.append(", ").append(buttonId);
        sb.append(", ").append(buttonType);
        sb.append(", ").append(buttonText);

        sb.append(")");
        return sb.toString();
    }
}
