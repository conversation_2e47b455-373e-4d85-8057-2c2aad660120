/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeAuditStat;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeAuditStatPo;


/**
 * 创意审核统计表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeAuditStatRecord extends UpdatableRecordImpl<LauCreativeAuditStatRecord> implements Record10<Long, Integer, <PERSON>, Integer, Inte<PERSON>, Inte<PERSON>, Inte<PERSON>, Inte<PERSON>, Timestamp, Timestamp> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_audit_stat.id</code>. 主键
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_audit_stat.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_creative_audit_stat.event</code>. 事件类型: 1-进入审核,
     * 2-通过, 3-驳回, 4-超时
     */
    public void setEvent(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_creative_audit_stat.event</code>. 事件类型: 1-进入审核,
     * 2-通过, 3-驳回, 4-超时
     */
    public Integer getEvent() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_creative_audit_stat.operator_username</code>. 操作员
     */
    public void setOperatorUsername(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_creative_audit_stat.operator_username</code>. 操作员
     */
    public String getOperatorUsername() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lau_creative_audit_stat.account_id</code>. 账户id
     */
    public void setAccountId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_creative_audit_stat.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_creative_audit_stat.campaign_id</code>. 计划id
     */
    public void setCampaignId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_creative_audit_stat.campaign_id</code>. 计划id
     */
    public Integer getCampaignId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_creative_audit_stat.unit_id</code>. 单元id
     */
    public void setUnitId(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_creative_audit_stat.unit_id</code>. 单元id
     */
    public Integer getUnitId() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_creative_audit_stat.creative_id</code>. 创意id
     */
    public void setCreativeId(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_creative_audit_stat.creative_id</code>. 创意id
     */
    public Integer getCreativeId() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lau_creative_audit_stat.is_deleted</code>. 软删除:1-有效,
     * 2-删除
     */
    public void setIsDeleted(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_creative_audit_stat.is_deleted</code>. 软删除:1-有效,
     * 2-删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>lau_creative_audit_stat.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_creative_audit_stat.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(8);
    }

    /**
     * Setter for <code>lau_creative_audit_stat.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_creative_audit_stat.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row10<Long, Integer, String, Integer, Integer, Integer, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    @Override
    public Row10<Long, Integer, String, Integer, Integer, Integer, Integer, Integer, Timestamp, Timestamp> valuesRow() {
        return (Row10) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT.EVENT;
    }

    @Override
    public Field<String> field3() {
        return TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT.OPERATOR_USERNAME;
    }

    @Override
    public Field<Integer> field4() {
        return TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT.ACCOUNT_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT.CAMPAIGN_ID;
    }

    @Override
    public Field<Integer> field6() {
        return TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT.UNIT_ID;
    }

    @Override
    public Field<Integer> field7() {
        return TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT.CREATIVE_ID;
    }

    @Override
    public Field<Integer> field8() {
        return TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field9() {
        return TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT.CTIME;
    }

    @Override
    public Field<Timestamp> field10() {
        return TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT.MTIME;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getEvent();
    }

    @Override
    public String component3() {
        return getOperatorUsername();
    }

    @Override
    public Integer component4() {
        return getAccountId();
    }

    @Override
    public Integer component5() {
        return getCampaignId();
    }

    @Override
    public Integer component6() {
        return getUnitId();
    }

    @Override
    public Integer component7() {
        return getCreativeId();
    }

    @Override
    public Integer component8() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component9() {
        return getCtime();
    }

    @Override
    public Timestamp component10() {
        return getMtime();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getEvent();
    }

    @Override
    public String value3() {
        return getOperatorUsername();
    }

    @Override
    public Integer value4() {
        return getAccountId();
    }

    @Override
    public Integer value5() {
        return getCampaignId();
    }

    @Override
    public Integer value6() {
        return getUnitId();
    }

    @Override
    public Integer value7() {
        return getCreativeId();
    }

    @Override
    public Integer value8() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value9() {
        return getCtime();
    }

    @Override
    public Timestamp value10() {
        return getMtime();
    }

    @Override
    public LauCreativeAuditStatRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativeAuditStatRecord value2(Integer value) {
        setEvent(value);
        return this;
    }

    @Override
    public LauCreativeAuditStatRecord value3(String value) {
        setOperatorUsername(value);
        return this;
    }

    @Override
    public LauCreativeAuditStatRecord value4(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauCreativeAuditStatRecord value5(Integer value) {
        setCampaignId(value);
        return this;
    }

    @Override
    public LauCreativeAuditStatRecord value6(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauCreativeAuditStatRecord value7(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeAuditStatRecord value8(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauCreativeAuditStatRecord value9(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativeAuditStatRecord value10(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativeAuditStatRecord values(Long value1, Integer value2, String value3, Integer value4, Integer value5, Integer value6, Integer value7, Integer value8, Timestamp value9, Timestamp value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativeAuditStatRecord
     */
    public LauCreativeAuditStatRecord() {
        super(TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT);
    }

    /**
     * Create a detached, initialised LauCreativeAuditStatRecord
     */
    public LauCreativeAuditStatRecord(Long id, Integer event, String operatorUsername, Integer accountId, Integer campaignId, Integer unitId, Integer creativeId, Integer isDeleted, Timestamp ctime, Timestamp mtime) {
        super(TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT);

        setId(id);
        setEvent(event);
        setOperatorUsername(operatorUsername);
        setAccountId(accountId);
        setCampaignId(campaignId);
        setUnitId(unitId);
        setCreativeId(creativeId);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
    }

    /**
     * Create a detached, initialised LauCreativeAuditStatRecord
     */
    public LauCreativeAuditStatRecord(LauCreativeAuditStatPo value) {
        super(TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT);

        if (value != null) {
            setId(value.getId());
            setEvent(value.getEvent());
            setOperatorUsername(value.getOperatorUsername());
            setAccountId(value.getAccountId());
            setCampaignId(value.getCampaignId());
            setUnitId(value.getUnitId());
            setCreativeId(value.getCreativeId());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
        }
    }
}
