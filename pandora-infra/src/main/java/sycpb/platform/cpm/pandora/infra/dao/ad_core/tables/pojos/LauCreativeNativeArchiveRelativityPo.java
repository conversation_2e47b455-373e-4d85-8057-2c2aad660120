/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 原生创意稿件相关性表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeNativeArchiveRelativityPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   creativeId;
    private Long      avid;
    private String    reason;
    private Integer   auditStatus;
    private Integer   isRecheck;
    private Integer   version;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   type;

    public LauCreativeNativeArchiveRelativityPo() {}

    public LauCreativeNativeArchiveRelativityPo(LauCreativeNativeArchiveRelativityPo value) {
        this.id = value.id;
        this.creativeId = value.creativeId;
        this.avid = value.avid;
        this.reason = value.reason;
        this.auditStatus = value.auditStatus;
        this.isRecheck = value.isRecheck;
        this.version = value.version;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.type = value.type;
    }

    public LauCreativeNativeArchiveRelativityPo(
        Long      id,
        Integer   creativeId,
        Long      avid,
        String    reason,
        Integer   auditStatus,
        Integer   isRecheck,
        Integer   version,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime,
        Integer   type
    ) {
        this.id = id;
        this.creativeId = creativeId;
        this.avid = avid;
        this.reason = reason;
        this.auditStatus = auditStatus;
        this.isRecheck = isRecheck;
        this.version = version;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
        this.type = type;
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.id</code>. id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.id</code>. id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for
     * <code>lau_creative_native_archive_relativity.creative_id</code>. 创意ID
     */
    public Integer getCreativeId() {
        return this.creativeId;
    }

    /**
     * Setter for
     * <code>lau_creative_native_archive_relativity.creative_id</code>. 创意ID
     */
    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.avid</code>.
     * 由type决定，type=1 为稿件id，type=2为动态id，type=3为直播间roomId
     */
    public Long getAvid() {
        return this.avid;
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.avid</code>.
     * 由type决定，type=1 为稿件id，type=2为动态id，type=3为直播间roomId
     */
    public void setAvid(Long avid) {
        this.avid = avid;
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.reason</code>.
     * 原因（审核拒绝时填写）
     */
    public String getReason() {
        return this.reason;
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.reason</code>.
     * 原因（审核拒绝时填写）
     */
    public void setReason(String reason) {
        this.reason = reason;
    }

    /**
     * Getter for
     * <code>lau_creative_native_archive_relativity.audit_status</code>.
     * 审核状态（1-待审核，2-审核通过，3-审核不通过）
     */
    public Integer getAuditStatus() {
        return this.auditStatus;
    }

    /**
     * Setter for
     * <code>lau_creative_native_archive_relativity.audit_status</code>.
     * 审核状态（1-待审核，2-审核通过，3-审核不通过）
     */
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    /**
     * Getter for
     * <code>lau_creative_native_archive_relativity.is_recheck</code>. 是否被质检 0否
     * 1是
     */
    public Integer getIsRecheck() {
        return this.isRecheck;
    }

    /**
     * Setter for
     * <code>lau_creative_native_archive_relativity.is_recheck</code>. 是否被质检 0否
     * 1是
     */
    public void setIsRecheck(Integer isRecheck) {
        this.isRecheck = isRecheck;
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.version</code>.
     * 版本号
     */
    public Integer getVersion() {
        return this.version;
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.version</code>.
     * 版本号
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     * Getter for
     * <code>lau_creative_native_archive_relativity.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for
     * <code>lau_creative_native_archive_relativity.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.ctime</code>.
     * 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.ctime</code>.
     * 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.mtime</code>.
     * 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.mtime</code>.
     * 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.type</code>.
     * 1原生稿件(默认值) 2动态 3直播间
     */
    public Integer getType() {
        return this.type;
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.type</code>.
     * 1原生稿件(默认值) 2动态 3直播间
     */
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauCreativeNativeArchiveRelativityPo (");

        sb.append(id);
        sb.append(", ").append(creativeId);
        sb.append(", ").append(avid);
        sb.append(", ").append(reason);
        sb.append(", ").append(auditStatus);
        sb.append(", ").append(isRecheck);
        sb.append(", ").append(version);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(type);

        sb.append(")");
        return sb.toString();
    }
}
