/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 投放资质包表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauQualificationPackagePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer   id;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   isDeleted;
    private Integer   accountId;
    private String    name;

    public LauQualificationPackagePo() {}

    public LauQualificationPackagePo(LauQualificationPackagePo value) {
        this.id = value.id;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.isDeleted = value.isDeleted;
        this.accountId = value.accountId;
        this.name = value.name;
    }

    public LauQualificationPackagePo(
        Integer   id,
        Timestamp ctime,
        Timestamp mtime,
        Integer   isDeleted,
        Integer   accountId,
        String    name
    ) {
        this.id = id;
        this.ctime = ctime;
        this.mtime = mtime;
        this.isDeleted = isDeleted;
        this.accountId = accountId;
        this.name = name;
    }

    /**
     * Getter for <code>lau_qualification_package.id</code>. 自增id
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_qualification_package.id</code>. 自增id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_qualification_package.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_qualification_package.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_qualification_package.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_qualification_package.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_qualification_package.is_deleted</code>. 软删除: 0 -
     * 未删除, 1 - 已删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_qualification_package.is_deleted</code>. 软删除: 0 -
     * 未删除, 1 - 已删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_qualification_package.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>lau_qualification_package.account_id</code>. 账号ID
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>lau_qualification_package.name</code>. 资质包名称
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>lau_qualification_package.name</code>. 资质包名称
     */
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauQualificationPackagePo (");

        sb.append(id);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(accountId);
        sb.append(", ").append(name);

        sb.append(")");
        return sb.toString();
    }
}
