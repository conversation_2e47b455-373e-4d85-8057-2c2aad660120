/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row5;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.PandoraMapiAsyncSaveDataFailRecordRecord;


/**
 * 新三连mapi异步保存失败记录
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TPandoraMapiAsyncSaveDataFailRecord extends TableImpl<PandoraMapiAsyncSaveDataFailRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>pandora_mapi_async_save_data_fail_record</code>
     */
    public static final TPandoraMapiAsyncSaveDataFailRecord PANDORA_MAPI_ASYNC_SAVE_DATA_FAIL_RECORD = new TPandoraMapiAsyncSaveDataFailRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PandoraMapiAsyncSaveDataFailRecordRecord> getRecordType() {
        return PandoraMapiAsyncSaveDataFailRecordRecord.class;
    }

    /**
     * The column <code>pandora_mapi_async_save_data_fail_record.id</code>. 自增主键
     */
    public final TableField<PandoraMapiAsyncSaveDataFailRecordRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增主键");

    /**
     * The column
     * <code>pandora_mapi_async_save_data_fail_record.is_deleted</code>. 是否被删除
     * 0-正常 1-被删除
     */
    public final TableField<PandoraMapiAsyncSaveDataFailRecordRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否被删除 0-正常 1-被删除");

    /**
     * The column <code>pandora_mapi_async_save_data_fail_record.ctime</code>.
     * 创建时间
     */
    public final TableField<PandoraMapiAsyncSaveDataFailRecordRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>pandora_mapi_async_save_data_fail_record.mtime</code>.
     * 更新时间
     */
    public final TableField<PandoraMapiAsyncSaveDataFailRecordRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column
     * <code>pandora_mapi_async_save_data_fail_record.cache_key</code>.
     * 异步执行内容缓存key
     */
    public final TableField<PandoraMapiAsyncSaveDataFailRecordRecord, String> CACHE_KEY = createField(DSL.name("cache_key"), SQLDataType.VARCHAR(256).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "异步执行内容缓存key");

    private TPandoraMapiAsyncSaveDataFailRecord(Name alias, Table<PandoraMapiAsyncSaveDataFailRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private TPandoraMapiAsyncSaveDataFailRecord(Name alias, Table<PandoraMapiAsyncSaveDataFailRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("新三连mapi异步保存失败记录"), TableOptions.table());
    }

    /**
     * Create an aliased <code>pandora_mapi_async_save_data_fail_record</code>
     * table reference
     */
    public TPandoraMapiAsyncSaveDataFailRecord(String alias) {
        this(DSL.name(alias), PANDORA_MAPI_ASYNC_SAVE_DATA_FAIL_RECORD);
    }

    /**
     * Create an aliased <code>pandora_mapi_async_save_data_fail_record</code>
     * table reference
     */
    public TPandoraMapiAsyncSaveDataFailRecord(Name alias) {
        this(alias, PANDORA_MAPI_ASYNC_SAVE_DATA_FAIL_RECORD);
    }

    /**
     * Create a <code>pandora_mapi_async_save_data_fail_record</code> table
     * reference
     */
    public TPandoraMapiAsyncSaveDataFailRecord() {
        this(DSL.name("pandora_mapi_async_save_data_fail_record"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<PandoraMapiAsyncSaveDataFailRecordRecord, Long> getIdentity() {
        return (Identity<PandoraMapiAsyncSaveDataFailRecordRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<PandoraMapiAsyncSaveDataFailRecordRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TPandoraMapiAsyncSaveDataFailRecord.PANDORA_MAPI_ASYNC_SAVE_DATA_FAIL_RECORD, DSL.name("KEY_pandora_mapi_async_save_data_fail_record_PRIMARY"), new TableField[] { TPandoraMapiAsyncSaveDataFailRecord.PANDORA_MAPI_ASYNC_SAVE_DATA_FAIL_RECORD.ID }, true);
    }

    @Override
    public TPandoraMapiAsyncSaveDataFailRecord as(String alias) {
        return new TPandoraMapiAsyncSaveDataFailRecord(DSL.name(alias), this);
    }

    @Override
    public TPandoraMapiAsyncSaveDataFailRecord as(Name alias) {
        return new TPandoraMapiAsyncSaveDataFailRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TPandoraMapiAsyncSaveDataFailRecord rename(String name) {
        return new TPandoraMapiAsyncSaveDataFailRecord(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TPandoraMapiAsyncSaveDataFailRecord rename(Name name) {
        return new TPandoraMapiAsyncSaveDataFailRecord(name, null);
    }

    // -------------------------------------------------------------------------
    // Row5 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row5<Long, Integer, Timestamp, Timestamp, String> fieldsRow() {
        return (Row5) super.fieldsRow();
    }
}
