/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TResTargetPackageProfessionInterestAutoUpgrade;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.ResTargetPackageProfessionInterestAutoUpgradePo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.ResTargetPackageProfessionInterestAutoUpgradeRecord;


/**
 * 单元-行业优选离线表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ResTargetPackageProfessionInterestAutoUpgradeDao extends DAOImpl<ResTargetPackageProfessionInterestAutoUpgradeRecord, ResTargetPackageProfessionInterestAutoUpgradePo, Integer> {

    /**
     * Create a new ResTargetPackageProfessionInterestAutoUpgradeDao without any
     * configuration
     */
    public ResTargetPackageProfessionInterestAutoUpgradeDao() {
        super(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE, ResTargetPackageProfessionInterestAutoUpgradePo.class);
    }

    /**
     * Create a new ResTargetPackageProfessionInterestAutoUpgradeDao with an
     * attached configuration
     */
    @Autowired
    public ResTargetPackageProfessionInterestAutoUpgradeDao(Configuration configuration) {
        super(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE, ResTargetPackageProfessionInterestAutoUpgradePo.class, configuration);
    }

    @Override
    public Integer getId(ResTargetPackageProfessionInterestAutoUpgradePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchById(Integer... values) {
        return fetch(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public ResTargetPackageProfessionInterestAutoUpgradePo fetchOneById(Integer value) {
        return fetchOne(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.ID, value);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchByAccountId(Integer... values) {
        return fetch(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>target_package_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchRangeOfTargetPackageId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.TARGET_PACKAGE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>target_package_id IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchByTargetPackageId(Integer... values) {
        return fetch(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.TARGET_PACKAGE_ID, values);
    }

    /**
     * Fetch records that have <code>interest_auto BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchRangeOfInterestAuto(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.INTEREST_AUTO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>interest_auto IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchByInterestAuto(Integer... values) {
        return fetch(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.INTEREST_AUTO, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchByCtime(Timestamp... values) {
        return fetch(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchByMtime(Timestamp... values) {
        return fetch(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.MTIME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestAutoUpgradePo> fetchByIsDeleted(Integer... values) {
        return fetch(TResTargetPackageProfessionInterestAutoUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_AUTO_UPGRADE.IS_DELETED, values);
    }
}
