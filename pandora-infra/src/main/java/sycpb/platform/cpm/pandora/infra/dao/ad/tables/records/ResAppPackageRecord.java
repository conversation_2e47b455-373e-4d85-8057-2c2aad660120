/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Timestamp;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TResAppPackage;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.ResAppPackagePo;


/**
 * app应用包信息表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResAppPackageRecord extends UpdatableRecordImpl<ResAppPackageRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>res_app_package.id</code>. 主键ID
     */
    public void setId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>res_app_package.id</code>. 主键ID
     */
    public Integer getId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>res_app_package.account_id</code>. 账号id
     */
    public void setAccountId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>res_app_package.account_id</code>. 账号id
     */
    public Integer getAccountId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>res_app_package.name</code>. 应用包名称
     */
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>res_app_package.name</code>. 应用包名称
     */
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>res_app_package.url</code>. 应用包原始下载链接
     */
    public void setUrl(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>res_app_package.url</code>. 应用包原始下载链接
     */
    public String getUrl() {
        return (String) get(3);
    }

    /**
     * Setter for <code>res_app_package.package_name</code>. 应用包的包名称
     */
    public void setPackageName(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>res_app_package.package_name</code>. 应用包的包名称
     */
    public String getPackageName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>res_app_package.app_name</code>. 应用名称
     */
    public void setAppName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>res_app_package.app_name</code>. 应用名称
     */
    public String getAppName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>res_app_package.platform</code>. 适应系统 1-IOS,
     * 2-Android,3-iphone, 4-ipad
     */
    public void setPlatform(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>res_app_package.platform</code>. 适应系统 1-IOS,
     * 2-Android,3-iphone, 4-ipad
     */
    public Integer getPlatform() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>res_app_package.version</code>. 版本号
     */
    public void setVersion(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>res_app_package.version</code>. 版本号
     */
    public String getVersion() {
        return (String) get(7);
    }

    /**
     * Setter for <code>res_app_package.size</code>. 应用包大小（单位字节）
     */
    public void setSize(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>res_app_package.size</code>. 应用包大小（单位字节）
     */
    public Integer getSize() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>res_app_package.md5</code>. 应用包的MD5
     */
    public void setMd5(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>res_app_package.md5</code>. 应用包的MD5
     */
    public String getMd5() {
        return (String) get(9);
    }

    /**
     * Setter for <code>res_app_package.icon_url</code>. 图标url
     */
    public void setIconUrl(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>res_app_package.icon_url</code>. 图标url
     */
    public String getIconUrl() {
        return (String) get(10);
    }

    /**
     * Setter for <code>res_app_package.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(11, value);
    }

    /**
     * Getter for <code>res_app_package.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(11);
    }

    /**
     * Setter for <code>res_app_package.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp value) {
        set(12, value);
    }

    /**
     * Getter for <code>res_app_package.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(12);
    }

    /**
     * Setter for <code>res_app_package.is_deleted</code>. 软删除 0-有效, 1-删除
     */
    public void setIsDeleted(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>res_app_package.is_deleted</code>. 软删除 0-有效, 1-删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>res_app_package.internal_url</code>. 应用包内部下载链接
     */
    public void setInternalUrl(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>res_app_package.internal_url</code>. 应用包内部下载链接
     */
    public String getInternalUrl() {
        return (String) get(14);
    }

    /**
     * Setter for <code>res_app_package.status</code>. 应用包状态 0-有效，1-无效
     */
    public void setStatus(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>res_app_package.status</code>. 应用包状态 0-有效，1-无效
     */
    public Integer getStatus() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>res_app_package.platform_status</code>. 平台状态 0-有效, 1-禁用
     */
    public void setPlatformStatus(Integer value) {
        set(16, value);
    }

    /**
     * Getter for <code>res_app_package.platform_status</code>. 平台状态 0-有效, 1-禁用
     */
    public Integer getPlatformStatus() {
        return (Integer) get(16);
    }

    /**
     * Setter for <code>res_app_package.developer_name</code>. 开发商名称
     */
    public void setDeveloperName(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>res_app_package.developer_name</code>. 开发商名称
     */
    public String getDeveloperName() {
        return (String) get(17);
    }

    /**
     * Setter for <code>res_app_package.authority_url</code>. 权限地址
     */
    public void setAuthorityUrl(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>res_app_package.authority_url</code>. 权限地址
     */
    public String getAuthorityUrl() {
        return (String) get(18);
    }

    /**
     * Setter for <code>res_app_package.auth_code_list</code>. 权限code
     */
    public void setAuthCodeList(String value) {
        set(19, value);
    }

    /**
     * Getter for <code>res_app_package.auth_code_list</code>. 权限code
     */
    public String getAuthCodeList() {
        return (String) get(19);
    }

    /**
     * Setter for <code>res_app_package.apk_update_time</code>. 安装包更新时间
     */
    public void setApkUpdateTime(Timestamp value) {
        set(20, value);
    }

    /**
     * Getter for <code>res_app_package.apk_update_time</code>. 安装包更新时间
     */
    public Timestamp getApkUpdateTime() {
        return (Timestamp) get(20);
    }

    /**
     * Setter for <code>res_app_package.privacy_policy</code>. 隐私政策地址
     */
    public void setPrivacyPolicy(String value) {
        set(21, value);
    }

    /**
     * Getter for <code>res_app_package.privacy_policy</code>. 隐私政策地址
     */
    public String getPrivacyPolicy() {
        return (String) get(21);
    }

    /**
     * Setter for <code>res_app_package.dmp_app_id</code>. dmp_app_id
     */
    public void setDmpAppId(Integer value) {
        set(22, value);
    }

    /**
     * Getter for <code>res_app_package.dmp_app_id</code>. dmp_app_id
     */
    public Integer getDmpAppId() {
        return (Integer) get(22);
    }

    /**
     * Setter for <code>res_app_package.is_new_fly</code>. 是否新起飞：0-否 1-是
     */
    public void setIsNewFly(Integer value) {
        set(23, value);
    }

    /**
     * Getter for <code>res_app_package.is_new_fly</code>. 是否新起飞：0-否 1-是
     */
    public Integer getIsNewFly() {
        return (Integer) get(23);
    }

    /**
     * Setter for <code>res_app_package.description</code>. 描述信息
     */
    public void setDescription(String value) {
        set(24, value);
    }

    /**
     * Getter for <code>res_app_package.description</code>. 描述信息
     */
    public String getDescription() {
        return (String) get(24);
    }

    /**
     * Setter for <code>res_app_package.sub_title</code>. 简介
     */
    public void setSubTitle(String value) {
        set(25, value);
    }

    /**
     * Getter for <code>res_app_package.sub_title</code>. 简介
     */
    public String getSubTitle() {
        return (String) get(25);
    }

    /**
     * Setter for <code>res_app_package.is_icon_valid</code>. 安卓应用包头像是否可用: 0-未知;
     * 1-可用; 2-不可用
     */
    public void setIsIconValid(Integer value) {
        set(26, value);
    }

    /**
     * Getter for <code>res_app_package.is_icon_valid</code>. 安卓应用包头像是否可用: 0-未知;
     * 1-可用; 2-不可用
     */
    public Integer getIsIconValid() {
        return (Integer) get(26);
    }

    /**
     * Setter for <code>res_app_package.device_app_store</code>. 已上架的安卓App
     * Store。存储格式  1,2,4  表示 小米、华为、VIVO 已上架
     */
    public void setDeviceAppStore(String value) {
        set(27, value);
    }

    /**
     * Getter for <code>res_app_package.device_app_store</code>. 已上架的安卓App
     * Store。存储格式  1,2,4  表示 小米、华为、VIVO 已上架
     */
    public String getDeviceAppStore() {
        return (String) get(27);
    }

    /**
     * Setter for <code>res_app_package.copy_source_account_id</code>. 复制来源账户id
     */
    public void setCopySourceAccountId(Integer value) {
        set(28, value);
    }

    /**
     * Getter for <code>res_app_package.copy_source_account_id</code>. 复制来源账户id
     */
    public Integer getCopySourceAccountId() {
        return (Integer) get(28);
    }

    /**
     * Setter for <code>res_app_package.record_number</code>. app备案号
     */
    public void setRecordNumber(String value) {
        set(29, value);
    }

    /**
     * Getter for <code>res_app_package.record_number</code>. app备案号
     */
    public String getRecordNumber() {
        return (String) get(29);
    }

    /**
     * Setter for <code>res_app_package.add_type</code>. 添加方式：链接添加(0)or手动上传(1)
     */
    public void setAddType(Integer value) {
        set(30, value);
    }

    /**
     * Getter for <code>res_app_package.add_type</code>. 添加方式：链接添加(0)or手动上传(1)
     */
    public Integer getAddType() {
        return (Integer) get(30);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ResAppPackageRecord
     */
    public ResAppPackageRecord() {
        super(TResAppPackage.RES_APP_PACKAGE);
    }

    /**
     * Create a detached, initialised ResAppPackageRecord
     */
    public ResAppPackageRecord(Integer id, Integer accountId, String name, String url, String packageName, String appName, Integer platform, String version, Integer size, String md5, String iconUrl, Timestamp ctime, Timestamp mtime, Integer isDeleted, String internalUrl, Integer status, Integer platformStatus, String developerName, String authorityUrl, String authCodeList, Timestamp apkUpdateTime, String privacyPolicy, Integer dmpAppId, Integer isNewFly, String description, String subTitle, Integer isIconValid, String deviceAppStore, Integer copySourceAccountId, String recordNumber, Integer addType) {
        super(TResAppPackage.RES_APP_PACKAGE);

        setId(id);
        setAccountId(accountId);
        setName(name);
        setUrl(url);
        setPackageName(packageName);
        setAppName(appName);
        setPlatform(platform);
        setVersion(version);
        setSize(size);
        setMd5(md5);
        setIconUrl(iconUrl);
        setCtime(ctime);
        setMtime(mtime);
        setIsDeleted(isDeleted);
        setInternalUrl(internalUrl);
        setStatus(status);
        setPlatformStatus(platformStatus);
        setDeveloperName(developerName);
        setAuthorityUrl(authorityUrl);
        setAuthCodeList(authCodeList);
        setApkUpdateTime(apkUpdateTime);
        setPrivacyPolicy(privacyPolicy);
        setDmpAppId(dmpAppId);
        setIsNewFly(isNewFly);
        setDescription(description);
        setSubTitle(subTitle);
        setIsIconValid(isIconValid);
        setDeviceAppStore(deviceAppStore);
        setCopySourceAccountId(copySourceAccountId);
        setRecordNumber(recordNumber);
        setAddType(addType);
    }

    /**
     * Create a detached, initialised ResAppPackageRecord
     */
    public ResAppPackageRecord(ResAppPackagePo value) {
        super(TResAppPackage.RES_APP_PACKAGE);

        if (value != null) {
            setId(value.getId());
            setAccountId(value.getAccountId());
            setName(value.getName());
            setUrl(value.getUrl());
            setPackageName(value.getPackageName());
            setAppName(value.getAppName());
            setPlatform(value.getPlatform());
            setVersion(value.getVersion());
            setSize(value.getSize());
            setMd5(value.getMd5());
            setIconUrl(value.getIconUrl());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setIsDeleted(value.getIsDeleted());
            setInternalUrl(value.getInternalUrl());
            setStatus(value.getStatus());
            setPlatformStatus(value.getPlatformStatus());
            setDeveloperName(value.getDeveloperName());
            setAuthorityUrl(value.getAuthorityUrl());
            setAuthCodeList(value.getAuthCodeList());
            setApkUpdateTime(value.getApkUpdateTime());
            setPrivacyPolicy(value.getPrivacyPolicy());
            setDmpAppId(value.getDmpAppId());
            setIsNewFly(value.getIsNewFly());
            setDescription(value.getDescription());
            setSubTitle(value.getSubTitle());
            setIsIconValid(value.getIsIconValid());
            setDeviceAppStore(value.getDeviceAppStore());
            setCopySourceAccountId(value.getCopySourceAccountId());
            setRecordNumber(value.getRecordNumber());
            setAddType(value.getAddType());
        }
    }
}
