/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TFlyAssistSearch;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.FlyAssistSearchPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.FlyAssistSearchRecord;


/**
 * 起飞辅助探索表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class FlyAssistSearchDao extends DAOImpl<FlyAssistSearchRecord, FlyAssistSearchPo, Long> {

    /**
     * Create a new FlyAssistSearchDao without any configuration
     */
    public FlyAssistSearchDao() {
        super(TFlyAssistSearch.FLY_ASSIST_SEARCH, FlyAssistSearchPo.class);
    }

    /**
     * Create a new FlyAssistSearchDao with an attached configuration
     */
    @Autowired
    public FlyAssistSearchDao(Configuration configuration) {
        super(TFlyAssistSearch.FLY_ASSIST_SEARCH, FlyAssistSearchPo.class, configuration);
    }

    @Override
    public Long getId(FlyAssistSearchPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<FlyAssistSearchPo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TFlyAssistSearch.FLY_ASSIST_SEARCH.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<FlyAssistSearchPo> fetchById(Long... values) {
        return fetch(TFlyAssistSearch.FLY_ASSIST_SEARCH.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public FlyAssistSearchPo fetchOneById(Long value) {
        return fetchOne(TFlyAssistSearch.FLY_ASSIST_SEARCH.ID, value);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<FlyAssistSearchPo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TFlyAssistSearch.FLY_ASSIST_SEARCH.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<FlyAssistSearchPo> fetchByUnitId(Integer... values) {
        return fetch(TFlyAssistSearch.FLY_ASSIST_SEARCH.UNIT_ID, values);
    }

    /**
     * Fetch a unique record that has <code>unit_id = value</code>
     */
    public FlyAssistSearchPo fetchOneByUnitId(Integer value) {
        return fetchOne(TFlyAssistSearch.FLY_ASSIST_SEARCH.UNIT_ID, value);
    }

    /**
     * Fetch records that have <code>is_search BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<FlyAssistSearchPo> fetchRangeOfIsSearch(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TFlyAssistSearch.FLY_ASSIST_SEARCH.IS_SEARCH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_search IN (values)</code>
     */
    public List<FlyAssistSearchPo> fetchByIsSearch(Integer... values) {
        return fetch(TFlyAssistSearch.FLY_ASSIST_SEARCH.IS_SEARCH, values);
    }

    /**
     * Fetch records that have <code>search_end_date BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<FlyAssistSearchPo> fetchRangeOfSearchEndDate(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TFlyAssistSearch.FLY_ASSIST_SEARCH.SEARCH_END_DATE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>search_end_date IN (values)</code>
     */
    public List<FlyAssistSearchPo> fetchBySearchEndDate(Timestamp... values) {
        return fetch(TFlyAssistSearch.FLY_ASSIST_SEARCH.SEARCH_END_DATE, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<FlyAssistSearchPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TFlyAssistSearch.FLY_ASSIST_SEARCH.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<FlyAssistSearchPo> fetchByCtime(Timestamp... values) {
        return fetch(TFlyAssistSearch.FLY_ASSIST_SEARCH.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<FlyAssistSearchPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TFlyAssistSearch.FLY_ASSIST_SEARCH.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<FlyAssistSearchPo> fetchByMtime(Timestamp... values) {
        return fetch(TFlyAssistSearch.FLY_ASSIST_SEARCH.MTIME, values);
    }
}
