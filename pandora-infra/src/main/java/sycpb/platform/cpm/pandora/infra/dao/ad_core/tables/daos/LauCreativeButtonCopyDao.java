/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeButtonCopy;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeButtonCopyPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeButtonCopyRecord;


/**
 * 创意按钮文案关联表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauCreativeButtonCopyDao extends DAOImpl<LauCreativeButtonCopyRecord, LauCreativeButtonCopyPo, Integer> {

    /**
     * Create a new LauCreativeButtonCopyDao without any configuration
     */
    public LauCreativeButtonCopyDao() {
        super(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY, LauCreativeButtonCopyPo.class);
    }

    /**
     * Create a new LauCreativeButtonCopyDao with an attached configuration
     */
    @Autowired
    public LauCreativeButtonCopyDao(Configuration configuration) {
        super(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY, LauCreativeButtonCopyPo.class, configuration);
    }

    @Override
    public Integer getId(LauCreativeButtonCopyPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchById(Integer... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauCreativeButtonCopyPo fetchOneById(Integer value) {
        return fetchOne(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.ID, value);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByUnitId(Integer... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByCreativeId(Integer... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.CREATIVE_ID, values);
    }

    /**
     * Fetch records that have <code>button_copy_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfButtonCopyId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.BUTTON_COPY_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>button_copy_id IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByButtonCopyId(Integer... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.BUTTON_COPY_ID, values);
    }

    /**
     * Fetch records that have <code>jump_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfJumpUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.JUMP_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>jump_url IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByJumpUrl(String... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.JUMP_URL, values);
    }

    /**
     * Fetch records that have <code>customized_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfCustomizedUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.CUSTOMIZED_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>customized_url IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByCustomizedUrl(String... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.CUSTOMIZED_URL, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.MTIME, values);
    }

    /**
     * Fetch records that have <code>button_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfButtonType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.BUTTON_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>button_type IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByButtonType(Integer... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.BUTTON_TYPE, values);
    }

    /**
     * Fetch records that have <code>extend_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfExtendUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.EXTEND_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>extend_url IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByExtendUrl(String... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.EXTEND_URL, values);
    }

    /**
     * Fetch records that have <code>android_jump_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfAndroidJumpUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.ANDROID_JUMP_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>android_jump_url IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByAndroidJumpUrl(String... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.ANDROID_JUMP_URL, values);
    }

    /**
     * Fetch records that have <code>ios_jump_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeButtonCopyPo> fetchRangeOfIosJumpUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.IOS_JUMP_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ios_jump_url IN (values)</code>
     */
    public List<LauCreativeButtonCopyPo> fetchByIosJumpUrl(String... values) {
        return fetch(TLauCreativeButtonCopy.LAU_CREATIVE_BUTTON_COPY.IOS_JUMP_URL, values);
    }
}
