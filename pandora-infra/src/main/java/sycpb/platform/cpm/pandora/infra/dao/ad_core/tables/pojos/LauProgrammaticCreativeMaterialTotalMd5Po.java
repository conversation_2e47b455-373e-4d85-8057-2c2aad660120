/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 程序化创意全物料md5信息表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauProgrammaticCreativeMaterialTotalMd5Po implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   accountId;
    private Integer   campaignId;
    private Integer   unitId;
    private Integer   creativeId;
    private String    totalMaterialMd5;
    private Integer   isDeleted;

    public LauProgrammaticCreativeMaterialTotalMd5Po() {}

    public LauProgrammaticCreativeMaterialTotalMd5Po(LauProgrammaticCreativeMaterialTotalMd5Po value) {
        this.id = value.id;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.accountId = value.accountId;
        this.campaignId = value.campaignId;
        this.unitId = value.unitId;
        this.creativeId = value.creativeId;
        this.totalMaterialMd5 = value.totalMaterialMd5;
        this.isDeleted = value.isDeleted;
    }

    public LauProgrammaticCreativeMaterialTotalMd5Po(
        Long      id,
        Timestamp ctime,
        Timestamp mtime,
        Integer   accountId,
        Integer   campaignId,
        Integer   unitId,
        Integer   creativeId,
        String    totalMaterialMd5,
        Integer   isDeleted
    ) {
        this.id = id;
        this.ctime = ctime;
        this.mtime = mtime;
        this.accountId = accountId;
        this.campaignId = campaignId;
        this.unitId = unitId;
        this.creativeId = creativeId;
        this.totalMaterialMd5 = totalMaterialMd5;
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_programmatic_creative_material_total_md5.id</code>.
     * 自增id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_programmatic_creative_material_total_md5.id</code>.
     * 自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for
     * <code>lau_programmatic_creative_material_total_md5.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for
     * <code>lau_programmatic_creative_material_total_md5.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for
     * <code>lau_programmatic_creative_material_total_md5.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for
     * <code>lau_programmatic_creative_material_total_md5.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for
     * <code>lau_programmatic_creative_material_total_md5.account_id</code>.
     * 账号ID
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for
     * <code>lau_programmatic_creative_material_total_md5.account_id</code>.
     * 账号ID
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for
     * <code>lau_programmatic_creative_material_total_md5.campaign_id</code>.
     * 计划ID
     */
    public Integer getCampaignId() {
        return this.campaignId;
    }

    /**
     * Setter for
     * <code>lau_programmatic_creative_material_total_md5.campaign_id</code>.
     * 计划ID
     */
    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    /**
     * Getter for
     * <code>lau_programmatic_creative_material_total_md5.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for
     * <code>lau_programmatic_creative_material_total_md5.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for
     * <code>lau_programmatic_creative_material_total_md5.creative_id</code>.
     * 创意ID
     */
    public Integer getCreativeId() {
        return this.creativeId;
    }

    /**
     * Setter for
     * <code>lau_programmatic_creative_material_total_md5.creative_id</code>.
     * 创意ID
     */
    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    /**
     * Getter for
     * <code>lau_programmatic_creative_material_total_md5.total_material_md5</code>.
     * 整个创意所有物料id拼接后的MD5值, 用来判断整个创意的物料是否相同 相似机审需求
     */
    public String getTotalMaterialMd5() {
        return this.totalMaterialMd5;
    }

    /**
     * Setter for
     * <code>lau_programmatic_creative_material_total_md5.total_material_md5</code>.
     * 整个创意所有物料id拼接后的MD5值, 用来判断整个创意的物料是否相同 相似机审需求
     */
    public void setTotalMaterialMd5(String totalMaterialMd5) {
        this.totalMaterialMd5 = totalMaterialMd5;
    }

    /**
     * Getter for
     * <code>lau_programmatic_creative_material_total_md5.is_deleted</code>.
     * 是否被删除 0-正常 1-被删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for
     * <code>lau_programmatic_creative_material_total_md5.is_deleted</code>.
     * 是否被删除 0-正常 1-被删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauProgrammaticCreativeMaterialTotalMd5Po (");

        sb.append(id);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(accountId);
        sb.append(", ").append(campaignId);
        sb.append(", ").append(unitId);
        sb.append(", ").append(creativeId);
        sb.append(", ").append(totalMaterialMd5);
        sb.append(", ").append(isDeleted);

        sb.append(")");
        return sb.toString();
    }
}
