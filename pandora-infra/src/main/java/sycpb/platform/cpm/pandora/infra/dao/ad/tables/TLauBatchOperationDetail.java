/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauBatchOperationDetailRecord;


/**
 * 批量操作明细
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauBatchOperationDetail extends TableImpl<LauBatchOperationDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_batch_operation_detail</code>
     */
    public static final TLauBatchOperationDetail LAU_BATCH_OPERATION_DETAIL = new TLauBatchOperationDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauBatchOperationDetailRecord> getRecordType() {
        return LauBatchOperationDetailRecord.class;
    }

    /**
     * The column <code>lau_batch_operation_detail.id</code>. ID
     */
    public final TableField<LauBatchOperationDetailRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "ID");

    /**
     * The column <code>lau_batch_operation_detail.ctime</code>. 添加时间
     */
    public final TableField<LauBatchOperationDetailRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column <code>lau_batch_operation_detail.mtime</code>. 更新时间
     */
    public final TableField<LauBatchOperationDetailRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>lau_batch_operation_detail.operation_id</code>. 操作id
     */
    public final TableField<LauBatchOperationDetailRecord, Long> OPERATION_ID = createField(DSL.name("operation_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "操作id");

    /**
     * The column <code>lau_batch_operation_detail.target_id</code>. 对象id
     */
    public final TableField<LauBatchOperationDetailRecord, Integer> TARGET_ID = createField(DSL.name("target_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "对象id");

    /**
     * The column <code>lau_batch_operation_detail.operation_status</code>.
     * 操作状态: 0-未知, 1-成功, 2-失败
     */
    public final TableField<LauBatchOperationDetailRecord, Integer> OPERATION_STATUS = createField(DSL.name("operation_status"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "操作状态: 0-未知, 1-成功, 2-失败");

    /**
     * The column <code>lau_batch_operation_detail.operation_ext</code>. 操作额外信息,
     * 包含错误信息, 追踪信息等, 改动前后信息
     */
    public final TableField<LauBatchOperationDetailRecord, String> OPERATION_EXT = createField(DSL.name("operation_ext"), SQLDataType.CLOB.nullable(false).defaultValue(DSL.inline("''", SQLDataType.CLOB)), this, "操作额外信息, 包含错误信息, 追踪信息等, 改动前后信息");

    private TLauBatchOperationDetail(Name alias, Table<LauBatchOperationDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauBatchOperationDetail(Name alias, Table<LauBatchOperationDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("批量操作明细"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_batch_operation_detail</code> table reference
     */
    public TLauBatchOperationDetail(String alias) {
        this(DSL.name(alias), LAU_BATCH_OPERATION_DETAIL);
    }

    /**
     * Create an aliased <code>lau_batch_operation_detail</code> table reference
     */
    public TLauBatchOperationDetail(Name alias) {
        this(alias, LAU_BATCH_OPERATION_DETAIL);
    }

    /**
     * Create a <code>lau_batch_operation_detail</code> table reference
     */
    public TLauBatchOperationDetail() {
        this(DSL.name("lau_batch_operation_detail"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauBatchOperationDetailRecord, Long> getIdentity() {
        return (Identity<LauBatchOperationDetailRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauBatchOperationDetailRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauBatchOperationDetail.LAU_BATCH_OPERATION_DETAIL, DSL.name("KEY_lau_batch_operation_detail_PRIMARY"), new TableField[] { TLauBatchOperationDetail.LAU_BATCH_OPERATION_DETAIL.ID }, true);
    }

    @Override
    public TLauBatchOperationDetail as(String alias) {
        return new TLauBatchOperationDetail(DSL.name(alias), this);
    }

    @Override
    public TLauBatchOperationDetail as(Name alias) {
        return new TLauBatchOperationDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauBatchOperationDetail rename(String name) {
        return new TLauBatchOperationDetail(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauBatchOperationDetail rename(Name name) {
        return new TLauBatchOperationDetail(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<Long, Timestamp, Timestamp, Long, Integer, Integer, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
