/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeImage;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeImagePo;


/**
 * 创意图片关联表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeImageRecord extends UpdatableRecordImpl<LauCreativeImageRecord> implements Record14<Integer, Integer, Integer, String, String, Integer, Integer, Timestamp, Timestamp, Integer, Long, Long, Integer, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_image.id</code>. 自增id
     */
    public void setId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_image.id</code>. 自增id
     */
    public Integer getId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_creative_image.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_creative_image.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_creative_image.creative_id</code>. 创意ID
     */
    public void setCreativeId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_creative_image.creative_id</code>. 创意ID
     */
    public Integer getCreativeId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_creative_image.image_url</code>. 图片URL
     */
    public void setImageUrl(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_creative_image.image_url</code>. 图片URL
     */
    public String getImageUrl() {
        return (String) get(3);
    }

    /**
     * Setter for <code>lau_creative_image.image_md5</code>. 图片的MD5值
     */
    public void setImageMd5(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_creative_image.image_md5</code>. 图片的MD5值
     */
    public String getImageMd5() {
        return (String) get(4);
    }

    /**
     * Setter for <code>lau_creative_image.type</code>. 图片类型：1-单列，2-双列
     */
    public void setType(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_creative_image.type</code>. 图片类型：1-单列，2-双列
     */
    public Integer getType() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_creative_image.is_deleted</code>. 软删除，0是有效,1是删除
     */
    public void setIsDeleted(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_creative_image.is_deleted</code>. 软删除，0是有效,1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lau_creative_image.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_creative_image.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(7);
    }

    /**
     * Setter for <code>lau_creative_image.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_creative_image.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(8);
    }

    /**
     * Setter for <code>lau_creative_image.mgk_template_id</code>. 素材中心模板id
     */
    public void setMgkTemplateId(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_creative_image.mgk_template_id</code>. 素材中心模板id
     */
    public Integer getMgkTemplateId() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>lau_creative_image.mgk_media_id</code>. 创意中心媒体id
     */
    public void setMgkMediaId(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_creative_image.mgk_media_id</code>. 创意中心媒体id
     */
    public Long getMgkMediaId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>lau_creative_image.material_id</code>.
     * 物料ID(参考lau_material)
     */
    public void setMaterialId(Long value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_creative_image.material_id</code>.
     * 物料ID(参考lau_material)
     */
    public Long getMaterialId() {
        return (Long) get(11);
    }

    /**
     * Setter for <code>lau_creative_image.is_use_smart_cut</code>.
     * 是否使用智能剪裁，0是不使用,1是使用
     */
    public void setIsUseSmartCut(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_creative_image.is_use_smart_cut</code>.
     * 是否使用智能剪裁，0是不使用,1是使用
     */
    public Integer getIsUseSmartCut() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>lau_creative_image.is_aigc_replace</code>. 是否为aigc替换图片
     */
    public void setIsAigcReplace(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_creative_image.is_aigc_replace</code>. 是否为aigc替换图片
     */
    public Integer getIsAigcReplace() {
        return (Integer) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<Integer, Integer, Integer, String, String, Integer, Integer, Timestamp, Timestamp, Integer, Long, Long, Integer, Integer> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<Integer, Integer, Integer, String, String, Integer, Integer, Timestamp, Timestamp, Integer, Long, Long, Integer, Integer> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<Integer> field1() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.UNIT_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.CREATIVE_ID;
    }

    @Override
    public Field<String> field4() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.IMAGE_URL;
    }

    @Override
    public Field<String> field5() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.IMAGE_MD5;
    }

    @Override
    public Field<Integer> field6() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.TYPE;
    }

    @Override
    public Field<Integer> field7() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field8() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.CTIME;
    }

    @Override
    public Field<Timestamp> field9() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.MTIME;
    }

    @Override
    public Field<Integer> field10() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.MGK_TEMPLATE_ID;
    }

    @Override
    public Field<Long> field11() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.MGK_MEDIA_ID;
    }

    @Override
    public Field<Long> field12() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.MATERIAL_ID;
    }

    @Override
    public Field<Integer> field13() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.IS_USE_SMART_CUT;
    }

    @Override
    public Field<Integer> field14() {
        return TLauCreativeImage.LAU_CREATIVE_IMAGE.IS_AIGC_REPLACE;
    }

    @Override
    public Integer component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getUnitId();
    }

    @Override
    public Integer component3() {
        return getCreativeId();
    }

    @Override
    public String component4() {
        return getImageUrl();
    }

    @Override
    public String component5() {
        return getImageMd5();
    }

    @Override
    public Integer component6() {
        return getType();
    }

    @Override
    public Integer component7() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component8() {
        return getCtime();
    }

    @Override
    public Timestamp component9() {
        return getMtime();
    }

    @Override
    public Integer component10() {
        return getMgkTemplateId();
    }

    @Override
    public Long component11() {
        return getMgkMediaId();
    }

    @Override
    public Long component12() {
        return getMaterialId();
    }

    @Override
    public Integer component13() {
        return getIsUseSmartCut();
    }

    @Override
    public Integer component14() {
        return getIsAigcReplace();
    }

    @Override
    public Integer value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getUnitId();
    }

    @Override
    public Integer value3() {
        return getCreativeId();
    }

    @Override
    public String value4() {
        return getImageUrl();
    }

    @Override
    public String value5() {
        return getImageMd5();
    }

    @Override
    public Integer value6() {
        return getType();
    }

    @Override
    public Integer value7() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value8() {
        return getCtime();
    }

    @Override
    public Timestamp value9() {
        return getMtime();
    }

    @Override
    public Integer value10() {
        return getMgkTemplateId();
    }

    @Override
    public Long value11() {
        return getMgkMediaId();
    }

    @Override
    public Long value12() {
        return getMaterialId();
    }

    @Override
    public Integer value13() {
        return getIsUseSmartCut();
    }

    @Override
    public Integer value14() {
        return getIsAigcReplace();
    }

    @Override
    public LauCreativeImageRecord value1(Integer value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value2(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value3(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value4(String value) {
        setImageUrl(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value5(String value) {
        setImageMd5(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value6(Integer value) {
        setType(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value7(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value8(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value9(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value10(Integer value) {
        setMgkTemplateId(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value11(Long value) {
        setMgkMediaId(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value12(Long value) {
        setMaterialId(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value13(Integer value) {
        setIsUseSmartCut(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord value14(Integer value) {
        setIsAigcReplace(value);
        return this;
    }

    @Override
    public LauCreativeImageRecord values(Integer value1, Integer value2, Integer value3, String value4, String value5, Integer value6, Integer value7, Timestamp value8, Timestamp value9, Integer value10, Long value11, Long value12, Integer value13, Integer value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativeImageRecord
     */
    public LauCreativeImageRecord() {
        super(TLauCreativeImage.LAU_CREATIVE_IMAGE);
    }

    /**
     * Create a detached, initialised LauCreativeImageRecord
     */
    public LauCreativeImageRecord(Integer id, Integer unitId, Integer creativeId, String imageUrl, String imageMd5, Integer type, Integer isDeleted, Timestamp ctime, Timestamp mtime, Integer mgkTemplateId, Long mgkMediaId, Long materialId, Integer isUseSmartCut, Integer isAigcReplace) {
        super(TLauCreativeImage.LAU_CREATIVE_IMAGE);

        setId(id);
        setUnitId(unitId);
        setCreativeId(creativeId);
        setImageUrl(imageUrl);
        setImageMd5(imageMd5);
        setType(type);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setMgkTemplateId(mgkTemplateId);
        setMgkMediaId(mgkMediaId);
        setMaterialId(materialId);
        setIsUseSmartCut(isUseSmartCut);
        setIsAigcReplace(isAigcReplace);
    }

    /**
     * Create a detached, initialised LauCreativeImageRecord
     */
    public LauCreativeImageRecord(LauCreativeImagePo value) {
        super(TLauCreativeImage.LAU_CREATIVE_IMAGE);

        if (value != null) {
            setId(value.getId());
            setUnitId(value.getUnitId());
            setCreativeId(value.getCreativeId());
            setImageUrl(value.getImageUrl());
            setImageMd5(value.getImageMd5());
            setType(value.getType());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setMgkTemplateId(value.getMgkTemplateId());
            setMgkMediaId(value.getMgkMediaId());
            setMaterialId(value.getMaterialId());
            setIsUseSmartCut(value.getIsUseSmartCut());
            setIsAigcReplace(value.getIsAigcReplace());
        }
    }
}
