/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row20;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeExtraRecord;


/**
 * 创意层级额外信息
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauCreativeExtra extends TableImpl<LauCreativeExtraRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_creative_extra</code>
     */
    public static final TLauCreativeExtra LAU_CREATIVE_EXTRA = new TLauCreativeExtra();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauCreativeExtraRecord> getRecordType() {
        return LauCreativeExtraRecord.class;
    }

    /**
     * The column <code>lau_creative_extra.id</code>. 主键
     */
    public final TableField<LauCreativeExtraRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键");

    /**
     * The column <code>lau_creative_extra.creative_id</code>. 创意id
     */
    public final TableField<LauCreativeExtraRecord, Integer> CREATIVE_ID = createField(DSL.name("creative_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "创意id");

    /**
     * The column <code>lau_creative_extra.account_id</code>. 账户id
     */
    public final TableField<LauCreativeExtraRecord, Integer> ACCOUNT_ID = createField(DSL.name("account_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "账户id");

    /**
     * The column <code>lau_creative_extra.campaign_id</code>. 计划id
     */
    public final TableField<LauCreativeExtraRecord, Integer> CAMPAIGN_ID = createField(DSL.name("campaign_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "计划id");

    /**
     * The column <code>lau_creative_extra.unit_id</code>. 单位ID
     */
    public final TableField<LauCreativeExtraRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单位ID");

    /**
     * The column <code>lau_creative_extra.image_macro_type</code>. 图片宏类型 1-图片
     * 2-gif
     */
    public final TableField<LauCreativeExtraRecord, Integer> IMAGE_MACRO_TYPE = createField(DSL.name("image_macro_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "图片宏类型 1-图片 2-gif");

    /**
     * The column <code>lau_creative_extra.image_macro</code>. 图片宏占位符
     */
    public final TableField<LauCreativeExtraRecord, String> IMAGE_MACRO = createField(DSL.name("image_macro"), SQLDataType.VARCHAR(128).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "图片宏占位符");

    /**
     * The column <code>lau_creative_extra.is_deleted</code>. 软删除 0有效 1删除
     */
    public final TableField<LauCreativeExtraRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除 0有效 1删除");

    /**
     * The column <code>lau_creative_extra.ctime</code>. 创建时间
     */
    public final TableField<LauCreativeExtraRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_creative_extra.mtime</code>. 修改时间
     */
    public final TableField<LauCreativeExtraRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>lau_creative_extra.qualification_package_id</code>.
     * 资质包id
     */
    public final TableField<LauCreativeExtraRecord, Integer> QUALIFICATION_PACKAGE_ID = createField(DSL.name("qualification_package_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "资质包id");

    /**
     * The column <code>lau_creative_extra.ad_space_id</code>. 用户自定义商业空间id
     */
    public final TableField<LauCreativeExtraRecord, Integer> AD_SPACE_ID = createField(DSL.name("ad_space_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "用户自定义商业空间id");

    /**
     * The column <code>lau_creative_extra.bili_space_mid</code>. b站空间mid
     */
    public final TableField<LauCreativeExtraRecord, Long> BILI_SPACE_MID = createField(DSL.name("bili_space_mid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "b站空间mid");

    /**
     * The column <code>lau_creative_extra.raw_jump_url</code>.
     * 用户填写的原始落地页(未添加任何宏参数)
     */
    public final TableField<LauCreativeExtraRecord, String> RAW_JUMP_URL = createField(DSL.name("raw_jump_url"), SQLDataType.VARCHAR(3072).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "用户填写的原始落地页(未添加任何宏参数)");

    /**
     * The column <code>lau_creative_extra.mgk_page_id</code>.
     * 建站页id(仅adp_version&gt;=6的数据有值)
     */
    public final TableField<LauCreativeExtraRecord, Long> MGK_PAGE_ID = createField(DSL.name("mgk_page_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "建站页id(仅adp_version>=6的数据有值)");

    /**
     * The column <code>lau_creative_extra.season_avid</code>. 课程avid
     */
    public final TableField<LauCreativeExtraRecord, Long> SEASON_AVID = createField(DSL.name("season_avid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "课程avid");

    /**
     * The column <code>lau_creative_extra.season_id</code>. 课程id
     */
    public final TableField<LauCreativeExtraRecord, Long> SEASON_ID = createField(DSL.name("season_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "课程id");

    /**
     * The column <code>lau_creative_extra.parent_creative_id</code>. 母创意id
     */
    public final TableField<LauCreativeExtraRecord, Integer> PARENT_CREATIVE_ID = createField(DSL.name("parent_creative_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "母创意id");

    /**
     * The column <code>lau_creative_extra.ppc_type</code>. 落地页类型，1-稿件播放页
     * 2-图文详情页 3-直播间 4-内容投放 5-游戏中心 6-其它
     */
    public final TableField<LauCreativeExtraRecord, Integer> PPC_TYPE = createField(DSL.name("ppc_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("6", SQLDataType.INTEGER)), this, "落地页类型，1-稿件播放页 2-图文详情页 3-直播间 4-内容投放 5-游戏中心 6-其它");

    /**
     * The column <code>lau_creative_extra.is_smart_derivative</code>.
     * 是否使用智能衍生，0是不使用,1是使用
     */
    public final TableField<LauCreativeExtraRecord, Integer> IS_SMART_DERIVATIVE = createField(DSL.name("is_smart_derivative"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否使用智能衍生，0是不使用,1是使用");

    private TLauCreativeExtra(Name alias, Table<LauCreativeExtraRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauCreativeExtra(Name alias, Table<LauCreativeExtraRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("创意层级额外信息"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_creative_extra</code> table reference
     */
    public TLauCreativeExtra(String alias) {
        this(DSL.name(alias), LAU_CREATIVE_EXTRA);
    }

    /**
     * Create an aliased <code>lau_creative_extra</code> table reference
     */
    public TLauCreativeExtra(Name alias) {
        this(alias, LAU_CREATIVE_EXTRA);
    }

    /**
     * Create a <code>lau_creative_extra</code> table reference
     */
    public TLauCreativeExtra() {
        this(DSL.name("lau_creative_extra"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauCreativeExtraRecord, Long> getIdentity() {
        return (Identity<LauCreativeExtraRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauCreativeExtraRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauCreativeExtra.LAU_CREATIVE_EXTRA, DSL.name("KEY_lau_creative_extra_PRIMARY"), new TableField[] { TLauCreativeExtra.LAU_CREATIVE_EXTRA.ID }, true);
    }

    @Override
    public List<UniqueKey<LauCreativeExtraRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TLauCreativeExtra.LAU_CREATIVE_EXTRA, DSL.name("KEY_lau_creative_extra_uk_creative_id"), new TableField[] { TLauCreativeExtra.LAU_CREATIVE_EXTRA.CREATIVE_ID }, true)
        );
    }

    @Override
    public TLauCreativeExtra as(String alias) {
        return new TLauCreativeExtra(DSL.name(alias), this);
    }

    @Override
    public TLauCreativeExtra as(Name alias) {
        return new TLauCreativeExtra(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeExtra rename(String name) {
        return new TLauCreativeExtra(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeExtra rename(Name name) {
        return new TLauCreativeExtra(name, null);
    }

    // -------------------------------------------------------------------------
    // Row20 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row20<Long, Integer, Integer, Integer, Integer, Integer, String, Integer, Timestamp, Timestamp, Integer, Integer, Long, String, Long, Long, Long, Integer, Integer, Integer> fieldsRow() {
        return (Row20) super.fieldsRow();
    }
}
