/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauStoryImageComponent;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauStoryImageComponentPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauStoryImageComponentRecord;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauStoryImageComponentDao extends DAOImpl<LauStoryImageComponentRecord, LauStoryImageComponentPo, Long> {

    /**
     * Create a new LauStoryImageComponentDao without any configuration
     */
    public LauStoryImageComponentDao() {
        super(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT, LauStoryImageComponentPo.class);
    }

    /**
     * Create a new LauStoryImageComponentDao with an attached configuration
     */
    @Autowired
    public LauStoryImageComponentDao(Configuration configuration) {
        super(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT, LauStoryImageComponentPo.class, configuration);
    }

    @Override
    public Long getId(LauStoryImageComponentPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchById(Long... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauStoryImageComponentPo fetchOneById(Long value) {
        return fetchOne(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.ID, value);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.MTIME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByAccountId(Integer... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>component_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfComponentId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.COMPONENT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>component_id IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByComponentId(Long... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.COMPONENT_ID, values);
    }

    /**
     * Fetch records that have <code>component_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfComponentName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.COMPONENT_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>component_name IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByComponentName(String... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.COMPONENT_NAME, values);
    }

    /**
     * Fetch records that have <code>image_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfImageUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.IMAGE_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_url IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByImageUrl(String... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.IMAGE_URL, values);
    }

    /**
     * Fetch records that have <code>image_md5 BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfImageMd5(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.IMAGE_MD5, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_md5 IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByImageMd5(String... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.IMAGE_MD5, values);
    }

    /**
     * Fetch records that have <code>is_contains_goods BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfIsContainsGoods(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.IS_CONTAINS_GOODS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_contains_goods IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByIsContainsGoods(Integer... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.IS_CONTAINS_GOODS, values);
    }

    /**
     * Fetch records that have <code>item_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfItemId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.ITEM_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>item_id IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByItemId(Long... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.ITEM_ID, values);
    }

    /**
     * Fetch records that have <code>item_source BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfItemSource(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.ITEM_SOURCE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>item_source IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByItemSource(Integer... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.ITEM_SOURCE, values);
    }

    /**
     * Fetch records that have <code>jump_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfJumpType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.JUMP_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>jump_type IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByJumpType(Integer... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.JUMP_TYPE, values);
    }

    /**
     * Fetch records that have <code>jump_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfJumpUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.JUMP_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>jump_url IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByJumpUrl(String... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.JUMP_URL, values);
    }

    /**
     * Fetch records that have <code>schema_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfSchemaUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.SCHEMA_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>schema_url IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchBySchemaUrl(String... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.SCHEMA_URL, values);
    }

    /**
     * Fetch records that have <code>mini_program_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfMiniProgramId(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.MINI_PROGRAM_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mini_program_id IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByMiniProgramId(String... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.MINI_PROGRAM_ID, values);
    }

    /**
     * Fetch records that have <code>mini_program_name BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfMiniProgramName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.MINI_PROGRAM_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mini_program_name IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByMiniProgramName(String... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.MINI_PROGRAM_NAME, values);
    }

    /**
     * Fetch records that have <code>mini_program_path BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauStoryImageComponentPo> fetchRangeOfMiniProgramPath(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.MINI_PROGRAM_PATH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mini_program_path IN (values)</code>
     */
    public List<LauStoryImageComponentPo> fetchByMiniProgramPath(String... values) {
        return fetch(TLauStoryImageComponent.LAU_STORY_IMAGE_COMPONENT.MINI_PROGRAM_PATH, values);
    }
}
