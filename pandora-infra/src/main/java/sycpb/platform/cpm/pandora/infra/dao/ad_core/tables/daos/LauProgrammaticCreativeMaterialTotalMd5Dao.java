/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauProgrammaticCreativeMaterialTotalMd5;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauProgrammaticCreativeMaterialTotalMd5Po;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauProgrammaticCreativeMaterialTotalMd5Record;


/**
 * 程序化创意全物料md5信息表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauProgrammaticCreativeMaterialTotalMd5Dao extends DAOImpl<LauProgrammaticCreativeMaterialTotalMd5Record, LauProgrammaticCreativeMaterialTotalMd5Po, Long> {

    /**
     * Create a new LauProgrammaticCreativeMaterialTotalMd5Dao without any
     * configuration
     */
    public LauProgrammaticCreativeMaterialTotalMd5Dao() {
        super(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5, LauProgrammaticCreativeMaterialTotalMd5Po.class);
    }

    /**
     * Create a new LauProgrammaticCreativeMaterialTotalMd5Dao with an attached
     * configuration
     */
    @Autowired
    public LauProgrammaticCreativeMaterialTotalMd5Dao(Configuration configuration) {
        super(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5, LauProgrammaticCreativeMaterialTotalMd5Po.class, configuration);
    }

    @Override
    public Long getId(LauProgrammaticCreativeMaterialTotalMd5Po object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchById(Long... values) {
        return fetch(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauProgrammaticCreativeMaterialTotalMd5Po fetchOneById(Long value) {
        return fetchOne(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.ID, value);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchByCtime(Timestamp... values) {
        return fetch(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchByMtime(Timestamp... values) {
        return fetch(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.MTIME, values);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchByAccountId(Integer... values) {
        return fetch(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>campaign_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchRangeOfCampaignId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.CAMPAIGN_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>campaign_id IN (values)</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchByCampaignId(Integer... values) {
        return fetch(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.CAMPAIGN_ID, values);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchByUnitId(Integer... values) {
        return fetch(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchByCreativeId(Integer... values) {
        return fetch(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.CREATIVE_ID, values);
    }

    /**
     * Fetch a unique record that has <code>creative_id = value</code>
     */
    public LauProgrammaticCreativeMaterialTotalMd5Po fetchOneByCreativeId(Integer value) {
        return fetchOne(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.CREATIVE_ID, value);
    }

    /**
     * Fetch records that have <code>total_material_md5 BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchRangeOfTotalMaterialMd5(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.TOTAL_MATERIAL_MD5, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>total_material_md5 IN (values)</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchByTotalMaterialMd5(String... values) {
        return fetch(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.TOTAL_MATERIAL_MD5, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauProgrammaticCreativeMaterialTotalMd5Po> fetchByIsDeleted(Integer... values) {
        return fetch(TLauProgrammaticCreativeMaterialTotalMd5.LAU_PROGRAMMATIC_CREATIVE_MATERIAL_TOTAL_MD5.IS_DELETED, values);
    }
}
