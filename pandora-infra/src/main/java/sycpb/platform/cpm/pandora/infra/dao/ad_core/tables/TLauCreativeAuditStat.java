/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row10;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeAuditStatRecord;


/**
 * 创意审核统计表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauCreativeAuditStat extends TableImpl<LauCreativeAuditStatRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_creative_audit_stat</code>
     */
    public static final TLauCreativeAuditStat LAU_CREATIVE_AUDIT_STAT = new TLauCreativeAuditStat();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauCreativeAuditStatRecord> getRecordType() {
        return LauCreativeAuditStatRecord.class;
    }

    /**
     * The column <code>lau_creative_audit_stat.id</code>. 主键
     */
    public final TableField<LauCreativeAuditStatRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键");

    /**
     * The column <code>lau_creative_audit_stat.event</code>. 事件类型: 1-进入审核,
     * 2-通过, 3-驳回, 4-超时
     */
    public final TableField<LauCreativeAuditStatRecord, Integer> EVENT = createField(DSL.name("event"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "事件类型: 1-进入审核, 2-通过, 3-驳回, 4-超时");

    /**
     * The column <code>lau_creative_audit_stat.operator_username</code>. 操作员
     */
    public final TableField<LauCreativeAuditStatRecord, String> OPERATOR_USERNAME = createField(DSL.name("operator_username"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "操作员");

    /**
     * The column <code>lau_creative_audit_stat.account_id</code>. 账户id
     */
    public final TableField<LauCreativeAuditStatRecord, Integer> ACCOUNT_ID = createField(DSL.name("account_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "账户id");

    /**
     * The column <code>lau_creative_audit_stat.campaign_id</code>. 计划id
     */
    public final TableField<LauCreativeAuditStatRecord, Integer> CAMPAIGN_ID = createField(DSL.name("campaign_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "计划id");

    /**
     * The column <code>lau_creative_audit_stat.unit_id</code>. 单元id
     */
    public final TableField<LauCreativeAuditStatRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元id");

    /**
     * The column <code>lau_creative_audit_stat.creative_id</code>. 创意id
     */
    public final TableField<LauCreativeAuditStatRecord, Integer> CREATIVE_ID = createField(DSL.name("creative_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "创意id");

    /**
     * The column <code>lau_creative_audit_stat.is_deleted</code>. 软删除:1-有效,
     * 2-删除
     */
    public final TableField<LauCreativeAuditStatRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除:1-有效, 2-删除");

    /**
     * The column <code>lau_creative_audit_stat.ctime</code>. 创建时间
     */
    public final TableField<LauCreativeAuditStatRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_creative_audit_stat.mtime</code>. 修改时间
     */
    public final TableField<LauCreativeAuditStatRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    private TLauCreativeAuditStat(Name alias, Table<LauCreativeAuditStatRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauCreativeAuditStat(Name alias, Table<LauCreativeAuditStatRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("创意审核统计表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_creative_audit_stat</code> table reference
     */
    public TLauCreativeAuditStat(String alias) {
        this(DSL.name(alias), LAU_CREATIVE_AUDIT_STAT);
    }

    /**
     * Create an aliased <code>lau_creative_audit_stat</code> table reference
     */
    public TLauCreativeAuditStat(Name alias) {
        this(alias, LAU_CREATIVE_AUDIT_STAT);
    }

    /**
     * Create a <code>lau_creative_audit_stat</code> table reference
     */
    public TLauCreativeAuditStat() {
        this(DSL.name("lau_creative_audit_stat"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauCreativeAuditStatRecord, Long> getIdentity() {
        return (Identity<LauCreativeAuditStatRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauCreativeAuditStatRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT, DSL.name("KEY_lau_creative_audit_stat_PRIMARY"), new TableField[] { TLauCreativeAuditStat.LAU_CREATIVE_AUDIT_STAT.ID }, true);
    }

    @Override
    public TLauCreativeAuditStat as(String alias) {
        return new TLauCreativeAuditStat(DSL.name(alias), this);
    }

    @Override
    public TLauCreativeAuditStat as(Name alias) {
        return new TLauCreativeAuditStat(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeAuditStat rename(String name) {
        return new TLauCreativeAuditStat(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeAuditStat rename(Name name) {
        return new TLauCreativeAuditStat(name, null);
    }

    // -------------------------------------------------------------------------
    // Row10 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row10<Long, Integer, String, Integer, Integer, Integer, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row10) super.fieldsRow();
    }
}
