/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauUnderframeComponent;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauUnderframeComponentPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauUnderframeComponentRecord;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauUnderframeComponentDao extends DAOImpl<LauUnderframeComponentRecord, LauUnderframeComponentPo, Long> {

    /**
     * Create a new LauUnderframeComponentDao without any configuration
     */
    public LauUnderframeComponentDao() {
        super(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT, LauUnderframeComponentPo.class);
    }

    /**
     * Create a new LauUnderframeComponentDao with an attached configuration
     */
    @Autowired
    public LauUnderframeComponentDao(Configuration configuration) {
        super(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT, LauUnderframeComponentPo.class, configuration);
    }

    @Override
    public Long getId(LauUnderframeComponentPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchById(Long... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauUnderframeComponentPo fetchOneById(Long value) {
        return fetchOne(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.ID, value);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.MTIME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByAccountId(Integer... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>component_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfComponentName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.COMPONENT_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>component_name IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByComponentName(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.COMPONENT_NAME, values);
    }

    /**
     * Fetch records that have <code>component_hash BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfComponentHash(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.COMPONENT_HASH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>component_hash IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByComponentHash(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.COMPONENT_HASH, values);
    }

    /**
     * Fetch records that have <code>promotion_purpose_type BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfPromotionPurposeType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.PROMOTION_PURPOSE_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>promotion_purpose_type IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByPromotionPurposeType(Integer... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.PROMOTION_PURPOSE_TYPE, values);
    }

    /**
     * Fetch records that have <code>title BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfTitle(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.TITLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>title IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByTitle(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.TITLE, values);
    }

    /**
     * Fetch records that have <code>description BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfDescription(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.DESCRIPTION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>description IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByDescription(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.DESCRIPTION, values);
    }

    /**
     * Fetch records that have <code>image_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfImageUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.IMAGE_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_url IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByImageUrl(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.IMAGE_URL, values);
    }

    /**
     * Fetch records that have <code>image_md5 BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfImageMd5(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.IMAGE_MD5, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_md5 IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByImageMd5(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.IMAGE_MD5, values);
    }

    /**
     * Fetch records that have <code>raw_jump_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfRawJumpUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.RAW_JUMP_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>raw_jump_url IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByRawJumpUrl(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.RAW_JUMP_URL, values);
    }

    /**
     * Fetch records that have <code>jump_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfJumpUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.JUMP_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>jump_url IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByJumpUrl(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.JUMP_URL, values);
    }

    /**
     * Fetch records that have <code>mgk_page_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfMgkPageId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.MGK_PAGE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mgk_page_id IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByMgkPageId(Long... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.MGK_PAGE_ID, values);
    }

    /**
     * Fetch records that have <code>button_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfButtonId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.BUTTON_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>button_id IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByButtonId(Integer... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.BUTTON_ID, values);
    }

    /**
     * Fetch records that have <code>button_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfButtonType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.BUTTON_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>button_type IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByButtonType(Integer... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.BUTTON_TYPE, values);
    }

    /**
     * Fetch records that have <code>button_text BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfButtonText(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.BUTTON_TEXT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>button_text IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByButtonText(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.BUTTON_TEXT, values);
    }

    /**
     * Fetch records that have <code>game_base_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfGameBaseId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.GAME_BASE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>game_base_id IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByGameBaseId(Integer... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.GAME_BASE_ID, values);
    }

    /**
     * Fetch records that have <code>app_package_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfAppPackageId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.APP_PACKAGE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>app_package_id IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByAppPackageId(Integer... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.APP_PACKAGE_ID, values);
    }

    /**
     * Fetch records that have <code>call_up_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfCallUpUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.CALL_UP_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>call_up_url IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByCallUpUrl(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.CALL_UP_URL, values);
    }

    /**
     * Fetch records that have <code>sub_pkg BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfSubPkg(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.SUB_PKG, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sub_pkg IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchBySubPkg(Integer... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.SUB_PKG, values);
    }

    /**
     * Fetch records that have <code>customized_imp_url BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfCustomizedImpUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.CUSTOMIZED_IMP_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>customized_imp_url IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByCustomizedImpUrl(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.CUSTOMIZED_IMP_URL, values);
    }

    /**
     * Fetch records that have <code>customized_click_url BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauUnderframeComponentPo> fetchRangeOfCustomizedClickUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.CUSTOMIZED_CLICK_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>customized_click_url IN (values)</code>
     */
    public List<LauUnderframeComponentPo> fetchByCustomizedClickUrl(String... values) {
        return fetch(TLauUnderframeComponent.LAU_UNDERFRAME_COMPONENT.CUSTOMIZED_CLICK_URL, values);
    }
}
