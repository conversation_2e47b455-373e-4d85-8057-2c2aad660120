/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 单元-行业优选离线表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitTargetProfessionInterestAutoPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer   id;
    private Integer   accountId;
    private Integer   unitId;
    private Integer   crowdId;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   isDeleted;
    private Integer   isComand;

    public LauUnitTargetProfessionInterestAutoPo() {}

    public LauUnitTargetProfessionInterestAutoPo(LauUnitTargetProfessionInterestAutoPo value) {
        this.id = value.id;
        this.accountId = value.accountId;
        this.unitId = value.unitId;
        this.crowdId = value.crowdId;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.isDeleted = value.isDeleted;
        this.isComand = value.isComand;
    }

    public LauUnitTargetProfessionInterestAutoPo(
        Integer   id,
        Integer   accountId,
        Integer   unitId,
        Integer   crowdId,
        Timestamp ctime,
        Timestamp mtime,
        Integer   isDeleted,
        Integer   isComand
    ) {
        this.id = id;
        this.accountId = accountId;
        this.unitId = unitId;
        this.crowdId = crowdId;
        this.ctime = ctime;
        this.mtime = mtime;
        this.isDeleted = isDeleted;
        this.isComand = isComand;
    }

    /**
     * Getter for <code>lau_unit_target_profession_interest_auto.id</code>.
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_unit_target_profession_interest_auto.id</code>.
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Getter for
     * <code>lau_unit_target_profession_interest_auto.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for
     * <code>lau_unit_target_profession_interest_auto.account_id</code>. 账户id
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>lau_unit_target_profession_interest_auto.unit_id</code>.
     * 单元ID
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_unit_target_profession_interest_auto.unit_id</code>.
     * 单元ID
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for
     * <code>lau_unit_target_profession_interest_auto.crowd_id</code>. 人群包ID
     */
    public Integer getCrowdId() {
        return this.crowdId;
    }

    /**
     * Setter for
     * <code>lau_unit_target_profession_interest_auto.crowd_id</code>. 人群包ID
     */
    public void setCrowdId(Integer crowdId) {
        this.crowdId = crowdId;
    }

    /**
     * Getter for <code>lau_unit_target_profession_interest_auto.ctime</code>.
     * 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_unit_target_profession_interest_auto.ctime</code>.
     * 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_unit_target_profession_interest_auto.mtime</code>.
     * 修改时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_unit_target_profession_interest_auto.mtime</code>.
     * 修改时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for
     * <code>lau_unit_target_profession_interest_auto.is_deleted</code>. 软删除
     * 0-有效，1-删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for
     * <code>lau_unit_target_profession_interest_auto.is_deleted</code>. 软删除
     * 0-有效，1-删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for
     * <code>lau_unit_target_profession_interest_auto.is_comand</code>. 是否处理过
     * 0-未处理，1-处理过
     */
    public Integer getIsComand() {
        return this.isComand;
    }

    /**
     * Setter for
     * <code>lau_unit_target_profession_interest_auto.is_comand</code>. 是否处理过
     * 0-未处理，1-处理过
     */
    public void setIsComand(Integer isComand) {
        this.isComand = isComand;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauUnitTargetProfessionInterestAutoPo (");

        sb.append(id);
        sb.append(", ").append(accountId);
        sb.append(", ").append(unitId);
        sb.append(", ").append(crowdId);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(isComand);

        sb.append(")");
        return sb.toString();
    }
}
