/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitTargetRule;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitTargetRulePo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitTargetRuleRecord;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauUnitTargetRuleDao extends DAOImpl<LauUnitTargetRuleRecord, LauUnitTargetRulePo, Integer> {

    /**
     * Create a new LauUnitTargetRuleDao without any configuration
     */
    public LauUnitTargetRuleDao() {
        super(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE, LauUnitTargetRulePo.class);
    }

    /**
     * Create a new LauUnitTargetRuleDao with an attached configuration
     */
    @Autowired
    public LauUnitTargetRuleDao(Configuration configuration) {
        super(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE, LauUnitTargetRulePo.class, configuration);
    }

    @Override
    public Integer getId(LauUnitTargetRulePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchById(Integer... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauUnitTargetRulePo fetchOneById(Integer value) {
        return fetchOne(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.ID, value);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByUnitId(Integer... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.UNIT_ID, values);
    }

    /**
     * Fetch a unique record that has <code>unit_id = value</code>
     */
    public LauUnitTargetRulePo fetchOneByUnitId(Integer value) {
        return fetchOne(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.UNIT_ID, value);
    }

    /**
     * Fetch records that have <code>area BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfArea(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AREA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>area IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByArea(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AREA, values);
    }

    /**
     * Fetch records that have <code>gender BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfGender(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.GENDER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>gender IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByGender(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.GENDER, values);
    }

    /**
     * Fetch records that have <code>age BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfAge(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AGE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>age IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByAge(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AGE, values);
    }

    /**
     * Fetch records that have <code>os BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfOs(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.OS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>os IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByOs(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.OS, values);
    }

    /**
     * Fetch records that have <code>network BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfNetwork(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.NETWORK, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>network IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByNetwork(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.NETWORK, values);
    }

    /**
     * Fetch records that have <code>category BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfCategory(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.CATEGORY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByCategory(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.CATEGORY, values);
    }

    /**
     * Fetch records that have <code>keyword BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfKeyword(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.KEYWORD, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>keyword IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByKeyword(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.KEYWORD, values);
    }

    /**
     * Fetch records that have <code>device_brand BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfDeviceBrand(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.DEVICE_BRAND, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>device_brand IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByDeviceBrand(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.DEVICE_BRAND, values);
    }

    /**
     * Fetch records that have <code>app_category BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfAppCategory(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.APP_CATEGORY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>app_category IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByAppCategory(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.APP_CATEGORY, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByCtime(Timestamp... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByMtime(Timestamp... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.MTIME, values);
    }

    /**
     * Fetch records that have <code>converted_user_filter BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfConvertedUserFilter(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.CONVERTED_USER_FILTER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>converted_user_filter IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByConvertedUserFilter(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.CONVERTED_USER_FILTER, values);
    }

    /**
     * Fetch records that have <code>intelligent_mass BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfIntelligentMass(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.INTELLIGENT_MASS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>intelligent_mass IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByIntelligentMass(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.INTELLIGENT_MASS, values);
    }

    /**
     * Fetch records that have <code>video_partition BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfVideoPartition(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.VIDEO_PARTITION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>video_partition IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByVideoPartition(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.VIDEO_PARTITION, values);
    }

    /**
     * Fetch records that have <code>age_customize BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfAgeCustomize(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AGE_CUSTOMIZE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>age_customize IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByAgeCustomize(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AGE_CUSTOMIZE, values);
    }

    /**
     * Fetch records that have <code>area_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfAreaType(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AREA_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>area_type IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByAreaType(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AREA_TYPE, values);
    }

    /**
     * Fetch records that have <code>phone_price BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfPhonePrice(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.PHONE_PRICE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>phone_price IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByPhonePrice(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.PHONE_PRICE, values);
    }

    /**
     * Fetch records that have <code>area_level BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetRulePo> fetchRangeOfAreaLevel(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AREA_LEVEL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>area_level IN (values)</code>
     */
    public List<LauUnitTargetRulePo> fetchByAreaLevel(String... values) {
        return fetch(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AREA_LEVEL, values);
    }
}
