/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TResTargetPackageRuleUpgrade;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.ResTargetPackageRuleUpgradePo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.ResTargetPackageRuleUpgradeRecord;


/**
 * 新版定向包信息
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ResTargetPackageRuleUpgradeDao extends DAOImpl<ResTargetPackageRuleUpgradeRecord, ResTargetPackageRuleUpgradePo, Long> {

    /**
     * Create a new ResTargetPackageRuleUpgradeDao without any configuration
     */
    public ResTargetPackageRuleUpgradeDao() {
        super(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE, ResTargetPackageRuleUpgradePo.class);
    }

    /**
     * Create a new ResTargetPackageRuleUpgradeDao with an attached
     * configuration
     */
    @Autowired
    public ResTargetPackageRuleUpgradeDao(Configuration configuration) {
        super(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE, ResTargetPackageRuleUpgradePo.class, configuration);
    }

    @Override
    public Long getId(ResTargetPackageRuleUpgradePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchById(Long... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public ResTargetPackageRuleUpgradePo fetchOneById(Long value) {
        return fetchOne(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.ID, value);
    }

    /**
     * Fetch records that have <code>target_package_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfTargetPackageId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.TARGET_PACKAGE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>target_package_id IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByTargetPackageId(Long... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.TARGET_PACKAGE_ID, values);
    }

    /**
     * Fetch a unique record that has <code>target_package_id = value</code>
     */
    public ResTargetPackageRuleUpgradePo fetchOneByTargetPackageId(Long value) {
        return fetchOne(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.TARGET_PACKAGE_ID, value);
    }

    /**
     * Fetch records that have <code>area BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfArea(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.AREA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>area IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByArea(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.AREA, values);
    }

    /**
     * Fetch records that have <code>gender BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfGender(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.GENDER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>gender IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByGender(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.GENDER, values);
    }

    /**
     * Fetch records that have <code>age BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfAge(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.AGE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>age IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByAge(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.AGE, values);
    }

    /**
     * Fetch records that have <code>os BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfOs(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.OS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>os IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByOs(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.OS, values);
    }

    /**
     * Fetch records that have <code>network BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfNetwork(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.NETWORK, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>network IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByNetwork(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.NETWORK, values);
    }

    /**
     * Fetch records that have <code>category BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfCategory(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.CATEGORY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByCategory(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.CATEGORY, values);
    }

    /**
     * Fetch records that have <code>keyword BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfKeyword(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.KEYWORD, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>keyword IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByKeyword(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.KEYWORD, values);
    }

    /**
     * Fetch records that have <code>device_brand BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfDeviceBrand(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.DEVICE_BRAND, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>device_brand IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByDeviceBrand(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.DEVICE_BRAND, values);
    }

    /**
     * Fetch records that have <code>app_category BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfAppCategory(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.APP_CATEGORY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>app_category IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByAppCategory(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.APP_CATEGORY, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByIsDeleted(Integer... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByCtime(Timestamp... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByMtime(Timestamp... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.MTIME, values);
    }

    /**
     * Fetch records that have <code>converted_user_filter BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfConvertedUserFilter(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.CONVERTED_USER_FILTER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>converted_user_filter IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByConvertedUserFilter(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.CONVERTED_USER_FILTER, values);
    }

    /**
     * Fetch records that have <code>intelligent_mass BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfIntelligentMass(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.INTELLIGENT_MASS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>intelligent_mass IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByIntelligentMass(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.INTELLIGENT_MASS, values);
    }

    /**
     * Fetch records that have <code>video_partition BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfVideoPartition(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.VIDEO_PARTITION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>video_partition IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByVideoPartition(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.VIDEO_PARTITION, values);
    }

    /**
     * Fetch records that have <code>age_customize BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfAgeCustomize(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.AGE_CUSTOMIZE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>age_customize IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByAgeCustomize(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.AGE_CUSTOMIZE, values);
    }

    /**
     * Fetch records that have <code>area_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfAreaType(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.AREA_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>area_type IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByAreaType(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.AREA_TYPE, values);
    }

    /**
     * Fetch records that have <code>phone_price BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfPhonePrice(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.PHONE_PRICE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>phone_price IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByPhonePrice(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.PHONE_PRICE, values);
    }

    /**
     * Fetch records that have <code>area_level BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchRangeOfAreaLevel(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.AREA_LEVEL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>area_level IN (values)</code>
     */
    public List<ResTargetPackageRuleUpgradePo> fetchByAreaLevel(String... values) {
        return fetch(TResTargetPackageRuleUpgrade.RES_TARGET_PACKAGE_RULE_UPGRADE.AREA_LEVEL, values);
    }
}
