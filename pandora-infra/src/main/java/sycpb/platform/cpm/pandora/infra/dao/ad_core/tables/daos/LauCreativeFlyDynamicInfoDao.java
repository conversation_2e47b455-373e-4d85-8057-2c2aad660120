/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeFlyDynamicInfo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeFlyDynamicInfoPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeFlyDynamicInfoRecord;


/**
 * 起飞创意的动态信息（引擎用）
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauCreativeFlyDynamicInfoDao extends DAOImpl<LauCreativeFlyDynamicInfoRecord, LauCreativeFlyDynamicInfoPo, Long> {

    /**
     * Create a new LauCreativeFlyDynamicInfoDao without any configuration
     */
    public LauCreativeFlyDynamicInfoDao() {
        super(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO, LauCreativeFlyDynamicInfoPo.class);
    }

    /**
     * Create a new LauCreativeFlyDynamicInfoDao with an attached configuration
     */
    @Autowired
    public LauCreativeFlyDynamicInfoDao(Configuration configuration) {
        super(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO, LauCreativeFlyDynamicInfoPo.class, configuration);
    }

    @Override
    public Long getId(LauCreativeFlyDynamicInfoPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchById(Long... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauCreativeFlyDynamicInfoPo fetchOneById(Long value) {
        return fetchOne(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.ID, value);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchByAccountId(Integer... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchByUnitId(Integer... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchByCreativeId(Integer... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.CREATIVE_ID, values);
    }

    /**
     * Fetch a unique record that has <code>creative_id = value</code>
     */
    public LauCreativeFlyDynamicInfoPo fetchOneByCreativeId(Integer value) {
        return fetchOne(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.CREATIVE_ID, value);
    }

    /**
     * Fetch records that have <code>dynamic_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfDynamicId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.DYNAMIC_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>dynamic_id IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchByDynamicId(Long... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.DYNAMIC_ID, values);
    }

    /**
     * Fetch records that have <code>like_count BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfLikeCount(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.LIKE_COUNT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>like_count IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchByLikeCount(String... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.LIKE_COUNT, values);
    }

    /**
     * Fetch records that have <code>nickname BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfNickname(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.NICKNAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>nickname IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchByNickname(String... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.NICKNAME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.MTIME, values);
    }

    /**
     * Fetch records that have <code>sid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfSid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.SID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sid IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchBySid(Long... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.SID, values);
    }

    /**
     * Fetch records that have <code>dynamic_up_mid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfDynamicUpMid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.DYNAMIC_UP_MID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>dynamic_up_mid IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchByDynamicUpMid(Long... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.DYNAMIC_UP_MID, values);
    }

    /**
     * Fetch records that have <code>source BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfSource(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.SOURCE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>source IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchBySource(Integer... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.SOURCE, values);
    }

    /**
     * Fetch records that have <code>second_show_mid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfSecondShowMid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.SECOND_SHOW_MID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>second_show_mid IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchBySecondShowMid(Long... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.SECOND_SHOW_MID, values);
    }

    /**
     * Fetch records that have <code>shadow_info BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchRangeOfShadowInfo(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.SHADOW_INFO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>shadow_info IN (values)</code>
     */
    public List<LauCreativeFlyDynamicInfoPo> fetchByShadowInfo(String... values) {
        return fetch(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.SHADOW_INFO, values);
    }
}
