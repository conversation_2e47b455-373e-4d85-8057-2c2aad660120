/*
 * This file is generated by jO<PERSON>Q.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeNativeArchiveRelativity;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeNativeArchiveRelativityPo;


/**
 * 原生创意稿件相关性表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeNativeArchiveRelativityRecord extends UpdatableRecordImpl<LauCreativeNativeArchiveRelativityRecord> implements Record11<Long, Inte<PERSON>, <PERSON>, <PERSON>, Inte<PERSON>, <PERSON>te<PERSON>, Inte<PERSON>, Integer, Timestamp, Timestamp, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_native_archive_relativity.id</code>. id
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.id</code>. id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for
     * <code>lau_creative_native_archive_relativity.creative_id</code>. 创意ID
     */
    public void setCreativeId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for
     * <code>lau_creative_native_archive_relativity.creative_id</code>. 创意ID
     */
    public Integer getCreativeId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.avid</code>.
     * 由type决定，type=1 为稿件id，type=2为动态id，type=3为直播间roomId
     */
    public void setAvid(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.avid</code>.
     * 由type决定，type=1 为稿件id，type=2为动态id，type=3为直播间roomId
     */
    public Long getAvid() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.reason</code>.
     * 原因（审核拒绝时填写）
     */
    public void setReason(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.reason</code>.
     * 原因（审核拒绝时填写）
     */
    public String getReason() {
        return (String) get(3);
    }

    /**
     * Setter for
     * <code>lau_creative_native_archive_relativity.audit_status</code>.
     * 审核状态（1-待审核，2-审核通过，3-审核不通过）
     */
    public void setAuditStatus(Integer value) {
        set(4, value);
    }

    /**
     * Getter for
     * <code>lau_creative_native_archive_relativity.audit_status</code>.
     * 审核状态（1-待审核，2-审核通过，3-审核不通过）
     */
    public Integer getAuditStatus() {
        return (Integer) get(4);
    }

    /**
     * Setter for
     * <code>lau_creative_native_archive_relativity.is_recheck</code>. 是否被质检 0否
     * 1是
     */
    public void setIsRecheck(Integer value) {
        set(5, value);
    }

    /**
     * Getter for
     * <code>lau_creative_native_archive_relativity.is_recheck</code>. 是否被质检 0否
     * 1是
     */
    public Integer getIsRecheck() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.version</code>.
     * 版本号
     */
    public void setVersion(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.version</code>.
     * 版本号
     */
    public Integer getVersion() {
        return (Integer) get(6);
    }

    /**
     * Setter for
     * <code>lau_creative_native_archive_relativity.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer value) {
        set(7, value);
    }

    /**
     * Getter for
     * <code>lau_creative_native_archive_relativity.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.ctime</code>.
     * 添加时间
     */
    public void setCtime(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.ctime</code>.
     * 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(8);
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.mtime</code>.
     * 更新时间
     */
    public void setMtime(Timestamp value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.mtime</code>.
     * 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(9);
    }

    /**
     * Setter for <code>lau_creative_native_archive_relativity.type</code>.
     * 1原生稿件(默认值) 2动态 3直播间
     */
    public void setType(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_creative_native_archive_relativity.type</code>.
     * 1原生稿件(默认值) 2动态 3直播间
     */
    public Integer getType() {
        return (Integer) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<Long, Integer, Long, String, Integer, Integer, Integer, Integer, Timestamp, Timestamp, Integer> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<Long, Integer, Long, String, Integer, Integer, Integer, Integer, Timestamp, Timestamp, Integer> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY.CREATIVE_ID;
    }

    @Override
    public Field<Long> field3() {
        return TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY.AVID;
    }

    @Override
    public Field<String> field4() {
        return TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY.REASON;
    }

    @Override
    public Field<Integer> field5() {
        return TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY.AUDIT_STATUS;
    }

    @Override
    public Field<Integer> field6() {
        return TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY.IS_RECHECK;
    }

    @Override
    public Field<Integer> field7() {
        return TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY.VERSION;
    }

    @Override
    public Field<Integer> field8() {
        return TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field9() {
        return TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY.CTIME;
    }

    @Override
    public Field<Timestamp> field10() {
        return TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY.MTIME;
    }

    @Override
    public Field<Integer> field11() {
        return TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY.TYPE;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getCreativeId();
    }

    @Override
    public Long component3() {
        return getAvid();
    }

    @Override
    public String component4() {
        return getReason();
    }

    @Override
    public Integer component5() {
        return getAuditStatus();
    }

    @Override
    public Integer component6() {
        return getIsRecheck();
    }

    @Override
    public Integer component7() {
        return getVersion();
    }

    @Override
    public Integer component8() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component9() {
        return getCtime();
    }

    @Override
    public Timestamp component10() {
        return getMtime();
    }

    @Override
    public Integer component11() {
        return getType();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getCreativeId();
    }

    @Override
    public Long value3() {
        return getAvid();
    }

    @Override
    public String value4() {
        return getReason();
    }

    @Override
    public Integer value5() {
        return getAuditStatus();
    }

    @Override
    public Integer value6() {
        return getIsRecheck();
    }

    @Override
    public Integer value7() {
        return getVersion();
    }

    @Override
    public Integer value8() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value9() {
        return getCtime();
    }

    @Override
    public Timestamp value10() {
        return getMtime();
    }

    @Override
    public Integer value11() {
        return getType();
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord value2(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord value3(Long value) {
        setAvid(value);
        return this;
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord value4(String value) {
        setReason(value);
        return this;
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord value5(Integer value) {
        setAuditStatus(value);
        return this;
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord value6(Integer value) {
        setIsRecheck(value);
        return this;
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord value7(Integer value) {
        setVersion(value);
        return this;
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord value8(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord value9(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord value10(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord value11(Integer value) {
        setType(value);
        return this;
    }

    @Override
    public LauCreativeNativeArchiveRelativityRecord values(Long value1, Integer value2, Long value3, String value4, Integer value5, Integer value6, Integer value7, Integer value8, Timestamp value9, Timestamp value10, Integer value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativeNativeArchiveRelativityRecord
     */
    public LauCreativeNativeArchiveRelativityRecord() {
        super(TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY);
    }

    /**
     * Create a detached, initialised LauCreativeNativeArchiveRelativityRecord
     */
    public LauCreativeNativeArchiveRelativityRecord(Long id, Integer creativeId, Long avid, String reason, Integer auditStatus, Integer isRecheck, Integer version, Integer isDeleted, Timestamp ctime, Timestamp mtime, Integer type) {
        super(TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY);

        setId(id);
        setCreativeId(creativeId);
        setAvid(avid);
        setReason(reason);
        setAuditStatus(auditStatus);
        setIsRecheck(isRecheck);
        setVersion(version);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setType(type);
    }

    /**
     * Create a detached, initialised LauCreativeNativeArchiveRelativityRecord
     */
    public LauCreativeNativeArchiveRelativityRecord(LauCreativeNativeArchiveRelativityPo value) {
        super(TLauCreativeNativeArchiveRelativity.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY);

        if (value != null) {
            setId(value.getId());
            setCreativeId(value.getCreativeId());
            setAvid(value.getAvid());
            setReason(value.getReason());
            setAuditStatus(value.getAuditStatus());
            setIsRecheck(value.getIsRecheck());
            setVersion(value.getVersion());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setType(value.getType());
        }
    }
}
