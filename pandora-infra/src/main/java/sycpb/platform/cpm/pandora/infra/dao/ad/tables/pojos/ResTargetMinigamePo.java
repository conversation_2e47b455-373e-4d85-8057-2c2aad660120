/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResTargetMinigamePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Long      unitId;
    private String    includeGameIds;
    private String    excludeGameIds;
    private Timestamp ctime;
    private Timestamp mtime;

    public ResTargetMinigamePo() {}

    public ResTargetMinigamePo(ResTargetMinigamePo value) {
        this.id = value.id;
        this.unitId = value.unitId;
        this.includeGameIds = value.includeGameIds;
        this.excludeGameIds = value.excludeGameIds;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
    }

    public ResTargetMinigamePo(
        Long      id,
        Long      unitId,
        String    includeGameIds,
        String    excludeGameIds,
        Timestamp ctime,
        Timestamp mtime
    ) {
        this.id = id;
        this.unitId = unitId;
        this.includeGameIds = includeGameIds;
        this.excludeGameIds = excludeGameIds;
        this.ctime = ctime;
        this.mtime = mtime;
    }

    /**
     * Getter for <code>res_target_minigame.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>res_target_minigame.id</code>. 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>res_target_minigame.unit_id</code>. 单元id
     */
    public Long getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>res_target_minigame.unit_id</code>. 单元id
     */
    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for <code>res_target_minigame.include_game_ids</code>. 定投游戏id，逗号分隔
     */
    public String getIncludeGameIds() {
        return this.includeGameIds;
    }

    /**
     * Setter for <code>res_target_minigame.include_game_ids</code>. 定投游戏id，逗号分隔
     */
    public void setIncludeGameIds(String includeGameIds) {
        this.includeGameIds = includeGameIds;
    }

    /**
     * Getter for <code>res_target_minigame.exclude_game_ids</code>. 排除游戏id，逗号分隔
     */
    public String getExcludeGameIds() {
        return this.excludeGameIds;
    }

    /**
     * Setter for <code>res_target_minigame.exclude_game_ids</code>. 排除游戏id，逗号分隔
     */
    public void setExcludeGameIds(String excludeGameIds) {
        this.excludeGameIds = excludeGameIds;
    }

    /**
     * Getter for <code>res_target_minigame.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>res_target_minigame.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>res_target_minigame.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>res_target_minigame.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ResTargetMinigamePo (");

        sb.append(id);
        sb.append(", ").append(unitId);
        sb.append(", ").append(includeGameIds);
        sb.append(", ").append(excludeGameIds);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);

        sb.append(")");
        return sb.toString();
    }
}
