package sycpb.platform.cpm.pandora.infra.api.env;

import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;

import java.util.Objects;

public interface IEnvService {
    int UNKNOWN = 0;
    int PROD = 1;
    int PRE = 2;
    int UAT = 3;
    int DEV = 4;

    static Integer encodeEnv(String env) {
        if (Objects.isNull(env)) return UNKNOWN;

        switch (env) {
            case "prod":
                return PROD;
            case "pre":
                return PRE;
            case "uat":
                return UAT;
            case "dev":
                return DEV;
            default:
                return UNKNOWN;
        }
    }
    static String getPandoraExecutorEnv(Environment environment) {
        var env = environment.getProperty("pandora_executor_env");
        if (!StringUtils.hasText(env)) {
            env = getDeployEnv(environment);
            if (!StringUtils.hasText(env)) {
                env = "dev";
            }
        }
        return env;
    }

    static String getDeployEnv(Environment environment) {
        var env = environment.getProperty("deploy_env");
        if (!StringUtils.hasText(env)) {
            env = "dev";
        }
        return env;
    }
}
