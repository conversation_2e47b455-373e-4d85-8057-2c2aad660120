/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row17;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauStoryCouponComponentRecord;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauStoryCouponComponent extends TableImpl<LauStoryCouponComponentRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_story_coupon_component</code>
     */
    public static final TLauStoryCouponComponent LAU_STORY_COUPON_COMPONENT = new TLauStoryCouponComponent();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauStoryCouponComponentRecord> getRecordType() {
        return LauStoryCouponComponentRecord.class;
    }

    /**
     * The column <code>lau_story_coupon_component.id</code>. id
     */
    public final TableField<LauStoryCouponComponentRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "id");

    /**
     * The column <code>lau_story_coupon_component.ctime</code>. 创建时间
     */
    public final TableField<LauStoryCouponComponentRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_story_coupon_component.mtime</code>. 更新时间
     */
    public final TableField<LauStoryCouponComponentRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>lau_story_coupon_component.is_deleted</code>. 软删除:
     * 0-未删除,1-已删除
     */
    public final TableField<LauStoryCouponComponentRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除: 0-未删除,1-已删除");

    /**
     * The column <code>lau_story_coupon_component.account_id</code>. 账号id
     */
    public final TableField<LauStoryCouponComponentRecord, Integer> ACCOUNT_ID = createField(DSL.name("account_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "账号id");

    /**
     * The column <code>lau_story_coupon_component.component_id</code>.
     * story组件id
     */
    public final TableField<LauStoryCouponComponentRecord, Long> COMPONENT_ID = createField(DSL.name("component_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "story组件id");

    /**
     * The column <code>lau_story_coupon_component.component_name</code>. 组件名称
     */
    public final TableField<LauStoryCouponComponentRecord, String> COMPONENT_NAME = createField(DSL.name("component_name"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "组件名称");

    /**
     * The column <code>lau_story_coupon_component.cost_fen</code>. 优惠金额(单位分)
     */
    public final TableField<LauStoryCouponComponentRecord, Integer> COST_FEN = createField(DSL.name("cost_fen"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "优惠金额(单位分)");

    /**
     * The column <code>lau_story_coupon_component.description</code>. 优惠券名称
     */
    public final TableField<LauStoryCouponComponentRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "优惠券名称");

    /**
     * The column <code>lau_story_coupon_component.comment</code>. 备注信息
     */
    public final TableField<LauStoryCouponComponentRecord, String> COMMENT = createField(DSL.name("comment"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "备注信息");

    /**
     * The column <code>lau_story_coupon_component.use_period_start</code>.
     * 优惠券有效期开始时间
     */
    public final TableField<LauStoryCouponComponentRecord, Timestamp> USE_PERIOD_START = createField(DSL.name("use_period_start"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "优惠券有效期开始时间");

    /**
     * The column <code>lau_story_coupon_component.use_period_end</code>.
     * 优惠券有效期结束时间
     */
    public final TableField<LauStoryCouponComponentRecord, Timestamp> USE_PERIOD_END = createField(DSL.name("use_period_end"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "优惠券有效期结束时间");

    /**
     * The column <code>lau_story_coupon_component.obtain_period_start</code>.
     * 优惠券获取开始时间
     */
    public final TableField<LauStoryCouponComponentRecord, Timestamp> OBTAIN_PERIOD_START = createField(DSL.name("obtain_period_start"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "优惠券获取开始时间");

    /**
     * The column <code>lau_story_coupon_component.obtain_period_end</code>.
     * 优惠券获取结束时间
     */
    public final TableField<LauStoryCouponComponentRecord, Timestamp> OBTAIN_PERIOD_END = createField(DSL.name("obtain_period_end"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "优惠券获取结束时间");

    /**
     * The column <code>lau_story_coupon_component.button_id</code>. 按钮id
     */
    public final TableField<LauStoryCouponComponentRecord, Integer> BUTTON_ID = createField(DSL.name("button_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "按钮id");

    /**
     * The column <code>lau_story_coupon_component.button_type</code>. 按钮类型
     */
    public final TableField<LauStoryCouponComponentRecord, Integer> BUTTON_TYPE = createField(DSL.name("button_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "按钮类型");

    /**
     * The column <code>lau_story_coupon_component.button_text</code>. 按钮文案
     */
    public final TableField<LauStoryCouponComponentRecord, String> BUTTON_TEXT = createField(DSL.name("button_text"), SQLDataType.VARCHAR(16).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "按钮文案");

    private TLauStoryCouponComponent(Name alias, Table<LauStoryCouponComponentRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauStoryCouponComponent(Name alias, Table<LauStoryCouponComponentRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_story_coupon_component</code> table reference
     */
    public TLauStoryCouponComponent(String alias) {
        this(DSL.name(alias), LAU_STORY_COUPON_COMPONENT);
    }

    /**
     * Create an aliased <code>lau_story_coupon_component</code> table reference
     */
    public TLauStoryCouponComponent(Name alias) {
        this(alias, LAU_STORY_COUPON_COMPONENT);
    }

    /**
     * Create a <code>lau_story_coupon_component</code> table reference
     */
    public TLauStoryCouponComponent() {
        this(DSL.name("lau_story_coupon_component"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauStoryCouponComponentRecord, Long> getIdentity() {
        return (Identity<LauStoryCouponComponentRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauStoryCouponComponentRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT, DSL.name("KEY_lau_story_coupon_component_PRIMARY"), new TableField[] { TLauStoryCouponComponent.LAU_STORY_COUPON_COMPONENT.ID }, true);
    }

    @Override
    public TLauStoryCouponComponent as(String alias) {
        return new TLauStoryCouponComponent(DSL.name(alias), this);
    }

    @Override
    public TLauStoryCouponComponent as(Name alias) {
        return new TLauStoryCouponComponent(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauStoryCouponComponent rename(String name) {
        return new TLauStoryCouponComponent(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauStoryCouponComponent rename(Name name) {
        return new TLauStoryCouponComponent(name, null);
    }

    // -------------------------------------------------------------------------
    // Row17 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row17<Long, Timestamp, Timestamp, Integer, Integer, Long, String, Integer, String, String, Timestamp, Timestamp, Timestamp, Timestamp, Integer, Integer, String> fieldsRow() {
        return (Row17) super.fieldsRow();
    }
}
