/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauDynamic;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauDynamicPo;


/**
 * 投放端动态表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauDynamicRecord extends UpdatableRecordImpl<LauDynamicRecord> implements Record8<Long, Long, Long, Long, String, Integer, Timestamp, Timestamp> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_dynamic.id</code>. 自增ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_dynamic.id</code>. 自增ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_dynamic.dynamic_id</code>. 动态id
     */
    public void setDynamicId(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_dynamic.dynamic_id</code>. 动态id
     */
    public Long getDynamicId() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>lau_dynamic.sid</code>. 动态关联的直播预约id
     */
    public void setSid(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_dynamic.sid</code>. 动态关联的直播预约id
     */
    public Long getSid() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>lau_dynamic.dynamic_up_mid</code>. 动态up主mid
     */
    public void setDynamicUpMid(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_dynamic.dynamic_up_mid</code>. 动态up主mid
     */
    public Long getDynamicUpMid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>lau_dynamic.cover</code>. 动态原始封面，多个取第一个
     */
    public void setCover(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_dynamic.cover</code>. 动态原始封面，多个取第一个
     */
    public String getCover() {
        return (String) get(4);
    }

    /**
     * Setter for <code>lau_dynamic.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_dynamic.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_dynamic.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_dynamic.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(6);
    }

    /**
     * Setter for <code>lau_dynamic.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_dynamic.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<Long, Long, Long, Long, String, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<Long, Long, Long, Long, String, Integer, Timestamp, Timestamp> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauDynamic.LAU_DYNAMIC.ID;
    }

    @Override
    public Field<Long> field2() {
        return TLauDynamic.LAU_DYNAMIC.DYNAMIC_ID;
    }

    @Override
    public Field<Long> field3() {
        return TLauDynamic.LAU_DYNAMIC.SID;
    }

    @Override
    public Field<Long> field4() {
        return TLauDynamic.LAU_DYNAMIC.DYNAMIC_UP_MID;
    }

    @Override
    public Field<String> field5() {
        return TLauDynamic.LAU_DYNAMIC.COVER;
    }

    @Override
    public Field<Integer> field6() {
        return TLauDynamic.LAU_DYNAMIC.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field7() {
        return TLauDynamic.LAU_DYNAMIC.CTIME;
    }

    @Override
    public Field<Timestamp> field8() {
        return TLauDynamic.LAU_DYNAMIC.MTIME;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Long component2() {
        return getDynamicId();
    }

    @Override
    public Long component3() {
        return getSid();
    }

    @Override
    public Long component4() {
        return getDynamicUpMid();
    }

    @Override
    public String component5() {
        return getCover();
    }

    @Override
    public Integer component6() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component7() {
        return getCtime();
    }

    @Override
    public Timestamp component8() {
        return getMtime();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Long value2() {
        return getDynamicId();
    }

    @Override
    public Long value3() {
        return getSid();
    }

    @Override
    public Long value4() {
        return getDynamicUpMid();
    }

    @Override
    public String value5() {
        return getCover();
    }

    @Override
    public Integer value6() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value7() {
        return getCtime();
    }

    @Override
    public Timestamp value8() {
        return getMtime();
    }

    @Override
    public LauDynamicRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauDynamicRecord value2(Long value) {
        setDynamicId(value);
        return this;
    }

    @Override
    public LauDynamicRecord value3(Long value) {
        setSid(value);
        return this;
    }

    @Override
    public LauDynamicRecord value4(Long value) {
        setDynamicUpMid(value);
        return this;
    }

    @Override
    public LauDynamicRecord value5(String value) {
        setCover(value);
        return this;
    }

    @Override
    public LauDynamicRecord value6(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauDynamicRecord value7(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauDynamicRecord value8(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauDynamicRecord values(Long value1, Long value2, Long value3, Long value4, String value5, Integer value6, Timestamp value7, Timestamp value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauDynamicRecord
     */
    public LauDynamicRecord() {
        super(TLauDynamic.LAU_DYNAMIC);
    }

    /**
     * Create a detached, initialised LauDynamicRecord
     */
    public LauDynamicRecord(Long id, Long dynamicId, Long sid, Long dynamicUpMid, String cover, Integer isDeleted, Timestamp ctime, Timestamp mtime) {
        super(TLauDynamic.LAU_DYNAMIC);

        setId(id);
        setDynamicId(dynamicId);
        setSid(sid);
        setDynamicUpMid(dynamicUpMid);
        setCover(cover);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
    }

    /**
     * Create a detached, initialised LauDynamicRecord
     */
    public LauDynamicRecord(LauDynamicPo value) {
        super(TLauDynamic.LAU_DYNAMIC);

        if (value != null) {
            setId(value.getId());
            setDynamicId(value.getDynamicId());
            setSid(value.getSid());
            setDynamicUpMid(value.getDynamicUpMid());
            setCover(value.getCover());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
        }
    }
}
