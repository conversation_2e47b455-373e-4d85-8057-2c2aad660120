/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 创意按钮文案关联表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeButtonCopyPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer   id;
    private Integer   unitId;
    private Integer   creativeId;
    private Integer   buttonCopyId;
    private String    jumpUrl;
    private String    customizedUrl;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   buttonType;
    private String    extendUrl;
    private String    androidJumpUrl;
    private String    iosJumpUrl;

    public LauCreativeButtonCopyPo() {}

    public LauCreativeButtonCopyPo(LauCreativeButtonCopyPo value) {
        this.id = value.id;
        this.unitId = value.unitId;
        this.creativeId = value.creativeId;
        this.buttonCopyId = value.buttonCopyId;
        this.jumpUrl = value.jumpUrl;
        this.customizedUrl = value.customizedUrl;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.buttonType = value.buttonType;
        this.extendUrl = value.extendUrl;
        this.androidJumpUrl = value.androidJumpUrl;
        this.iosJumpUrl = value.iosJumpUrl;
    }

    public LauCreativeButtonCopyPo(
        Integer   id,
        Integer   unitId,
        Integer   creativeId,
        Integer   buttonCopyId,
        String    jumpUrl,
        String    customizedUrl,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime,
        Integer   buttonType,
        String    extendUrl,
        String    androidJumpUrl,
        String    iosJumpUrl
    ) {
        this.id = id;
        this.unitId = unitId;
        this.creativeId = creativeId;
        this.buttonCopyId = buttonCopyId;
        this.jumpUrl = jumpUrl;
        this.customizedUrl = customizedUrl;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
        this.buttonType = buttonType;
        this.extendUrl = extendUrl;
        this.androidJumpUrl = androidJumpUrl;
        this.iosJumpUrl = iosJumpUrl;
    }

    /**
     * Getter for <code>lau_creative_button_copy.id</code>. 自增id
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_creative_button_copy.id</code>. 自增id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_creative_button_copy.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_creative_button_copy.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for <code>lau_creative_button_copy.creative_id</code>. 创意ID
     */
    public Integer getCreativeId() {
        return this.creativeId;
    }

    /**
     * Setter for <code>lau_creative_button_copy.creative_id</code>. 创意ID
     */
    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    /**
     * Getter for <code>lau_creative_button_copy.button_copy_id</code>. 按钮文案ID
     */
    public Integer getButtonCopyId() {
        return this.buttonCopyId;
    }

    /**
     * Setter for <code>lau_creative_button_copy.button_copy_id</code>. 按钮文案ID
     */
    public void setButtonCopyId(Integer buttonCopyId) {
        this.buttonCopyId = buttonCopyId;
    }

    /**
     * Getter for <code>lau_creative_button_copy.jump_url</code>. 跳转URL
     */
    public String getJumpUrl() {
        return this.jumpUrl;
    }

    /**
     * Setter for <code>lau_creative_button_copy.jump_url</code>. 跳转URL
     */
    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    /**
     * Getter for <code>lau_creative_button_copy.customized_url</code>. 监控链接
     */
    public String getCustomizedUrl() {
        return this.customizedUrl;
    }

    /**
     * Setter for <code>lau_creative_button_copy.customized_url</code>. 监控链接
     */
    public void setCustomizedUrl(String customizedUrl) {
        this.customizedUrl = customizedUrl;
    }

    /**
     * Getter for <code>lau_creative_button_copy.is_deleted</code>.
     * 软删除，0是有效,1是删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_creative_button_copy.is_deleted</code>.
     * 软删除，0是有效,1是删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_creative_button_copy.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_creative_button_copy.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_creative_button_copy.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_creative_button_copy.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_creative_button_copy.button_type</code>. 按钮类型: 1-落地页
     * 2-应用唤起 3-应用下载 6-直播预约
     */
    public Integer getButtonType() {
        return this.buttonType;
    }

    /**
     * Setter for <code>lau_creative_button_copy.button_type</code>. 按钮类型: 1-落地页
     * 2-应用唤起 3-应用下载 6-直播预约
     */
    public void setButtonType(Integer buttonType) {
        this.buttonType = buttonType;
    }

    /**
     * Getter for <code>lau_creative_button_copy.extend_url</code>.
     * 扩展URL(button_type=3代表scheme链接）
     */
    public String getExtendUrl() {
        return this.extendUrl;
    }

    /**
     * Setter for <code>lau_creative_button_copy.extend_url</code>.
     * 扩展URL(button_type=3代表scheme链接）
     */
    public void setExtendUrl(String extendUrl) {
        this.extendUrl = extendUrl;
    }

    /**
     * Getter for <code>lau_creative_button_copy.android_jump_url</code>.
     * android跳转URL
     */
    public String getAndroidJumpUrl() {
        return this.androidJumpUrl;
    }

    /**
     * Setter for <code>lau_creative_button_copy.android_jump_url</code>.
     * android跳转URL
     */
    public void setAndroidJumpUrl(String androidJumpUrl) {
        this.androidJumpUrl = androidJumpUrl;
    }

    /**
     * Getter for <code>lau_creative_button_copy.ios_jump_url</code>. ios跳转URL
     */
    public String getIosJumpUrl() {
        return this.iosJumpUrl;
    }

    /**
     * Setter for <code>lau_creative_button_copy.ios_jump_url</code>. ios跳转URL
     */
    public void setIosJumpUrl(String iosJumpUrl) {
        this.iosJumpUrl = iosJumpUrl;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauCreativeButtonCopyPo (");

        sb.append(id);
        sb.append(", ").append(unitId);
        sb.append(", ").append(creativeId);
        sb.append(", ").append(buttonCopyId);
        sb.append(", ").append(jumpUrl);
        sb.append(", ").append(customizedUrl);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(buttonType);
        sb.append(", ").append(extendUrl);
        sb.append(", ").append(androidJumpUrl);
        sb.append(", ").append(iosJumpUrl);

        sb.append(")");
        return sb.toString();
    }
}
