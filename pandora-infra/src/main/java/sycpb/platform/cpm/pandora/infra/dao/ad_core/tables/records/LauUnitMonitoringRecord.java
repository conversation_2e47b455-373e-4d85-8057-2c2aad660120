/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitMonitoring;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitMonitoringPo;


/**
 * 单元监控链接表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitMonitoringRecord extends UpdatableRecordImpl<LauUnitMonitoringRecord> implements Record6<Long, Timestamp, Timestamp, Integer, String, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_unit_monitoring.id</code>. id
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_unit_monitoring.id</code>. id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_unit_monitoring.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_unit_monitoring.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(1);
    }

    /**
     * Setter for <code>lau_unit_monitoring.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_unit_monitoring.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(2);
    }

    /**
     * Setter for <code>lau_unit_monitoring.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_unit_monitoring.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_unit_monitoring.url</code>. 监控链接
     */
    public void setUrl(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_unit_monitoring.url</code>. 监控链接
     */
    public String getUrl() {
        return (String) get(4);
    }

    /**
     * Setter for <code>lau_unit_monitoring.type</code>. 监控类型: 1-展示, 2-点击,
     * 3-播放0s, 4-播放3s, 5-播放5s, 6-游戏点击, 8-评论链接点击
     */
    public void setType(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_unit_monitoring.type</code>. 监控类型: 1-展示, 2-点击,
     * 3-播放0s, 4-播放3s, 5-播放5s, 6-游戏点击, 8-评论链接点击
     */
    public Integer getType() {
        return (Integer) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Timestamp, Timestamp, Integer, String, Integer> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<Long, Timestamp, Timestamp, Integer, String, Integer> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauUnitMonitoring.LAU_UNIT_MONITORING.ID;
    }

    @Override
    public Field<Timestamp> field2() {
        return TLauUnitMonitoring.LAU_UNIT_MONITORING.CTIME;
    }

    @Override
    public Field<Timestamp> field3() {
        return TLauUnitMonitoring.LAU_UNIT_MONITORING.MTIME;
    }

    @Override
    public Field<Integer> field4() {
        return TLauUnitMonitoring.LAU_UNIT_MONITORING.UNIT_ID;
    }

    @Override
    public Field<String> field5() {
        return TLauUnitMonitoring.LAU_UNIT_MONITORING.URL;
    }

    @Override
    public Field<Integer> field6() {
        return TLauUnitMonitoring.LAU_UNIT_MONITORING.TYPE;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Timestamp component2() {
        return getCtime();
    }

    @Override
    public Timestamp component3() {
        return getMtime();
    }

    @Override
    public Integer component4() {
        return getUnitId();
    }

    @Override
    public String component5() {
        return getUrl();
    }

    @Override
    public Integer component6() {
        return getType();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Timestamp value2() {
        return getCtime();
    }

    @Override
    public Timestamp value3() {
        return getMtime();
    }

    @Override
    public Integer value4() {
        return getUnitId();
    }

    @Override
    public String value5() {
        return getUrl();
    }

    @Override
    public Integer value6() {
        return getType();
    }

    @Override
    public LauUnitMonitoringRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauUnitMonitoringRecord value2(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauUnitMonitoringRecord value3(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauUnitMonitoringRecord value4(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauUnitMonitoringRecord value5(String value) {
        setUrl(value);
        return this;
    }

    @Override
    public LauUnitMonitoringRecord value6(Integer value) {
        setType(value);
        return this;
    }

    @Override
    public LauUnitMonitoringRecord values(Long value1, Timestamp value2, Timestamp value3, Integer value4, String value5, Integer value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauUnitMonitoringRecord
     */
    public LauUnitMonitoringRecord() {
        super(TLauUnitMonitoring.LAU_UNIT_MONITORING);
    }

    /**
     * Create a detached, initialised LauUnitMonitoringRecord
     */
    public LauUnitMonitoringRecord(Long id, Timestamp ctime, Timestamp mtime, Integer unitId, String url, Integer type) {
        super(TLauUnitMonitoring.LAU_UNIT_MONITORING);

        setId(id);
        setCtime(ctime);
        setMtime(mtime);
        setUnitId(unitId);
        setUrl(url);
        setType(type);
    }

    /**
     * Create a detached, initialised LauUnitMonitoringRecord
     */
    public LauUnitMonitoringRecord(LauUnitMonitoringPo value) {
        super(TLauUnitMonitoring.LAU_UNIT_MONITORING);

        if (value != null) {
            setId(value.getId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setUnitId(value.getUnitId());
            setUrl(value.getUrl());
            setType(value.getType());
        }
    }
}
