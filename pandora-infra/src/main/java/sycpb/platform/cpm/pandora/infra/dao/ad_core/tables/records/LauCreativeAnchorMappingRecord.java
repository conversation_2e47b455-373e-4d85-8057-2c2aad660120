/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeAnchorMapping;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeAnchorMappingPo;


/**
 * 创意锚点关系
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeAnchorMappingRecord extends UpdatableRecordImpl<LauCreativeAnchorMappingRecord> implements Record9<Long, Long, <PERSON>, Inte<PERSON>, Inte<PERSON>, Integer, Integer, Timestamp, Timestamp> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_anchor_mapping.id</code>. id
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_anchor_mapping.id</code>. id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_creative_anchor_mapping.aid</code>. 稿件id
     */
    public void setAid(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_creative_anchor_mapping.aid</code>. 稿件id
     */
    public Long getAid() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>lau_creative_anchor_mapping.anchor_id</code>. 锚点id
     */
    public void setAnchorId(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_creative_anchor_mapping.anchor_id</code>. 锚点id
     */
    public Long getAnchorId() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>lau_creative_anchor_mapping.creative_id</code>. 创意id
     */
    public void setCreativeId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_creative_anchor_mapping.creative_id</code>. 创意id
     */
    public Integer getCreativeId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_creative_anchor_mapping.unit_id</code>. 单元id
     */
    public void setUnitId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_creative_anchor_mapping.unit_id</code>. 单元id
     */
    public Integer getUnitId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_creative_anchor_mapping.system_type</code>.
     * 锚点系统类型：1三连 2带货 3花火 0未知
     */
    public void setSystemType(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_creative_anchor_mapping.system_type</code>.
     * 锚点系统类型：1三连 2带货 3花火 0未知
     */
    public Integer getSystemType() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_creative_anchor_mapping.is_deleted</code>. 是否删除
     */
    public void setIsDeleted(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_creative_anchor_mapping.is_deleted</code>. 是否删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lau_creative_anchor_mapping.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_creative_anchor_mapping.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(7);
    }

    /**
     * Setter for <code>lau_creative_anchor_mapping.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_creative_anchor_mapping.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row9<Long, Long, Long, Integer, Integer, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    @Override
    public Row9<Long, Long, Long, Integer, Integer, Integer, Integer, Timestamp, Timestamp> valuesRow() {
        return (Row9) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.ID;
    }

    @Override
    public Field<Long> field2() {
        return TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.AID;
    }

    @Override
    public Field<Long> field3() {
        return TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.ANCHOR_ID;
    }

    @Override
    public Field<Integer> field4() {
        return TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.CREATIVE_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.UNIT_ID;
    }

    @Override
    public Field<Integer> field6() {
        return TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.SYSTEM_TYPE;
    }

    @Override
    public Field<Integer> field7() {
        return TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field8() {
        return TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.CTIME;
    }

    @Override
    public Field<Timestamp> field9() {
        return TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING.MTIME;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Long component2() {
        return getAid();
    }

    @Override
    public Long component3() {
        return getAnchorId();
    }

    @Override
    public Integer component4() {
        return getCreativeId();
    }

    @Override
    public Integer component5() {
        return getUnitId();
    }

    @Override
    public Integer component6() {
        return getSystemType();
    }

    @Override
    public Integer component7() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component8() {
        return getCtime();
    }

    @Override
    public Timestamp component9() {
        return getMtime();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Long value2() {
        return getAid();
    }

    @Override
    public Long value3() {
        return getAnchorId();
    }

    @Override
    public Integer value4() {
        return getCreativeId();
    }

    @Override
    public Integer value5() {
        return getUnitId();
    }

    @Override
    public Integer value6() {
        return getSystemType();
    }

    @Override
    public Integer value7() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value8() {
        return getCtime();
    }

    @Override
    public Timestamp value9() {
        return getMtime();
    }

    @Override
    public LauCreativeAnchorMappingRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativeAnchorMappingRecord value2(Long value) {
        setAid(value);
        return this;
    }

    @Override
    public LauCreativeAnchorMappingRecord value3(Long value) {
        setAnchorId(value);
        return this;
    }

    @Override
    public LauCreativeAnchorMappingRecord value4(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeAnchorMappingRecord value5(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauCreativeAnchorMappingRecord value6(Integer value) {
        setSystemType(value);
        return this;
    }

    @Override
    public LauCreativeAnchorMappingRecord value7(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauCreativeAnchorMappingRecord value8(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativeAnchorMappingRecord value9(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativeAnchorMappingRecord values(Long value1, Long value2, Long value3, Integer value4, Integer value5, Integer value6, Integer value7, Timestamp value8, Timestamp value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativeAnchorMappingRecord
     */
    public LauCreativeAnchorMappingRecord() {
        super(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING);
    }

    /**
     * Create a detached, initialised LauCreativeAnchorMappingRecord
     */
    public LauCreativeAnchorMappingRecord(Long id, Long aid, Long anchorId, Integer creativeId, Integer unitId, Integer systemType, Integer isDeleted, Timestamp ctime, Timestamp mtime) {
        super(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING);

        setId(id);
        setAid(aid);
        setAnchorId(anchorId);
        setCreativeId(creativeId);
        setUnitId(unitId);
        setSystemType(systemType);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
    }

    /**
     * Create a detached, initialised LauCreativeAnchorMappingRecord
     */
    public LauCreativeAnchorMappingRecord(LauCreativeAnchorMappingPo value) {
        super(TLauCreativeAnchorMapping.LAU_CREATIVE_ANCHOR_MAPPING);

        if (value != null) {
            setId(value.getId());
            setAid(value.getAid());
            setAnchorId(value.getAnchorId());
            setCreativeId(value.getCreativeId());
            setUnitId(value.getUnitId());
            setSystemType(value.getSystemType());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
        }
    }
}
