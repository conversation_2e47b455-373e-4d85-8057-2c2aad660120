/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 程序化创意明细表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauProgrammaticCreativeDetailPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   accountId;
    private Integer   campaignId;
    private Integer   unitId;
    private Integer   creativeId;
    private Integer   templateGroupId;
    private Long      materialId;
    private Integer   bizStatus;
    private Integer   materialType;
    private String    materialMd5;
    private String    rejectedReason;
    private Integer   mgkTemplateId;
    private Long      mgkMediaId;

    public LauProgrammaticCreativeDetailPo() {}

    public LauProgrammaticCreativeDetailPo(LauProgrammaticCreativeDetailPo value) {
        this.id = value.id;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.accountId = value.accountId;
        this.campaignId = value.campaignId;
        this.unitId = value.unitId;
        this.creativeId = value.creativeId;
        this.templateGroupId = value.templateGroupId;
        this.materialId = value.materialId;
        this.bizStatus = value.bizStatus;
        this.materialType = value.materialType;
        this.materialMd5 = value.materialMd5;
        this.rejectedReason = value.rejectedReason;
        this.mgkTemplateId = value.mgkTemplateId;
        this.mgkMediaId = value.mgkMediaId;
    }

    public LauProgrammaticCreativeDetailPo(
        Long      id,
        Timestamp ctime,
        Timestamp mtime,
        Integer   accountId,
        Integer   campaignId,
        Integer   unitId,
        Integer   creativeId,
        Integer   templateGroupId,
        Long      materialId,
        Integer   bizStatus,
        Integer   materialType,
        String    materialMd5,
        String    rejectedReason,
        Integer   mgkTemplateId,
        Long      mgkMediaId
    ) {
        this.id = id;
        this.ctime = ctime;
        this.mtime = mtime;
        this.accountId = accountId;
        this.campaignId = campaignId;
        this.unitId = unitId;
        this.creativeId = creativeId;
        this.templateGroupId = templateGroupId;
        this.materialId = materialId;
        this.bizStatus = bizStatus;
        this.materialType = materialType;
        this.materialMd5 = materialMd5;
        this.rejectedReason = rejectedReason;
        this.mgkTemplateId = mgkTemplateId;
        this.mgkMediaId = mgkMediaId;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.id</code>. 自增id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.id</code>. 自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.account_id</code>. 账号ID
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.campaign_id</code>.
     * 计划ID
     */
    public Integer getCampaignId() {
        return this.campaignId;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.campaign_id</code>.
     * 计划ID
     */
    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.creative_id</code>.
     * 创意ID
     */
    public Integer getCreativeId() {
        return this.creativeId;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.creative_id</code>.
     * 创意ID
     */
    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    /**
     * Getter for
     * <code>lau_programmatic_creative_detail.template_group_id</code>. 模板组ID
     */
    public Integer getTemplateGroupId() {
        return this.templateGroupId;
    }

    /**
     * Setter for
     * <code>lau_programmatic_creative_detail.template_group_id</code>. 模板组ID
     */
    public void setTemplateGroupId(Integer templateGroupId) {
        this.templateGroupId = templateGroupId;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.material_id</code>.
     * 物料ID
     */
    public Long getMaterialId() {
        return this.materialId;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.material_id</code>.
     * 物料ID
     */
    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.biz_status</code>.
     * 业务状态: 0-审核通过, 1-待审核, 2-审核驳回
     */
    public Integer getBizStatus() {
        return this.bizStatus;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.biz_status</code>.
     * 业务状态: 0-审核通过, 1-待审核, 2-审核驳回
     */
    public void setBizStatus(Integer bizStatus) {
        this.bizStatus = bizStatus;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.material_type</code>.
     * 物料类型, 0 - 未知, 1 - 图片, 2 - 视频, 3 - GIF, 4, 标题, 5 - 三图, 6 - 视频和封面, 7-稿件
     */
    public Integer getMaterialType() {
        return this.materialType;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.material_type</code>.
     * 物料类型, 0 - 未知, 1 - 图片, 2 - 视频, 3 - GIF, 4, 标题, 5 - 三图, 6 - 视频和封面, 7-稿件
     */
    public void setMaterialType(Integer materialType) {
        this.materialType = materialType;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.material_md5</code>.
     * 物料内容的MD5值, 用来判断物料是否相同
     */
    public String getMaterialMd5() {
        return this.materialMd5;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.material_md5</code>.
     * 物料内容的MD5值, 用来判断物料是否相同
     */
    public void setMaterialMd5(String materialMd5) {
        this.materialMd5 = materialMd5;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.rejected_reason</code>.
     * 审核拒绝原因
     */
    public String getRejectedReason() {
        return this.rejectedReason;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.rejected_reason</code>.
     * 审核拒绝原因
     */
    public void setRejectedReason(String rejectedReason) {
        this.rejectedReason = rejectedReason;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.mgk_template_id</code>.
     * 创意中心模板id
     */
    public Integer getMgkTemplateId() {
        return this.mgkTemplateId;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.mgk_template_id</code>.
     * 创意中心模板id
     */
    public void setMgkTemplateId(Integer mgkTemplateId) {
        this.mgkTemplateId = mgkTemplateId;
    }

    /**
     * Getter for <code>lau_programmatic_creative_detail.mgk_media_id</code>.
     * 创意中心媒体id
     */
    public Long getMgkMediaId() {
        return this.mgkMediaId;
    }

    /**
     * Setter for <code>lau_programmatic_creative_detail.mgk_media_id</code>.
     * 创意中心媒体id
     */
    public void setMgkMediaId(Long mgkMediaId) {
        this.mgkMediaId = mgkMediaId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauProgrammaticCreativeDetailPo (");

        sb.append(id);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(accountId);
        sb.append(", ").append(campaignId);
        sb.append(", ").append(unitId);
        sb.append(", ").append(creativeId);
        sb.append(", ").append(templateGroupId);
        sb.append(", ").append(materialId);
        sb.append(", ").append(bizStatus);
        sb.append(", ").append(materialType);
        sb.append(", ").append(materialMd5);
        sb.append(", ").append(rejectedReason);
        sb.append(", ").append(mgkTemplateId);
        sb.append(", ").append(mgkMediaId);

        sb.append(")");
        return sb.toString();
    }
}
