/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 搜索否词表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitSearchNegativeKeywordPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   unitId;
    private Integer   type;
    private String    keyword;
    private String    keywordMd5;

    public LauUnitSearchNegativeKeywordPo() {}

    public LauUnitSearchNegativeKeywordPo(LauUnitSearchNegativeKeywordPo value) {
        this.id = value.id;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.unitId = value.unitId;
        this.type = value.type;
        this.keyword = value.keyword;
        this.keywordMd5 = value.keywordMd5;
    }

    public LauUnitSearchNegativeKeywordPo(
        Long      id,
        Timestamp ctime,
        Timestamp mtime,
        Integer   unitId,
        Integer   type,
        String    keyword,
        String    keywordMd5
    ) {
        this.id = id;
        this.ctime = ctime;
        this.mtime = mtime;
        this.unitId = unitId;
        this.type = type;
        this.keyword = keyword;
        this.keywordMd5 = keywordMd5;
    }

    /**
     * Getter for <code>lau_unit_search_negative_keyword.id</code>. 自增ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_unit_search_negative_keyword.id</code>. 自增ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_unit_search_negative_keyword.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_unit_search_negative_keyword.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_unit_search_negative_keyword.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_unit_search_negative_keyword.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_unit_search_negative_keyword.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_unit_search_negative_keyword.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for <code>lau_unit_search_negative_keyword.type</code>. 0-未知,
     * 1-精确否定, 2-短语否定
     */
    public Integer getType() {
        return this.type;
    }

    /**
     * Setter for <code>lau_unit_search_negative_keyword.type</code>. 0-未知,
     * 1-精确否定, 2-短语否定
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * Getter for <code>lau_unit_search_negative_keyword.keyword</code>. 关键词
     */
    public String getKeyword() {
        return this.keyword;
    }

    /**
     * Setter for <code>lau_unit_search_negative_keyword.keyword</code>. 关键词
     */
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    /**
     * Getter for <code>lau_unit_search_negative_keyword.keyword_md5</code>.
     * 关键词的md5
     */
    public String getKeywordMd5() {
        return this.keywordMd5;
    }

    /**
     * Setter for <code>lau_unit_search_negative_keyword.keyword_md5</code>.
     * 关键词的md5
     */
    public void setKeywordMd5(String keywordMd5) {
        this.keywordMd5 = keywordMd5;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauUnitSearchNegativeKeywordPo (");

        sb.append(id);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(unitId);
        sb.append(", ").append(type);
        sb.append(", ").append(keyword);
        sb.append(", ").append(keywordMd5);

        sb.append(")");
        return sb.toString();
    }
}
