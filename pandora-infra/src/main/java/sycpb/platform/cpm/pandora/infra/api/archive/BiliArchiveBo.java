package sycpb.platform.cpm.pandora.infra.api.archive;

import lombok.Data;

@Data
public class BiliArchiveBo {
    private Long avid;
    private Long cid;
    private Long mid;
    private Integer tid;
    private Integer width;
    private Integer height;
    private String nickName;
    private String coverUrl;
    private Integer state;
    private String title;
    private String tagIds;

    private Integer autoplay;

    // 播放量
    private Integer play;

    /**
     * 稿件总时长
     */
    private Integer duration;
    /**
     * 视频分辨率
     */
    private DimensionBo dimension;
    /**
     * 稿件状态详情
     */
    private String stateDesc;
    /**
     * 是否竖屏
     */
    private Boolean isVerticalScreen;
    /**
     * 是否pgc 根据 attribute 计算
     */
    private Boolean isPgc;
    /**
     * 是否pugv 根据 attribute 计算
     */
    private Boolean isPugv;

    private Integer tidV2;

}
