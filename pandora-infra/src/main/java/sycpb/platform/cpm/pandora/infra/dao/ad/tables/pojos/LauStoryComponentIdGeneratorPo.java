/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauStoryComponentIdGeneratorPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Timestamp ctime;
    private Timestamp mtime;

    public LauStoryComponentIdGeneratorPo() {}

    public LauStoryComponentIdGeneratorPo(LauStoryComponentIdGeneratorPo value) {
        this.id = value.id;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
    }

    public LauStoryComponentIdGeneratorPo(
        Long      id,
        Timestamp ctime,
        Timestamp mtime
    ) {
        this.id = id;
        this.ctime = ctime;
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_story_component_id_generator.id</code>. id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_story_component_id_generator.id</code>. id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_story_component_id_generator.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_story_component_id_generator.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_story_component_id_generator.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_story_component_id_generator.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauStoryComponentIdGeneratorPo (");

        sb.append(id);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);

        sb.append(")");
        return sb.toString();
    }
}
