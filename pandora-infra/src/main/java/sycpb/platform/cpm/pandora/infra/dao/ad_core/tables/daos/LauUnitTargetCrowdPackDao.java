/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitTargetCrowdPack;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitTargetCrowdPackPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitTargetCrowdPackRecord;


/**
 * 单元-人群包定向表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauUnitTargetCrowdPackDao extends DAOImpl<LauUnitTargetCrowdPackRecord, LauUnitTargetCrowdPackPo, Integer> {

    /**
     * Create a new LauUnitTargetCrowdPackDao without any configuration
     */
    public LauUnitTargetCrowdPackDao() {
        super(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK, LauUnitTargetCrowdPackPo.class);
    }

    /**
     * Create a new LauUnitTargetCrowdPackDao with an attached configuration
     */
    @Autowired
    public LauUnitTargetCrowdPackDao(Configuration configuration) {
        super(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK, LauUnitTargetCrowdPackPo.class, configuration);
    }

    @Override
    public Integer getId(LauUnitTargetCrowdPackPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchById(Integer... values) {
        return fetch(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauUnitTargetCrowdPackPo fetchOneById(Integer value) {
        return fetchOne(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.ID, value);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchByUnitId(Integer... values) {
        return fetch(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>crowd_pack_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchRangeOfCrowdPackId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.CROWD_PACK_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>crowd_pack_id IN (values)</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchByCrowdPackId(Integer... values) {
        return fetch(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.CROWD_PACK_ID, values);
    }

    /**
     * Fetch records that have <code>group_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchRangeOfGroupId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.GROUP_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>group_id IN (values)</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchByGroupId(Integer... values) {
        return fetch(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.GROUP_ID, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.MTIME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchRangeOfType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<LauUnitTargetCrowdPackPo> fetchByType(Integer... values) {
        return fetch(TLauUnitTargetCrowdPack.LAU_UNIT_TARGET_CROWD_PACK.TYPE, values);
    }
}
