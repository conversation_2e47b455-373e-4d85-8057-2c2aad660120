/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitBiliMiniGameRecord;


/**
 * 单元b站小游戏表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauUnitBiliMiniGame extends TableImpl<LauUnitBiliMiniGameRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_unit_bili_mini_game</code>
     */
    public static final TLauUnitBiliMiniGame LAU_UNIT_BILI_MINI_GAME = new TLauUnitBiliMiniGame();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauUnitBiliMiniGameRecord> getRecordType() {
        return LauUnitBiliMiniGameRecord.class;
    }

    /**
     * The column <code>lau_unit_bili_mini_game.id</code>.
     */
    public final TableField<LauUnitBiliMiniGameRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

    /**
     * The column <code>lau_unit_bili_mini_game.ctime</code>. 创建时间
     */
    public final TableField<LauUnitBiliMiniGameRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_unit_bili_mini_game.mtime</code>. 修改时间
     */
    public final TableField<LauUnitBiliMiniGameRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>lau_unit_bili_mini_game.account_id</code>. 账号id
     */
    public final TableField<LauUnitBiliMiniGameRecord, Integer> ACCOUNT_ID = createField(DSL.name("account_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "账号id");

    /**
     * The column <code>lau_unit_bili_mini_game.campaign_id</code>. 计划id
     */
    public final TableField<LauUnitBiliMiniGameRecord, Integer> CAMPAIGN_ID = createField(DSL.name("campaign_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "计划id");

    /**
     * The column <code>lau_unit_bili_mini_game.unit_id</code>. 单元id
     */
    public final TableField<LauUnitBiliMiniGameRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元id");

    /**
     * The column <code>lau_unit_bili_mini_game.game_base_id</code>. 游戏平台id
     */
    public final TableField<LauUnitBiliMiniGameRecord, Integer> GAME_BASE_ID = createField(DSL.name("game_base_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "游戏平台id");

    /**
     * The column <code>lau_unit_bili_mini_game.cp_mid</code>. 游戏绑定的cp_mid
     */
    public final TableField<LauUnitBiliMiniGameRecord, Long> CP_MID = createField(DSL.name("cp_mid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "游戏绑定的cp_mid");

    /**
     * The column <code>lau_unit_bili_mini_game.app_id</code>. 小游戏id
     */
    public final TableField<LauUnitBiliMiniGameRecord, String> APP_ID = createField(DSL.name("app_id"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "小游戏id");

    private TLauUnitBiliMiniGame(Name alias, Table<LauUnitBiliMiniGameRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauUnitBiliMiniGame(Name alias, Table<LauUnitBiliMiniGameRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("单元b站小游戏表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_unit_bili_mini_game</code> table reference
     */
    public TLauUnitBiliMiniGame(String alias) {
        this(DSL.name(alias), LAU_UNIT_BILI_MINI_GAME);
    }

    /**
     * Create an aliased <code>lau_unit_bili_mini_game</code> table reference
     */
    public TLauUnitBiliMiniGame(Name alias) {
        this(alias, LAU_UNIT_BILI_MINI_GAME);
    }

    /**
     * Create a <code>lau_unit_bili_mini_game</code> table reference
     */
    public TLauUnitBiliMiniGame() {
        this(DSL.name("lau_unit_bili_mini_game"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauUnitBiliMiniGameRecord, Long> getIdentity() {
        return (Identity<LauUnitBiliMiniGameRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauUnitBiliMiniGameRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauUnitBiliMiniGame.LAU_UNIT_BILI_MINI_GAME, DSL.name("KEY_lau_unit_bili_mini_game_PRIMARY"), new TableField[] { TLauUnitBiliMiniGame.LAU_UNIT_BILI_MINI_GAME.ID }, true);
    }

    @Override
    public List<UniqueKey<LauUnitBiliMiniGameRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TLauUnitBiliMiniGame.LAU_UNIT_BILI_MINI_GAME, DSL.name("KEY_lau_unit_bili_mini_game_uk_unit_id"), new TableField[] { TLauUnitBiliMiniGame.LAU_UNIT_BILI_MINI_GAME.UNIT_ID }, true)
        );
    }

    @Override
    public TLauUnitBiliMiniGame as(String alias) {
        return new TLauUnitBiliMiniGame(DSL.name(alias), this);
    }

    @Override
    public TLauUnitBiliMiniGame as(Name alias) {
        return new TLauUnitBiliMiniGame(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitBiliMiniGame rename(String name) {
        return new TLauUnitBiliMiniGame(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitBiliMiniGame rename(Name name) {
        return new TLauUnitBiliMiniGame(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<Long, Timestamp, Timestamp, Integer, Integer, Integer, Integer, Long, String> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
