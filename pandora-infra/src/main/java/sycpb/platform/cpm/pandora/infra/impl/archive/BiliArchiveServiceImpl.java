package sycpb.platform.cpm.pandora.infra.impl.archive;

import com.bapis.archive.service.Arc;
import com.bapis.archive.service.ArchiveGrpc;
import com.bapis.archive.service.ArcsRequest;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;
import sycpb.platform.cpm.pandora.infra.api.archive.BiliArchiveBo;
import sycpb.platform.cpm.pandora.infra.api.archive.IBiliArchiveService;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class BiliArchiveServiceImpl implements IBiliArchiveService {
    @RPCClient("archive.service")
    private ArchiveGrpc.ArchiveBlockingStub archiveBlockingStub;

    @Override
    public Map<Long, BiliArchiveBo> fetchMap(Collection<Long> x) {
        final var validAvids = x.stream()
                .filter(NumberUtils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validAvids)) return Map.of();

        final Map<Long, BiliArchiveBo> map = new HashMap<>();
        for (List<Long> avids : Lists.partition(validAvids, 50)) {
            final var resp = archiveBlockingStub.arcs(ArcsRequest.newBuilder()
                    .addAllAids(avids)
                    .build());
            for (Map.Entry<Long, Arc> entry : resp.getArcsMap().entrySet()) {
                map.put(entry.getKey(), BiliArchiveMapper.MAPPER.fromRo(entry.getValue()));
            }
        }
        return map;
    }

    @Override
    public BiliArchiveBo get(Long x) {
        return fetchMap(List.of(x)).get(x);
    }


}
