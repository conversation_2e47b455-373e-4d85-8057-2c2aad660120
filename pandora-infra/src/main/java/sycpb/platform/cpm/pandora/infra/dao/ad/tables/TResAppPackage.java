/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.ResAppPackageRecord;


/**
 * app应用包信息表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TResAppPackage extends TableImpl<ResAppPackageRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>res_app_package</code>
     */
    public static final TResAppPackage RES_APP_PACKAGE = new TResAppPackage();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResAppPackageRecord> getRecordType() {
        return ResAppPackageRecord.class;
    }

    /**
     * The column <code>res_app_package.id</code>. 主键ID
     */
    public final TableField<ResAppPackageRecord, Integer> ID = createField(DSL.name("id"), SQLDataType.INTEGER.nullable(false).identity(true), this, "主键ID");

    /**
     * The column <code>res_app_package.account_id</code>. 账号id
     */
    public final TableField<ResAppPackageRecord, Integer> ACCOUNT_ID = createField(DSL.name("account_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "账号id");

    /**
     * The column <code>res_app_package.name</code>. 应用包名称
     */
    public final TableField<ResAppPackageRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(32).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "应用包名称");

    /**
     * The column <code>res_app_package.url</code>. 应用包原始下载链接
     */
    public final TableField<ResAppPackageRecord, String> URL = createField(DSL.name("url"), SQLDataType.VARCHAR(512).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "应用包原始下载链接");

    /**
     * The column <code>res_app_package.package_name</code>. 应用包的包名称
     */
    public final TableField<ResAppPackageRecord, String> PACKAGE_NAME = createField(DSL.name("package_name"), SQLDataType.VARCHAR(128).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "应用包的包名称");

    /**
     * The column <code>res_app_package.app_name</code>. 应用名称
     */
    public final TableField<ResAppPackageRecord, String> APP_NAME = createField(DSL.name("app_name"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "应用名称");

    /**
     * The column <code>res_app_package.platform</code>. 适应系统 1-IOS,
     * 2-Android,3-iphone, 4-ipad
     */
    public final TableField<ResAppPackageRecord, Integer> PLATFORM = createField(DSL.name("platform"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "适应系统 1-IOS, 2-Android,3-iphone, 4-ipad");

    /**
     * The column <code>res_app_package.version</code>. 版本号
     */
    public final TableField<ResAppPackageRecord, String> VERSION = createField(DSL.name("version"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "版本号");

    /**
     * The column <code>res_app_package.size</code>. 应用包大小（单位字节）
     */
    public final TableField<ResAppPackageRecord, Integer> SIZE = createField(DSL.name("size"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "应用包大小（单位字节）");

    /**
     * The column <code>res_app_package.md5</code>. 应用包的MD5
     */
    public final TableField<ResAppPackageRecord, String> MD5 = createField(DSL.name("md5"), SQLDataType.VARCHAR(32).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "应用包的MD5");

    /**
     * The column <code>res_app_package.icon_url</code>. 图标url
     */
    public final TableField<ResAppPackageRecord, String> ICON_URL = createField(DSL.name("icon_url"), SQLDataType.VARCHAR(255).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "图标url");

    /**
     * The column <code>res_app_package.ctime</code>. 创建时间
     */
    public final TableField<ResAppPackageRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>res_app_package.mtime</code>. 修改时间
     */
    public final TableField<ResAppPackageRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>res_app_package.is_deleted</code>. 软删除 0-有效, 1-删除
     */
    public final TableField<ResAppPackageRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除 0-有效, 1-删除");

    /**
     * The column <code>res_app_package.internal_url</code>. 应用包内部下载链接
     */
    public final TableField<ResAppPackageRecord, String> INTERNAL_URL = createField(DSL.name("internal_url"), SQLDataType.VARCHAR(255).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "应用包内部下载链接");

    /**
     * The column <code>res_app_package.status</code>. 应用包状态 0-有效，1-无效
     */
    public final TableField<ResAppPackageRecord, Integer> STATUS = createField(DSL.name("status"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "应用包状态 0-有效，1-无效");

    /**
     * The column <code>res_app_package.platform_status</code>. 平台状态 0-有效, 1-禁用
     */
    public final TableField<ResAppPackageRecord, Integer> PLATFORM_STATUS = createField(DSL.name("platform_status"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "平台状态 0-有效, 1-禁用");

    /**
     * The column <code>res_app_package.developer_name</code>. 开发商名称
     */
    public final TableField<ResAppPackageRecord, String> DEVELOPER_NAME = createField(DSL.name("developer_name"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "开发商名称");

    /**
     * The column <code>res_app_package.authority_url</code>. 权限地址
     */
    public final TableField<ResAppPackageRecord, String> AUTHORITY_URL = createField(DSL.name("authority_url"), SQLDataType.VARCHAR(128).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "权限地址");

    /**
     * The column <code>res_app_package.auth_code_list</code>. 权限code
     */
    public final TableField<ResAppPackageRecord, String> AUTH_CODE_LIST = createField(DSL.name("auth_code_list"), SQLDataType.VARCHAR(512).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "权限code");

    /**
     * The column <code>res_app_package.apk_update_time</code>. 安装包更新时间
     */
    public final TableField<ResAppPackageRecord, Timestamp> APK_UPDATE_TIME = createField(DSL.name("apk_update_time"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.inline("'1970-01-01 00:00:00'", SQLDataType.TIMESTAMP)), this, "安装包更新时间");

    /**
     * The column <code>res_app_package.privacy_policy</code>. 隐私政策地址
     */
    public final TableField<ResAppPackageRecord, String> PRIVACY_POLICY = createField(DSL.name("privacy_policy"), SQLDataType.VARCHAR(512).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "隐私政策地址");

    /**
     * The column <code>res_app_package.dmp_app_id</code>. dmp_app_id
     */
    public final TableField<ResAppPackageRecord, Integer> DMP_APP_ID = createField(DSL.name("dmp_app_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "dmp_app_id");

    /**
     * The column <code>res_app_package.is_new_fly</code>. 是否新起飞：0-否 1-是
     */
    public final TableField<ResAppPackageRecord, Integer> IS_NEW_FLY = createField(DSL.name("is_new_fly"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否新起飞：0-否 1-是");

    /**
     * The column <code>res_app_package.description</code>. 描述信息
     */
    public final TableField<ResAppPackageRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.CLOB.defaultValue(DSL.inline("NULL", SQLDataType.CLOB)), this, "描述信息");

    /**
     * The column <code>res_app_package.sub_title</code>. 简介
     */
    public final TableField<ResAppPackageRecord, String> SUB_TITLE = createField(DSL.name("sub_title"), SQLDataType.VARCHAR(256).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "简介");

    /**
     * The column <code>res_app_package.is_icon_valid</code>. 安卓应用包头像是否可用: 0-未知;
     * 1-可用; 2-不可用
     */
    public final TableField<ResAppPackageRecord, Integer> IS_ICON_VALID = createField(DSL.name("is_icon_valid"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "安卓应用包头像是否可用: 0-未知; 1-可用; 2-不可用");

    /**
     * The column <code>res_app_package.device_app_store</code>. 已上架的安卓App
     * Store。存储格式  1,2,4  表示 小米、华为、VIVO 已上架
     */
    public final TableField<ResAppPackageRecord, String> DEVICE_APP_STORE = createField(DSL.name("device_app_store"), SQLDataType.VARCHAR(256).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "已上架的安卓App Store。存储格式  1,2,4  表示 小米、华为、VIVO 已上架");

    /**
     * The column <code>res_app_package.copy_source_account_id</code>. 复制来源账户id
     */
    public final TableField<ResAppPackageRecord, Integer> COPY_SOURCE_ACCOUNT_ID = createField(DSL.name("copy_source_account_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "复制来源账户id");

    /**
     * The column <code>res_app_package.record_number</code>. app备案号
     */
    public final TableField<ResAppPackageRecord, String> RECORD_NUMBER = createField(DSL.name("record_number"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "app备案号");

    /**
     * The column <code>res_app_package.add_type</code>. 添加方式：链接添加(0)or手动上传(1)
     */
    public final TableField<ResAppPackageRecord, Integer> ADD_TYPE = createField(DSL.name("add_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "添加方式：链接添加(0)or手动上传(1)");

    private TResAppPackage(Name alias, Table<ResAppPackageRecord> aliased) {
        this(alias, aliased, null);
    }

    private TResAppPackage(Name alias, Table<ResAppPackageRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("app应用包信息表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>res_app_package</code> table reference
     */
    public TResAppPackage(String alias) {
        this(DSL.name(alias), RES_APP_PACKAGE);
    }

    /**
     * Create an aliased <code>res_app_package</code> table reference
     */
    public TResAppPackage(Name alias) {
        this(alias, RES_APP_PACKAGE);
    }

    /**
     * Create a <code>res_app_package</code> table reference
     */
    public TResAppPackage() {
        this(DSL.name("res_app_package"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<ResAppPackageRecord, Integer> getIdentity() {
        return (Identity<ResAppPackageRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<ResAppPackageRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TResAppPackage.RES_APP_PACKAGE, DSL.name("KEY_res_app_package_PRIMARY"), new TableField[] { TResAppPackage.RES_APP_PACKAGE.ID }, true);
    }

    @Override
    public TResAppPackage as(String alias) {
        return new TResAppPackage(DSL.name(alias), this);
    }

    @Override
    public TResAppPackage as(Name alias) {
        return new TResAppPackage(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TResAppPackage rename(String name) {
        return new TResAppPackage(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TResAppPackage rename(Name name) {
        return new TResAppPackage(name, null);
    }
}
