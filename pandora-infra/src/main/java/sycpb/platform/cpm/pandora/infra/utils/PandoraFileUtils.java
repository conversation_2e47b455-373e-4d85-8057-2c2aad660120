package sycpb.platform.cpm.pandora.infra.utils;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.MessageFormat;

@Slf4j
public class PandoraFileUtils {
    @SneakyThrows
    public static File createIfNotExists(Path path) {
        final var file = path.toFile();
        if (file.exists()) return file;

        createDirIfNotExists(Paths.get(file.getParent()).toFile());
        final var ok = file.createNewFile();
        Assert.isTrue(ok, MessageFormat.format("文件{0}创建失败", path.toString()));
        return file;
    }

    private static void createDirIfNotExists(File f) {
        if (f.exists()) return;

        createDirIfNotExists(f.getParentFile());
        final boolean ok = f.mkdir();
        Assert.isTrue(ok, MessageFormat.format("目录{0}创建失败", f.getPath()));
    }
}
