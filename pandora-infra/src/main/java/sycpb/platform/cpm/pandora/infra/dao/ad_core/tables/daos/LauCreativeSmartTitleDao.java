/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeSmartTitle;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeSmartTitlePo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeSmartTitleRecord;


/**
 * 创意智能标题表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauCreativeSmartTitleDao extends DAOImpl<LauCreativeSmartTitleRecord, LauCreativeSmartTitlePo, Long> {

    /**
     * Create a new LauCreativeSmartTitleDao without any configuration
     */
    public LauCreativeSmartTitleDao() {
        super(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE, LauCreativeSmartTitlePo.class);
    }

    /**
     * Create a new LauCreativeSmartTitleDao with an attached configuration
     */
    @Autowired
    public LauCreativeSmartTitleDao(Configuration configuration) {
        super(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE, LauCreativeSmartTitlePo.class, configuration);
    }

    @Override
    public Long getId(LauCreativeSmartTitlePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeSmartTitlePo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauCreativeSmartTitlePo> fetchById(Long... values) {
        return fetch(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauCreativeSmartTitlePo fetchOneById(Long value) {
        return fetchOne(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.ID, value);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeSmartTitlePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauCreativeSmartTitlePo> fetchByCtime(Timestamp... values) {
        return fetch(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeSmartTitlePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauCreativeSmartTitlePo> fetchByMtime(Timestamp... values) {
        return fetch(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.MTIME, values);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeSmartTitlePo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauCreativeSmartTitlePo> fetchByUnitId(Integer... values) {
        return fetch(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeSmartTitlePo> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauCreativeSmartTitlePo> fetchByCreativeId(Integer... values) {
        return fetch(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.CREATIVE_ID, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeSmartTitlePo> fetchRangeOfType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<LauCreativeSmartTitlePo> fetchByType(Integer... values) {
        return fetch(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.TYPE, values);
    }

    /**
     * Fetch records that have <code>default_value BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeSmartTitlePo> fetchRangeOfDefaultValue(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.DEFAULT_VALUE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>default_value IN (values)</code>
     */
    public List<LauCreativeSmartTitlePo> fetchByDefaultValue(String... values) {
        return fetch(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.DEFAULT_VALUE, values);
    }

    /**
     * Fetch records that have <code>material_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeSmartTitlePo> fetchRangeOfMaterialId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.MATERIAL_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>material_id IN (values)</code>
     */
    public List<LauCreativeSmartTitlePo> fetchByMaterialId(Long... values) {
        return fetch(TLauCreativeSmartTitle.LAU_CREATIVE_SMART_TITLE.MATERIAL_ID, values);
    }
}
