/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 投放产品全部信息
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AdProductTotalInfoPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      adProductId;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   bizStatus;
    private Timestamp deleteTime;
    private Integer   belongType;
    private Integer   libraryType;
    private String    adProductName;
    private Long      firstCategoryId;
    private Long      secondCategoryId;
    private Long      thirdCategoryId;
    private String    firstCategoryName;
    private String    secondCategoryName;
    private String    adOriginalPrice;
    private String    adMainImgUrl;
    private String    adExtraImgUrl;
    private String    h5LandingPageUrl;
    private String    pcLandingPageUrl;
    private String    iosLandingPageUrl;
    private String    androidLandingPageUrl;
    private String    adAttributes;
    private Long      libraryId;
    private Integer   isDeleted;
    private String    thirdCategoryName;
    private Integer   accountId;

    public AdProductTotalInfoPo() {}

    public AdProductTotalInfoPo(AdProductTotalInfoPo value) {
        this.adProductId = value.adProductId;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.bizStatus = value.bizStatus;
        this.deleteTime = value.deleteTime;
        this.belongType = value.belongType;
        this.libraryType = value.libraryType;
        this.adProductName = value.adProductName;
        this.firstCategoryId = value.firstCategoryId;
        this.secondCategoryId = value.secondCategoryId;
        this.thirdCategoryId = value.thirdCategoryId;
        this.firstCategoryName = value.firstCategoryName;
        this.secondCategoryName = value.secondCategoryName;
        this.adOriginalPrice = value.adOriginalPrice;
        this.adMainImgUrl = value.adMainImgUrl;
        this.adExtraImgUrl = value.adExtraImgUrl;
        this.h5LandingPageUrl = value.h5LandingPageUrl;
        this.pcLandingPageUrl = value.pcLandingPageUrl;
        this.iosLandingPageUrl = value.iosLandingPageUrl;
        this.androidLandingPageUrl = value.androidLandingPageUrl;
        this.adAttributes = value.adAttributes;
        this.libraryId = value.libraryId;
        this.isDeleted = value.isDeleted;
        this.thirdCategoryName = value.thirdCategoryName;
        this.accountId = value.accountId;
    }

    public AdProductTotalInfoPo(
        Long      adProductId,
        Timestamp ctime,
        Timestamp mtime,
        Integer   bizStatus,
        Timestamp deleteTime,
        Integer   belongType,
        Integer   libraryType,
        String    adProductName,
        Long      firstCategoryId,
        Long      secondCategoryId,
        Long      thirdCategoryId,
        String    firstCategoryName,
        String    secondCategoryName,
        String    adOriginalPrice,
        String    adMainImgUrl,
        String    adExtraImgUrl,
        String    h5LandingPageUrl,
        String    pcLandingPageUrl,
        String    iosLandingPageUrl,
        String    androidLandingPageUrl,
        String    adAttributes,
        Long      libraryId,
        Integer   isDeleted,
        String    thirdCategoryName,
        Integer   accountId
    ) {
        this.adProductId = adProductId;
        this.ctime = ctime;
        this.mtime = mtime;
        this.bizStatus = bizStatus;
        this.deleteTime = deleteTime;
        this.belongType = belongType;
        this.libraryType = libraryType;
        this.adProductName = adProductName;
        this.firstCategoryId = firstCategoryId;
        this.secondCategoryId = secondCategoryId;
        this.thirdCategoryId = thirdCategoryId;
        this.firstCategoryName = firstCategoryName;
        this.secondCategoryName = secondCategoryName;
        this.adOriginalPrice = adOriginalPrice;
        this.adMainImgUrl = adMainImgUrl;
        this.adExtraImgUrl = adExtraImgUrl;
        this.h5LandingPageUrl = h5LandingPageUrl;
        this.pcLandingPageUrl = pcLandingPageUrl;
        this.iosLandingPageUrl = iosLandingPageUrl;
        this.androidLandingPageUrl = androidLandingPageUrl;
        this.adAttributes = adAttributes;
        this.libraryId = libraryId;
        this.isDeleted = isDeleted;
        this.thirdCategoryName = thirdCategoryName;
        this.accountId = accountId;
    }

    /**
     * Getter for <code>ad_product_total_info.ad_product_id</code>. 产品id
     */
    public Long getAdProductId() {
        return this.adProductId;
    }

    /**
     * Setter for <code>ad_product_total_info.ad_product_id</code>. 产品id
     */
    public void setAdProductId(Long adProductId) {
        this.adProductId = adProductId;
    }

    /**
     * Getter for <code>ad_product_total_info.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>ad_product_total_info.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>ad_product_total_info.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>ad_product_total_info.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>ad_product_total_info.biz_status</code>. 在线状态 1-有效 0-无效
     */
    public Integer getBizStatus() {
        return this.bizStatus;
    }

    /**
     * Setter for <code>ad_product_total_info.biz_status</code>. 在线状态 1-有效 0-无效
     */
    public void setBizStatus(Integer bizStatus) {
        this.bizStatus = bizStatus;
    }

    /**
     * Getter for <code>ad_product_total_info.delete_time</code>. 删除时间
     */
    public Timestamp getDeleteTime() {
        return this.deleteTime;
    }

    /**
     * Setter for <code>ad_product_total_info.delete_time</code>. 删除时间
     */
    public void setDeleteTime(Timestamp deleteTime) {
        this.deleteTime = deleteTime;
    }

    /**
     * Getter for <code>ad_product_total_info.belong_type</code>. 数据来源
     */
    public Integer getBelongType() {
        return this.belongType;
    }

    /**
     * Setter for <code>ad_product_total_info.belong_type</code>. 数据来源
     */
    public void setBelongType(Integer belongType) {
        this.belongType = belongType;
    }

    /**
     * Getter for <code>ad_product_total_info.library_type</code>. 产品库类型
     */
    public Integer getLibraryType() {
        return this.libraryType;
    }

    /**
     * Setter for <code>ad_product_total_info.library_type</code>. 产品库类型
     */
    public void setLibraryType(Integer libraryType) {
        this.libraryType = libraryType;
    }

    /**
     * Getter for <code>ad_product_total_info.ad_product_name</code>. 产品名称
     */
    public String getAdProductName() {
        return this.adProductName;
    }

    /**
     * Setter for <code>ad_product_total_info.ad_product_name</code>. 产品名称
     */
    public void setAdProductName(String adProductName) {
        this.adProductName = adProductName;
    }

    /**
     * Getter for <code>ad_product_total_info.first_category_id</code>. 产品一级类目id
     */
    public Long getFirstCategoryId() {
        return this.firstCategoryId;
    }

    /**
     * Setter for <code>ad_product_total_info.first_category_id</code>. 产品一级类目id
     */
    public void setFirstCategoryId(Long firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    /**
     * Getter for <code>ad_product_total_info.second_category_id</code>.
     * 产品二级类目ID
     */
    public Long getSecondCategoryId() {
        return this.secondCategoryId;
    }

    /**
     * Setter for <code>ad_product_total_info.second_category_id</code>.
     * 产品二级类目ID
     */
    public void setSecondCategoryId(Long secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
    }

    /**
     * Getter for <code>ad_product_total_info.third_category_id</code>. 产品三级类目id
     */
    public Long getThirdCategoryId() {
        return this.thirdCategoryId;
    }

    /**
     * Setter for <code>ad_product_total_info.third_category_id</code>. 产品三级类目id
     */
    public void setThirdCategoryId(Long thirdCategoryId) {
        this.thirdCategoryId = thirdCategoryId;
    }

    /**
     * Getter for <code>ad_product_total_info.first_category_name</code>.
     * 产品一级类目名称
     */
    public String getFirstCategoryName() {
        return this.firstCategoryName;
    }

    /**
     * Setter for <code>ad_product_total_info.first_category_name</code>.
     * 产品一级类目名称
     */
    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    /**
     * Getter for <code>ad_product_total_info.second_category_name</code>.
     * 产品二级类目名称
     */
    public String getSecondCategoryName() {
        return this.secondCategoryName;
    }

    /**
     * Setter for <code>ad_product_total_info.second_category_name</code>.
     * 产品二级类目名称
     */
    public void setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
    }

    /**
     * Getter for <code>ad_product_total_info.ad_original_price</code>. 产品原价
     */
    public String getAdOriginalPrice() {
        return this.adOriginalPrice;
    }

    /**
     * Setter for <code>ad_product_total_info.ad_original_price</code>. 产品原价
     */
    public void setAdOriginalPrice(String adOriginalPrice) {
        this.adOriginalPrice = adOriginalPrice;
    }

    /**
     * Getter for <code>ad_product_total_info.ad_main_img_url</code>. 主图URL
     */
    public String getAdMainImgUrl() {
        return this.adMainImgUrl;
    }

    /**
     * Setter for <code>ad_product_total_info.ad_main_img_url</code>. 主图URL
     */
    public void setAdMainImgUrl(String adMainImgUrl) {
        this.adMainImgUrl = adMainImgUrl;
    }

    /**
     * Getter for <code>ad_product_total_info.ad_extra_img_url</code>. 额外图片URL
     */
    public String getAdExtraImgUrl() {
        return this.adExtraImgUrl;
    }

    /**
     * Setter for <code>ad_product_total_info.ad_extra_img_url</code>. 额外图片URL
     */
    public void setAdExtraImgUrl(String adExtraImgUrl) {
        this.adExtraImgUrl = adExtraImgUrl;
    }

    /**
     * Getter for <code>ad_product_total_info.h5_landing_page_url</code>.
     * H5落地页URL
     */
    public String getH5LandingPageUrl() {
        return this.h5LandingPageUrl;
    }

    /**
     * Setter for <code>ad_product_total_info.h5_landing_page_url</code>.
     * H5落地页URL
     */
    public void setH5LandingPageUrl(String h5LandingPageUrl) {
        this.h5LandingPageUrl = h5LandingPageUrl;
    }

    /**
     * Getter for <code>ad_product_total_info.pc_landing_page_url</code>.
     * PC端落地页URL
     */
    public String getPcLandingPageUrl() {
        return this.pcLandingPageUrl;
    }

    /**
     * Setter for <code>ad_product_total_info.pc_landing_page_url</code>.
     * PC端落地页URL
     */
    public void setPcLandingPageUrl(String pcLandingPageUrl) {
        this.pcLandingPageUrl = pcLandingPageUrl;
    }

    /**
     * Getter for <code>ad_product_total_info.ios_landing_page_url</code>.
     * IOS落地页URL
     */
    public String getIosLandingPageUrl() {
        return this.iosLandingPageUrl;
    }

    /**
     * Setter for <code>ad_product_total_info.ios_landing_page_url</code>.
     * IOS落地页URL
     */
    public void setIosLandingPageUrl(String iosLandingPageUrl) {
        this.iosLandingPageUrl = iosLandingPageUrl;
    }

    /**
     * Getter for <code>ad_product_total_info.android_landing_page_url</code>.
     * Android落地页URL
     */
    public String getAndroidLandingPageUrl() {
        return this.androidLandingPageUrl;
    }

    /**
     * Setter for <code>ad_product_total_info.android_landing_page_url</code>.
     * Android落地页URL
     */
    public void setAndroidLandingPageUrl(String androidLandingPageUrl) {
        this.androidLandingPageUrl = androidLandingPageUrl;
    }

    /**
     * Getter for <code>ad_product_total_info.ad_attributes</code>. 产品属性
     */
    public String getAdAttributes() {
        return this.adAttributes;
    }

    /**
     * Setter for <code>ad_product_total_info.ad_attributes</code>. 产品属性
     */
    public void setAdAttributes(String adAttributes) {
        this.adAttributes = adAttributes;
    }

    /**
     * Getter for <code>ad_product_total_info.library_id</code>. 产品库id
     */
    public Long getLibraryId() {
        return this.libraryId;
    }

    /**
     * Setter for <code>ad_product_total_info.library_id</code>. 产品库id
     */
    public void setLibraryId(Long libraryId) {
        this.libraryId = libraryId;
    }

    /**
     * Getter for <code>ad_product_total_info.is_deleted</code>. 是否删除 0-正常 1-已删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>ad_product_total_info.is_deleted</code>. 是否删除 0-正常 1-已删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>ad_product_total_info.third_category_name</code>.
     * 产品二级类目名称
     */
    public String getThirdCategoryName() {
        return this.thirdCategoryName;
    }

    /**
     * Setter for <code>ad_product_total_info.third_category_name</code>.
     * 产品二级类目名称
     */
    public void setThirdCategoryName(String thirdCategoryName) {
        this.thirdCategoryName = thirdCategoryName;
    }

    /**
     * Getter for <code>ad_product_total_info.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>ad_product_total_info.account_id</code>. 账户id
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("AdProductTotalInfoPo (");

        sb.append(adProductId);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(bizStatus);
        sb.append(", ").append(deleteTime);
        sb.append(", ").append(belongType);
        sb.append(", ").append(libraryType);
        sb.append(", ").append(adProductName);
        sb.append(", ").append(firstCategoryId);
        sb.append(", ").append(secondCategoryId);
        sb.append(", ").append(thirdCategoryId);
        sb.append(", ").append(firstCategoryName);
        sb.append(", ").append(secondCategoryName);
        sb.append(", ").append(adOriginalPrice);
        sb.append(", ").append(adMainImgUrl);
        sb.append(", ").append(adExtraImgUrl);
        sb.append(", ").append(h5LandingPageUrl);
        sb.append(", ").append(pcLandingPageUrl);
        sb.append(", ").append(iosLandingPageUrl);
        sb.append(", ").append(androidLandingPageUrl);
        sb.append(", ").append(adAttributes);
        sb.append(", ").append(libraryId);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(thirdCategoryName);
        sb.append(", ").append(accountId);

        sb.append(")");
        return sb.toString();
    }
}
