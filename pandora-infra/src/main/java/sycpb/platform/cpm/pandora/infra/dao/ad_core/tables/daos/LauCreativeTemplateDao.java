/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeTemplate;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeTemplatePo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeTemplateRecord;


/**
 * 创意与广告位组及模板关联表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauCreativeTemplateDao extends DAOImpl<LauCreativeTemplateRecord, LauCreativeTemplatePo, Long> {

    /**
     * Create a new LauCreativeTemplateDao without any configuration
     */
    public LauCreativeTemplateDao() {
        super(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE, LauCreativeTemplatePo.class);
    }

    /**
     * Create a new LauCreativeTemplateDao with an attached configuration
     */
    @Autowired
    public LauCreativeTemplateDao(Configuration configuration) {
        super(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE, LauCreativeTemplatePo.class, configuration);
    }

    @Override
    public Long getId(LauCreativeTemplatePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchById(Long... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauCreativeTemplatePo fetchOneById(Long value) {
        return fetchOne(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.ID, value);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByAccountId(Integer... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByUnitId(Integer... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfCreativeId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByCreativeId(Long... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.CREATIVE_ID, values);
    }

    /**
     * Fetch records that have <code>slot_group_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfSlotGroupId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.SLOT_GROUP_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>slot_group_id IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchBySlotGroupId(Integer... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.SLOT_GROUP_ID, values);
    }

    /**
     * Fetch records that have <code>template_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfTemplateId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.TEMPLATE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>template_id IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByTemplateId(Integer... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.TEMPLATE_ID, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByCtime(Timestamp... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByMtime(Timestamp... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.MTIME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>biz_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfBizStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.BIZ_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_status IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByBizStatus(Integer... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.BIZ_STATUS, values);
    }

    /**
     * Fetch records that have <code>reserved_price BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfReservedPrice(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.RESERVED_PRICE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reserved_price IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByReservedPrice(Integer... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.RESERVED_PRICE, values);
    }

    /**
     * Fetch records that have <code>reserve_rule_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfReserveRuleId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.RESERVE_RULE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reserve_rule_id IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByReserveRuleId(Integer... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.RESERVE_RULE_ID, values);
    }

    /**
     * Fetch records that have <code>creative_style BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfCreativeStyle(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.CREATIVE_STYLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_style IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByCreativeStyle(Integer... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.CREATIVE_STYLE, values);
    }

    /**
     * Fetch records that have <code>bus_mark_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeTemplatePo> fetchRangeOfBusMarkId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.BUS_MARK_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>bus_mark_id IN (values)</code>
     */
    public List<LauCreativeTemplatePo> fetchByBusMarkId(Integer... values) {
        return fetch(TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE.BUS_MARK_ID, values);
    }
}
