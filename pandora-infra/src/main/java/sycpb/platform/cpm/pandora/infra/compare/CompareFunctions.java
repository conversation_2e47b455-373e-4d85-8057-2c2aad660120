package sycpb.platform.cpm.pandora.infra.compare;

import com.alibaba.fastjson.JSON;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.compare.bos.*;
import sycpb.platform.cpm.pandora.infra.err.PandoraAssert;

import java.lang.reflect.Method;
import java.text.MessageFormat;
import java.util.*;

public class CompareFunctions {
    public static String getLogGroupId(Object currentVersion, Object newVersion, Class<?> clazz) {
        if (Objects.isNull(currentVersion) && Objects.isNull(newVersion)) return null;

        if (Objects.isNull(clazz)) {
            if (Objects.isNull(currentVersion)) {
                clazz = newVersion.getClass();
            } else {
                clazz = currentVersion.getClass();
            }
        }
        final var meta = CompareClassRegistry.get(clazz);
        return Optional.ofNullable(meta.getLogGroupIdGetterFunc()).map(x -> x.apply(Objects.isNull(currentVersion) ? newVersion : currentVersion)).orElse(null);
    }

    @SneakyThrows
    public static CompareBo entityCompare(Object currentVersion, Object newVersion) {
        final var compareBo = new CompareBo();
        if (Objects.isNull(currentVersion) && Objects.isNull(newVersion)) return compareBo;

        final Class<?> clazz;
        final Object sample;
        if (Objects.isNull(currentVersion)) {
            clazz = newVersion.getClass();
            sample = newVersion;
            compareBo.setOperationType(OperationType.INSERT);
        } else {
            clazz = currentVersion.getClass();
            sample = currentVersion;
            if (Objects.isNull(newVersion)) {
                compareBo.setOperationType(OperationType.DELETE);
            } else {
                compareBo.setOperationType(OperationType.UPDATE);
            }
        }
        compareBo.setClazz(clazz);
        final var meta = CompareClassRegistry.get(clazz);
        for (Map.Entry<String, CompareMeta> entry : meta.getCompareMetaMap().entrySet()) {
            final var fieldName = entry.getKey();
            final var compareMeta = entry.getValue();
            if (!compareMeta.comparable() && !compareMeta.copyFromCurrentVersion()) continue;

            final var getterMethod = getGetterMethod(meta, fieldName);
            Object lValue = null, rValue = null;
            if (OperationType.isInsert(compareBo.getOperationType()) || OperationType.isUpdate(compareBo.getOperationType())) {
                rValue = getterMethod.invoke(newVersion);
            }
            if (OperationType.isUpdate(compareBo.getOperationType()) || OperationType.isDelete(compareBo.getOperationType())) {
                lValue = getterMethod.invoke(currentVersion);
            }
            if (Objects.isNull(lValue) && Objects.isNull(rValue)) continue;

            if (OperationType.isUpdate(compareBo.getOperationType()) && compareMeta.copyFromCurrentVersion()) {
                getSetterMethod(meta, fieldName).invoke(newVersion, lValue);
            }
            if (!compareMeta.comparable()) continue;

            // 更新时 所有逻辑走完依然传入null 不进行比对
            if (Objects.isNull(rValue) && OperationType.isUpdate(compareBo.getOperationType())) {
                continue;
            }

            if (!Objects.equals(lValue, rValue)) {
                final var fieldChange = new FieldChangeBo(fieldName, lValue, rValue);
                compareBo.getFieldChangeBos().add(fieldChange);
                final var changeBo = generateChangeBo(meta, compareMeta, fieldName, sample, lValue, rValue);
                if (Objects.nonNull(changeBo) && !compareMeta.skipLog()) {
                    compareBo.getRawChangeLogBos().add(changeBo);
                }
            }
        }
        for (Map.Entry<String, Method> entry : meta.getLogValueGetterMap().entrySet()) {
            final var valueMethod = entry.getValue();
            final var lValue = Objects.isNull(currentVersion) ? null : valueMethod.invoke(currentVersion);
            final var rValue = Objects.isNull(newVersion) ? null : valueMethod.invoke(newVersion);
            if (!Objects.equals(lValue, rValue)) {
                final var changeBo = generateUdfChangeBo(meta, entry.getKey(), sample, lValue, rValue);
                if (Objects.nonNull(changeBo)) {
                    compareBo.getRawChangeLogBos().add(changeBo);
                }
            }
        }
        return compareBo;
    }

    @SneakyThrows
    public static <T> T generateObjectWithOnlyChangedFields(T currentVersion, T newVersion) {
        Assert.notNull(currentVersion, "generateObjectWithOnlyChangedFields failed, currentVersion is null");
        Assert.notNull(newVersion, "generateObjectWithOnlyChangedFields failed, newVersion is null");

        // 拷贝newVersion
        Class<?> clazz = newVersion.getClass();
        T updateObject = (T) clazz.getDeclaredConstructor().newInstance();
        BeanUtils.copyProperties(newVersion, updateObject);

        final var meta = CompareClassRegistry.get(clazz);
        for (Map.Entry<String, CompareMeta> entry : meta.getCompareMetaMap().entrySet()) {
            final var fieldName = entry.getKey();
            final var compareMeta = entry.getValue();
            if (!compareMeta.comparable() || compareMeta.copyFromCurrentVersion()) continue;
            final var getterMethod = getGetterMethod(meta, fieldName);
            final var setterMethod = getSetterMethod(meta, fieldName);

            Object lValue = getterMethod.invoke(currentVersion);
            Object rValue = getterMethod.invoke(newVersion);
            if (Objects.equals(lValue, rValue)) {
                Object nullVal = null;
                setterMethod.invoke(updateObject, nullVal);
            }
        }

        return updateObject;
    }

    @SneakyThrows
    public static <T> List<CompareGroupBo<T>> groupCompare(Iterable<T> currentVersions, Iterable<T> newVersions, Class<T> clazz) {
        Assert.notNull(clazz, "iterable group: class is not specified");
        final var meta = CompareClassRegistry.get(clazz);
        Assert.notNull(meta.getUkGetterMethod(), MessageFormat.format("iterable group: uk must be specified for class={0}", clazz.getName()));
        final var elements = new ArrayList<CompareGroupBo<T>>();
        final var currentMap = new HashMap<Object, T>();
        if (!Objects.isNull(currentVersions)) {
            for (T currentVersion : currentVersions) {
                final var uk = meta.getUkGetterMethod().invoke(currentVersion);
                currentMap.put(uk, currentVersion);
            }
        }
        if (!Objects.isNull(newVersions)) {
            for (T newVersion : newVersions) {
                final var uk = meta.getUkGetterMethod().invoke(newVersion);
                final var currentVersion = currentMap.get(uk);
                elements.add(new CompareGroupBo<>(currentVersion, newVersion, clazz));
                if (Objects.nonNull(currentVersion)) {
                    currentMap.remove(uk);
                }
            }
        }
        for (T currentVersion : currentMap.values()) {
            elements.add(new CompareGroupBo<>(currentVersion, null, clazz));
        }
        return elements;
    }

    public static Method getGetterMethod(CompareClassMetaBo meta, String fieldName) {
        final var getterMethod = meta.getFieldGetterMap().get(fieldName);
        Assert.notNull(getterMethod, MessageFormat.format("entity compare: failed to obtain getter method, class={0}, field={1}", meta.getClazz().getName(), fieldName));
        return getterMethod;
    }

    public static Method getSetterMethod(CompareClassMetaBo meta, String fieldName) {
        final var setterMethod = meta.getFieldSetterMap().get(fieldName);
        Assert.notNull(setterMethod, MessageFormat.format("entity compare: failed to obtain setter method, class={0}, field={1}", meta.getClazz().getName(), fieldName));
        return setterMethod;
    }

    @SneakyThrows
    private static RawChangeLogBo generateChangeBo(CompareClassMetaBo meta, CompareMeta compareMeta, String fieldName, Object newVersion, Object lValue, Object rValue) {
        final String logDescription;
        if (StringUtils.hasText(compareMeta.logDescription())) {
            logDescription = compareMeta.logDescription();
        } else {
            final var logDescriptionGetterMethod = meta.getLogDescriptionGetterMap().get(fieldName);
            if (Objects.nonNull(logDescriptionGetterMethod) && Objects.nonNull(newVersion)) {
                logDescription = (String) logDescriptionGetterMethod.invoke(newVersion);
            } else {
                return null;
            }
        }
        final String logKey;
        if (StringUtils.hasText(compareMeta.logKey())) {
            logKey = compareMeta.logKey();
        } else {
            final var logKeyGetterMethod = meta.getLogKeyGetterMap().get(fieldName);
            if (Objects.nonNull(logKeyGetterMethod) && Objects.nonNull(newVersion)) {
                logKey = (String) logKeyGetterMethod.invoke(newVersion);
            } else {
                logKey = fieldName;
            }
        }
        final var changeBo = new RawChangeLogBo();
        changeBo.setFieldName(fieldName);
        changeBo.setKey(logKey);
        changeBo.setDesc(logDescription);
        changeBo.setOldValue(lValue);
        changeBo.setNewValue(rValue);
        return changeBo;
    }

    @SneakyThrows
    private static RawChangeLogBo generateUdfChangeBo(CompareClassMetaBo meta, String fieldName, Object sample, Object lValue, Object rValue) {
        if (Objects.isNull(rValue)) {
            return null;
        }
        final var descriptionMethod = meta.getLogDescriptionGetterMap().get(fieldName);
        PandoraAssert.exists(descriptionMethod, fieldName, "操作日志描述");
        final String logDescription = (String) descriptionMethod.invoke(sample);
        final var logKeyGetterMethod = meta.getLogKeyGetterMap().get(fieldName);
        final String logKey;
        if (Objects.nonNull(logKeyGetterMethod)) {
            logKey = (String) logKeyGetterMethod.invoke(sample);
        } else {
            logKey = fieldName;
        }
        final var changeBo = new RawChangeLogBo();
        changeBo.setFieldName(fieldName);
        changeBo.setKey(logKey);
        changeBo.setDesc(logDescription);
        changeBo.setOldValue(lValue);
        changeBo.setNewValue(rValue);
        return changeBo;
    }
}
