/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 效果单元微信小游戏绑定关系映射表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitMiniGameMappingPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   unitId;
    private String    gameUrl;
    private Integer   miniGameId;
    private Integer   accountId;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;

    public LauUnitMiniGameMappingPo() {}

    public LauUnitMiniGameMappingPo(LauUnitMiniGameMappingPo value) {
        this.id = value.id;
        this.unitId = value.unitId;
        this.gameUrl = value.gameUrl;
        this.miniGameId = value.miniGameId;
        this.accountId = value.accountId;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
    }

    public LauUnitMiniGameMappingPo(
        Long      id,
        Integer   unitId,
        String    gameUrl,
        Integer   miniGameId,
        Integer   accountId,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime
    ) {
        this.id = id;
        this.unitId = unitId;
        this.gameUrl = gameUrl;
        this.miniGameId = miniGameId;
        this.accountId = accountId;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_unit_mini_game_mapping.id</code>. 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_unit_mini_game_mapping.id</code>. 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_unit_mini_game_mapping.unit_id</code>. 单元id
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_unit_mini_game_mapping.unit_id</code>. 单元id
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for <code>lau_unit_mini_game_mapping.game_url</code>. 微信小游戏链接
     */
    public String getGameUrl() {
        return this.gameUrl;
    }

    /**
     * Setter for <code>lau_unit_mini_game_mapping.game_url</code>. 微信小游戏链接
     */
    public void setGameUrl(String gameUrl) {
        this.gameUrl = gameUrl;
    }

    /**
     * Getter for <code>lau_unit_mini_game_mapping.mini_game_id</code>. 微信小游戏id
     */
    public Integer getMiniGameId() {
        return this.miniGameId;
    }

    /**
     * Setter for <code>lau_unit_mini_game_mapping.mini_game_id</code>. 微信小游戏id
     */
    public void setMiniGameId(Integer miniGameId) {
        this.miniGameId = miniGameId;
    }

    /**
     * Getter for <code>lau_unit_mini_game_mapping.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>lau_unit_mini_game_mapping.account_id</code>. 账户id
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>lau_unit_mini_game_mapping.is_deleted</code>. 是否被删除 0-正常
     * 1-被删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_unit_mini_game_mapping.is_deleted</code>. 是否被删除 0-正常
     * 1-被删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_unit_mini_game_mapping.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_unit_mini_game_mapping.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_unit_mini_game_mapping.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_unit_mini_game_mapping.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauUnitMiniGameMappingPo (");

        sb.append(id);
        sb.append(", ").append(unitId);
        sb.append(", ").append(gameUrl);
        sb.append(", ").append(miniGameId);
        sb.append(", ").append(accountId);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);

        sb.append(")");
        return sb.toString();
    }
}
