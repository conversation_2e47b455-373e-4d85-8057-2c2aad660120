/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauQualificationPackage;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauQualificationPackagePo;


/**
 * 投放资质包表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauQualificationPackageRecord extends UpdatableRecordImpl<LauQualificationPackageRecord> implements Record6<Integer, Timestamp, Timestamp, Integer, Integer, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_qualification_package.id</code>. 自增id
     */
    public void setId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_qualification_package.id</code>. 自增id
     */
    public Integer getId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_qualification_package.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_qualification_package.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(1);
    }

    /**
     * Setter for <code>lau_qualification_package.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_qualification_package.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(2);
    }

    /**
     * Setter for <code>lau_qualification_package.is_deleted</code>. 软删除: 0 -
     * 未删除, 1 - 已删除
     */
    public void setIsDeleted(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_qualification_package.is_deleted</code>. 软删除: 0 -
     * 未删除, 1 - 已删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_qualification_package.account_id</code>. 账号ID
     */
    public void setAccountId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_qualification_package.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_qualification_package.name</code>. 资质包名称
     */
    public void setName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_qualification_package.name</code>. 资质包名称
     */
    public String getName() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<Integer, Timestamp, Timestamp, Integer, Integer, String> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<Integer, Timestamp, Timestamp, Integer, Integer, String> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<Integer> field1() {
        return TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.ID;
    }

    @Override
    public Field<Timestamp> field2() {
        return TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.CTIME;
    }

    @Override
    public Field<Timestamp> field3() {
        return TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.MTIME;
    }

    @Override
    public Field<Integer> field4() {
        return TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.IS_DELETED;
    }

    @Override
    public Field<Integer> field5() {
        return TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.ACCOUNT_ID;
    }

    @Override
    public Field<String> field6() {
        return TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE.NAME;
    }

    @Override
    public Integer component1() {
        return getId();
    }

    @Override
    public Timestamp component2() {
        return getCtime();
    }

    @Override
    public Timestamp component3() {
        return getMtime();
    }

    @Override
    public Integer component4() {
        return getIsDeleted();
    }

    @Override
    public Integer component5() {
        return getAccountId();
    }

    @Override
    public String component6() {
        return getName();
    }

    @Override
    public Integer value1() {
        return getId();
    }

    @Override
    public Timestamp value2() {
        return getCtime();
    }

    @Override
    public Timestamp value3() {
        return getMtime();
    }

    @Override
    public Integer value4() {
        return getIsDeleted();
    }

    @Override
    public Integer value5() {
        return getAccountId();
    }

    @Override
    public String value6() {
        return getName();
    }

    @Override
    public LauQualificationPackageRecord value1(Integer value) {
        setId(value);
        return this;
    }

    @Override
    public LauQualificationPackageRecord value2(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauQualificationPackageRecord value3(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauQualificationPackageRecord value4(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauQualificationPackageRecord value5(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauQualificationPackageRecord value6(String value) {
        setName(value);
        return this;
    }

    @Override
    public LauQualificationPackageRecord values(Integer value1, Timestamp value2, Timestamp value3, Integer value4, Integer value5, String value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauQualificationPackageRecord
     */
    public LauQualificationPackageRecord() {
        super(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE);
    }

    /**
     * Create a detached, initialised LauQualificationPackageRecord
     */
    public LauQualificationPackageRecord(Integer id, Timestamp ctime, Timestamp mtime, Integer isDeleted, Integer accountId, String name) {
        super(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE);

        setId(id);
        setCtime(ctime);
        setMtime(mtime);
        setIsDeleted(isDeleted);
        setAccountId(accountId);
        setName(name);
    }

    /**
     * Create a detached, initialised LauQualificationPackageRecord
     */
    public LauQualificationPackageRecord(LauQualificationPackagePo value) {
        super(TLauQualificationPackage.LAU_QUALIFICATION_PACKAGE);

        if (value != null) {
            setId(value.getId());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setIsDeleted(value.getIsDeleted());
            setAccountId(value.getAccountId());
            setName(value.getName());
        }
    }
}
