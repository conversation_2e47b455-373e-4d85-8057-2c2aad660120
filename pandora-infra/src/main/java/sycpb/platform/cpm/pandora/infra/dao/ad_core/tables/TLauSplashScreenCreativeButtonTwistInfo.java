/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauSplashScreenCreativeButtonTwistInfoRecord;


/**
 * 效果闪屏扭一扭按钮表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauSplashScreenCreativeButtonTwistInfo extends TableImpl<LauSplashScreenCreativeButtonTwistInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>lau_splash_screen_creative_button_twist_info</code>
     */
    public static final TLauSplashScreenCreativeButtonTwistInfo LAU_SPLASH_SCREEN_CREATIVE_BUTTON_TWIST_INFO = new TLauSplashScreenCreativeButtonTwistInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauSplashScreenCreativeButtonTwistInfoRecord> getRecordType() {
        return LauSplashScreenCreativeButtonTwistInfoRecord.class;
    }

    /**
     * The column <code>lau_splash_screen_creative_button_twist_info.id</code>.
     * 自增id
     */
    public final TableField<LauSplashScreenCreativeButtonTwistInfoRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增id");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_twist_info.creative_id</code>.
     * 创意id
     */
    public final TableField<LauSplashScreenCreativeButtonTwistInfoRecord, Integer> CREATIVE_ID = createField(DSL.name("creative_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "创意id");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_twist_info.button_id</code>. 按钮id
     */
    public final TableField<LauSplashScreenCreativeButtonTwistInfoRecord, Long> BUTTON_ID = createField(DSL.name("button_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "按钮id");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_twist_info.twist_angle</code>.
     * 扭动角度
     */
    public final TableField<LauSplashScreenCreativeButtonTwistInfoRecord, Double> TWIST_ANGLE = createField(DSL.name("twist_angle"), SQLDataType.FLOAT.nullable(false).defaultValue(DSL.inline("0.00", SQLDataType.FLOAT)), this, "扭动角度");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_twist_info.twist_speed</code>.
     * 扭动加速度
     */
    public final TableField<LauSplashScreenCreativeButtonTwistInfoRecord, Double> TWIST_SPEED = createField(DSL.name("twist_speed"), SQLDataType.FLOAT.nullable(false).defaultValue(DSL.inline("-1.00", SQLDataType.FLOAT)), this, "扭动加速度");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_twist_info.twist_x_type</code>.
     * 闪屏按钮扭一扭旋转轴x参数，-1：当前轴无效，0：任意，1：左扭，2：右扭
     */
    public final TableField<LauSplashScreenCreativeButtonTwistInfoRecord, Integer> TWIST_X_TYPE = createField(DSL.name("twist_x_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "闪屏按钮扭一扭旋转轴x参数，-1：当前轴无效，0：任意，1：左扭，2：右扭");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_twist_info.twist_y_type</code>.
     * 闪屏按钮扭一扭旋转轴y参数，-1：当前轴无效，0：任意，1：左扭，2：右扭
     */
    public final TableField<LauSplashScreenCreativeButtonTwistInfoRecord, Integer> TWIST_Y_TYPE = createField(DSL.name("twist_y_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "闪屏按钮扭一扭旋转轴y参数，-1：当前轴无效，0：任意，1：左扭，2：右扭");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_twist_info.twist_z_type</code>.
     * 闪屏按钮扭一扭旋转轴z参数，-1：当前轴无效，0：任意，1：左扭，2：右扭
     */
    public final TableField<LauSplashScreenCreativeButtonTwistInfoRecord, Integer> TWIST_Z_TYPE = createField(DSL.name("twist_z_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "闪屏按钮扭一扭旋转轴z参数，-1：当前轴无效，0：任意，1：左扭，2：右扭");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_twist_info.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public final TableField<LauSplashScreenCreativeButtonTwistInfoRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除，0是有效，1是删除");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_twist_info.ctime</code>. 添加时间
     */
    public final TableField<LauSplashScreenCreativeButtonTwistInfoRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_twist_info.mtime</code>. 更新时间
     */
    public final TableField<LauSplashScreenCreativeButtonTwistInfoRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    private TLauSplashScreenCreativeButtonTwistInfo(Name alias, Table<LauSplashScreenCreativeButtonTwistInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauSplashScreenCreativeButtonTwistInfo(Name alias, Table<LauSplashScreenCreativeButtonTwistInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("效果闪屏扭一扭按钮表"), TableOptions.table());
    }

    /**
     * Create an aliased
     * <code>lau_splash_screen_creative_button_twist_info</code> table reference
     */
    public TLauSplashScreenCreativeButtonTwistInfo(String alias) {
        this(DSL.name(alias), LAU_SPLASH_SCREEN_CREATIVE_BUTTON_TWIST_INFO);
    }

    /**
     * Create an aliased
     * <code>lau_splash_screen_creative_button_twist_info</code> table reference
     */
    public TLauSplashScreenCreativeButtonTwistInfo(Name alias) {
        this(alias, LAU_SPLASH_SCREEN_CREATIVE_BUTTON_TWIST_INFO);
    }

    /**
     * Create a <code>lau_splash_screen_creative_button_twist_info</code> table
     * reference
     */
    public TLauSplashScreenCreativeButtonTwistInfo() {
        this(DSL.name("lau_splash_screen_creative_button_twist_info"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauSplashScreenCreativeButtonTwistInfoRecord, Long> getIdentity() {
        return (Identity<LauSplashScreenCreativeButtonTwistInfoRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauSplashScreenCreativeButtonTwistInfoRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauSplashScreenCreativeButtonTwistInfo.LAU_SPLASH_SCREEN_CREATIVE_BUTTON_TWIST_INFO, DSL.name("KEY_lau_splash_screen_creative_button_twist_info_PRIMARY"), new TableField[] { TLauSplashScreenCreativeButtonTwistInfo.LAU_SPLASH_SCREEN_CREATIVE_BUTTON_TWIST_INFO.ID }, true);
    }

    @Override
    public TLauSplashScreenCreativeButtonTwistInfo as(String alias) {
        return new TLauSplashScreenCreativeButtonTwistInfo(DSL.name(alias), this);
    }

    @Override
    public TLauSplashScreenCreativeButtonTwistInfo as(Name alias) {
        return new TLauSplashScreenCreativeButtonTwistInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauSplashScreenCreativeButtonTwistInfo rename(String name) {
        return new TLauSplashScreenCreativeButtonTwistInfo(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauSplashScreenCreativeButtonTwistInfo rename(Name name) {
        return new TLauSplashScreenCreativeButtonTwistInfo(name, null);
    }

    // -------------------------------------------------------------------------
    // Row11 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row11<Long, Integer, Long, Double, Double, Integer, Integer, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row11) super.fieldsRow();
    }
}
