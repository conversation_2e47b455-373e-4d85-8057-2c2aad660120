/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row5;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitLiveReserveRecord;


/**
 * 单元直播预约表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauUnitLiveReserve extends TableImpl<LauUnitLiveReserveRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_unit_live_reserve</code>
     */
    public static final TLauUnitLiveReserve LAU_UNIT_LIVE_RESERVE = new TLauUnitLiveReserve();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauUnitLiveReserveRecord> getRecordType() {
        return LauUnitLiveReserveRecord.class;
    }

    /**
     * The column <code>lau_unit_live_reserve.id</code>. 自增id
     */
    public final TableField<LauUnitLiveReserveRecord, Integer> ID = createField(DSL.name("id"), SQLDataType.INTEGER.nullable(false).identity(true), this, "自增id");

    /**
     * The column <code>lau_unit_live_reserve.ctime</code>. 添加时间
     */
    public final TableField<LauUnitLiveReserveRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column <code>lau_unit_live_reserve.mtime</code>. 更新时间
     */
    public final TableField<LauUnitLiveReserveRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>lau_unit_live_reserve.sid</code>. 预约id
     */
    public final TableField<LauUnitLiveReserveRecord, Long> SID = createField(DSL.name("sid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "预约id");

    /**
     * The column <code>lau_unit_live_reserve.unit_id</code>. 单元id
     */
    public final TableField<LauUnitLiveReserveRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元id");

    private TLauUnitLiveReserve(Name alias, Table<LauUnitLiveReserveRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauUnitLiveReserve(Name alias, Table<LauUnitLiveReserveRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("单元直播预约表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_unit_live_reserve</code> table reference
     */
    public TLauUnitLiveReserve(String alias) {
        this(DSL.name(alias), LAU_UNIT_LIVE_RESERVE);
    }

    /**
     * Create an aliased <code>lau_unit_live_reserve</code> table reference
     */
    public TLauUnitLiveReserve(Name alias) {
        this(alias, LAU_UNIT_LIVE_RESERVE);
    }

    /**
     * Create a <code>lau_unit_live_reserve</code> table reference
     */
    public TLauUnitLiveReserve() {
        this(DSL.name("lau_unit_live_reserve"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauUnitLiveReserveRecord, Integer> getIdentity() {
        return (Identity<LauUnitLiveReserveRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauUnitLiveReserveRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE, DSL.name("KEY_lau_unit_live_reserve_PRIMARY"), new TableField[] { TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.ID }, true);
    }

    @Override
    public List<UniqueKey<LauUnitLiveReserveRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE, DSL.name("KEY_lau_unit_live_reserve_uk_uid"), new TableField[] { TLauUnitLiveReserve.LAU_UNIT_LIVE_RESERVE.UNIT_ID }, true)
        );
    }

    @Override
    public TLauUnitLiveReserve as(String alias) {
        return new TLauUnitLiveReserve(DSL.name(alias), this);
    }

    @Override
    public TLauUnitLiveReserve as(Name alias) {
        return new TLauUnitLiveReserve(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitLiveReserve rename(String name) {
        return new TLauUnitLiveReserve(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitLiveReserve rename(Name name) {
        return new TLauUnitLiveReserve(name, null);
    }

    // -------------------------------------------------------------------------
    // Row5 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row5<Integer, Timestamp, Timestamp, Long, Integer> fieldsRow() {
        return (Row5) super.fieldsRow();
    }
}
