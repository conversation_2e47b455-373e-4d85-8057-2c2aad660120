/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitTargetProfessionInterestRecord;


/**
 * 单元-行业兴趣人群定向表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauUnitTargetProfessionInterest extends TableImpl<LauUnitTargetProfessionInterestRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>lau_unit_target_profession_interest</code>
     */
    public static final TLauUnitTargetProfessionInterest LAU_UNIT_TARGET_PROFESSION_INTEREST = new TLauUnitTargetProfessionInterest();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauUnitTargetProfessionInterestRecord> getRecordType() {
        return LauUnitTargetProfessionInterestRecord.class;
    }

    /**
     * The column <code>lau_unit_target_profession_interest.id</code>.
     */
    public final TableField<LauUnitTargetProfessionInterestRecord, Integer> ID = createField(DSL.name("id"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column <code>lau_unit_target_profession_interest.unit_id</code>. 单元ID
     */
    public final TableField<LauUnitTargetProfessionInterestRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元ID");

    /**
     * The column <code>lau_unit_target_profession_interest.crowd_id</code>.
     * 人群包ID
     */
    public final TableField<LauUnitTargetProfessionInterestRecord, Integer> CROWD_ID = createField(DSL.name("crowd_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "人群包ID");

    /**
     * The column <code>lau_unit_target_profession_interest.ctime</code>. 创建时间
     */
    public final TableField<LauUnitTargetProfessionInterestRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_unit_target_profession_interest.mtime</code>. 修改时间
     */
    public final TableField<LauUnitTargetProfessionInterestRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>lau_unit_target_profession_interest.is_deleted</code>.
     * 软删除 0-有效，1-删除
     */
    public final TableField<LauUnitTargetProfessionInterestRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除 0-有效，1-删除");

    private TLauUnitTargetProfessionInterest(Name alias, Table<LauUnitTargetProfessionInterestRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauUnitTargetProfessionInterest(Name alias, Table<LauUnitTargetProfessionInterestRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("单元-行业兴趣人群定向表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_unit_target_profession_interest</code> table
     * reference
     */
    public TLauUnitTargetProfessionInterest(String alias) {
        this(DSL.name(alias), LAU_UNIT_TARGET_PROFESSION_INTEREST);
    }

    /**
     * Create an aliased <code>lau_unit_target_profession_interest</code> table
     * reference
     */
    public TLauUnitTargetProfessionInterest(Name alias) {
        this(alias, LAU_UNIT_TARGET_PROFESSION_INTEREST);
    }

    /**
     * Create a <code>lau_unit_target_profession_interest</code> table reference
     */
    public TLauUnitTargetProfessionInterest() {
        this(DSL.name("lau_unit_target_profession_interest"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauUnitTargetProfessionInterestRecord, Integer> getIdentity() {
        return (Identity<LauUnitTargetProfessionInterestRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauUnitTargetProfessionInterestRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauUnitTargetProfessionInterest.LAU_UNIT_TARGET_PROFESSION_INTEREST, DSL.name("KEY_lau_unit_target_profession_interest_PRIMARY"), new TableField[] { TLauUnitTargetProfessionInterest.LAU_UNIT_TARGET_PROFESSION_INTEREST.ID }, true);
    }

    @Override
    public TLauUnitTargetProfessionInterest as(String alias) {
        return new TLauUnitTargetProfessionInterest(DSL.name(alias), this);
    }

    @Override
    public TLauUnitTargetProfessionInterest as(Name alias) {
        return new TLauUnitTargetProfessionInterest(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitTargetProfessionInterest rename(String name) {
        return new TLauUnitTargetProfessionInterest(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitTargetProfessionInterest rename(Name name) {
        return new TLauUnitTargetProfessionInterest(name, null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<Integer, Integer, Integer, Timestamp, Timestamp, Integer> fieldsRow() {
        return (Row6) super.fieldsRow();
    }
}
