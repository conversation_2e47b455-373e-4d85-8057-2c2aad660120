/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TResCmImage;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.ResCmImagePo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.ResCmImageRecord;


/**
 * 图片素材表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ResCmImageDao extends DAOImpl<ResCmImageRecord, ResCmImagePo, Long> {

    /**
     * Create a new ResCmImageDao without any configuration
     */
    public ResCmImageDao() {
        super(TResCmImage.RES_CM_IMAGE, ResCmImagePo.class);
    }

    /**
     * Create a new ResCmImageDao with an attached configuration
     */
    @Autowired
    public ResCmImageDao(Configuration configuration) {
        super(TResCmImage.RES_CM_IMAGE, ResCmImagePo.class, configuration);
    }

    @Override
    public Long getId(ResCmImagePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResCmImagePo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TResCmImage.RES_CM_IMAGE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<ResCmImagePo> fetchById(Long... values) {
        return fetch(TResCmImage.RES_CM_IMAGE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public ResCmImagePo fetchOneById(Long value) {
        return fetchOne(TResCmImage.RES_CM_IMAGE.ID, value);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResCmImagePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResCmImage.RES_CM_IMAGE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<ResCmImagePo> fetchByCtime(Timestamp... values) {
        return fetch(TResCmImage.RES_CM_IMAGE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResCmImagePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResCmImage.RES_CM_IMAGE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<ResCmImagePo> fetchByMtime(Timestamp... values) {
        return fetch(TResCmImage.RES_CM_IMAGE.MTIME, values);
    }

    /**
     * Fetch records that have <code>url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResCmImagePo> fetchRangeOfUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResCmImage.RES_CM_IMAGE.URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>url IN (values)</code>
     */
    public List<ResCmImagePo> fetchByUrl(String... values) {
        return fetch(TResCmImage.RES_CM_IMAGE.URL, values);
    }

    /**
     * Fetch records that have <code>md5 BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResCmImagePo> fetchRangeOfMd5(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResCmImage.RES_CM_IMAGE.MD5, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>md5 IN (values)</code>
     */
    public List<ResCmImagePo> fetchByMd5(String... values) {
        return fetch(TResCmImage.RES_CM_IMAGE.MD5, values);
    }

    /**
     * Fetch records that have <code>width BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResCmImagePo> fetchRangeOfWidth(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResCmImage.RES_CM_IMAGE.WIDTH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>width IN (values)</code>
     */
    public List<ResCmImagePo> fetchByWidth(Integer... values) {
        return fetch(TResCmImage.RES_CM_IMAGE.WIDTH, values);
    }

    /**
     * Fetch records that have <code>height BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResCmImagePo> fetchRangeOfHeight(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResCmImage.RES_CM_IMAGE.HEIGHT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>height IN (values)</code>
     */
    public List<ResCmImagePo> fetchByHeight(Integer... values) {
        return fetch(TResCmImage.RES_CM_IMAGE.HEIGHT, values);
    }

    /**
     * Fetch records that have <code>size BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResCmImagePo> fetchRangeOfSize(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResCmImage.RES_CM_IMAGE.SIZE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>size IN (values)</code>
     */
    public List<ResCmImagePo> fetchBySize(Integer... values) {
        return fetch(TResCmImage.RES_CM_IMAGE.SIZE, values);
    }

    /**
     * Fetch records that have <code>material_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResCmImagePo> fetchRangeOfMaterialId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TResCmImage.RES_CM_IMAGE.MATERIAL_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>material_id IN (values)</code>
     */
    public List<ResCmImagePo> fetchByMaterialId(Long... values) {
        return fetch(TResCmImage.RES_CM_IMAGE.MATERIAL_ID, values);
    }

    /**
     * Fetch records that have <code>is_gif BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResCmImagePo> fetchRangeOfIsGif(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResCmImage.RES_CM_IMAGE.IS_GIF, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_gif IN (values)</code>
     */
    public List<ResCmImagePo> fetchByIsGif(Integer... values) {
        return fetch(TResCmImage.RES_CM_IMAGE.IS_GIF, values);
    }
}
