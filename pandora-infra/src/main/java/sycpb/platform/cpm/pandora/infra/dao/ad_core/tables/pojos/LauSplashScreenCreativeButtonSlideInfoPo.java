/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 效果闪屏滑动按钮表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauSplashScreenCreativeButtonSlideInfoPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   creativeId;
    private Long      buttonId;
    private Integer   slideAngle;
    private Integer   slideDistance;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;

    public LauSplashScreenCreativeButtonSlideInfoPo() {}

    public LauSplashScreenCreativeButtonSlideInfoPo(LauSplashScreenCreativeButtonSlideInfoPo value) {
        this.id = value.id;
        this.creativeId = value.creativeId;
        this.buttonId = value.buttonId;
        this.slideAngle = value.slideAngle;
        this.slideDistance = value.slideDistance;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
    }

    public LauSplashScreenCreativeButtonSlideInfoPo(
        Long      id,
        Integer   creativeId,
        Long      buttonId,
        Integer   slideAngle,
        Integer   slideDistance,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime
    ) {
        this.id = id;
        this.creativeId = creativeId;
        this.buttonId = buttonId;
        this.slideAngle = slideAngle;
        this.slideDistance = slideDistance;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_splash_screen_creative_button_slide_info.id</code>.
     * 自增id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_splash_screen_creative_button_slide_info.id</code>.
     * 自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_slide_info.creative_id</code>.
     * 创意id
     */
    public Integer getCreativeId() {
        return this.creativeId;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_slide_info.creative_id</code>.
     * 创意id
     */
    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_slide_info.button_id</code>. 按钮id
     */
    public Long getButtonId() {
        return this.buttonId;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_slide_info.button_id</code>. 按钮id
     */
    public void setButtonId(Long buttonId) {
        this.buttonId = buttonId;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_slide_info.slide_angle</code>.
     * 滑动角度
     */
    public Integer getSlideAngle() {
        return this.slideAngle;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_slide_info.slide_angle</code>.
     * 滑动角度
     */
    public void setSlideAngle(Integer slideAngle) {
        this.slideAngle = slideAngle;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_slide_info.slide_distance</code>.
     * 滑动距离
     */
    public Integer getSlideDistance() {
        return this.slideDistance;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_slide_info.slide_distance</code>.
     * 滑动距离
     */
    public void setSlideDistance(Integer slideDistance) {
        this.slideDistance = slideDistance;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_slide_info.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_slide_info.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_slide_info.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_slide_info.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for
     * <code>lau_splash_screen_creative_button_slide_info.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for
     * <code>lau_splash_screen_creative_button_slide_info.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauSplashScreenCreativeButtonSlideInfoPo (");

        sb.append(id);
        sb.append(", ").append(creativeId);
        sb.append(", ").append(buttonId);
        sb.append(", ").append(slideAngle);
        sb.append(", ").append(slideDistance);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);

        sb.append(")");
        return sb.toString();
    }
}
