/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.ResTargetPackageOsVersionUpgradeRecord;


/**
 * 新版定向包分端系统版本定向表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TResTargetPackageOsVersionUpgrade extends TableImpl<ResTargetPackageOsVersionUpgradeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>res_target_package_os_version_upgrade</code>
     */
    public static final TResTargetPackageOsVersionUpgrade RES_TARGET_PACKAGE_OS_VERSION_UPGRADE = new TResTargetPackageOsVersionUpgrade();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResTargetPackageOsVersionUpgradeRecord> getRecordType() {
        return ResTargetPackageOsVersionUpgradeRecord.class;
    }

    /**
     * The column <code>res_target_package_os_version_upgrade.id</code>. 自增id
     */
    public final TableField<ResTargetPackageOsVersionUpgradeRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增id");

    /**
     * The column
     * <code>res_target_package_os_version_upgrade.target_package_id</code>.
     * 定向包id
     */
    public final TableField<ResTargetPackageOsVersionUpgradeRecord, Integer> TARGET_PACKAGE_ID = createField(DSL.name("target_package_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "定向包id");

    /**
     * The column <code>res_target_package_os_version_upgrade.os</code>. 设备平台
     * android-399,iphone-398,ipad-421
     */
    public final TableField<ResTargetPackageOsVersionUpgradeRecord, Integer> OS = createField(DSL.name("os"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "设备平台 android-399,iphone-398,ipad-421");

    /**
     * The column
     * <code>res_target_package_os_version_upgrade.os_versions</code>.
     * 系统版本列表(,分隔)没有为[-1]
     */
    public final TableField<ResTargetPackageOsVersionUpgradeRecord, String> OS_VERSIONS = createField(DSL.name("os_versions"), SQLDataType.VARCHAR(256).nullable(false).defaultValue(DSL.inline("'[-1]'", SQLDataType.VARCHAR)), this, "系统版本列表(,分隔)没有为[-1]");

    /**
     * The column <code>res_target_package_os_version_upgrade.is_deleted</code>.
     * 软删除 0 否 1是
     */
    public final TableField<ResTargetPackageOsVersionUpgradeRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除 0 否 1是");

    /**
     * The column <code>res_target_package_os_version_upgrade.ctime</code>. 创建时间
     */
    public final TableField<ResTargetPackageOsVersionUpgradeRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>res_target_package_os_version_upgrade.mtime</code>. 更新时间
     */
    public final TableField<ResTargetPackageOsVersionUpgradeRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    private TResTargetPackageOsVersionUpgrade(Name alias, Table<ResTargetPackageOsVersionUpgradeRecord> aliased) {
        this(alias, aliased, null);
    }

    private TResTargetPackageOsVersionUpgrade(Name alias, Table<ResTargetPackageOsVersionUpgradeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("新版定向包分端系统版本定向表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>res_target_package_os_version_upgrade</code>
     * table reference
     */
    public TResTargetPackageOsVersionUpgrade(String alias) {
        this(DSL.name(alias), RES_TARGET_PACKAGE_OS_VERSION_UPGRADE);
    }

    /**
     * Create an aliased <code>res_target_package_os_version_upgrade</code>
     * table reference
     */
    public TResTargetPackageOsVersionUpgrade(Name alias) {
        this(alias, RES_TARGET_PACKAGE_OS_VERSION_UPGRADE);
    }

    /**
     * Create a <code>res_target_package_os_version_upgrade</code> table
     * reference
     */
    public TResTargetPackageOsVersionUpgrade() {
        this(DSL.name("res_target_package_os_version_upgrade"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<ResTargetPackageOsVersionUpgradeRecord, Long> getIdentity() {
        return (Identity<ResTargetPackageOsVersionUpgradeRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<ResTargetPackageOsVersionUpgradeRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TResTargetPackageOsVersionUpgrade.RES_TARGET_PACKAGE_OS_VERSION_UPGRADE, DSL.name("KEY_res_target_package_os_version_upgrade_PRIMARY"), new TableField[] { TResTargetPackageOsVersionUpgrade.RES_TARGET_PACKAGE_OS_VERSION_UPGRADE.ID }, true);
    }

    @Override
    public List<UniqueKey<ResTargetPackageOsVersionUpgradeRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TResTargetPackageOsVersionUpgrade.RES_TARGET_PACKAGE_OS_VERSION_UPGRADE, DSL.name("KEY_res_target_package_os_version_upgrade_uk_target_package_id_os"), new TableField[] { TResTargetPackageOsVersionUpgrade.RES_TARGET_PACKAGE_OS_VERSION_UPGRADE.TARGET_PACKAGE_ID, TResTargetPackageOsVersionUpgrade.RES_TARGET_PACKAGE_OS_VERSION_UPGRADE.OS }, true)
        );
    }

    @Override
    public TResTargetPackageOsVersionUpgrade as(String alias) {
        return new TResTargetPackageOsVersionUpgrade(DSL.name(alias), this);
    }

    @Override
    public TResTargetPackageOsVersionUpgrade as(Name alias) {
        return new TResTargetPackageOsVersionUpgrade(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TResTargetPackageOsVersionUpgrade rename(String name) {
        return new TResTargetPackageOsVersionUpgrade(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TResTargetPackageOsVersionUpgrade rename(Name name) {
        return new TResTargetPackageOsVersionUpgrade(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<Long, Integer, Integer, String, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
