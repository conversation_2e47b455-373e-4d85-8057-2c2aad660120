/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Date;
import java.sql.Timestamp;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitCreative;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitCreativePo;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitCreativeRecord extends UpdatableRecordImpl<LauUnitCreativeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_unit_creative.creative_id</code>. 创意ID
     */
    public void setCreativeId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_unit_creative.creative_id</code>. 创意ID
     */
    public Integer getCreativeId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_unit_creative.account_id</code>. 账户id
     */
    public void setAccountId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_unit_creative.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_unit_creative.campaign_id</code>. 计划id
     */
    public void setCampaignId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_unit_creative.campaign_id</code>. 计划id
     */
    public Integer getCampaignId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_unit_creative.creative_type</code>.
     * 1-图片，2-视频，3-feeds图片，4-feeds视频(冗余字段，从模板中获取)
     */
    public void setCreativeType(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_unit_creative.creative_type</code>.
     * 1-图片，2-视频，3-feeds图片，4-feeds视频(冗余字段，从模板中获取)
     */
    public Integer getCreativeType() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_unit_creative.unit_id</code>. 单位ID
     */
    public void setUnitId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_unit_creative.unit_id</code>. 单位ID
     */
    public Integer getUnitId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_unit_creative.creative_name</code>. 创意名称
     */
    public void setCreativeName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_unit_creative.creative_name</code>. 创意名称
     */
    public String getCreativeName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>lau_unit_creative.promotion_purpose_content</code>.
     * 推广目的内容
     */
    public void setPromotionPurposeContent(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_unit_creative.promotion_purpose_content</code>.
     * 推广目的内容
     */
    public String getPromotionPurposeContent() {
        return (String) get(6);
    }

    /**
     * Setter for <code>lau_unit_creative.customized_imp_url</code>. 展示监控链接
     */
    public void setCustomizedImpUrl(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_unit_creative.customized_imp_url</code>. 展示监控链接
     */
    public String getCustomizedImpUrl() {
        return (String) get(7);
    }

    /**
     * Setter for <code>lau_unit_creative.customized_click_url</code>. 点击监控链接
     */
    public void setCustomizedClickUrl(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_unit_creative.customized_click_url</code>. 点击监控链接
     */
    public String getCustomizedClickUrl() {
        return (String) get(8);
    }

    /**
     * Setter for <code>lau_unit_creative.title</code>. 标题
     */
    public void setTitle(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_unit_creative.title</code>. 标题
     */
    public String getTitle() {
        return (String) get(9);
    }

    /**
     * Setter for <code>lau_unit_creative.description</code>. 描述
     */
    public void setDescription(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_unit_creative.description</code>. 描述
     */
    public String getDescription() {
        return (String) get(10);
    }

    /**
     * Setter for <code>lau_unit_creative.ext_description</code>. 扩展的描述(广告主名称等)
     */
    public void setExtDescription(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_unit_creative.ext_description</code>. 扩展的描述(广告主名称等)
     */
    public String getExtDescription() {
        return (String) get(11);
    }

    /**
     * Setter for <code>lau_unit_creative.image_url</code>. 图片URL
     */
    public void setImageUrl(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_unit_creative.image_url</code>. 图片URL
     */
    public String getImageUrl() {
        return (String) get(12);
    }

    /**
     * Setter for <code>lau_unit_creative.image_md5</code>. 图片的MD5值
     */
    public void setImageMd5(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_unit_creative.image_md5</code>. 图片的MD5值
     */
    public String getImageMd5() {
        return (String) get(13);
    }

    /**
     * Setter for <code>lau_unit_creative.video_id</code>. 视频ID(如AVID等)
     */
    public void setVideoId(Long value) {
        set(14, value);
    }

    /**
     * Getter for <code>lau_unit_creative.video_id</code>. 视频ID(如AVID等)
     */
    public Long getVideoId() {
        return (Long) get(14);
    }

    /**
     * Setter for <code>lau_unit_creative.video_url</code>. 视频URL
     */
    public void setVideoUrl(String value) {
        set(15, value);
    }

    /**
     * Getter for <code>lau_unit_creative.video_url</code>. 视频URL
     */
    public String getVideoUrl() {
        return (String) get(15);
    }

    /**
     * Setter for <code>lau_unit_creative.ext_image_url</code>.
     * 扩展图片URL(缩略图、logo等的URL)
     */
    public void setExtImageUrl(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>lau_unit_creative.ext_image_url</code>.
     * 扩展图片URL(缩略图、logo等的URL)
     */
    public String getExtImageUrl() {
        return (String) get(16);
    }

    /**
     * Setter for <code>lau_unit_creative.ext_image_md5</code>. 扩展图片MD5值
     */
    public void setExtImageMd5(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>lau_unit_creative.ext_image_md5</code>. 扩展图片MD5值
     */
    public String getExtImageMd5() {
        return (String) get(17);
    }

    /**
     * Setter for <code>lau_unit_creative.creative_json</code>. 创意相关JSON串
     */
    public void setCreativeJson(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>lau_unit_creative.creative_json</code>. 创意相关JSON串
     */
    public String getCreativeJson() {
        return (String) get(18);
    }

    /**
     * Setter for <code>lau_unit_creative.reason</code>. 原因（审核拒绝时填写）
     */
    public void setReason(String value) {
        set(19, value);
    }

    /**
     * Getter for <code>lau_unit_creative.reason</code>. 原因（审核拒绝时填写）
     */
    public String getReason() {
        return (String) get(19);
    }

    /**
     * Setter for <code>lau_unit_creative.template_id</code>. 创意模板ID
     */
    public void setTemplateId(Integer value) {
        set(20, value);
    }

    /**
     * Getter for <code>lau_unit_creative.template_id</code>. 创意模板ID
     */
    public Integer getTemplateId() {
        return (Integer) get(20);
    }

    /**
     * Setter for <code>lau_unit_creative.audit_status</code>.
     * 审核状态（1-待审核，2-审核通过，3-审核不通过）
     */
    public void setAuditStatus(Integer value) {
        set(21, value);
    }

    /**
     * Getter for <code>lau_unit_creative.audit_status</code>.
     * 审核状态（1-待审核，2-审核通过，3-审核不通过）
     */
    public Integer getAuditStatus() {
        return (Integer) get(21);
    }

    /**
     * Setter for <code>lau_unit_creative.status</code>. 状态（1-有效，2-暂停, 3-删除 4-结束
     * 5-修改待下线）
     */
    public void setStatus(Integer value) {
        set(22, value);
    }

    /**
     * Getter for <code>lau_unit_creative.status</code>. 状态（1-有效，2-暂停, 3-删除 4-结束
     * 5-修改待下线）
     */
    public Integer getStatus() {
        return (Integer) get(22);
    }

    /**
     * Setter for <code>lau_unit_creative.version</code>. 版本号
     */
    public void setVersion(Integer value) {
        set(23, value);
    }

    /**
     * Getter for <code>lau_unit_creative.version</code>. 版本号
     */
    public Integer getVersion() {
        return (Integer) get(23);
    }

    /**
     * Setter for <code>lau_unit_creative.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer value) {
        set(24, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(24);
    }

    /**
     * Setter for <code>lau_unit_creative.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(25, value);
    }

    /**
     * Getter for <code>lau_unit_creative.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(25);
    }

    /**
     * Setter for <code>lau_unit_creative.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(26, value);
    }

    /**
     * Getter for <code>lau_unit_creative.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(26);
    }

    /**
     * Setter for <code>lau_unit_creative.order_id</code>. 订单ID
     */
    public void setOrderId(Integer value) {
        set(27, value);
    }

    /**
     * Getter for <code>lau_unit_creative.order_id</code>. 订单ID
     */
    public Integer getOrderId() {
        return (Integer) get(27);
    }

    /**
     * Setter for <code>lau_unit_creative.sales_type</code>. 售卖类型 11-CPM,
     * 12-CPC, 21-GD
     */
    public void setSalesType(Integer value) {
        set(28, value);
    }

    /**
     * Getter for <code>lau_unit_creative.sales_type</code>. 售卖类型 11-CPM,
     * 12-CPC, 21-GD
     */
    public Integer getSalesType() {
        return (Integer) get(28);
    }

    /**
     * Setter for <code>lau_unit_creative.cm_mark</code>. 广告角标(默认为广告)
     */
    public void setCmMark(Integer value) {
        set(29, value);
    }

    /**
     * Getter for <code>lau_unit_creative.cm_mark</code>. 广告角标(默认为广告)
     */
    public Integer getCmMark() {
        return (Integer) get(29);
    }

    /**
     * Setter for <code>lau_unit_creative.button_copy</code>. 按钮文案
     */
    public void setButtonCopy(String value) {
        set(30, value);
    }

    /**
     * Getter for <code>lau_unit_creative.button_copy</code>. 按钮文案
     */
    public String getButtonCopy() {
        return (String) get(30);
    }

    /**
     * Setter for <code>lau_unit_creative.category_first_id</code>. 创意一级分类id
     */
    public void setCategoryFirstId(Integer value) {
        set(31, value);
    }

    /**
     * Getter for <code>lau_unit_creative.category_first_id</code>. 创意一级分类id
     */
    public Integer getCategoryFirstId() {
        return (Integer) get(31);
    }

    /**
     * Setter for <code>lau_unit_creative.category_second_id</code>. 创意二级分类id
     */
    public void setCategorySecondId(Integer value) {
        set(32, value);
    }

    /**
     * Getter for <code>lau_unit_creative.category_second_id</code>. 创意二级分类id
     */
    public Integer getCategorySecondId() {
        return (Integer) get(32);
    }

    /**
     * Setter for <code>lau_unit_creative.is_history</code>. 是否是历史创意: 0-否 1-是
     */
    public void setIsHistory(Integer value) {
        set(33, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_history</code>. 是否是历史创意: 0-否 1-是
     */
    public Integer getIsHistory() {
        return (Integer) get(33);
    }

    /**
     * Setter for <code>lau_unit_creative.tags</code>. 创意标签
     */
    public void setTags(String value) {
        set(34, value);
    }

    /**
     * Getter for <code>lau_unit_creative.tags</code>. 创意标签
     */
    public String getTags() {
        return (String) get(34);
    }

    /**
     * Setter for <code>lau_unit_creative.begin_time</code>. 投放开始时间
     */
    public void setBeginTime(Date value) {
        set(35, value);
    }

    /**
     * Getter for <code>lau_unit_creative.begin_time</code>. 投放开始时间
     */
    public Date getBeginTime() {
        return (Date) get(35);
    }

    /**
     * Setter for <code>lau_unit_creative.end_time</code>. 投放结束时间
     */
    public void setEndTime(Date value) {
        set(36, value);
    }

    /**
     * Getter for <code>lau_unit_creative.end_time</code>. 投放结束时间
     */
    public Date getEndTime() {
        return (Date) get(36);
    }

    /**
     * Setter for <code>lau_unit_creative.creative_status</code>.
     * 创意状态:1-有效,2-已暂停,3-已结束,4-已删除,5-审核中,6-审核拒绝, 7-修改待下线, 8-已下线,9-落地页待审核创意待送审
     */
    public void setCreativeStatus(Integer value) {
        set(37, value);
    }

    /**
     * Getter for <code>lau_unit_creative.creative_status</code>.
     * 创意状态:1-有效,2-已暂停,3-已结束,4-已删除,5-审核中,6-审核拒绝, 7-修改待下线, 8-已下线,9-落地页待审核创意待送审
     */
    public Integer getCreativeStatus() {
        return (Integer) get(37);
    }

    /**
     * Setter for <code>lau_unit_creative.is_mark</code>. 校验创意分类 0:未校验 1:准确
     * 2:不准确
     */
    public void setIsMark(Integer value) {
        set(38, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_mark</code>. 校验创意分类 0:未校验 1:准确
     * 2:不准确
     */
    public Integer getIsMark() {
        return (Integer) get(38);
    }

    /**
     * Setter for <code>lau_unit_creative.is_tag</code>. 是否标签标注 0:未标注 1:已标注
     */
    public void setIsTag(Integer value) {
        set(39, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_tag</code>. 是否标签标注 0:未标注 1:已标注
     */
    public Integer getIsTag() {
        return (Integer) get(39);
    }

    /**
     * Setter for <code>lau_unit_creative.scheme_url</code>. 唤起scheme链接
     */
    public void setSchemeUrl(String value) {
        set(40, value);
    }

    /**
     * Getter for <code>lau_unit_creative.scheme_url</code>. 唤起scheme链接
     */
    public String getSchemeUrl() {
        return (String) get(40);
    }

    /**
     * Setter for <code>lau_unit_creative.jump_type</code>. 跳转类型(1-链接 2-移动视频
     * 3-游戏 4-Web视频 5-页面ID）
     */
    public void setJumpType(Integer value) {
        set(41, value);
    }

    /**
     * Getter for <code>lau_unit_creative.jump_type</code>. 跳转类型(1-链接 2-移动视频
     * 3-游戏 4-Web视频 5-页面ID）
     */
    public Integer getJumpType() {
        return (Integer) get(41);
    }

    /**
     * Setter for <code>lau_unit_creative.bilibili_user_id</code>. 创意来源
     * 0-未知，1-个人起飞 2-现金版个人起飞 3-签约金订单 4-签约金托管个人起飞  5-通用托管个人起飞 
     */
    public void setBilibiliUserId(Integer value) {
        set(42, value);
    }

    /**
     * Getter for <code>lau_unit_creative.bilibili_user_id</code>. 创意来源
     * 0-未知，1-个人起飞 2-现金版个人起飞 3-签约金订单 4-签约金托管个人起飞  5-通用托管个人起飞 
     */
    public Integer getBilibiliUserId() {
        return (Integer) get(42);
    }

    /**
     * Setter for <code>lau_unit_creative.ad_version_controll_id</code>.
     * 广告版本控制ID
     */
    public void setAdVersionControllId(Integer value) {
        set(43, value);
    }

    /**
     * Getter for <code>lau_unit_creative.ad_version_controll_id</code>.
     * 广告版本控制ID
     */
    public Integer getAdVersionControllId() {
        return (Integer) get(43);
    }

    /**
     * Setter for <code>lau_unit_creative.mgk_page_id</code>. 万花筒页面ID
     */
    public void setMgkPageId(Long value) {
        set(44, value);
    }

    /**
     * Getter for <code>lau_unit_creative.mgk_page_id</code>. 万花筒页面ID
     */
    public Long getMgkPageId() {
        return (Long) get(44);
    }

    /**
     * Setter for <code>lau_unit_creative.ad_mark</code>. 广告业务标
     */
    public void setAdMark(String value) {
        set(45, value);
    }

    /**
     * Getter for <code>lau_unit_creative.ad_mark</code>. 广告业务标
     */
    public String getAdMark() {
        return (String) get(45);
    }

    /**
     * Setter for <code>lau_unit_creative.modify_offline_creative_id</code>.
     * 修改待下线的创意ID
     */
    public void setModifyOfflineCreativeId(Integer value) {
        set(46, value);
    }

    /**
     * Getter for <code>lau_unit_creative.modify_offline_creative_id</code>.
     * 修改待下线的创意ID
     */
    public Integer getModifyOfflineCreativeId() {
        return (Integer) get(46);
    }

    /**
     * Setter for <code>lau_unit_creative.flow_weight_state</code>. 流量权重状态
     * 1-正常，2-即将降权，3-已降权
     */
    public void setFlowWeightState(Integer value) {
        set(47, value);
    }

    /**
     * Getter for <code>lau_unit_creative.flow_weight_state</code>. 流量权重状态
     * 1-正常，2-即将降权，3-已降权
     */
    public Integer getFlowWeightState() {
        return (Integer) get(47);
    }

    /**
     * Setter for <code>lau_unit_creative.bus_mark_id</code>. 商业标id
     */
    public void setBusMarkId(Integer value) {
        set(48, value);
    }

    /**
     * Getter for <code>lau_unit_creative.bus_mark_id</code>. 商业标id
     */
    public Integer getBusMarkId() {
        return (Integer) get(48);
    }

    /**
     * Setter for <code>lau_unit_creative.style_ability</code>. 创意形态 1-静态图文
     * 2-动态图文 3-静态视频 4-广告位播放视频
     */
    public void setStyleAbility(Integer value) {
        set(49, value);
    }

    /**
     * Getter for <code>lau_unit_creative.style_ability</code>. 创意形态 1-静态图文
     * 2-动态图文 3-静态视频 4-广告位播放视频
     */
    public Integer getStyleAbility() {
        return (Integer) get(49);
    }

    /**
     * Setter for <code>lau_unit_creative.adp_version</code>. 广告平台版本号 0-老版 1-新版
     */
    public void setAdpVersion(Integer value) {
        set(50, value);
    }

    /**
     * Getter for <code>lau_unit_creative.adp_version</code>. 广告平台版本号 0-老版 1-新版
     */
    public Integer getAdpVersion() {
        return (Integer) get(50);
    }

    /**
     * Setter for <code>lau_unit_creative.template_group_id</code>. 模板组ID
     */
    public void setTemplateGroupId(Integer value) {
        set(51, value);
    }

    /**
     * Getter for <code>lau_unit_creative.template_group_id</code>. 模板组ID
     */
    public Integer getTemplateGroupId() {
        return (Integer) get(51);
    }

    /**
     * Setter for <code>lau_unit_creative.prefer_scene</code>. 优选广告位 0-否 1-是
     */
    public void setPreferScene(Integer value) {
        set(52, value);
    }

    /**
     * Getter for <code>lau_unit_creative.prefer_scene</code>. 优选广告位 0-否 1-是
     */
    public Integer getPreferScene() {
        return (Integer) get(52);
    }

    /**
     * Setter for <code>lau_unit_creative.is_programmatic</code>. 是否程序化创意(0 - 否;
     * 1 - 是)
     */
    public void setIsProgrammatic(Integer value) {
        set(53, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_programmatic</code>. 是否程序化创意(0 - 否;
     * 1 - 是)
     */
    public Integer getIsProgrammatic() {
        return (Integer) get(53);
    }

    /**
     * Setter for <code>lau_unit_creative.material_id</code>. 物料ID(0-未知)
     */
    public void setMaterialId(Long value) {
        set(54, value);
    }

    /**
     * Getter for <code>lau_unit_creative.material_id</code>. 物料ID(0-未知)
     */
    public Long getMaterialId() {
        return (Long) get(54);
    }

    /**
     * Setter for <code>lau_unit_creative.title_id</code>. 标题ID(0-未知)
     */
    public void setTitleId(Long value) {
        set(55, value);
    }

    /**
     * Getter for <code>lau_unit_creative.title_id</code>. 标题ID(0-未知)
     */
    public Long getTitleId() {
        return (Long) get(55);
    }

    /**
     * Setter for <code>lau_unit_creative.auto_audit_flag</code>. 是否被机审 0否 1是
     */
    public void setAutoAuditFlag(Integer value) {
        set(56, value);
    }

    /**
     * Getter for <code>lau_unit_creative.auto_audit_flag</code>. 是否被机审 0否 1是
     */
    public Integer getAutoAuditFlag() {
        return (Integer) get(56);
    }

    /**
     * Setter for <code>lau_unit_creative.is_new_fly</code>. 新起飞：0-否 1-是
     */
    public void setIsNewFly(Integer value) {
        set(57, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_new_fly</code>. 新起飞：0-否 1-是
     */
    public Integer getIsNewFly() {
        return (Integer) get(57);
    }

    /**
     * Setter for <code>lau_unit_creative.prog_audit_status</code>. 程序化创意审核状态:
     * 0-审核完成 1-待审
     */
    public void setProgAuditStatus(Integer value) {
        set(58, value);
    }

    /**
     * Getter for <code>lau_unit_creative.prog_audit_status</code>. 程序化创意审核状态:
     * 0-审核完成 1-待审
     */
    public Integer getProgAuditStatus() {
        return (Integer) get(58);
    }

    /**
     * Setter for <code>lau_unit_creative.prog_misc_elem_audit_status</code>.
     * 程序化创意额外元素审核状态: 0-审核通过 1-待审 2-审核拒绝 3-落地页待审核
     */
    public void setProgMiscElemAuditStatus(Integer value) {
        set(59, value);
    }

    /**
     * Getter for <code>lau_unit_creative.prog_misc_elem_audit_status</code>.
     * 程序化创意额外元素审核状态: 0-审核通过 1-待审 2-审核拒绝 3-落地页待审核
     */
    public Integer getProgMiscElemAuditStatus() {
        return (Integer) get(59);
    }

    /**
     * Setter for <code>lau_unit_creative.is_recheck</code>. 是否被质检 0否 1是
     */
    public void setIsRecheck(Integer value) {
        set(60, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_recheck</code>. 是否被质检 0否 1是
     */
    public Integer getIsRecheck() {
        return (Integer) get(60);
    }

    /**
     * Setter for <code>lau_unit_creative.flag</code>. 多种类型、枚举等通用字段。 0~100 来源
     */
    public void setFlag(Integer value) {
        set(61, value);
    }

    /**
     * Getter for <code>lau_unit_creative.flag</code>. 多种类型、枚举等通用字段。 0~100 来源
     */
    public Integer getFlag() {
        return (Integer) get(61);
    }

    /**
     * Setter for <code>lau_unit_creative.material_video_id</code>. 素材视频id
     */
    public void setMaterialVideoId(Long value) {
        set(62, value);
    }

    /**
     * Getter for <code>lau_unit_creative.material_video_id</code>. 素材视频id
     */
    public Long getMaterialVideoId() {
        return (Long) get(62);
    }

    /**
     * Setter for <code>lau_unit_creative.under_frame_audit_flag</code>.
     * 框下位置是否允许投放 0不允许 1允许
     */
    public void setUnderFrameAuditFlag(Integer value) {
        set(63, value);
    }

    /**
     * Getter for <code>lau_unit_creative.under_frame_audit_flag</code>.
     * 框下位置是否允许投放 0不允许 1允许
     */
    public Integer getUnderFrameAuditFlag() {
        return (Integer) get(63);
    }

    /**
     * Setter for <code>lau_unit_creative.is_auto_fill</code>. 是否自动填写0-手动填写
     * 1-自动填写
     */
    public void setIsAutoFill(Integer value) {
        set(64, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_auto_fill</code>. 是否自动填写0-手动填写
     * 1-自动填写
     */
    public Integer getIsAutoFill() {
        return (Integer) get(64);
    }

    /**
     * Setter for
     * <code>lau_unit_creative.promotion_purpose_content_secondary</code>.
     * 推广目的降级内容
     */
    public void setPromotionPurposeContentSecondary(String value) {
        set(65, value);
    }

    /**
     * Getter for
     * <code>lau_unit_creative.promotion_purpose_content_secondary</code>.
     * 推广目的降级内容
     */
    public String getPromotionPurposeContentSecondary() {
        return (String) get(65);
    }

    /**
     * Setter for <code>lau_unit_creative.is_managed</code>. 是否是专业托管的创意
     */
    public void setIsManaged(Integer value) {
        set(66, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_managed</code>. 是否是专业托管的创意
     */
    public Integer getIsManaged() {
        return (Integer) get(66);
    }

    /**
     * Setter for <code>lau_unit_creative.is_gd_plus</code>. 是否gd+：0-否 1-是
     */
    public void setIsGdPlus(Integer value) {
        set(67, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_gd_plus</code>. 是否gd+：0-否 1-是
     */
    public Integer getIsGdPlus() {
        return (Integer) get(67);
    }

    /**
     * Setter for <code>lau_unit_creative.advertising_mode</code>.
     * 投放模式(0-普通投放，1-原生内容投放)
     */
    public void setAdvertisingMode(Integer value) {
        set(68, value);
    }

    /**
     * Getter for <code>lau_unit_creative.advertising_mode</code>.
     * 投放模式(0-普通投放，1-原生内容投放)
     */
    public Integer getAdvertisingMode() {
        return (Integer) get(68);
    }

    /**
     * Setter for <code>lau_unit_creative.is_middle_ad</code>. 是新中台广告：0-否 1-是
     */
    public void setIsMiddleAd(Integer value) {
        set(69, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_middle_ad</code>. 是新中台广告：0-否 1-是
     */
    public Integer getIsMiddleAd() {
        return (Integer) get(69);
    }

    /**
     * Setter for <code>lau_unit_creative.is_video_bind</code>. 是否视频已授权，0：否，1：是
     */
    public void setIsVideoBind(Integer value) {
        set(70, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_video_bind</code>. 是否视频已授权，0：否，1：是
     */
    public Integer getIsVideoBind() {
        return (Integer) get(70);
    }

    /**
     * Setter for <code>lau_unit_creative.trackadf</code>. trackadf
     */
    public void setTrackadf(String value) {
        set(71, value);
    }

    /**
     * Getter for <code>lau_unit_creative.trackadf</code>. trackadf
     */
    public String getTrackadf() {
        return (String) get(71);
    }

    /**
     * Setter for <code>lau_unit_creative.is_page_group</code>. 是否绑定了落地页组 0-不是
     * 1-是
     */
    public void setIsPageGroup(Integer value) {
        set(72, value);
    }

    /**
     * Getter for <code>lau_unit_creative.is_page_group</code>. 是否绑定了落地页组 0-不是
     * 1-是
     */
    public Integer getIsPageGroup() {
        return (Integer) get(72);
    }

    /**
     * Setter for <code>lau_unit_creative.parent_creative_id</code>. 母创意id
     */
    public void setParentCreativeId(Integer value) {
        set(73, value);
    }

    /**
     * Getter for <code>lau_unit_creative.parent_creative_id</code>. 母创意id
     */
    public Integer getParentCreativeId() {
        return (Integer) get(73);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauUnitCreativeRecord
     */
    public LauUnitCreativeRecord() {
        super(TLauUnitCreative.LAU_UNIT_CREATIVE);
    }

    /**
     * Create a detached, initialised LauUnitCreativeRecord
     */
    public LauUnitCreativeRecord(Integer creativeId, Integer accountId, Integer campaignId, Integer creativeType, Integer unitId, String creativeName, String promotionPurposeContent, String customizedImpUrl, String customizedClickUrl, String title, String description, String extDescription, String imageUrl, String imageMd5, Long videoId, String videoUrl, String extImageUrl, String extImageMd5, String creativeJson, String reason, Integer templateId, Integer auditStatus, Integer status, Integer version, Integer isDeleted, Timestamp ctime, Timestamp mtime, Integer orderId, Integer salesType, Integer cmMark, String buttonCopy, Integer categoryFirstId, Integer categorySecondId, Integer isHistory, String tags, Date beginTime, Date endTime, Integer creativeStatus, Integer isMark, Integer isTag, String schemeUrl, Integer jumpType, Integer bilibiliUserId, Integer adVersionControllId, Long mgkPageId, String adMark, Integer modifyOfflineCreativeId, Integer flowWeightState, Integer busMarkId, Integer styleAbility, Integer adpVersion, Integer templateGroupId, Integer preferScene, Integer isProgrammatic, Long materialId, Long titleId, Integer autoAuditFlag, Integer isNewFly, Integer progAuditStatus, Integer progMiscElemAuditStatus, Integer isRecheck, Integer flag, Long materialVideoId, Integer underFrameAuditFlag, Integer isAutoFill, String promotionPurposeContentSecondary, Integer isManaged, Integer isGdPlus, Integer advertisingMode, Integer isMiddleAd, Integer isVideoBind, String trackadf, Integer isPageGroup, Integer parentCreativeId) {
        super(TLauUnitCreative.LAU_UNIT_CREATIVE);

        setCreativeId(creativeId);
        setAccountId(accountId);
        setCampaignId(campaignId);
        setCreativeType(creativeType);
        setUnitId(unitId);
        setCreativeName(creativeName);
        setPromotionPurposeContent(promotionPurposeContent);
        setCustomizedImpUrl(customizedImpUrl);
        setCustomizedClickUrl(customizedClickUrl);
        setTitle(title);
        setDescription(description);
        setExtDescription(extDescription);
        setImageUrl(imageUrl);
        setImageMd5(imageMd5);
        setVideoId(videoId);
        setVideoUrl(videoUrl);
        setExtImageUrl(extImageUrl);
        setExtImageMd5(extImageMd5);
        setCreativeJson(creativeJson);
        setReason(reason);
        setTemplateId(templateId);
        setAuditStatus(auditStatus);
        setStatus(status);
        setVersion(version);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setOrderId(orderId);
        setSalesType(salesType);
        setCmMark(cmMark);
        setButtonCopy(buttonCopy);
        setCategoryFirstId(categoryFirstId);
        setCategorySecondId(categorySecondId);
        setIsHistory(isHistory);
        setTags(tags);
        setBeginTime(beginTime);
        setEndTime(endTime);
        setCreativeStatus(creativeStatus);
        setIsMark(isMark);
        setIsTag(isTag);
        setSchemeUrl(schemeUrl);
        setJumpType(jumpType);
        setBilibiliUserId(bilibiliUserId);
        setAdVersionControllId(adVersionControllId);
        setMgkPageId(mgkPageId);
        setAdMark(adMark);
        setModifyOfflineCreativeId(modifyOfflineCreativeId);
        setFlowWeightState(flowWeightState);
        setBusMarkId(busMarkId);
        setStyleAbility(styleAbility);
        setAdpVersion(adpVersion);
        setTemplateGroupId(templateGroupId);
        setPreferScene(preferScene);
        setIsProgrammatic(isProgrammatic);
        setMaterialId(materialId);
        setTitleId(titleId);
        setAutoAuditFlag(autoAuditFlag);
        setIsNewFly(isNewFly);
        setProgAuditStatus(progAuditStatus);
        setProgMiscElemAuditStatus(progMiscElemAuditStatus);
        setIsRecheck(isRecheck);
        setFlag(flag);
        setMaterialVideoId(materialVideoId);
        setUnderFrameAuditFlag(underFrameAuditFlag);
        setIsAutoFill(isAutoFill);
        setPromotionPurposeContentSecondary(promotionPurposeContentSecondary);
        setIsManaged(isManaged);
        setIsGdPlus(isGdPlus);
        setAdvertisingMode(advertisingMode);
        setIsMiddleAd(isMiddleAd);
        setIsVideoBind(isVideoBind);
        setTrackadf(trackadf);
        setIsPageGroup(isPageGroup);
        setParentCreativeId(parentCreativeId);
    }

    /**
     * Create a detached, initialised LauUnitCreativeRecord
     */
    public LauUnitCreativeRecord(LauUnitCreativePo value) {
        super(TLauUnitCreative.LAU_UNIT_CREATIVE);

        if (value != null) {
            setCreativeId(value.getCreativeId());
            setAccountId(value.getAccountId());
            setCampaignId(value.getCampaignId());
            setCreativeType(value.getCreativeType());
            setUnitId(value.getUnitId());
            setCreativeName(value.getCreativeName());
            setPromotionPurposeContent(value.getPromotionPurposeContent());
            setCustomizedImpUrl(value.getCustomizedImpUrl());
            setCustomizedClickUrl(value.getCustomizedClickUrl());
            setTitle(value.getTitle());
            setDescription(value.getDescription());
            setExtDescription(value.getExtDescription());
            setImageUrl(value.getImageUrl());
            setImageMd5(value.getImageMd5());
            setVideoId(value.getVideoId());
            setVideoUrl(value.getVideoUrl());
            setExtImageUrl(value.getExtImageUrl());
            setExtImageMd5(value.getExtImageMd5());
            setCreativeJson(value.getCreativeJson());
            setReason(value.getReason());
            setTemplateId(value.getTemplateId());
            setAuditStatus(value.getAuditStatus());
            setStatus(value.getStatus());
            setVersion(value.getVersion());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setOrderId(value.getOrderId());
            setSalesType(value.getSalesType());
            setCmMark(value.getCmMark());
            setButtonCopy(value.getButtonCopy());
            setCategoryFirstId(value.getCategoryFirstId());
            setCategorySecondId(value.getCategorySecondId());
            setIsHistory(value.getIsHistory());
            setTags(value.getTags());
            setBeginTime(value.getBeginTime());
            setEndTime(value.getEndTime());
            setCreativeStatus(value.getCreativeStatus());
            setIsMark(value.getIsMark());
            setIsTag(value.getIsTag());
            setSchemeUrl(value.getSchemeUrl());
            setJumpType(value.getJumpType());
            setBilibiliUserId(value.getBilibiliUserId());
            setAdVersionControllId(value.getAdVersionControllId());
            setMgkPageId(value.getMgkPageId());
            setAdMark(value.getAdMark());
            setModifyOfflineCreativeId(value.getModifyOfflineCreativeId());
            setFlowWeightState(value.getFlowWeightState());
            setBusMarkId(value.getBusMarkId());
            setStyleAbility(value.getStyleAbility());
            setAdpVersion(value.getAdpVersion());
            setTemplateGroupId(value.getTemplateGroupId());
            setPreferScene(value.getPreferScene());
            setIsProgrammatic(value.getIsProgrammatic());
            setMaterialId(value.getMaterialId());
            setTitleId(value.getTitleId());
            setAutoAuditFlag(value.getAutoAuditFlag());
            setIsNewFly(value.getIsNewFly());
            setProgAuditStatus(value.getProgAuditStatus());
            setProgMiscElemAuditStatus(value.getProgMiscElemAuditStatus());
            setIsRecheck(value.getIsRecheck());
            setFlag(value.getFlag());
            setMaterialVideoId(value.getMaterialVideoId());
            setUnderFrameAuditFlag(value.getUnderFrameAuditFlag());
            setIsAutoFill(value.getIsAutoFill());
            setPromotionPurposeContentSecondary(value.getPromotionPurposeContentSecondary());
            setIsManaged(value.getIsManaged());
            setIsGdPlus(value.getIsGdPlus());
            setAdvertisingMode(value.getAdvertisingMode());
            setIsMiddleAd(value.getIsMiddleAd());
            setIsVideoBind(value.getIsVideoBind());
            setTrackadf(value.getTrackadf());
            setIsPageGroup(value.getIsPageGroup());
            setParentCreativeId(value.getParentCreativeId());
        }
    }
}
