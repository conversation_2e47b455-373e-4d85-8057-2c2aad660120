/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauCreativeMonitoringRecord;


/**
 * 效果创意-监控链接表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauCreativeMonitoring extends TableImpl<LauCreativeMonitoringRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_creative_monitoring</code>
     */
    public static final TLauCreativeMonitoring LAU_CREATIVE_MONITORING = new TLauCreativeMonitoring();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauCreativeMonitoringRecord> getRecordType() {
        return LauCreativeMonitoringRecord.class;
    }

    /**
     * The column <code>lau_creative_monitoring.id</code>. id
     */
    public final TableField<LauCreativeMonitoringRecord, Integer> ID = createField(DSL.name("id"), SQLDataType.INTEGER.nullable(false).identity(true), this, "id");

    /**
     * The column <code>lau_creative_monitoring.unit_id</code>. 单元ID
     */
    public final TableField<LauCreativeMonitoringRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元ID");

    /**
     * The column <code>lau_creative_monitoring.creative_id</code>. 创意ID
     */
    public final TableField<LauCreativeMonitoringRecord, Long> CREATIVE_ID = createField(DSL.name("creative_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "创意ID");

    /**
     * The column <code>lau_creative_monitoring.url</code>. 监控URL
     */
    public final TableField<LauCreativeMonitoringRecord, String> URL = createField(DSL.name("url"), SQLDataType.VARCHAR(1024).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "监控URL");

    /**
     * The column <code>lau_creative_monitoring.type</code>. 1-展示监控 2-点击监控
     * 3-播放0s监控 4-播放3s监控 5-播放5s监控 6-游戏点击
     */
    public final TableField<LauCreativeMonitoringRecord, Integer> TYPE = createField(DSL.name("type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "1-展示监控 2-点击监控 3-播放0s监控 4-播放3s监控 5-播放5s监控 6-游戏点击");

    /**
     * The column <code>lau_creative_monitoring.is_deleted</code>. 是否删除0否 1是
     */
    public final TableField<LauCreativeMonitoringRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除0否 1是");

    /**
     * The column <code>lau_creative_monitoring.ctime</code>. 创建时间
     */
    public final TableField<LauCreativeMonitoringRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_creative_monitoring.mtime</code>. 更新时间
     */
    public final TableField<LauCreativeMonitoringRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    private TLauCreativeMonitoring(Name alias, Table<LauCreativeMonitoringRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauCreativeMonitoring(Name alias, Table<LauCreativeMonitoringRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("效果创意-监控链接表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_creative_monitoring</code> table reference
     */
    public TLauCreativeMonitoring(String alias) {
        this(DSL.name(alias), LAU_CREATIVE_MONITORING);
    }

    /**
     * Create an aliased <code>lau_creative_monitoring</code> table reference
     */
    public TLauCreativeMonitoring(Name alias) {
        this(alias, LAU_CREATIVE_MONITORING);
    }

    /**
     * Create a <code>lau_creative_monitoring</code> table reference
     */
    public TLauCreativeMonitoring() {
        this(DSL.name("lau_creative_monitoring"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauCreativeMonitoringRecord, Integer> getIdentity() {
        return (Identity<LauCreativeMonitoringRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauCreativeMonitoringRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauCreativeMonitoring.LAU_CREATIVE_MONITORING, DSL.name("KEY_lau_creative_monitoring_PRIMARY"), new TableField[] { TLauCreativeMonitoring.LAU_CREATIVE_MONITORING.ID }, true);
    }

    @Override
    public TLauCreativeMonitoring as(String alias) {
        return new TLauCreativeMonitoring(DSL.name(alias), this);
    }

    @Override
    public TLauCreativeMonitoring as(Name alias) {
        return new TLauCreativeMonitoring(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeMonitoring rename(String name) {
        return new TLauCreativeMonitoring(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeMonitoring rename(Name name) {
        return new TLauCreativeMonitoring(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<Integer, Integer, Long, String, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
