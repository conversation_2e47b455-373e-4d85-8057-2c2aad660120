package sycpb.platform.cpm.pandora.infra.compare;

import lombok.RequiredArgsConstructor;
import org.javers.core.Javers;
import org.javers.core.diff.changetype.PropertyChange;
import org.springframework.stereotype.Service;

import java.util.List;

@Deprecated
@Service
@RequiredArgsConstructor
public class CompareServiceImpl implements ICompareService {
    private final Javers javers;

    @Override
    public <T> List<PropertyChange> propertyChanges(T oldVersion, T newVersion) {
        final var diff = javers.compare(oldVersion, newVersion);
        if (!diff.hasChanges()) return List.of();

        return diff.getChangesByType(PropertyChange.class);
    }
}
