/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record21;
import org.jooq.Row21;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitTargetRule;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitTargetRulePo;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitTargetRuleRecord extends UpdatableRecordImpl<LauUnitTargetRuleRecord> implements Record21<Integer, Integer, String, String, String, String, String, String, String, String, String, Integer, Timestamp, Timestamp, String, String, String, String, String, String, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_unit_target_rule.id</code>.
     */
    public void setId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.id</code>.
     */
    public Integer getId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_unit_target_rule.unit_id</code>. 单位ID
     */
    public void setUnitId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.unit_id</code>. 单位ID
     */
    public Integer getUnitId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_unit_target_rule.area</code>. 区域定向
     */
    public void setArea(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.area</code>. 区域定向
     */
    public String getArea() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lau_unit_target_rule.gender</code>. 性别定向
     */
    public void setGender(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.gender</code>. 性别定向
     */
    public String getGender() {
        return (String) get(3);
    }

    /**
     * Setter for <code>lau_unit_target_rule.age</code>. 年龄定向
     */
    public void setAge(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.age</code>. 年龄定向
     */
    public String getAge() {
        return (String) get(4);
    }

    /**
     * Setter for <code>lau_unit_target_rule.os</code>. 平台定向
     */
    public void setOs(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.os</code>. 平台定向
     */
    public String getOs() {
        return (String) get(5);
    }

    /**
     * Setter for <code>lau_unit_target_rule.network</code>. 网络环境定向
     */
    public void setNetwork(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.network</code>. 网络环境定向
     */
    public String getNetwork() {
        return (String) get(6);
    }

    /**
     * Setter for <code>lau_unit_target_rule.category</code>. 分区定向
     */
    public void setCategory(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.category</code>. 分区定向
     */
    public String getCategory() {
        return (String) get(7);
    }

    /**
     * Setter for <code>lau_unit_target_rule.keyword</code>. 关键词定向
     */
    public void setKeyword(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.keyword</code>. 关键词定向
     */
    public String getKeyword() {
        return (String) get(8);
    }

    /**
     * Setter for <code>lau_unit_target_rule.device_brand</code>. 设备品牌定向
     */
    public void setDeviceBrand(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.device_brand</code>. 设备品牌定向
     */
    public String getDeviceBrand() {
        return (String) get(9);
    }

    /**
     * Setter for <code>lau_unit_target_rule.app_category</code>. APP分类定向
     */
    public void setAppCategory(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.app_category</code>. APP分类定向
     */
    public String getAppCategory() {
        return (String) get(10);
    }

    /**
     * Setter for <code>lau_unit_target_rule.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>lau_unit_target_rule.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(12);
    }

    /**
     * Setter for <code>lau_unit_target_rule.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(13);
    }

    /**
     * Setter for <code>lau_unit_target_rule.converted_user_filter</code>.
     * 已转化用户过滤定向
     */
    public void setConvertedUserFilter(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.converted_user_filter</code>.
     * 已转化用户过滤定向
     */
    public String getConvertedUserFilter() {
        return (String) get(14);
    }

    /**
     * Setter for <code>lau_unit_target_rule.intelligent_mass</code>. 智能放量
     */
    public void setIntelligentMass(String value) {
        set(15, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.intelligent_mass</code>. 智能放量
     */
    public String getIntelligentMass() {
        return (String) get(15);
    }

    /**
     * Setter for <code>lau_unit_target_rule.video_partition</code>. 视频分区
     */
    public void setVideoPartition(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.video_partition</code>. 视频分区
     */
    public String getVideoPartition() {
        return (String) get(16);
    }

    /**
     * Setter for <code>lau_unit_target_rule.age_customize</code>. 自定义年龄定向
     */
    public void setAgeCustomize(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.age_customize</code>. 自定义年龄定向
     */
    public String getAgeCustomize() {
        return (String) get(17);
    }

    /**
     * Setter for <code>lau_unit_target_rule.area_type</code>.
     * 地域类型：0-全部，1-实时在此的用户，2-常住在此的用户，3-旅游在此的用户
     */
    public void setAreaType(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.area_type</code>.
     * 地域类型：0-全部，1-实时在此的用户，2-常住在此的用户，3-旅游在此的用户
     */
    public String getAreaType() {
        return (String) get(18);
    }

    /**
     * Setter for <code>lau_unit_target_rule.phone_price</code>. 手机价格定向
     * -1(不限)、0(0-2000元)、1(2000-4000元)、2(4000-6000元)、3(6000-8000元)、4(8000-10000元)、5(10000元以上)
     */
    public void setPhonePrice(String value) {
        set(19, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.phone_price</code>. 手机价格定向
     * -1(不限)、0(0-2000元)、1(2000-4000元)、2(4000-6000元)、3(6000-8000元)、4(8000-10000元)、5(10000元以上)
     */
    public String getPhonePrice() {
        return (String) get(19);
    }

    /**
     * Setter for <code>lau_unit_target_rule.area_level</code>. 地域定向-发展划分
     */
    public void setAreaLevel(String value) {
        set(20, value);
    }

    /**
     * Getter for <code>lau_unit_target_rule.area_level</code>. 地域定向-发展划分
     */
    public String getAreaLevel() {
        return (String) get(20);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record21 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row21<Integer, Integer, String, String, String, String, String, String, String, String, String, Integer, Timestamp, Timestamp, String, String, String, String, String, String, String> fieldsRow() {
        return (Row21) super.fieldsRow();
    }

    @Override
    public Row21<Integer, Integer, String, String, String, String, String, String, String, String, String, Integer, Timestamp, Timestamp, String, String, String, String, String, String, String> valuesRow() {
        return (Row21) super.valuesRow();
    }

    @Override
    public Field<Integer> field1() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.UNIT_ID;
    }

    @Override
    public Field<String> field3() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AREA;
    }

    @Override
    public Field<String> field4() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.GENDER;
    }

    @Override
    public Field<String> field5() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AGE;
    }

    @Override
    public Field<String> field6() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.OS;
    }

    @Override
    public Field<String> field7() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.NETWORK;
    }

    @Override
    public Field<String> field8() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.CATEGORY;
    }

    @Override
    public Field<String> field9() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.KEYWORD;
    }

    @Override
    public Field<String> field10() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.DEVICE_BRAND;
    }

    @Override
    public Field<String> field11() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.APP_CATEGORY;
    }

    @Override
    public Field<Integer> field12() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field13() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.CTIME;
    }

    @Override
    public Field<Timestamp> field14() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.MTIME;
    }

    @Override
    public Field<String> field15() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.CONVERTED_USER_FILTER;
    }

    @Override
    public Field<String> field16() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.INTELLIGENT_MASS;
    }

    @Override
    public Field<String> field17() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.VIDEO_PARTITION;
    }

    @Override
    public Field<String> field18() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AGE_CUSTOMIZE;
    }

    @Override
    public Field<String> field19() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AREA_TYPE;
    }

    @Override
    public Field<String> field20() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.PHONE_PRICE;
    }

    @Override
    public Field<String> field21() {
        return TLauUnitTargetRule.LAU_UNIT_TARGET_RULE.AREA_LEVEL;
    }

    @Override
    public Integer component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getUnitId();
    }

    @Override
    public String component3() {
        return getArea();
    }

    @Override
    public String component4() {
        return getGender();
    }

    @Override
    public String component5() {
        return getAge();
    }

    @Override
    public String component6() {
        return getOs();
    }

    @Override
    public String component7() {
        return getNetwork();
    }

    @Override
    public String component8() {
        return getCategory();
    }

    @Override
    public String component9() {
        return getKeyword();
    }

    @Override
    public String component10() {
        return getDeviceBrand();
    }

    @Override
    public String component11() {
        return getAppCategory();
    }

    @Override
    public Integer component12() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component13() {
        return getCtime();
    }

    @Override
    public Timestamp component14() {
        return getMtime();
    }

    @Override
    public String component15() {
        return getConvertedUserFilter();
    }

    @Override
    public String component16() {
        return getIntelligentMass();
    }

    @Override
    public String component17() {
        return getVideoPartition();
    }

    @Override
    public String component18() {
        return getAgeCustomize();
    }

    @Override
    public String component19() {
        return getAreaType();
    }

    @Override
    public String component20() {
        return getPhonePrice();
    }

    @Override
    public String component21() {
        return getAreaLevel();
    }

    @Override
    public Integer value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getUnitId();
    }

    @Override
    public String value3() {
        return getArea();
    }

    @Override
    public String value4() {
        return getGender();
    }

    @Override
    public String value5() {
        return getAge();
    }

    @Override
    public String value6() {
        return getOs();
    }

    @Override
    public String value7() {
        return getNetwork();
    }

    @Override
    public String value8() {
        return getCategory();
    }

    @Override
    public String value9() {
        return getKeyword();
    }

    @Override
    public String value10() {
        return getDeviceBrand();
    }

    @Override
    public String value11() {
        return getAppCategory();
    }

    @Override
    public Integer value12() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value13() {
        return getCtime();
    }

    @Override
    public Timestamp value14() {
        return getMtime();
    }

    @Override
    public String value15() {
        return getConvertedUserFilter();
    }

    @Override
    public String value16() {
        return getIntelligentMass();
    }

    @Override
    public String value17() {
        return getVideoPartition();
    }

    @Override
    public String value18() {
        return getAgeCustomize();
    }

    @Override
    public String value19() {
        return getAreaType();
    }

    @Override
    public String value20() {
        return getPhonePrice();
    }

    @Override
    public String value21() {
        return getAreaLevel();
    }

    @Override
    public LauUnitTargetRuleRecord value1(Integer value) {
        setId(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value2(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value3(String value) {
        setArea(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value4(String value) {
        setGender(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value5(String value) {
        setAge(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value6(String value) {
        setOs(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value7(String value) {
        setNetwork(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value8(String value) {
        setCategory(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value9(String value) {
        setKeyword(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value10(String value) {
        setDeviceBrand(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value11(String value) {
        setAppCategory(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value12(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value13(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value14(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value15(String value) {
        setConvertedUserFilter(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value16(String value) {
        setIntelligentMass(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value17(String value) {
        setVideoPartition(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value18(String value) {
        setAgeCustomize(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value19(String value) {
        setAreaType(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value20(String value) {
        setPhonePrice(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord value21(String value) {
        setAreaLevel(value);
        return this;
    }

    @Override
    public LauUnitTargetRuleRecord values(Integer value1, Integer value2, String value3, String value4, String value5, String value6, String value7, String value8, String value9, String value10, String value11, Integer value12, Timestamp value13, Timestamp value14, String value15, String value16, String value17, String value18, String value19, String value20, String value21) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        value20(value20);
        value21(value21);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauUnitTargetRuleRecord
     */
    public LauUnitTargetRuleRecord() {
        super(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE);
    }

    /**
     * Create a detached, initialised LauUnitTargetRuleRecord
     */
    public LauUnitTargetRuleRecord(Integer id, Integer unitId, String area, String gender, String age, String os, String network, String category, String keyword, String deviceBrand, String appCategory, Integer isDeleted, Timestamp ctime, Timestamp mtime, String convertedUserFilter, String intelligentMass, String videoPartition, String ageCustomize, String areaType, String phonePrice, String areaLevel) {
        super(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE);

        setId(id);
        setUnitId(unitId);
        setArea(area);
        setGender(gender);
        setAge(age);
        setOs(os);
        setNetwork(network);
        setCategory(category);
        setKeyword(keyword);
        setDeviceBrand(deviceBrand);
        setAppCategory(appCategory);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setConvertedUserFilter(convertedUserFilter);
        setIntelligentMass(intelligentMass);
        setVideoPartition(videoPartition);
        setAgeCustomize(ageCustomize);
        setAreaType(areaType);
        setPhonePrice(phonePrice);
        setAreaLevel(areaLevel);
    }

    /**
     * Create a detached, initialised LauUnitTargetRuleRecord
     */
    public LauUnitTargetRuleRecord(LauUnitTargetRulePo value) {
        super(TLauUnitTargetRule.LAU_UNIT_TARGET_RULE);

        if (value != null) {
            setId(value.getId());
            setUnitId(value.getUnitId());
            setArea(value.getArea());
            setGender(value.getGender());
            setAge(value.getAge());
            setOs(value.getOs());
            setNetwork(value.getNetwork());
            setCategory(value.getCategory());
            setKeyword(value.getKeyword());
            setDeviceBrand(value.getDeviceBrand());
            setAppCategory(value.getAppCategory());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setConvertedUserFilter(value.getConvertedUserFilter());
            setIntelligentMass(value.getIntelligentMass());
            setVideoPartition(value.getVideoPartition());
            setAgeCustomize(value.getAgeCustomize());
            setAreaType(value.getAreaType());
            setPhonePrice(value.getPhonePrice());
            setAreaLevel(value.getAreaLevel());
        }
    }
}
