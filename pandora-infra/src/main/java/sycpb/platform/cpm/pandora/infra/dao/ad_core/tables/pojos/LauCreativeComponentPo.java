/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeComponentPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   isDeleted;
    private Integer   accountId;
    private Integer   campaignId;
    private Integer   unitId;
    private Integer   creativeId;
    private Integer   componentType;
    private Long      componentId;

    public LauCreativeComponentPo() {}

    public LauCreativeComponentPo(LauCreativeComponentPo value) {
        this.id = value.id;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.isDeleted = value.isDeleted;
        this.accountId = value.accountId;
        this.campaignId = value.campaignId;
        this.unitId = value.unitId;
        this.creativeId = value.creativeId;
        this.componentType = value.componentType;
        this.componentId = value.componentId;
    }

    public LauCreativeComponentPo(
        Long      id,
        Timestamp ctime,
        Timestamp mtime,
        Integer   isDeleted,
        Integer   accountId,
        Integer   campaignId,
        Integer   unitId,
        Integer   creativeId,
        Integer   componentType,
        Long      componentId
    ) {
        this.id = id;
        this.ctime = ctime;
        this.mtime = mtime;
        this.isDeleted = isDeleted;
        this.accountId = accountId;
        this.campaignId = campaignId;
        this.unitId = unitId;
        this.creativeId = creativeId;
        this.componentType = componentType;
        this.componentId = componentId;
    }

    /**
     * Getter for <code>lau_creative_component.id</code>. id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_creative_component.id</code>. id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_creative_component.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_creative_component.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_creative_component.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_creative_component.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_creative_component.is_deleted</code>. 软删除:
     * 0-未删除,1-已删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_creative_component.is_deleted</code>. 软删除:
     * 0-未删除,1-已删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_creative_component.account_id</code>. 账号id
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>lau_creative_component.account_id</code>. 账号id
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>lau_creative_component.campaign_id</code>. 计划id
     */
    public Integer getCampaignId() {
        return this.campaignId;
    }

    /**
     * Setter for <code>lau_creative_component.campaign_id</code>. 计划id
     */
    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    /**
     * Getter for <code>lau_creative_component.unit_id</code>. 单元id
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_creative_component.unit_id</code>. 单元id
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for <code>lau_creative_component.creative_id</code>. 创意id
     */
    public Integer getCreativeId() {
        return this.creativeId;
    }

    /**
     * Setter for <code>lau_creative_component.creative_id</code>. 创意id
     */
    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    /**
     * Getter for <code>lau_creative_component.component_type</code>. 组件类型:
     * 0-未定义, 1-框下组件, 2-story通用组件, 3-story优惠券组件, 4-story图片组件
     */
    public Integer getComponentType() {
        return this.componentType;
    }

    /**
     * Setter for <code>lau_creative_component.component_type</code>. 组件类型:
     * 0-未定义, 1-框下组件, 2-story通用组件, 3-story优惠券组件, 4-story图片组件
     */
    public void setComponentType(Integer componentType) {
        this.componentType = componentType;
    }

    /**
     * Getter for <code>lau_creative_component.component_id</code>. 组件id
     */
    public Long getComponentId() {
        return this.componentId;
    }

    /**
     * Setter for <code>lau_creative_component.component_id</code>. 组件id
     */
    public void setComponentId(Long componentId) {
        this.componentId = componentId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauCreativeComponentPo (");

        sb.append(id);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(accountId);
        sb.append(", ").append(campaignId);
        sb.append(", ").append(unitId);
        sb.append(", ").append(creativeId);
        sb.append(", ").append(componentType);
        sb.append(", ").append(componentId);

        sb.append(")");
        return sb.toString();
    }
}
