/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TResTargetPackageInstalledUserFilterUpgrade;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.ResTargetPackageInstalledUserFilterUpgradePo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.ResTargetPackageInstalledUserFilterUpgradeRecord;


/**
 * 新版定向包已安装用户过滤定向表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ResTargetPackageInstalledUserFilterUpgradeDao extends DAOImpl<ResTargetPackageInstalledUserFilterUpgradeRecord, ResTargetPackageInstalledUserFilterUpgradePo, Long> {

    /**
     * Create a new ResTargetPackageInstalledUserFilterUpgradeDao without any
     * configuration
     */
    public ResTargetPackageInstalledUserFilterUpgradeDao() {
        super(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE, ResTargetPackageInstalledUserFilterUpgradePo.class);
    }

    /**
     * Create a new ResTargetPackageInstalledUserFilterUpgradeDao with an
     * attached configuration
     */
    @Autowired
    public ResTargetPackageInstalledUserFilterUpgradeDao(Configuration configuration) {
        super(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE, ResTargetPackageInstalledUserFilterUpgradePo.class, configuration);
    }

    @Override
    public Long getId(ResTargetPackageInstalledUserFilterUpgradePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchById(Long... values) {
        return fetch(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public ResTargetPackageInstalledUserFilterUpgradePo fetchOneById(Long value) {
        return fetchOne(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.ID, value);
    }

    /**
     * Fetch records that have <code>target_package_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchRangeOfTargetPackageId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.TARGET_PACKAGE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>target_package_id IN (values)</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchByTargetPackageId(Integer... values) {
        return fetch(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.TARGET_PACKAGE_ID, values);
    }

    /**
     * Fetch a unique record that has <code>target_package_id = value</code>
     */
    public ResTargetPackageInstalledUserFilterUpgradePo fetchOneByTargetPackageId(Integer value) {
        return fetchOne(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.TARGET_PACKAGE_ID, value);
    }

    /**
     * Fetch records that have <code>filter_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchRangeOfFilterType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.FILTER_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>filter_type IN (values)</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchByFilterType(Integer... values) {
        return fetch(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.FILTER_TYPE, values);
    }

    /**
     * Fetch records that have <code>target_content BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchRangeOfTargetContent(String lowerInclusive, String upperInclusive) {
        return fetchRange(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.TARGET_CONTENT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>target_content IN (values)</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchByTargetContent(String... values) {
        return fetch(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.TARGET_CONTENT, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchByIsDeleted(Integer... values) {
        return fetch(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchByCtime(Timestamp... values) {
        return fetch(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<ResTargetPackageInstalledUserFilterUpgradePo> fetchByMtime(Timestamp... values) {
        return fetch(TResTargetPackageInstalledUserFilterUpgrade.RES_TARGET_PACKAGE_INSTALLED_USER_FILTER_UPGRADE.MTIME, values);
    }
}
