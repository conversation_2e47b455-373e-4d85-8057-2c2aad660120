/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauSplashScreenCreativeButtonSlideInfoRecord;


/**
 * 效果闪屏滑动按钮表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauSplashScreenCreativeButtonSlideInfo extends TableImpl<LauSplashScreenCreativeButtonSlideInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>lau_splash_screen_creative_button_slide_info</code>
     */
    public static final TLauSplashScreenCreativeButtonSlideInfo LAU_SPLASH_SCREEN_CREATIVE_BUTTON_SLIDE_INFO = new TLauSplashScreenCreativeButtonSlideInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauSplashScreenCreativeButtonSlideInfoRecord> getRecordType() {
        return LauSplashScreenCreativeButtonSlideInfoRecord.class;
    }

    /**
     * The column <code>lau_splash_screen_creative_button_slide_info.id</code>.
     * 自增id
     */
    public final TableField<LauSplashScreenCreativeButtonSlideInfoRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增id");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_slide_info.creative_id</code>.
     * 创意id
     */
    public final TableField<LauSplashScreenCreativeButtonSlideInfoRecord, Integer> CREATIVE_ID = createField(DSL.name("creative_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "创意id");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_slide_info.button_id</code>. 按钮id
     */
    public final TableField<LauSplashScreenCreativeButtonSlideInfoRecord, Long> BUTTON_ID = createField(DSL.name("button_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "按钮id");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_slide_info.slide_angle</code>.
     * 滑动角度
     */
    public final TableField<LauSplashScreenCreativeButtonSlideInfoRecord, Integer> SLIDE_ANGLE = createField(DSL.name("slide_angle"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "滑动角度");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_slide_info.slide_distance</code>.
     * 滑动距离
     */
    public final TableField<LauSplashScreenCreativeButtonSlideInfoRecord, Integer> SLIDE_DISTANCE = createField(DSL.name("slide_distance"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "滑动距离");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_slide_info.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public final TableField<LauSplashScreenCreativeButtonSlideInfoRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除，0是有效，1是删除");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_slide_info.ctime</code>. 添加时间
     */
    public final TableField<LauSplashScreenCreativeButtonSlideInfoRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column
     * <code>lau_splash_screen_creative_button_slide_info.mtime</code>. 更新时间
     */
    public final TableField<LauSplashScreenCreativeButtonSlideInfoRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    private TLauSplashScreenCreativeButtonSlideInfo(Name alias, Table<LauSplashScreenCreativeButtonSlideInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauSplashScreenCreativeButtonSlideInfo(Name alias, Table<LauSplashScreenCreativeButtonSlideInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("效果闪屏滑动按钮表"), TableOptions.table());
    }

    /**
     * Create an aliased
     * <code>lau_splash_screen_creative_button_slide_info</code> table reference
     */
    public TLauSplashScreenCreativeButtonSlideInfo(String alias) {
        this(DSL.name(alias), LAU_SPLASH_SCREEN_CREATIVE_BUTTON_SLIDE_INFO);
    }

    /**
     * Create an aliased
     * <code>lau_splash_screen_creative_button_slide_info</code> table reference
     */
    public TLauSplashScreenCreativeButtonSlideInfo(Name alias) {
        this(alias, LAU_SPLASH_SCREEN_CREATIVE_BUTTON_SLIDE_INFO);
    }

    /**
     * Create a <code>lau_splash_screen_creative_button_slide_info</code> table
     * reference
     */
    public TLauSplashScreenCreativeButtonSlideInfo() {
        this(DSL.name("lau_splash_screen_creative_button_slide_info"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauSplashScreenCreativeButtonSlideInfoRecord, Long> getIdentity() {
        return (Identity<LauSplashScreenCreativeButtonSlideInfoRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauSplashScreenCreativeButtonSlideInfoRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauSplashScreenCreativeButtonSlideInfo.LAU_SPLASH_SCREEN_CREATIVE_BUTTON_SLIDE_INFO, DSL.name("KEY_lau_splash_screen_creative_button_slide_info_PRIMARY"), new TableField[] { TLauSplashScreenCreativeButtonSlideInfo.LAU_SPLASH_SCREEN_CREATIVE_BUTTON_SLIDE_INFO.ID }, true);
    }

    @Override
    public TLauSplashScreenCreativeButtonSlideInfo as(String alias) {
        return new TLauSplashScreenCreativeButtonSlideInfo(DSL.name(alias), this);
    }

    @Override
    public TLauSplashScreenCreativeButtonSlideInfo as(Name alias) {
        return new TLauSplashScreenCreativeButtonSlideInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauSplashScreenCreativeButtonSlideInfo rename(String name) {
        return new TLauSplashScreenCreativeButtonSlideInfo(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauSplashScreenCreativeButtonSlideInfo rename(Name name) {
        return new TLauSplashScreenCreativeButtonSlideInfo(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<Long, Integer, Long, Integer, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
