/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TOcpxBidSection;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.OcpxBidSectionPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.OcpxBidSectionRecord;


/**
 * 优化目标出价配置
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class OcpxBidSectionDao extends DAOImpl<OcpxBidSectionRecord, OcpxBidSectionPo, Integer> {

    /**
     * Create a new OcpxBidSectionDao without any configuration
     */
    public OcpxBidSectionDao() {
        super(TOcpxBidSection.OCPX_BID_SECTION, OcpxBidSectionPo.class);
    }

    /**
     * Create a new OcpxBidSectionDao with an attached configuration
     */
    @Autowired
    public OcpxBidSectionDao(Configuration configuration) {
        super(TOcpxBidSection.OCPX_BID_SECTION, OcpxBidSectionPo.class, configuration);
    }

    @Override
    public Integer getId(OcpxBidSectionPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<OcpxBidSectionPo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TOcpxBidSection.OCPX_BID_SECTION.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<OcpxBidSectionPo> fetchById(Integer... values) {
        return fetch(TOcpxBidSection.OCPX_BID_SECTION.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public OcpxBidSectionPo fetchOneById(Integer value) {
        return fetchOne(TOcpxBidSection.OCPX_BID_SECTION.ID, value);
    }

    /**
     * Fetch records that have <code>ocpx_target_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<OcpxBidSectionPo> fetchRangeOfOcpxTargetId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TOcpxBidSection.OCPX_BID_SECTION.OCPX_TARGET_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ocpx_target_id IN (values)</code>
     */
    public List<OcpxBidSectionPo> fetchByOcpxTargetId(Integer... values) {
        return fetch(TOcpxBidSection.OCPX_BID_SECTION.OCPX_TARGET_ID, values);
    }

    /**
     * Fetch records that have <code>category_first_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<OcpxBidSectionPo> fetchRangeOfCategoryFirstId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TOcpxBidSection.OCPX_BID_SECTION.CATEGORY_FIRST_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category_first_id IN (values)</code>
     */
    public List<OcpxBidSectionPo> fetchByCategoryFirstId(Integer... values) {
        return fetch(TOcpxBidSection.OCPX_BID_SECTION.CATEGORY_FIRST_ID, values);
    }

    /**
     * Fetch records that have <code>category_second_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<OcpxBidSectionPo> fetchRangeOfCategorySecondId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TOcpxBidSection.OCPX_BID_SECTION.CATEGORY_SECOND_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category_second_id IN (values)</code>
     */
    public List<OcpxBidSectionPo> fetchByCategorySecondId(Integer... values) {
        return fetch(TOcpxBidSection.OCPX_BID_SECTION.CATEGORY_SECOND_ID, values);
    }

    /**
     * Fetch records that have <code>effective_nums BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<OcpxBidSectionPo> fetchRangeOfEffectiveNums(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TOcpxBidSection.OCPX_BID_SECTION.EFFECTIVE_NUMS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>effective_nums IN (values)</code>
     */
    public List<OcpxBidSectionPo> fetchByEffectiveNums(Integer... values) {
        return fetch(TOcpxBidSection.OCPX_BID_SECTION.EFFECTIVE_NUMS, values);
    }

    /**
     * Fetch records that have <code>ninety_percent_bid BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<OcpxBidSectionPo> fetchRangeOfNinetyPercentBid(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TOcpxBidSection.OCPX_BID_SECTION.NINETY_PERCENT_BID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ninety_percent_bid IN (values)</code>
     */
    public List<OcpxBidSectionPo> fetchByNinetyPercentBid(Integer... values) {
        return fetch(TOcpxBidSection.OCPX_BID_SECTION.NINETY_PERCENT_BID, values);
    }

    /**
     * Fetch records that have <code>fifty_percent_bid BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<OcpxBidSectionPo> fetchRangeOfFiftyPercentBid(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TOcpxBidSection.OCPX_BID_SECTION.FIFTY_PERCENT_BID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>fifty_percent_bid IN (values)</code>
     */
    public List<OcpxBidSectionPo> fetchByFiftyPercentBid(Integer... values) {
        return fetch(TOcpxBidSection.OCPX_BID_SECTION.FIFTY_PERCENT_BID, values);
    }

    /**
     * Fetch records that have <code>thirty_percent_bid BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<OcpxBidSectionPo> fetchRangeOfThirtyPercentBid(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TOcpxBidSection.OCPX_BID_SECTION.THIRTY_PERCENT_BID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>thirty_percent_bid IN (values)</code>
     */
    public List<OcpxBidSectionPo> fetchByThirtyPercentBid(Integer... values) {
        return fetch(TOcpxBidSection.OCPX_BID_SECTION.THIRTY_PERCENT_BID, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<OcpxBidSectionPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TOcpxBidSection.OCPX_BID_SECTION.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<OcpxBidSectionPo> fetchByCtime(Timestamp... values) {
        return fetch(TOcpxBidSection.OCPX_BID_SECTION.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<OcpxBidSectionPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TOcpxBidSection.OCPX_BID_SECTION.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<OcpxBidSectionPo> fetchByMtime(Timestamp... values) {
        return fetch(TOcpxBidSection.OCPX_BID_SECTION.MTIME, values);
    }
}
