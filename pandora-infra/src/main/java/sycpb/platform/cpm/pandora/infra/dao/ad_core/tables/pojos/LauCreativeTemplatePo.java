/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 创意与广告位组及模板关联表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeTemplatePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Integer   accountId;
    private Integer   unitId;
    private Long      creativeId;
    private Integer   slotGroupId;
    private Integer   templateId;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   isDeleted;
    private Integer   bizStatus;
    private Integer   reservedPrice;
    private Integer   reserveRuleId;
    private Integer   creativeStyle;
    private Integer   busMarkId;

    public LauCreativeTemplatePo() {}

    public LauCreativeTemplatePo(LauCreativeTemplatePo value) {
        this.id = value.id;
        this.accountId = value.accountId;
        this.unitId = value.unitId;
        this.creativeId = value.creativeId;
        this.slotGroupId = value.slotGroupId;
        this.templateId = value.templateId;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.isDeleted = value.isDeleted;
        this.bizStatus = value.bizStatus;
        this.reservedPrice = value.reservedPrice;
        this.reserveRuleId = value.reserveRuleId;
        this.creativeStyle = value.creativeStyle;
        this.busMarkId = value.busMarkId;
    }

    public LauCreativeTemplatePo(
        Long      id,
        Integer   accountId,
        Integer   unitId,
        Long      creativeId,
        Integer   slotGroupId,
        Integer   templateId,
        Timestamp ctime,
        Timestamp mtime,
        Integer   isDeleted,
        Integer   bizStatus,
        Integer   reservedPrice,
        Integer   reserveRuleId,
        Integer   creativeStyle,
        Integer   busMarkId
    ) {
        this.id = id;
        this.accountId = accountId;
        this.unitId = unitId;
        this.creativeId = creativeId;
        this.slotGroupId = slotGroupId;
        this.templateId = templateId;
        this.ctime = ctime;
        this.mtime = mtime;
        this.isDeleted = isDeleted;
        this.bizStatus = bizStatus;
        this.reservedPrice = reservedPrice;
        this.reserveRuleId = reserveRuleId;
        this.creativeStyle = creativeStyle;
        this.busMarkId = busMarkId;
    }

    /**
     * Getter for <code>lau_creative_template.id</code>. 自增ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_creative_template.id</code>. 自增ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_creative_template.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return this.accountId;
    }

    /**
     * Setter for <code>lau_creative_template.account_id</code>. 账号ID
     */
    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    /**
     * Getter for <code>lau_creative_template.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_creative_template.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for <code>lau_creative_template.creative_id</code>. 创意ID
     */
    public Long getCreativeId() {
        return this.creativeId;
    }

    /**
     * Setter for <code>lau_creative_template.creative_id</code>. 创意ID
     */
    public void setCreativeId(Long creativeId) {
        this.creativeId = creativeId;
    }

    /**
     * Getter for <code>lau_creative_template.slot_group_id</code>. 广告位组ID
     */
    public Integer getSlotGroupId() {
        return this.slotGroupId;
    }

    /**
     * Setter for <code>lau_creative_template.slot_group_id</code>. 广告位组ID
     */
    public void setSlotGroupId(Integer slotGroupId) {
        this.slotGroupId = slotGroupId;
    }

    /**
     * Getter for <code>lau_creative_template.template_id</code>. 模板ID
     */
    public Integer getTemplateId() {
        return this.templateId;
    }

    /**
     * Setter for <code>lau_creative_template.template_id</code>. 模板ID
     */
    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    /**
     * Getter for <code>lau_creative_template.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_creative_template.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_creative_template.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_creative_template.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_creative_template.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_creative_template.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_creative_template.biz_status</code>. 0-正常, 1-低于保留价
     */
    public Integer getBizStatus() {
        return this.bizStatus;
    }

    /**
     * Setter for <code>lau_creative_template.biz_status</code>. 0-正常, 1-低于保留价
     */
    public void setBizStatus(Integer bizStatus) {
        this.bizStatus = bizStatus;
    }

    /**
     * Getter for <code>lau_creative_template.reserved_price</code>. 保留价
     */
    public Integer getReservedPrice() {
        return this.reservedPrice;
    }

    /**
     * Setter for <code>lau_creative_template.reserved_price</code>. 保留价
     */
    public void setReservedPrice(Integer reservedPrice) {
        this.reservedPrice = reservedPrice;
    }

    /**
     * Getter for <code>lau_creative_template.reserve_rule_id</code>. 决定保留价的规则id
     */
    public Integer getReserveRuleId() {
        return this.reserveRuleId;
    }

    /**
     * Setter for <code>lau_creative_template.reserve_rule_id</code>. 决定保留价的规则id
     */
    public void setReserveRuleId(Integer reserveRuleId) {
        this.reserveRuleId = reserveRuleId;
    }

    /**
     * Getter for <code>lau_creative_template.creative_style</code>.
     * 创意形态(视频版位合并下移至模板维度)
     */
    public Integer getCreativeStyle() {
        return this.creativeStyle;
    }

    /**
     * Setter for <code>lau_creative_template.creative_style</code>.
     * 创意形态(视频版位合并下移至模板维度)
     */
    public void setCreativeStyle(Integer creativeStyle) {
        this.creativeStyle = creativeStyle;
    }

    /**
     * Getter for <code>lau_creative_template.bus_mark_id</code>.
     * 商业标ID(视频版位合并下移至模板维度)
     */
    public Integer getBusMarkId() {
        return this.busMarkId;
    }

    /**
     * Setter for <code>lau_creative_template.bus_mark_id</code>.
     * 商业标ID(视频版位合并下移至模板维度)
     */
    public void setBusMarkId(Integer busMarkId) {
        this.busMarkId = busMarkId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauCreativeTemplatePo (");

        sb.append(id);
        sb.append(", ").append(accountId);
        sb.append(", ").append(unitId);
        sb.append(", ").append(creativeId);
        sb.append(", ").append(slotGroupId);
        sb.append(", ").append(templateId);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(bizStatus);
        sb.append(", ").append(reservedPrice);
        sb.append(", ").append(reserveRuleId);
        sb.append(", ").append(creativeStyle);
        sb.append(", ").append(busMarkId);

        sb.append(")");
        return sb.toString();
    }
}
