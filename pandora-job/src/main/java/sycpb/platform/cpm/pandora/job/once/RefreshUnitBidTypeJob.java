package sycpb.platform.cpm.pandora.job.once;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.SelectLimitPercentStep;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitRecord;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.constants.AdpVersion;
import sycpb.platform.cpm.pandora.service.constants.BidType;
import sycpb.platform.cpm.pandora.service.constants.BusinessDomain;
import sycpb.platform.cpm.pandora.service.constants.LaunchStatus;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnit.LAU_UNIT;

/**
 * @ClassName RefreshUnitBidTypeJob
 * <AUTHOR>
 * @Date 2025/4/14 9:57 下午
 * @Version 1.0
 **/
@Component
@JobHandler("RefreshUnitBidTypeJob")
@Slf4j
public class RefreshUnitBidTypeJob extends IJobHandler {

    @Resource(name = MySqlConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;


    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Integer startUnitId = 0;
        Integer endUnitId = null;
        if (StringUtils.hasText(param)) {
            String[] params = param.split(",");
            startUnitId = Integer.parseInt(params[0]);
            endUnitId = Integer.parseInt(params[1]);
        }

        Integer limit = 1000;

        List<Integer> unitIdList = refreshByPage(startUnitId, endUnitId, limit);
        while (!CollectionUtils.isEmpty(unitIdList)) {
            startUnitId = unitIdList.get(unitIdList.size() - 1);
            unitIdList = refreshByPage(startUnitId, endUnitId, limit);
        }
        return SUCCESS;
    }

    private List<Integer> refreshByPage(Integer startUnitId, Integer endUnitId, Integer limit) {
        log.info("RefreshUnitBidTypeJob refreshByPage startUnitId:{}", startUnitId);
        List<LauUnitPo> lauUnitPoList = adCore.selectFrom(LAU_UNIT)
                .where(LAU_UNIT.UNIT_ID.gt(startUnitId)
                        .and(LAU_UNIT.STATUS.ne(LaunchStatus.DELETED)))
                .orderBy(LAU_UNIT.UNIT_ID.asc())
                .limit(limit)
                .fetch().into(LauUnitPo.class);

        if (CollectionUtils.isEmpty(lauUnitPoList)) {
            return Collections.emptyList();
        }

        List<Integer> unitIdList = lauUnitPoList.stream()
                .map(LauUnitPo::getUnitId)
                .collect(Collectors.toList());

        if (NumberUtils.isPositive(endUnitId) && unitIdList.stream().allMatch(unitId -> unitId > endUnitId)) {
            return Collections.emptyList();
        }

        // 过滤出需要处理的po 老三连的个人起飞或者新三连单元
        List<LauUnitPo> needRefreshUnitPos = lauUnitPoList.stream()
                .filter(unitPo -> AdpVersion.isModelMerge(unitPo.getAdpVersion())
                        || BusinessDomain.PERSONAL_FLY == unitPo.getBusinessDomain()
                        && AdpVersion.ARCHIVE_MERGE == unitPo.getAdpVersion())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needRefreshUnitPos)) {
            return unitIdList;
        }

        needRefreshUnitPos.forEach(unitPo -> {
            Integer bidType = generateBidType(unitPo);
            adCore.update(LAU_UNIT)
                    .set(LAU_UNIT.BID_TYPE, bidType)
                    .set(LAU_UNIT.MTIME, unitPo.getMtime())
                    .where(LAU_UNIT.UNIT_ID.eq(unitPo.getUnitId()))
                    .execute();
            log.info("RefreshUnitBidTypeJob executeUpdate unitId:{}, bidType:{}", unitPo.getUnitId(), bidType);
        });

        return unitIdList;

    }

    private Integer generateBidType(LauUnitPo po) {
        if (Objects.isNull(po)) {
            return 0;
        }

        if (NumberUtils.isPositive(po.getIsNoBid())) {
            return BidType.NO_BID;
        }

        if (NumberUtils.isPositive(po.getOcpcTarget())) {
            return BidType.OCPM;
        }

        if (Objects.equals(po.getSalesType(), 11)) {
            return BidType.CPM;
        }

        if (Objects.equals(po.getSalesType(), 12)) {
            return BidType.CPC;
        }

        log.info("unknown generated bid type, unitId:{}, isNoBid, ocpcTarget:{}, salesType:{}",
                po.getUnitId(), po.getOcpcTarget(), po.getSalesType());
        return 0;
    }
}
