package sycpb.platform.cpm.pandora.job.once;

import com.bapis.ad.pandora.resource.CpaTarget;
import com.bapis.ad.pandora.resource.PromotionContentType;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeTemplatePo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitPo;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.resource.location.ILocationService;
import sycpb.platform.cpm.pandora.service.api.resource.location.bos.TemplateBo;
import sycpb.platform.cpm.pandora.service.constants.AdpVersion;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;
import static sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig.AD_CORE_DSL_CONTEXT;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnit.LAU_UNIT;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitCreative.LAU_UNIT_CREATIVE;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitExtra.LAU_UNIT_EXTRA;

/**
 * @ClassName Adp6RemoveNativeTemplateRefreshJob
 * <AUTHOR>
 * @Date 2024/12/20 12:03 下午
 * @Version 1.0
 **/
@Component
@JobHandler("Adp6RemoveNativeTemplateRefreshJob")
@Slf4j
public class Adp6RemoveNativeTemplateRefreshJob extends IJobHandler {

    @Resource(name = AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;

    @Autowired
    private ILocationService locDimensionService;

    private static final List<Integer> NEED_HANDLE_PPC_LIST =
            Lists.newArrayList(
                    PromotionContentType.PPC_CLUE_VALUE,
                    PromotionContentType.PPC_APP_DOWNLOAD_VALUE,
                    PromotionContentType.PPC_GAME_CENTER_VALUE);

    private static final List<Integer> NEED_HANDLE_OCPC_TARGET_LIST =
            Lists.newArrayList(CpaTarget.CPA_APP_ACTIVE_VALUE, CpaTarget.CPA_FORM_SUBMIT_VALUE);

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Integer startUnitId = 0;
        if (StringUtils.hasText(param)) {
            startUnitId = Integer.parseInt(param);
        }

        List<LauUnitPo> unitPoList = getUnitListByPage(startUnitId, 1000);
        while (!CollectionUtils.isEmpty(unitPoList)) {
            log.info("Adp6RemoveNativeTemplateRefreshJob currentStartUnitId:{}", startUnitId);
            filterAndHandleUnitPoList(unitPoList);
            startUnitId = unitPoList.get(unitPoList.size() - 1).getUnitId();
            unitPoList = getUnitListByPage(startUnitId, 1000);
        }


        return SUCCESS;
    }


    private List<LauUnitPo> getUnitListByPage(Integer startUnitId, Integer limit) {
        if (!NumberUtils.isPositive(limit)) {
            return Collections.emptyList();
        }

        return adCore.selectFrom(LAU_UNIT)
                .where(LAU_UNIT.UNIT_ID.gt(startUnitId)
                        .and(LAU_UNIT.STATUS.ne(3))
                        .and(LAU_UNIT.IS_DELETED.eq(0)))
                .orderBy(LAU_UNIT.UNIT_ID.asc())
                .limit(limit)
                .fetch().into(LauUnitPo.class);
    }

    private void filterAndHandleUnitPoList(List<LauUnitPo> unitPoList) {
        if (CollectionUtils.isEmpty(unitPoList)) {
            return;
        }

        List<LauUnitPo> needHandleUnitPoList = unitPoList.stream()
                .filter(unitPo -> AdpVersion.MODEL_MERGE == unitPo.getAdpVersion())
                .filter(unitPo -> NEED_HANDLE_PPC_LIST.contains(unitPo.getPromotionPurposeType()))
                .filter(unitPo -> NEED_HANDLE_OCPC_TARGET_LIST.contains(unitPo.getOcpcTarget()))
                .filter(unitPo -> NumberUtils.isPositive(unitPo.getOcpxTargetTwo()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needHandleUnitPoList)) {
            return;
        }

        List<Integer> needHandleUnitIdList = needHandleUnitPoList.stream()
                .map(LauUnitPo::getUnitId)
                .collect(Collectors.toList());

        // 常规投放且已经有创意了
        List<Integer> nonBiliNativeUnitIds = adCore.select(LAU_UNIT_EXTRA.UNIT_ID)
                .from(LAU_UNIT_EXTRA)
                .where(LAU_UNIT_EXTRA.UNIT_ID.in(needHandleUnitIdList)
                        .and(LAU_UNIT_EXTRA.IS_BILI_NATIVE.eq(0)))
                .fetch().into(Integer.class);

        if (CollectionUtils.isEmpty(nonBiliNativeUnitIds)) {
            return;
        }

        List<Integer> needHandleCreativeIds = adCore.select(LAU_UNIT_CREATIVE.CREATIVE_ID)
                .from(LAU_UNIT_CREATIVE)
                .where(LAU_UNIT_CREATIVE.UNIT_ID.in(nonBiliNativeUnitIds)
                        .and(LAU_UNIT_CREATIVE.STATUS.ne(3))
                        .and(LAU_UNIT_CREATIVE.ADP_VERSION.eq(6)))
                .fetch().into(Integer.class);

        if (CollectionUtils.isEmpty(needHandleCreativeIds)) {
            return;
        }

        Set<Integer> biliNativeTemplateIdSet = locDimensionService.getTemplateMap().values().stream()
                .filter(templateBo -> NumberUtils.isPositive(templateBo.getIsSupportNative()))
                .map(TemplateBo::getTemplateId)
                .collect(Collectors.toSet());

        needHandleCreativeIds.forEach(creativeId -> handleSingleCreativeTemplate(creativeId, biliNativeTemplateIdSet));
    }

    private void handleSingleCreativeTemplate(Integer creativeId, Set<Integer> biliNativeTemplateIdSet) {
        List<LauCreativeTemplatePo> needCheckCreativeTemplatePoList = adCore.selectFrom(LAU_CREATIVE_TEMPLATE)
                .where(LAU_CREATIVE_TEMPLATE.CREATIVE_ID.eq(creativeId.longValue()))
                .fetch().into(LauCreativeTemplatePo.class);

        List<Integer> needRemoveCreativeTemplateIds = needCheckCreativeTemplatePoList.stream()
                .filter(creativeTemplatePo -> biliNativeTemplateIdSet.contains(creativeTemplatePo.getTemplateId()))
                .map(LauCreativeTemplatePo::getTemplateId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needRemoveCreativeTemplateIds)) {
            return;
        }

        adCore.deleteFrom(LAU_CREATIVE_TEMPLATE)
                .where(LAU_CREATIVE_TEMPLATE.CREATIVE_ID.eq(creativeId.longValue()))
                .and(LAU_CREATIVE_TEMPLATE.TEMPLATE_ID.in(needRemoveCreativeTemplateIds))
                .execute();

        log.info("handleSingleCreativeTemplate deleted creativeId:{}, templateIds:{}", creativeId, needRemoveCreativeTemplateIds);
    }
}
