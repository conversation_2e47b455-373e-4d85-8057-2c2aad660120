package sycpb.platform.cpm.pandora.job.once;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.CreativeLandingPageService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.LauCreativeLandingPageBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.UrlParamService;

import java.util.List;
import java.util.stream.Collectors;

import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;

/**
 * @ClassName Adp6TemplatePageUrlRefreshJob
 * <AUTHOR>
 * @Date 2024/12/17 5:56 下午
 * @Version 1.0
 **/
@Component
@JobHandler("Adp6TemplatePageUrlRefreshJob")
@Slf4j
public class Adp6TemplatePageUrlRefreshJob extends IJobHandler {

    @Autowired
    private CreativeLandingPageService creativeLandingPageService;

    @Autowired
    private UrlParamService urlParamService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Integer startCreativeId = 0;
        if (StringUtils.hasText(param)) {
            startCreativeId = Integer.parseInt(param);
        }

        List<LauCreativeLandingPageBo> creativeLandingPageBoList =
                creativeLandingPageService.getPageByStartCreativeId(startCreativeId, 1000);
        while(!CollectionUtils.isEmpty(creativeLandingPageBoList)) {
            log.info("Adp6TemplatePageUrlRefreshJob startCreativeId:{}", startCreativeId);
            handleAndUpdateCreativeLandingPage(creativeLandingPageBoList);
            startCreativeId = creativeLandingPageBoList.get(creativeLandingPageBoList.size() - 1).getCreativeId();
            creativeLandingPageBoList =
                    creativeLandingPageService.getPageByStartCreativeId(startCreativeId, 1000);
        }

        return SUCCESS;
    }

    // 具体更新联投落地页
    // 理论上链接没有sycp的都缺宏参数
    private void handleAndUpdateCreativeLandingPage(List<LauCreativeLandingPageBo> creativeLandingPageBoList) {
        if (CollectionUtils.isEmpty(creativeLandingPageBoList)) {
            return;
        }

        List<LauCreativeLandingPageBo> needUpdateLandingPageBoList = creativeLandingPageBoList.stream()
                .filter(bo -> !bo.getContainerUrl().contains("sycp"))
                .map(bo -> {
                    boolean isAppletsTemplatePage = StringUtils.hasText(bo.getContainerSecondaryUrl());
                    String containerUrl = urlParamService.applyUrlParamsForTemplatePage(bo.getContainerUrl(), isAppletsTemplatePage);
                    String containerSecondaryUrl =
                            urlParamService.applyUrlParamsForTemplatePage(bo.getContainerSecondaryUrl(), isAppletsTemplatePage);
                    LauCreativeLandingPageBo updateBo = new LauCreativeLandingPageBo();
                    updateBo.setId(bo.getId());
                    updateBo.setAccountId(bo.getAccountId());
                    updateBo.setCampaignId(bo.getCampaignId());
                    updateBo.setUnitId(bo.getUnitId());
                    updateBo.setCreativeId(bo.getCreativeId());
                    updateBo.setContainerPageId(bo.getContainerPageId());
                    updateBo.setContainerUrl(containerUrl);
                    updateBo.setContainerSecondaryUrl(containerSecondaryUrl);
                    return updateBo;
                }).collect(Collectors.toList());

        creativeLandingPageService.batchUpdateCreativeLandingPageUrl(needUpdateLandingPageBoList);
    }

}
