package sycpb.platform.cpm.pandora.job.mapi;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.stereotype.Component;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeLandingPagePo;

import javax.annotation.Resource;
import java.util.List;

import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;
import static sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig.AD_CORE_DSL_CONTEXT;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE;


/**
 * @ClassName AsyncSaveFailureRetryJob
 * <AUTHOR>
 * @Date 2024/8/9 2:21 下午
 * @Version 1.0
 **/
@Component
@JobHandler("LandingPage205Job")
@Slf4j
public class LandingPage205Job extends IJobHandler {

    @Resource(name = AD_CORE_DSL_CONTEXT)
    private DSLContext dsl;


    private int count = 0;

    @Override
    public ReturnT<String> execute(String s) {
        count = 0;
        log.info("LandingPage205Job start");


        process();

        log.info("LandingPage205Job end , count ={}", count);
        return SUCCESS;
    }


    private void process() {
        Long id = 0L;

        while (true) {
            var condition = LAU_CREATIVE_LANDING_PAGE.ID.gt(id);
            List<LauCreativeLandingPagePo> creativeLandingPagePos = dsl.selectFrom(LAU_CREATIVE_LANDING_PAGE)
                    .where(condition)
                    .orderBy(LAU_CREATIVE_LANDING_PAGE.ID.asc())
                    .limit(1000)
                    .fetchInto(LauCreativeLandingPagePo.class);

            if (creativeLandingPagePos.isEmpty()) {
                return;
            }
            for (LauCreativeLandingPagePo creativeLandingPagePo : creativeLandingPagePos) {
                try {
                    filter(creativeLandingPagePo);
                    id = creativeLandingPagePo.getId();
                } catch (Exception e) {
                    log.error("LandingPage205Job is ERROR ,filter = {} ", creativeLandingPagePo, e);
                }
            }
        }


    }

    public void filter(LauCreativeLandingPagePo creativeLandingPagePo) {
        if (!creativeLandingPagePo.getContainerUrl().contains("page_type=205")) {
            return;
        }

        dsl.deleteFrom(LAU_CREATIVE_LANDING_PAGE)
                .where(LAU_CREATIVE_LANDING_PAGE.ID.eq(creativeLandingPagePo.getId()))
                .execute();
        log.info("LandingPage205Job in = {}", creativeLandingPagePo.getId());

    }


}
