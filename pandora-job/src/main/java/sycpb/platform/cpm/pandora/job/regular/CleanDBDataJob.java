package sycpb.platform.cpm.pandora.job.regular;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;

import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;
import static sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig.AD_CORE_DSL_CONTEXT;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeLandingPage.LAU_CREATIVE_LANDING_PAGE;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeTemplate.LAU_CREATIVE_TEMPLATE;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitMonitoring.LAU_UNIT_MONITORING;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitTargetRule.LAU_UNIT_TARGET_RULE;
import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TSearchAdUnitKeyWord.SEARCH_AD_UNIT_KEY_WORD;

/**
 * @ClassName KafkaJob
 * <AUTHOR>
 * @Date 2024/8/9 2:21 下午
 * @Version 1.0
 **/
@Component
@JobHandler("CleanDBDataJob")
@Slf4j
public class CleanDBDataJob extends IJobHandler {

    @Value("${job.batch.clean.size:3}")
    private Integer clean_size;

    @Resource(name = AD_CORE_DSL_CONTEXT)
    private DSLContext dsl;


    private int count = 0;
    final String TOPIC_NAME = "r_ods.business_ad_mysql_clean";
    final String BOOTSTRAP_SERVERS = "jscs-kafka-sycpb-pa-162.host.bilibili.co:9092,jscs-kafka-sycpb-pa-163.host.bilibili.co:9092,jscs-kafka-sycpb-pa-164.host.bilibili.co:9092,jscs-kafka-sycpb-pa-165.host.bilibili.co:9092,jscs-kafka-sycpb-pa-166.host.bilibili.co:9092";

    private static ArrayList<Long> list1 = new ArrayList<>();
    private static ArrayList<Long> list2 = new ArrayList<>();

    private static ArrayList<Long> list3 = new ArrayList<>();

    private static ArrayList<Long> list4 = new ArrayList<>();

    private static ArrayList<Long> list5 = new ArrayList<>();


    private static ArrayList<Long> list6 = new ArrayList<>();

    @Override
    public ReturnT<String> execute(String s) {
        log.info("CleanDBDataJob start");
        list1 = new ArrayList<>();
        list2 = new ArrayList<>();
        list3 = new ArrayList<>();
        list4 = new ArrayList<>();
        list5 = new ArrayList<>();
        list6= new ArrayList<>();

        a();
        log.info("CleanDBDataJob end");
        return SUCCESS;
    }

    private void a() {
        // 设置消费者的配置属性
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS); // Kafka 服务器地址
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "test-group"); // 消费者组ID
        props.put("enable.auto.commit", "true");
        props.put("auto.commit.interval.ms", "1000");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());

        // 创建 Kafka 消费者实例
        try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props)) {
            // 订阅主题（你可以订阅多个主题）
            consumer.subscribe(Collections.singletonList(TOPIC_NAME)); // 这里改为你的主题名
            while (true) {
                // 轮询记录（拉取数据）
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(100)); // 拉取数据的超时时间
                log.info("CleanDBDataJob job ");
                for (ConsumerRecord<String, String> record : records) {
                    JSONObject object = JSON.parseObject(record.value());
                    log.info("CleanDBDataJob Offset = {}, Key = {}, Value = {}, dbid= {}", record.offset(), record.key(), record.value(), object.get("dbid"));
                    String key = object.getString("key");
                    switch (key) {
                        case "lau_creative_template":
                            list2.add(object.getLong("dbid"));
                            if (list2.size() >= clean_size) {
                                dsl.deleteFrom(LAU_CREATIVE_TEMPLATE)
                                        .where(LAU_CREATIVE_TEMPLATE.ID.in(list2))
                                        .execute();
                                list2 = new ArrayList<>();
                            }
                            break;
                        case "search_ad_unit_key_word":
                            list1.add(object.getLong("dbid"));
                            if (list1.size() >= clean_size) {
                                dsl.deleteFrom(SEARCH_AD_UNIT_KEY_WORD)
                                        .where(SEARCH_AD_UNIT_KEY_WORD.ID.in(list1))
                                        .execute();
                                list1 = new ArrayList<>();
                            }
                            break;
                        case "lau_unit_target_rule":
                            list3.add(object.getLong("dbid"));
                            if (list3.size() >= clean_size) {
                                dsl.deleteFrom(LAU_UNIT_TARGET_RULE)
                                        .where(LAU_UNIT_TARGET_RULE.ID.in(list3))
                                        .execute();
                                list3 = new ArrayList<>();
                            }
                            break;
                        case "lau_creative_fly_ext_info":
                            list4.add(object.getLong("dbid"));
                            if (list4.size() >= clean_size) {
                                dsl.deleteFrom(LAU_CREATIVE_FLY_EXT_INFO)
                                        .where(LAU_CREATIVE_FLY_EXT_INFO.ID.in(list4))
                                        .execute();
                                list4 = new ArrayList<>();
                            }
                            break;
                        case "lau_creative_landing_page":
                            list5.add(object.getLong("dbid"));
                            if (list5.size() >= clean_size) {
                                dsl.deleteFrom(LAU_CREATIVE_LANDING_PAGE)
                                        .where(LAU_CREATIVE_LANDING_PAGE.ID.in(list5))
                                        .execute();
                                list5 = new ArrayList<>();
                            }
                            break;
                        case "lau_unit_monitoring":
                            list6.add(object.getLong("dbid"));
                            if (list6.size() >= clean_size) {
                                dsl.deleteFrom(LAU_UNIT_MONITORING)
                                        .where(LAU_UNIT_MONITORING.ID.in(list6))
                                        .execute();
                                list6 = new ArrayList<>();
                            }
                            break;


                        default:
                            break;
                    }
                    log.info("CleanDBDataJob, dbid= {}", object.getLong("dbid"));
                }

            }
        } catch (Exception e) {
            log.error("CleanDBDataJob error", e);
        }
    }


}
