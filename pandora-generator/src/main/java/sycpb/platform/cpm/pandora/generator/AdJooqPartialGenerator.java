package sycpb.platform.cpm.pandora.generator;

import lombok.SneakyThrows;
import org.jooq.codegen.GenerationTool;
import org.jooq.meta.jaxb.Configuration;
import org.jooq.meta.jaxb.Jdbc;
import org.jooq.meta.jaxb.Target;

import java.util.List;
import java.util.stream.Collectors;

public class AdJooqPartialGenerator {

    @SneakyThrows
    public static void main(String[] args) {
        final List<List<String>> excludedFields = List.of();
        final var includeRegex = Utils.genTableRegex("lau_creative_explore_customize_condition");
        final var excludeRegex = excludedFields.stream().map(Utils::genColumnsRegex).collect(Collectors.joining("|"));
        final var configuration = new Configuration()
                .withJdbc(new Jdbc()
                        .withUrl("******************************************************************************************************************************************************************************************************")
                        .withUser("bilibili_business_ad")
                        .withPassword("32CrCuwxOpuXTJirRt9VnE6kkFnlNz6U"))
                .withGenerator(Utils.newGenerator()
                        .withDatabase(Utils.newDatabase(includeRegex, excludeRegex, "bilibili_business_ad"))
                        .withTarget(new Target()
                                .withPackageName("sycpb.platform.cpm.pandora.infra.dao.ad")
                                .withDirectory("pandora-infra/src/main/java")
                                .withClean(false)
                        ));
        GenerationTool.generate(configuration);
    }
}
