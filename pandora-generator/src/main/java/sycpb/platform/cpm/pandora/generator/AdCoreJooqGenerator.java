package sycpb.platform.cpm.pandora.generator;

import lombok.SneakyThrows;
import org.jooq.codegen.GenerationTool;
import org.jooq.meta.jaxb.Configuration;
import org.jooq.meta.jaxb.Jdbc;
import org.jooq.meta.jaxb.Target;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class AdCoreJooqGenerator {

    protected final static List<String> migrationTables = Stream.of(
            "lau_native_archive",
            "ad_product_mapping",
            "fly_assist_search",
            "lau_campaign",
            "lau_creative_link_replace",
            "lau_creative_anchor_mapping",
            "lau_creative_archive",
            "lau_creative_button_copy",
            "lau_creative_component",
            "lau_creative_extra",
            "lau_creative_fly_dynamic_info",
            "lau_creative_fly_ext_info",
            "lau_creative_image",
            "lau_creative_landing_page",
            "lau_creative_landing_page_group",
            "lau_creative_mini_game_mapping",
            "lau_creative_native_archive_relativity",
            "lau_creative_pgc_archive",
            "lau_creative_qualification",
            "lau_creative_smart_title",
            "lau_creative_tab",
            "lau_creative_template",
            "lau_creative_title",
            "lau_programmatic_creative_detail",
            "lau_programmatic_creative_material_total_md5",
            "lau_shadow_creative",
            "lau_tag_ids",
            "lau_unit",
            "lau_creative_audit_stat",
            "lau_unit_business_category",
            "lau_unit_creative",
            "lau_unit_extra",
            "lau_unit_game",
            "lau_unit_goods",
            "lau_unit_shop_goods",
            "lau_unit_keywords_log",
            "lau_unit_live_reserve",
            "lau_unit_monitoring",
            "lau_unit_scene",
            "lau_unit_search_negative_keyword",
            "lau_unit_target_bili_client_upgrade",
            "lau_unit_target_crowd_pack",
            "lau_unit_target_extra",
            "lau_unit_target_extra_crowd_pack",
            "lau_unit_target_installed_user_filter",
            "lau_unit_target_os_version_upgrade",
            "lau_unit_target_profession_interest",
            "lau_unit_target_profession_interest_auto",
            "lau_unit_target_rule",
            "lau_unit_target_tag",
            "lau_unit_target_archive",
            "search_ad_unit_key_word",
            "search_ad_unit_key_word_package",
            "lau_unit_bili_mini_game",
            "lau_unit_mini_game_mapping",
            "lau_creative_aigc_material_replace_history",
            "lau_splash_screen_creative",
            "lau_splash_screen_creative_image",
            "lau_splash_screen_creative_button",
            "lau_splash_screen_creative_button_slide_info",
            "lau_splash_screen_creative_button_twist_info"
    ).collect(Collectors.toList());


    @SneakyThrows
    public static void main(String[] args) {
        final List<List<String>> excludedFields = List.of();
        //统计出的写相关表
        final var includeRegex = migrationTables.stream().map(Utils::genTableRegex).collect(Collectors.joining("|"));
        final var excludeRegex = excludedFields.stream().map(Utils::genColumnsRegex).collect(Collectors.joining("|"));
        final var configuration = new Configuration()
                .withJdbc(new Jdbc()
                        .withUrl("******************************************************************************************************************************************************************************************************")
                        .withUser("bilibili_business_ad")
                        .withPassword("32CrCuwxOpuXTJirRt9VnE6kkFnlNz6U"))
                        //.withUrl("**************************************************************************************************************************************************************************************************")
                        //.withUser("business_ad_core")
                        //.withPassword("7eRFXGE5lIUfKUfIAzBjFoFhynOKPXPH"))
                .withGenerator(Utils.newGenerator()
                        .withDatabase(Utils.newDatabase(includeRegex, excludeRegex, "bilibili_business_ad"))
//                        .withDatabase(Utils.newDatabase(includeRegex, excludeRegex, "business_ad_core"))
                        .withTarget(new Target()
                                .withPackageName("sycpb.platform.cpm.pandora.infra.dao.ad_core")
                                .withDirectory("pandora-infra/src/main/java")
                        ));
        GenerationTool.generate(configuration);
    }
}
